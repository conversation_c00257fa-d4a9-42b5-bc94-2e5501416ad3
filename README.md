# Flame Clash

一款基于Godot 4.4引擎开发的3D动作冒险游戏，玩家需要点燃树木并躲避敌人的追击。

## 版本控制规则

本项目使用Git标签进行版本控制，标签命名规则如下：

### 标签格式

标签格式为：`v年年年年月月日日时时分分秒秒`

例如：`v20250326153101` 表示在2025年3月26日15:31:01创建的版本。

### 自动创建标签

项目根目录提供了一个自动生成标签的脚本：`version_tag.sh`。使用方法：

```bash
# 添加执行权限（仅需第一次执行）
chmod +x version_tag.sh

# 运行脚本
./version_tag.sh
```

脚本会自动：
1. 生成基于当前时间的标签
2. 提示输入提交信息
3. 提交所有更改
4. 创建带有提交信息的标签

### 手动创建标签

也可以手动创建标签：

```bash
# 提交更改
git add .
git commit -m "提交信息"

# 创建标签（使用当前时间）
TAG_NAME="v$(date +"%Y%m%d%H%M%S")"
git tag -a $TAG_NAME -m "标签说明"

# 推送标签
git push --tags
```

## 游戏功能

- 关卡生成器：程序化生成游戏关卡
- 迷雾系统：探索式游戏体验
- 小地图：帮助玩家导航
- AI敌人：会追踪玩家并扑灭火焰
- 多样化关卡：支持多个游戏关卡 