#!/bin/bash

# 创建一个函数来重命名文件并更新引用
rename_and_update() {
    local old_path=$1
    local new_path=$2
    
    # 检查文件是否存在
    if [ ! -f "$old_path" ]; then
        echo "错误: 文件 $old_path 不存在"
        return 1
    fi
    
    # 重命名文件
    echo "重命名: $old_path -> $new_path"
    mv "$old_path" "$new_path"
    
    # 更新所有引用
    echo "更新引用..."
    old_path_escaped=$(echo "$old_path" | sed 's/\//\\\//g')
    new_path_escaped=$(echo "$new_path" | sed 's/\//\\\//g')
    
    # 在所有.gd和.tscn文件中更新路径引用
    find . -type f \( -name "*.gd" -o -name "*.tscn" -o -name "*.tres" \) -exec sed -i '' "s/$old_path_escaped/$new_path_escaped/g" {} \;
    
    echo "完成: $old_path -> $new_path"
    echo ""
}

# 脚本文件重命名
echo "开始重命名脚本文件..."

# UI脚本
rename_and_update "Scripts/UI/game_over_ui.gd" "Scripts/UI/GameOverUI.gd"
rename_and_update "Scripts/UI/game_win_ui.gd" "Scripts/UI/GameWinUI.gd"
rename_and_update "Scripts/UI/item_boxes_ui.gd" "Scripts/UI/ItemBoxesUI.gd"
rename_and_update "Scripts/UI/level_select.gd" "Scripts/UI/LevelSelect.gd"
rename_and_update "Scripts/UI/loading_screen.gd" "Scripts/UI/LoadingScreen.gd"
rename_and_update "Scripts/UI/main_menu.gd" "Scripts/UI/MainMenu.gd"
rename_and_update "Scripts/UI/player_avatar_ui.gd" "Scripts/UI/PlayerAvatarUI.gd"
rename_and_update "Scripts/UI/skill_box_ui.gd" "Scripts/UI/SkillBoxUI.gd"

# 其他脚本
rename_and_update "Scripts/editor_tools.gd" "Scripts/EditorTools.gd"
rename_and_update "Scripts/fog_of_war.gd" "Scripts/FogOfWar.gd"
rename_and_update "Scripts/game_data.gd" "Scripts/GameData.gd"
rename_and_update "Scripts/game_manager.gd" "Scripts/GameManager.gd"
rename_and_update "Scripts/game_restart_helper.gd" "Scripts/GameRestartHelper.gd"
rename_and_update "Scripts/level01.gd" "Scripts/Level01.gd"
rename_and_update "Scripts/main.gd" "Scripts/Main.gd"
rename_and_update "Scripts/minimap.gd" "Scripts/Minimap.gd"
rename_and_update "Scripts/passive_skill_initializer.gd" "Scripts/PassiveSkillInitializer.gd"
rename_and_update "Scripts/player.gd" "Scripts/Player.gd"
rename_and_update "Scripts/progress_bar_3d.gd" "Scripts/ProgressBar3D.gd"
rename_and_update "Scripts/tree.gd" "Scripts/Tree.gd"
rename_and_update "Scripts/tree_signals.gd" "Scripts/TreeSignals.gd"

echo "脚本文件重命名完成"
echo ""

# 场景文件重命名
echo "开始重命名场景文件..."

# UI场景
rename_and_update "Scenes/UI/game_over_ui.tscn" "Scenes/UI/GameOverUI.tscn"
rename_and_update "Scenes/UI/game_win_ui.tscn" "Scenes/UI/GameWinUI.tscn"
rename_and_update "Scenes/UI/level_select.tscn" "Scenes/UI/LevelSelect.tscn"
rename_and_update "Scenes/UI/loading_screen.tscn" "Scenes/UI/LoadingScreen.tscn"
rename_and_update "Scenes/UI/main_menu.tscn" "Scenes/UI/MainMenu.tscn"

# 其他场景
rename_and_update "Scenes/fog_of_war.tscn" "Scenes/FogOfWar.tscn"
rename_and_update "Scenes/gas_item_pickup.tscn" "Scenes/GasItemPickup.tscn"
rename_and_update "Scenes/item_boxes.tscn" "Scenes/ItemBoxes.tscn"
rename_and_update "Scenes/item_system.tscn" "Scenes/ItemSystem.tscn"
rename_and_update "Scenes/levels/level_01.tscn" "Scenes/levels/Level01.tscn"
rename_and_update "Scenes/levels/level_02.tscn" "Scenes/levels/Level02.tscn"
rename_and_update "Scenes/main.tscn" "Scenes/Main.tscn"
rename_and_update "Scenes/minimap.tscn" "Scenes/Minimap.tscn"
rename_and_update "Scenes/player.tscn" "Scenes/Player.tscn"
rename_and_update "Scenes/player_avatar.tscn" "Scenes/PlayerAvatar.tscn"
rename_and_update "Scenes/progress_bar_3d.tscn" "Scenes/ProgressBar3D.tscn"
rename_and_update "Scenes/skill_box_ui.tscn" "Scenes/SkillBoxUI.tscn"
rename_and_update "Scenes/wall_of_sighs_wall.tscn" "Scenes/WallOfSighsWall.tscn"

echo "场景文件重命名完成"
echo ""

echo "所有文件重命名完成！"
