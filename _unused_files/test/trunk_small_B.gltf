{"asset": {"generator": "Khronos glTF Blender I/O v3.4.50", "version": "2.0"}, "scene": 0, "scenes": [{"name": "Scene", "nodes": [0]}], "nodes": [{"mesh": 0, "name": "trunk_small_B"}], "materials": [{"name": "texture", "pbrMetallicRoughness": {"baseColorTexture": {"index": 0}, "metallicFactor": 0, "roughnessFactor": 0.44999998807907104}}], "meshes": [{"name": "trunk_small_B", "primitives": [{"attributes": {"POSITION": 0, "TEXCOORD_0": 1, "NORMAL": 2}, "indices": 3, "material": 0}]}], "textures": [{"sampler": 0, "source": 0}], "images": [{"mimeType": "image/png", "name": "dungeon_texture", "uri": "dungeon_texture.png"}], "accessors": [{"bufferView": 0, "componentType": 5126, "count": 358, "max": [0.3376796543598175, 0.4843124747276306, 0.2916831970214844], "min": [-0.3376792371273041, -1.0734540722978636e-07, -0.29168379306793213], "type": "VEC3"}, {"bufferView": 1, "componentType": 5126, "count": 358, "type": "VEC2"}, {"bufferView": 2, "componentType": 5126, "count": 358, "type": "VEC3"}, {"bufferView": 3, "componentType": 5123, "count": 612, "type": "SCALAR"}], "bufferViews": [{"buffer": 0, "byteLength": 4296, "byteOffset": 0, "target": 34962}, {"buffer": 0, "byteLength": 2864, "byteOffset": 4296, "target": 34962}, {"buffer": 0, "byteLength": 4296, "byteOffset": 7160, "target": 34962}, {"buffer": 0, "byteLength": 1224, "byteOffset": 11456, "target": 34963}], "samplers": [{"magFilter": 9729, "minFilter": 9987}], "buffers": [{"byteLength": 12680, "uri": "trunk_small_B.bin"}]}