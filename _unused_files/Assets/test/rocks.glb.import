[remap]

importer="scene"
importer_version=1
type="PackedScene"
uid="uid://brgst622oyem1"
path="res://.godot/imported/rocks.glb-86a5e987023f8cc9a38d89dc0d528afe.scn"

[deps]

source_file="res://Assets/test/rocks.glb"
dest_files=["res://.godot/imported/rocks.glb-86a5e987023f8cc9a38d89dc0d528afe.scn"]

[params]

nodes/root_type=""
nodes/root_name=""
nodes/apply_root_scale=true
nodes/root_scale=1.0
nodes/import_as_skeleton_bones=false
nodes/use_node_type_suffixes=true
meshes/ensure_tangents=true
meshes/generate_lods=true
meshes/create_shadow_meshes=true
meshes/light_baking=1
meshes/lightmap_texel_size=0.2
meshes/force_disable_compression=false
skins/use_named_skins=true
animation/import=true
animation/fps=30
animation/trimming=false
animation/remove_immutable_tracks=true
animation/import_rest_as_RESET=false
import_script/path=""
_subresources={}
gltf/naming_version=1
gltf/embedded_image_handling=1
