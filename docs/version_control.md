# Flame Clash 版本控制指南

## 版本命名规则

本项目使用基于时间戳的版本标签，格式为：`v年年年年月月日日时时分分秒秒`

例如：`v20250326153101` 表示2025年3月26日15:31:01创建的版本。

### 主要优势

1. **唯一性**：基于时间的标签确保版本号不会重复
2. **顺序性**：按时间排序的标签可以轻松识别版本顺序
3. **信息性**：包含创建标签的准确时间信息

## 版本控制工作流

### 常规开发流程

1. 在主分支（main）上进行开发
2. 完成功能或修复后，使用标签脚本创建新版本
3. 推送代码和标签到远程仓库

### 版本标签创建

提供了两种创建版本标签的方法：

#### 1. 使用自动化脚本（推荐）

项目根目录中的 `version_tag.sh` 脚本可以自动化标签创建流程：

```bash
./version_tag.sh
```

脚本会自动：
- 生成基于当前时间的标签名称
- 提示输入提交信息
- 提交所有更改到Git
- 创建带有提交信息的Git标签

#### 2. 手动创建

也可以手动创建符合规范的标签：

```bash
# 提交更改
git add .
git commit -m "提交的更改内容"

# 使用当前时间创建标签名
TAG_NAME="v$(date +"%Y%m%d%H%M%S")"
git tag -a $TAG_NAME -m "标签描述"

# 推送代码和标签
git push
git push --tags
```

## 查看版本历史

查看所有版本标签：

```bash
git tag
```

查看特定标签的详细信息：

```bash
git show v20250326153101
```

按时间顺序列出标签：

```bash
git tag --sort=creatordate
```

## 版本回退

回退到特定版本：

```bash
git checkout v20250326153101
```

## 最佳实践

1. **定期创建标签**：每完成一个功能或修复一个关键bug后创建标签
2. **详细的提交信息**：为每个标签提供清晰、描述性的信息
3. **推送标签**：记得将本地创建的标签推送到远程仓库：`git push --tags`
4. **不要删除标签**：一旦创建并推送，避免删除或修改已发布的标签 