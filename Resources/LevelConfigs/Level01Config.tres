[gd_resource type="Resource" script_class="LevelConfig" load_steps=2 format=3 uid="uid://dk0pj2fy5r1sq"]

[ext_resource type="Script" uid="uid://ppwn5m8bh6v2" path="res://Scripts/LevelConfig.gd" id="1_2k4m3"]

[resource]
script = ExtResource("1_2k4m3")
level_id = "level_01"
level_name = "第一关"
level_size = Vector2(100, 100)
cell_size = 2.5
wall_height = 2.0
enabled_modules = Array[String](["floor_generator", "wall_generator", "tree_generator", "fog_of_war_generator"])
module_configs = {
"floor_generator": {
"border_margin": 5.0,
"floor_scene": "res://Scenes/Prefabs/floor.tscn"
},
"fog_of_war_generator": {
"enabled": true,
"fog_of_war_scene": "res://Scenes/FogOfWar.tscn",
"player_vision_radius": 8.0
},
"tree_generator": {
"border_margin": 5.0,
"distribution_type": "random",
"max_trees": 10,
"min_distance": 5.0,
"min_distance_to_chests": 3.0,
"tree_scene": "res://Scenes/Prefabs/tree.tscn"
},
"wall_generator": {
"only_border": true,
"spacing": 3.5,
"wall_height": 0.83,
"wall_scene": "res://Scenes/Prefabs/Wall.tscn",
"wall_density": 0.4,
"random_scale_range": 0.4,
"random_rotation_range": 0.35,
"large_wall_ratio": 0.25,
"overlap_wall_ratio": 0.15,
"height_variation": 0.5,
"tilt_range": 0.2
}
}
