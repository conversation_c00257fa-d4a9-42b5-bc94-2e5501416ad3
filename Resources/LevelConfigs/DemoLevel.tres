[gd_resource type="Resource" script_class="LevelConfig" load_steps=2 format=3 uid="uid://cq154alollvdv"]

[ext_resource type="Script" uid="uid://ppwn5m8bh6v2" path="res://Scripts/LevelConfig.gd" id="1_2k4m3"]

[resource]
script = ExtResource("1_2k4m3")
level_id = "demo_level"
level_name = "演示关卡"
level_size = Vector2(80, 80)
cell_size = 2.0
wall_height = 2.5
enabled_modules = Array[String](["floor_generator", "wall_generator", "tree_generator", "chest_generator", "decoration_generator"])
module_configs = {
"chest_generator": {
"border_margin": 5.0,
"chest_scene": "res://Scenes/Prefabs/Chest.tscn",
"distribution_type": "random",
"max_chests": 5,
"min_distance": 5.0,
"min_distance_to_trees": 3.0,
"random_rotation": true
},
"decoration_generator": {
"barrel_scene": "res://Scenes/Prefabs/Barrel.tscn",
"border_margin": 5.0,
"distribution": "along_paths",
"flower_scene": "res://Scenes/Prefabs/Decoration.tscn",
"max_barrels": 10,
"max_flowers": 20,
"max_rocks": 15,
"max_scale": 1.2,
"min_distance": 3.0,
"min_distance_to_chests": 3.0,
"min_distance_to_trees": 3.0,
"min_scale": 0.8,
"path_points": [Vector2(-30, -30), Vector2(0, 0), Vector2(30, 30)],
"path_width": 5.0,
"random_rotation": true,
"random_scale": true,
"rock_scene": "res://Scenes/Prefabs/Rock.tscn"
},
"floor_generator": {
"border_margin": 5.0,
"floor_scene": "res://Scenes/Prefabs/Floor.tscn"
},
"tree_generator": {
"border_margin": 5.0,
"distribution_type": "random",
"max_trees": 20,
"min_distance": 5.0,
"min_distance_to_chests": 3.0,
"tree_scene": "res://Scenes/Prefabs/Tree.tscn"
},
"wall_generator": {
"only_border": true,
"spacing": 2.0,
"wall_height": 0.83,
"wall_scene": "res://Scenes/Prefabs/Wall.tscn"
}
}
