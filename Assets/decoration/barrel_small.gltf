{"asset": {"generator": "Khronos glTF Blender I/O v3.4.50", "version": "2.0"}, "scene": 0, "scenes": [{"name": "Scene", "nodes": [0]}], "nodes": [{"mesh": 0, "name": "barrel_small"}], "materials": [{"name": "texture", "pbrMetallicRoughness": {"baseColorTexture": {"index": 0}, "metallicFactor": 0, "roughnessFactor": 0.44999998807907104}}], "meshes": [{"name": "barrel_small", "primitives": [{"attributes": {"POSITION": 0, "TEXCOORD_0": 1, "NORMAL": 2}, "indices": 3, "material": 0}]}], "textures": [{"sampler": 0, "source": 0}], "images": [{"mimeType": "image/png", "name": "dungeon_texture", "uri": "dungeon_texture.png"}], "accessors": [{"bufferView": 0, "componentType": 5126, "count": 416, "max": [0.5006820559501648, 1.0176637172698975, 0.5006809234619141], "min": [-0.5006820559501648, -1.1641532182693481e-09, -0.5006832480430603], "type": "VEC3"}, {"bufferView": 1, "componentType": 5126, "count": 416, "type": "VEC2"}, {"bufferView": 2, "componentType": 5126, "count": 416, "type": "VEC3"}, {"bufferView": 3, "componentType": 5123, "count": 621, "type": "SCALAR"}], "bufferViews": [{"buffer": 0, "byteLength": 4992, "byteOffset": 0, "target": 34962}, {"buffer": 0, "byteLength": 3328, "byteOffset": 4992, "target": 34962}, {"buffer": 0, "byteLength": 4992, "byteOffset": 8320, "target": 34962}, {"buffer": 0, "byteLength": 1242, "byteOffset": 13312, "target": 34963}], "samplers": [{"magFilter": 9729, "minFilter": 9987}], "buffers": [{"byteLength": 14556, "uri": "barrel_small.bin"}]}