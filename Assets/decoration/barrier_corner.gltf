{"asset": {"generator": "Khronos glTF Blender I/O v3.4.50", "version": "2.0"}, "scene": 0, "scenes": [{"name": "Scene", "nodes": [0]}], "nodes": [{"mesh": 0, "name": "barrier_corner"}], "materials": [{"name": "texture", "pbrMetallicRoughness": {"baseColorTexture": {"index": 0}, "metallicFactor": 0, "roughnessFactor": 0.44999998807907104}}], "meshes": [{"name": "barrier_corner", "primitives": [{"attributes": {"POSITION": 0, "TEXCOORD_0": 1, "NORMAL": 2}, "indices": 3, "material": 0}]}], "textures": [{"sampler": 0, "source": 0}], "images": [{"mimeType": "image/png", "name": "dungeon_texture", "uri": "dungeon_texture.png"}], "accessors": [{"bufferView": 0, "componentType": 5126, "count": 260, "max": [0.3949747383594513, 1.4000000953674316, 2], "min": [-2, 0, -0.3949747085571289], "type": "VEC3"}, {"bufferView": 1, "componentType": 5126, "count": 260, "type": "VEC2"}, {"bufferView": 2, "componentType": 5126, "count": 260, "type": "VEC3"}, {"bufferView": 3, "componentType": 5123, "count": 384, "type": "SCALAR"}], "bufferViews": [{"buffer": 0, "byteLength": 3120, "byteOffset": 0, "target": 34962}, {"buffer": 0, "byteLength": 2080, "byteOffset": 3120, "target": 34962}, {"buffer": 0, "byteLength": 3120, "byteOffset": 5200, "target": 34962}, {"buffer": 0, "byteLength": 768, "byteOffset": 8320, "target": 34963}], "samplers": [{"magFilter": 9729, "minFilter": 9987}], "buffers": [{"byteLength": 9088, "uri": "barrier_corner.bin"}]}