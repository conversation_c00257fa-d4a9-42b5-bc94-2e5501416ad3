{"asset": {"generator": "Khronos glTF Blender I/O v3.4.50", "version": "2.0"}, "scene": 0, "scenes": [{"name": "Scene", "nodes": [0]}], "nodes": [{"mesh": 0, "name": "box_small_decorated"}], "materials": [{"name": "texture", "pbrMetallicRoughness": {"baseColorTexture": {"index": 0}, "metallicFactor": 0, "roughnessFactor": 0.44999998807907104}}], "meshes": [{"name": "box_small_decorated", "primitives": [{"attributes": {"POSITION": 0, "TEXCOORD_0": 1, "NORMAL": 2}, "indices": 3, "material": 0}]}], "textures": [{"sampler": 0, "source": 0}], "images": [{"mimeType": "image/png", "name": "dungeon_texture", "uri": "dungeon_texture.png"}], "accessors": [{"bufferView": 0, "componentType": 5126, "count": 1522, "max": [0.8780018091201782, 1.4828314781188965, 0.8544781804084778], "min": [-0.6118646860122681, -5.960464477539062e-07, -0.6327710151672363], "type": "VEC3"}, {"bufferView": 1, "componentType": 5126, "count": 1522, "type": "VEC2"}, {"bufferView": 2, "componentType": 5126, "count": 1522, "type": "VEC3"}, {"bufferView": 3, "componentType": 5123, "count": 2784, "type": "SCALAR"}], "bufferViews": [{"buffer": 0, "byteLength": 18264, "byteOffset": 0, "target": 34962}, {"buffer": 0, "byteLength": 12176, "byteOffset": 18264, "target": 34962}, {"buffer": 0, "byteLength": 18264, "byteOffset": 30440, "target": 34962}, {"buffer": 0, "byteLength": 5568, "byteOffset": 48704, "target": 34963}], "samplers": [{"magFilter": 9729, "minFilter": 9987}], "buffers": [{"byteLength": 54272, "uri": "box_small_decorated.bin"}]}