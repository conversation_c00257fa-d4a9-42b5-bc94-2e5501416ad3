{"asset": {"generator": "Khronos glTF Blender I/O v3.4.50", "version": "2.0"}, "scene": 0, "scenes": [{"name": "Scene", "nodes": [0]}], "nodes": [{"mesh": 0, "name": "box_small"}], "materials": [{"name": "texture", "pbrMetallicRoughness": {"baseColorTexture": {"index": 0}, "metallicFactor": 0, "roughnessFactor": 0.44999998807907104}}], "meshes": [{"name": "box_small", "primitives": [{"attributes": {"POSITION": 0, "TEXCOORD_0": 1, "NORMAL": 2}, "indices": 3, "material": 0}]}], "textures": [{"sampler": 0, "source": 0}], "images": [{"mimeType": "image/png", "name": "dungeon_texture", "uri": "dungeon_texture.png"}], "accessors": [{"bufferView": 0, "componentType": 5126, "count": 320, "max": [0.5, 1, 0.5], "min": [-0.5, 2.9802322387695312e-08, -0.5], "type": "VEC3"}, {"bufferView": 1, "componentType": 5126, "count": 320, "type": "VEC2"}, {"bufferView": 2, "componentType": 5126, "count": 320, "type": "VEC3"}, {"bufferView": 3, "componentType": 5123, "count": 564, "type": "SCALAR"}], "bufferViews": [{"buffer": 0, "byteLength": 3840, "byteOffset": 0, "target": 34962}, {"buffer": 0, "byteLength": 2560, "byteOffset": 3840, "target": 34962}, {"buffer": 0, "byteLength": 3840, "byteOffset": 6400, "target": 34962}, {"buffer": 0, "byteLength": 1128, "byteOffset": 10240, "target": 34963}], "samplers": [{"magFilter": 9729, "minFilter": 9987}], "buffers": [{"byteLength": 11368, "uri": "box_small.bin"}]}