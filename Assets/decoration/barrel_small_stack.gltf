{"asset": {"generator": "Khronos glTF Blender I/O v3.4.50", "version": "2.0"}, "scene": 0, "scenes": [{"name": "Scene", "nodes": [0]}], "nodes": [{"mesh": 0, "name": "barrel_small_stack"}], "materials": [{"name": "texture", "pbrMetallicRoughness": {"baseColorTexture": {"index": 0}, "metallicFactor": 0, "roughnessFactor": 0.44999998807907104}}], "meshes": [{"name": "barrel_small_stack", "primitives": [{"attributes": {"POSITION": 0, "TEXCOORD_0": 1, "NORMAL": 2}, "indices": 3, "material": 0}]}], "textures": [{"sampler": 0, "source": 0}], "images": [{"mimeType": "image/png", "name": "dungeon_texture", "uri": "dungeon_texture.png"}], "accessors": [{"bufferView": 0, "componentType": 5126, "count": 2082, "max": [0.9250689148902893, 1.7701389789581299, 0.5000000596046448], "min": [-0.9250713586807251, 7.502316634599993e-07, -0.5], "type": "VEC3"}, {"bufferView": 1, "componentType": 5126, "count": 2082, "type": "VEC2"}, {"bufferView": 2, "componentType": 5126, "count": 2082, "type": "VEC3"}, {"bufferView": 3, "componentType": 5123, "count": 3447, "type": "SCALAR"}], "bufferViews": [{"buffer": 0, "byteLength": 24984, "byteOffset": 0, "target": 34962}, {"buffer": 0, "byteLength": 16656, "byteOffset": 24984, "target": 34962}, {"buffer": 0, "byteLength": 24984, "byteOffset": 41640, "target": 34962}, {"buffer": 0, "byteLength": 6894, "byteOffset": 66624, "target": 34963}], "samplers": [{"magFilter": 9729, "minFilter": 9987}], "buffers": [{"byteLength": 73520, "uri": "barrel_small_stack.bin"}]}