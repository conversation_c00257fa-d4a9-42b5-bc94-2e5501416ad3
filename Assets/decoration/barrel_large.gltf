{"asset": {"generator": "Khronos glTF Blender I/O v3.4.50", "version": "2.0"}, "scene": 0, "scenes": [{"name": "Scene", "nodes": [0]}], "nodes": [{"mesh": 0, "name": "barrel_large"}], "materials": [{"name": "texture", "pbrMetallicRoughness": {"baseColorTexture": {"index": 0}, "metallicFactor": 0, "roughnessFactor": 0.44999998807907104}}], "meshes": [{"name": "barrel_large", "primitives": [{"attributes": {"POSITION": 0, "TEXCOORD_0": 1, "NORMAL": 2}, "indices": 3, "material": 0}]}], "textures": [{"sampler": 0, "source": 0}], "images": [{"mimeType": "image/png", "name": "dungeon_texture", "uri": "dungeon_texture.png"}], "accessors": [{"bufferView": 0, "componentType": 5126, "count": 700, "max": [0.8999999761581421, 2.0000009536743164, 0.9000003337860107], "min": [-0.9000000357627869, 9.5367431640625e-07, -0.8999995589256287], "type": "VEC3"}, {"bufferView": 1, "componentType": 5126, "count": 700, "type": "VEC2"}, {"bufferView": 2, "componentType": 5126, "count": 700, "type": "VEC3"}, {"bufferView": 3, "componentType": 5123, "count": 1683, "type": "SCALAR"}], "bufferViews": [{"buffer": 0, "byteLength": 8400, "byteOffset": 0, "target": 34962}, {"buffer": 0, "byteLength": 5600, "byteOffset": 8400, "target": 34962}, {"buffer": 0, "byteLength": 8400, "byteOffset": 14000, "target": 34962}, {"buffer": 0, "byteLength": 3366, "byteOffset": 22400, "target": 34963}], "samplers": [{"magFilter": 9729, "minFilter": 9987}], "buffers": [{"byteLength": 25768, "uri": "barrel_large.bin"}]}