[remap]

importer="texture"
type="CompressedTexture2D"
uid="uid://w3jfu3qn6486"
path.s3tc="res://.godot/imported/colormap.png-2ebc8adefa47ce87bf4ed6c6fbd67347.s3tc.ctex"
metadata={
"imported_formats": ["s3tc_bptc"],
"vram_texture": true
}

[deps]

source_file="res://Assets/character/Textures/colormap.png"
dest_files=["res://.godot/imported/colormap.png-2ebc8adefa47ce87bf4ed6c6fbd67347.s3tc.ctex"]

[params]

compress/mode=2
compress/high_quality=false
compress/lossy_quality=0.7
compress/hdr_compression=1
compress/normal_map=0
compress/channel_pack=0
mipmaps/generate=true
mipmaps/limit=-1
roughness/mode=0
roughness/src_normal=""
process/fix_alpha_border=true
process/premult_alpha=false
process/normal_map_invert_y=false
process/hdr_as_srgb=false
process/hdr_clamp_exposure=false
process/size_limit=0
detect_3d/compress_to=0
