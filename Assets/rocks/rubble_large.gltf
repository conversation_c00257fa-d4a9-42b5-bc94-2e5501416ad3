{"asset": {"generator": "Khronos glTF Blender I/O v3.4.50", "version": "2.0"}, "scene": 0, "scenes": [{"name": "Scene", "nodes": [0]}], "nodes": [{"mesh": 0, "name": "rubble_large"}], "materials": [{"name": "texture", "pbrMetallicRoughness": {"baseColorTexture": {"index": 0}, "metallicFactor": 0, "roughnessFactor": 0.44999998807907104}}], "meshes": [{"name": "rubble_large", "primitives": [{"attributes": {"POSITION": 0, "TEXCOORD_0": 1, "NORMAL": 2}, "indices": 3, "material": 0}]}], "textures": [{"sampler": 0, "source": 0}], "images": [{"mimeType": "image/png", "name": "dungeon_texture", "uri": "dungeon_texture.png"}], "accessors": [{"bufferView": 0, "componentType": 5126, "count": 1200, "max": [4.060417175292969, 3.4960880279541016, 1.597455382347107], "min": [-4.068856716156006, 0, -1.5856541395187378], "type": "VEC3"}, {"bufferView": 1, "componentType": 5126, "count": 1200, "type": "VEC2"}, {"bufferView": 2, "componentType": 5126, "count": 1200, "type": "VEC3"}, {"bufferView": 3, "componentType": 5123, "count": 2364, "type": "SCALAR"}], "bufferViews": [{"buffer": 0, "byteLength": 14400, "byteOffset": 0, "target": 34962}, {"buffer": 0, "byteLength": 9600, "byteOffset": 14400, "target": 34962}, {"buffer": 0, "byteLength": 14400, "byteOffset": 24000, "target": 34962}, {"buffer": 0, "byteLength": 4728, "byteOffset": 38400, "target": 34963}], "samplers": [{"magFilter": 9729, "minFilter": 9987}], "buffers": [{"byteLength": 43128, "uri": "rubble_large.bin"}]}