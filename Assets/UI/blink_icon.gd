@tool
extends Node

# 此脚本用于在编辑器中生成闪烁技能的图标
# 执行此脚本将创建并保存一个图标到指定位置

func _ready() -> void:
	if Engine.is_editor_hint():
		generate_blink_icon()

func generate_blink_icon() -> void:
	var image = Image.create(128, 128, false, Image.FORMAT_RGBA8)
	
	# 填充灰色背景
	image.fill(Color(0.4, 0.4, 0.4, 1.0))
	
	# 绘制闪烁图案 - 简单的箭头图案
	draw_blink_symbol(image)
	
	# 保存图片
	var save_path = "res://Assets/UI/blink_icon.png"
	var err = image.save_png(save_path)
	if err == OK:
		print("生成闪烁图标成功：" + save_path)
	else:
		push_error("生成闪烁图标失败：" + str(err))

func draw_blink_symbol(image: Image) -> void:
	# 绘制闪电/箭头符号
	var color = Color(0.8, 0.8, 0.8, 1.0)
	var outline_color = Color(0.3, 0.3, 0.3, 1.0)
	
	# 定义闪电形状的点
	var points = [
		Vector2(40, 70),  # 左下
		Vector2(60, 30),  # 中上
		Vector2(70, 40),  # 中右
		Vector2(60, 60),  # 中间
		Vector2(80, 90),  # 右下
		Vector2(55, 60),  # 中下
		Vector2(40, 70)   # 闭合
	]
	
	# 绘制填充
	for y in range(image.get_height()):
		for x in range(image.get_width()):
			var point = Vector2(x, y)
			if is_point_in_polygon(point, points):
				image.set_pixel(x, y, color)
	
	# 绘制轮廓
	for i in range(points.size() - 1):
		draw_line_on_image(image, points[i], points[i+1], outline_color)

func is_point_in_polygon(point: Vector2, polygon: Array) -> bool:
	# 检查点是否在多边形内部的简单实现
	var inside = false
	var j = polygon.size() - 1
	
	for i in range(polygon.size()):
		if ((polygon[i].y > point.y) != (polygon[j].y > point.y)) and \
		   (point.x < (polygon[j].x - polygon[i].x) * (point.y - polygon[i].y) / (polygon[j].y - polygon[i].y) + polygon[i].x):
			inside = not inside
		j = i
	
	return inside

func draw_line_on_image(image: Image, from: Vector2, to: Vector2, color: Color) -> void:
	# Bresenham算法绘制直线
	var x0 = int(from.x)
	var y0 = int(from.y)
	var x1 = int(to.x)
	var y1 = int(to.y)
	
	var dx = abs(x1 - x0)
	var dy = -abs(y1 - y0)
	var sx = 1 if x0 < x1 else -1
	var sy = 1 if y0 < y1 else -1
	var err = dx + dy
	
	while true:
		if x0 >= 0 and x0 < image.get_width() and y0 >= 0 and y0 < image.get_height():
			image.set_pixel(x0, y0, color)
		
		if x0 == x1 and y0 == y1:
			break
		
		var e2 = 2 * err
		if e2 >= dy:
			if x0 == x1:
				break
			err += dy
			x0 += sx
		
		if e2 <= dx:
			if y0 == y1:
				break
			err += dx
			y0 += sy 