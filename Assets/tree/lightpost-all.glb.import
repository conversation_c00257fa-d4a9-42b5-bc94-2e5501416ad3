[remap]

importer="scene"
importer_version=1
type="PackedScene"
uid="uid://neeyb7vmj7hw"
path="res://.godot/imported/lightpost-all.glb-bd9a0639029012ffe298be6035eee504.scn"

[deps]

source_file="res://Assets/tree/lightpost-all.glb"
dest_files=["res://.godot/imported/lightpost-all.glb-bd9a0639029012ffe298be6035eee504.scn"]

[params]

nodes/root_type=""
nodes/root_name=""
nodes/apply_root_scale=true
nodes/root_scale=1.0
nodes/import_as_skeleton_bones=false
nodes/use_node_type_suffixes=true
meshes/ensure_tangents=true
meshes/generate_lods=true
meshes/create_shadow_meshes=true
meshes/light_baking=1
meshes/lightmap_texel_size=0.2
meshes/force_disable_compression=false
skins/use_named_skins=true
animation/import=true
animation/fps=30
animation/trimming=false
animation/remove_immutable_tracks=true
animation/import_rest_as_RESET=false
import_script/path=""
_subresources={}
gltf/naming_version=1
gltf/embedded_image_handling=1
