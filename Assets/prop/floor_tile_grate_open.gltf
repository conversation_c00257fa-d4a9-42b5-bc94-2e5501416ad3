{"asset": {"generator": "Khronos glTF Blender I/O v3.4.50", "version": "2.0"}, "scene": 0, "scenes": [{"name": "Scene", "nodes": [0]}], "nodes": [{"mesh": 0, "name": "floor_tile_grate_open"}], "materials": [{"name": "texture", "pbrMetallicRoughness": {"baseColorTexture": {"index": 0}, "metallicFactor": 0, "roughnessFactor": 0.44999998807907104}}], "meshes": [{"name": "floor_tile_grate_open", "primitives": [{"attributes": {"POSITION": 0, "TEXCOORD_0": 1, "NORMAL": 2}, "indices": 3, "material": 0}]}], "textures": [{"sampler": 0, "source": 0}], "images": [{"mimeType": "image/png", "name": "dungeon_texture", "uri": "dungeon_texture.png"}], "accessors": [{"bufferView": 0, "componentType": 5126, "count": 272, "max": [2, 0.04999999701976776, 1], "min": [-2, -0.9799999594688416, -1], "type": "VEC3"}, {"bufferView": 1, "componentType": 5126, "count": 272, "type": "VEC2"}, {"bufferView": 2, "componentType": 5126, "count": 272, "type": "VEC3"}, {"bufferView": 3, "componentType": 5123, "count": 420, "type": "SCALAR"}], "bufferViews": [{"buffer": 0, "byteLength": 3264, "byteOffset": 0, "target": 34962}, {"buffer": 0, "byteLength": 2176, "byteOffset": 3264, "target": 34962}, {"buffer": 0, "byteLength": 3264, "byteOffset": 5440, "target": 34962}, {"buffer": 0, "byteLength": 840, "byteOffset": 8704, "target": 34963}], "samplers": [{"magFilter": 9729, "minFilter": 9987}], "buffers": [{"byteLength": 9544, "uri": "floor_tile_grate_open.bin"}]}