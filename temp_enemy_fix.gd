# 查找模型中的AnimationPlayer
func _find_model_animation_player():
	if not _skin:
		return null

	# 首先检查Enemy01节点是否存在
	var enemy01 = get_node_or_null("Enemy01")
	if enemy01 and enemy01.has_node("AnimationPlayer"):
		return enemy01.get_node("AnimationPlayer")

	# 如果找不到Enemy01节点的AnimationPlayer，尝试在_skin中查找
	if _skin.has_node("AnimationPlayer"):
		return _skin.get_node("AnimationPlayer")

	# 递归查找AnimationPlayer
	var result = _find_animation_player_recursive(_skin)
	if result:
		return result

	return null

# 递归查找AnimationPlayer
func _find_animation_player_recursive(node: Node):
	if node is AnimationPlayer:
		return node

	# 递归检查所有子节点
	for child in node.get_children():
		var result = _find_animation_player_recursive(child)
		if result:
			return result

	return null

# 检查是否是分身（MirageClone）
func check_is_mirage_clone(player: Node) -> bool:
	if player.get_script() and player.get_script().get_path().ends_with("MirageClone.gd"):
		# 是分身，直接销毁它但不结束游戏
		if is_instance_valid(player):
			player.queue_free()
		return true  # 返回true表示是分身
	return false  # 返回false表示不是分身
