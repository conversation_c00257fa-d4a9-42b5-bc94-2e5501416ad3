[remap]

importer="texture"
type="CompressedTexture2D"
uid="uid://cof5y5trwabfw"
path.s3tc="res://.godot/imported/Tree01_0.jpg-1c4b45e08ad5a32f894dcb3288224007.s3tc.ctex"
metadata={
"imported_formats": ["s3tc_bptc"],
"vram_texture": true
}
generator_parameters={
"md5": "f92135c4e0a38494db7350235034b9d1"
}

[deps]

source_file="res://Tree01_0.jpg"
dest_files=["res://.godot/imported/Tree01_0.jpg-1c4b45e08ad5a32f894dcb3288224007.s3tc.ctex"]

[params]

compress/mode=2
compress/high_quality=false
compress/lossy_quality=0.7
compress/hdr_compression=1
compress/normal_map=0
compress/channel_pack=0
mipmaps/generate=true
mipmaps/limit=-1
roughness/mode=0
roughness/src_normal=""
process/fix_alpha_border=true
process/premult_alpha=false
process/normal_map_invert_y=false
process/hdr_as_srgb=false
process/hdr_clamp_exposure=false
process/size_limit=0
detect_3d/compress_to=0
