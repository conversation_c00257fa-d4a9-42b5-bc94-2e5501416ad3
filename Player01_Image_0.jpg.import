[remap]

importer="texture"
type="CompressedTexture2D"
uid="uid://drbceple6nsn4"
path.s3tc="res://.godot/imported/Player01_Image_0.jpg-1b9378392b2962994578aa03fc98182f.s3tc.ctex"
metadata={
"imported_formats": ["s3tc_bptc"],
"vram_texture": true
}
generator_parameters={
"md5": "5d3ee8e63be8a00fa41b9d5f43ed9267"
}

[deps]

source_file="res://Player01_Image_0.jpg"
dest_files=["res://.godot/imported/Player01_Image_0.jpg-1b9378392b2962994578aa03fc98182f.s3tc.ctex"]

[params]

compress/mode=2
compress/high_quality=false
compress/lossy_quality=0.7
compress/hdr_compression=1
compress/normal_map=0
compress/channel_pack=0
mipmaps/generate=true
mipmaps/limit=-1
roughness/mode=0
roughness/src_normal=""
process/fix_alpha_border=true
process/premult_alpha=false
process/normal_map_invert_y=false
process/hdr_as_srgb=false
process/hdr_clamp_exposure=false
process/size_limit=0
detect_3d/compress_to=0
