# 测试Camera重构的简单脚本
# 这个脚本可以用来验证Camera系统是否正常工作

extends Node

func _ready():
	print("=== Camera重构测试 ===")
	
	# 测试1: 检查Camera是否存在
	var camera = get_viewport().get_camera_3d()
	if camera:
		print("✓ Camera存在: ", camera.name)
		print("  Camera位置: ", camera.global_position)
		print("  Camera旋转: ", camera.global_rotation)
	else:
		print("✗ Camera不存在")
	
	# 测试2: 检查CameraPivot是否存在
	var camera_pivot = get_tree().get_first_node_in_group("camera_controller")
	if not camera_pivot:
		# 尝试通过unique name查找
		camera_pivot = get_node_or_null("%CameraPivot")
	
	if camera_pivot:
		print("✓ CameraPivot存在: ", camera_pivot.name)
		print("  CameraPivot脚本: ", camera_pivot.get_script())
	else:
		print("✗ CameraPivot不存在")
	
	# 测试3: 检查Player是否存在
	var player = get_tree().get_first_node_in_group("player")
	if player:
		print("✓ Player存在: ", player.name)
		print("  Player位置: ", player.global_position)
		
		# 检查Player是否还有Camera相关的节点
		var player_camera_pivot = player.get_node_or_null("CameraPivot")
		if player_camera_pivot:
			print("✗ Player仍然有CameraPivot节点 - 需要清理")
		else:
			print("✓ Player已正确移除CameraPivot节点")
			
		# 检查Player是否有PlayerInteractor
		var interactor = player.get_node_or_null("PlayerInteractor")
		if interactor:
			print("✓ PlayerInteractor存在: ", interactor.name)
		else:
			print("✗ PlayerInteractor不存在")
	else:
		print("✗ Player不存在")
	
	print("=== 测试完成 ===")
	
	# 5秒后自动删除测试节点
	await get_tree().create_timer(5.0).timeout
	queue_free()
