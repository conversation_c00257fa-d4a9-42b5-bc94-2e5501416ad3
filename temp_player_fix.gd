# 查找模型中的AnimationPlayer
func _find_model_animation_player():
if not _skin:
return null

# 使用try-except包装所有可能导致错误的操作
# 注意：GDScript不支持try-except，所以我们使用if检查代替

# 直接查找模型中的AnimationPlayer
if _skin.has_node("AnimationPlayer"):
var animation_player = _skin.get_node("AnimationPlayer")
if animation_player and animation_player is AnimationPlayer:
return animation_player

# 递归查找AnimationPlayer
var result = _find_animation_player_recursive(_skin)
if result:
return result

# 如果还是找不到，尝试直接使用Player01.glb中的AnimationPlayer
if has_node("Player01"):
var player01 = get_node("Player01")
if player01 and player01.has_node("AnimationPlayer"):
var animation_player = player01.get_node("AnimationPlayer")
if animation_player and animation_player is AnimationPlayer:
return animation_player

# 如果所有方法都失败，返回null
return null
