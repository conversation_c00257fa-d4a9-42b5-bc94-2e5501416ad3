extends Item
class_name LightningOrb

# 道具固定属性
var ignite_count: int = 3  # 点燃树木的数量

func _init() -> void:
	# 设置道具基本属性
	item_name = "闪电球"
	item_description = "召唤闪电，立刻随机点燃三棵树木。"
	is_one_time_use = true  # 一次性道具
	cooldown_time = 0.0  # 无冷却时间
	item_category = "utility"

# 覆盖初始化函数
func initialize(item_owner = null) -> void:
	# 调用父类的初始化方法
	super.initialize(item_owner)

# 使用道具的逻辑
func use() -> bool:
	# 检查是否有有效的所有者（玩家）
	if not owner:
		return false

	# 通过owner获取场景树
	var scene_tree = owner.get_tree()
	if not scene_tree:
		return false

	# 获取场景中所有树木
	var trees = scene_tree.get_nodes_in_group("trees")

	# 过滤出可以点燃的树木（未燃烧或已熄灭）
	var ignitable_trees = []
	for tree in trees:
		if tree.has_method("start_burning") and (not tree.has_method("is_burning") or not tree.is_burning()):
			ignitable_trees.append(tree)

	# 如果没有可点燃的树木，返回失败
	if ignitable_trees.is_empty():
		return false

	# 随机打乱树木列表
	ignitable_trees.shuffle()

	# 点燃指定数量的树木（最多三棵，或者所有可用的树木）
	var trees_to_ignite = min(ignite_count, ignitable_trees.size())
	var ignited_trees = []

	for i in range(trees_to_ignite):
		var tree = ignitable_trees[i]
		tree.start_burning()
		ignited_trees.append(tree)

		# 创建闪电特效
		create_lightning_effect(tree)

	# 显示使用效果
	show_use_effects(trees_to_ignite)

	# 返回使用成功
	return true

# 创建闪电特效
func create_lightning_effect(tree) -> void:
	# 创建闪电粒子效果
	var lightning = GPUParticles3D.new()
	owner.add_child(lightning)

	# 设置粒子系统属性
	lightning.emitting = true
	lightning.one_shot = true
	lightning.explosiveness = 0.8
	lightning.global_position = tree.global_position + Vector3(0, 10, 0)  # 从上方射下
	lightning.lifetime = 2.0  # 粒子生命周期

	# 设置粒子材质
	var material = ParticleProcessMaterial.new()
	material.emission_shape = ParticleProcessMaterial.EMISSION_SHAPE_SPHERE
	material.emission_sphere_radius = 0.5
	material.direction = Vector3(0, -1, 0)
	material.spread = 10.0
	material.gravity = Vector3(0, -20, 0)
	material.initial_velocity_min = 10.0
	material.initial_velocity_max = 15.0
	material.color = Color(0.5, 0.7, 1.0, 1.0)  # 蓝白色
	lightning.process_material = material

	# 使用计时器自动销毁粒子效果
	var timer = Timer.new()
	lightning.add_child(timer)
	timer.wait_time = 3.0  # 3秒后销毁（比粒子生命周期长一点）
	timer.one_shot = true
	timer.timeout.connect(func(): lightning.queue_free())
	timer.start()

# 显示使用效果
func show_use_effects(trees_ignited: int) -> void:
	if not owner:
		return

	# 播放音效（如果需要）
	# var audio_player = AudioStreamPlayer3D.new()
	# owner.add_child(audio_player)
	# audio_player.stream = preload("res://sounds/lightning.ogg")  # 替换为实际音效路径
	# audio_player.play()
	# audio_player.finished.connect(func(): audio_player.queue_free())

	# 显示UI提示
	if owner.has_method("show_notification"):
		owner.show_notification("闪电已召唤！点燃了 " + str(trees_ignited) + " 棵树木")
