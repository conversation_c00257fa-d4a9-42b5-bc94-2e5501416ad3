class_name Enemy02
extends Enemy

# Enemy02特有的皮肤组件引用
@onready var skin_component: Enemy02Skin = $Skin

func _ready() -> void:
	super._ready()

	# 连接皮肤组件的信号
	if skin_component:
		if not skin_component.animation_finished.is_connected(_on_skin_animation_finished):
			skin_component.animation_finished.connect(_on_skin_animation_finished)

# 重写状态设置方法，将状态同步到皮肤组件
func set_state(new_state: State) -> void:
	super.set_state(new_state)

	# 将Enemy状态转换为Enemy02Skin状态并同步
	if skin_component:
		var skin_state: Enemy02Skin.EnemyState
		match new_state:
			State.PATROL:
				skin_state = Enemy02Skin.EnemyState.PATROL
			State.IDLE:
				skin_state = Enemy02Skin.EnemyState.IDLE
			State.MOVE_TO_FIRE:
				skin_state = Enemy02Skin.EnemyState.MOVE_TO_FIRE
			State.EXTINGUISH:
				skin_state = Enemy02Skin.EnemyState.EXTINGUISH
			State.CHASE_PLAYER:
				skin_state = Enemy02Skin.EnemyState.CHASE_PLAYER
			State.TRAPPED:
				skin_state = Enemy02Skin.EnemyState.TRAPPED
			_:
				skin_state = Enemy02Skin.EnemyState.IDLE

		skin_component.set_enemy_state(skin_state)

# 皮肤动画完成回调
func _on_skin_animation_finished(anim_name: String) -> void:
	# 可以在这里处理特定动画完成后的逻辑
	pass


