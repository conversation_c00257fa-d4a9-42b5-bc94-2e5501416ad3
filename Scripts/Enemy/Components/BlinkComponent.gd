extends Node
class_name EnemyBlinkComponent

signal blink_started
signal blink_finished

@export var host: Node3D
@export var blink_distance: float = 5.0
@export var cooldown_time: float = 10.0

var _can_blink: bool = true
var _cooldown_timer: Timer
var _is_chasing_player: bool = false
var _is_extinguishing_fire: bool = false

# 闪烁特效场景
var _effect_scene = preload("res://Scenes/BlinkEffect.tscn")

func _ready() -> void:
    _cooldown_timer = Timer.new()
    _cooldown_timer.one_shot = true
    _cooldown_timer.wait_time = cooldown_time
    _cooldown_timer.timeout.connect(_on_cooldown_timeout)
    add_child(_cooldown_timer)

func update_state(is_chasing: bool, is_extinguishing: bool) -> void:
    _is_chasing_player = is_chasing
    _is_extinguishing_fire = is_extinguishing

func can_blink() -> bool:
    return _can_blink and (_is_chasing_player or _is_extinguishing_fire)

func try_blink() -> bool:
    if not can_blink():
        return false
        
    var forward_direction = host.transform.basis.z.normalized()
    var target_position = host.global_position - forward_direction * blink_distance
    
    # 保存起始位置用于特效
    var start_pos = host.global_position
    
    # 执行闪烁
    host.global_position = target_position
    
    # 生成特效
    spawn_effect(start_pos, target_position)
    
    # 开始冷却
    start_cooldown()
    
    emit_signal("blink_started")
    emit_signal("blink_finished")
    
    return true

func spawn_effect(start_pos: Vector3, end_pos: Vector3) -> void:
    if _effect_scene:
        var start_effect = _effect_scene.instantiate()
        var end_effect = _effect_scene.instantiate()
        
        get_tree().root.add_child(start_effect)
        get_tree().root.add_child(end_effect)
        
        start_effect.global_position = start_pos
        end_effect.global_position = end_pos

func start_cooldown() -> void:
    _can_blink = false
    _cooldown_timer.start()

func _on_cooldown_timeout() -> void:
    _can_blink = true