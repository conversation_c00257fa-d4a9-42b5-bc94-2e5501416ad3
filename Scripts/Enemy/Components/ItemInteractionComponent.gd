extends Node

class_name EnemyItemInteractionComponent

signal trapped_state_changed(is_trapped: bool)

@export var host: CharacterBody3D
@export var trapped_duration := 20.0

var is_trapped := false
var trapped_timer := 0.0

func _ready() -> void:
    if not host:
        host = get_parent()

func _process(delta: float) -> void:
    if is_trapped:
        trapped_timer += delta
        if trapped_timer >= trapped_duration:
            set_trapped(false)

func set_trapped(trapped: bool) -> void:
    if is_trapped == trapped:
        return
        
    is_trapped = trapped
    if is_trapped:
        trapped_timer = 0.0
    else:
        trapped_timer = 0.0
    
    trapped_state_changed.emit(is_trapped)

func handle_trap_collision(trap: Node3D) -> void:
    if not is_trapped and trap.has_method("activate"):
        trap.activate()
        set_trapped(true)

func is_in_trapped_state() -> bool:
    return is_trapped