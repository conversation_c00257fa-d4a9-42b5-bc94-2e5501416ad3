extends Node

class_name EnemyTreeInteractionComponent

signal tree_extinguish_started(tree: Node3D)
signal tree_extinguish_completed(tree: Node3D)

@export var host: CharacterBody3D
@export var extinguish_distance := 0.8
@export var extinguish_duration := 5.0

var _current_tree: Node3D = null
var _is_extinguishing := false
var _extinguish_progress := 0.0
var burning_trees: Array[Node3D] = []

var extinguish_timer: Timer

func _ready() -> void:
	if not host:
		host = get_parent()
		
	setup_extinguish_timer()
	connect_tree_signals()

func setup_extinguish_timer() -> void:
	extinguish_timer = Timer.new()
	extinguish_timer.name = "ExtinguishTimer"
	extinguish_timer.one_shot = true
	extinguish_timer.timeout.connect(_on_extinguish_timer_timeout)
	add_child(extinguish_timer)

func connect_tree_signals() -> void:
	var tree_signals = get_node("/root/TreeSignals")
	if tree_signals:
		if not tree_signals.is_connected("tree_caught_fire", _on_tree_caught_fire):
			tree_signals.tree_caught_fire.connect(_on_tree_caught_fire)
		if not tree_signals.is_connected("tree_extinguished", _on_tree_extinguished):
			tree_signals.tree_extinguished.connect(_on_tree_extinguished)
		if not tree_signals.is_connected("tree_burned_out", _on_tree_burned_out):
			tree_signals.tree_burned_out.connect(_on_tree_burned_out)

func start_extinguishing(tree: Node3D) -> void:
	if not is_instance_valid(tree):
		return

	var is_burning = false
	if tree.has_method("is_burning"):
		is_burning = tree.is_burning()
	elif "current_state" in tree and tree.current_state == 2: # TreeState.BURNING
		is_burning = true

	if is_burning:
		if _is_extinguishing and _current_tree == tree:
			return

		if _is_extinguishing and _current_tree != null:
			stop_extinguishing()

		_current_tree = tree
		_is_extinguishing = true
		_extinguish_progress = 0.0

		if tree.has_method("start_extinguishing"):
			tree.start_extinguishing(host)
			tree_extinguish_started.emit(tree)
	else:
		refresh_burning_trees()
		if _current_tree == tree:
			_current_tree = null
			_is_extinguishing = false

func stop_extinguishing() -> void:
	if _current_tree and _is_extinguishing:
		if _current_tree.has_method("stop_extinguishing"):
			_current_tree.stop_extinguishing(host)
		_current_tree = null
		_extinguish_progress = 0.0
		_is_extinguishing = false

func process_extinguishing(delta: float) -> void:
	if _is_extinguishing and _current_tree and is_instance_valid(_current_tree):
		_extinguish_progress += delta / extinguish_duration
		
		if _extinguish_progress >= 1.0:
			_extinguish_progress = 0.0
			_is_extinguishing = false
			
			if _current_tree.has_method("stop_burning"):
				_current_tree.stop_burning()
				tree_extinguish_completed.emit(_current_tree)
			
			_current_tree = null
			refresh_burning_trees()

func refresh_burning_trees() -> void:
	burning_trees.clear()
	var trees = get_tree().get_nodes_in_group("burning_trees")
	for tree in trees:
		if is_instance_valid(tree):
			burning_trees.append(tree)

func find_nearest_burning_tree(from_position: Vector3) -> Node3D:
	var nearest_distance := INF
	var nearest_tree: Node3D = null

	for tree in burning_trees:
		if is_instance_valid(tree):
			var distance = from_position.distance_to(tree.global_position)
			if distance < nearest_distance:
				nearest_distance = distance
				nearest_tree = tree

	return nearest_tree

func _on_tree_caught_fire(tree: Node3D) -> void:
	if not is_instance_valid(tree):
		return
	refresh_burning_trees()

func _on_tree_extinguished(tree: Node3D) -> void:
	if not is_instance_valid(tree):
		return
	refresh_burning_trees()
	if tree == _current_tree:
		_current_tree = null
		_is_extinguishing = false

func _on_tree_burned_out(tree: Node3D) -> void:
	if not is_instance_valid(tree):
		return
	refresh_burning_trees()
	if tree == _current_tree:
		_current_tree = null
		_is_extinguishing = false

func _on_extinguish_timer_timeout() -> void:
	if _current_tree and burning_trees.has(_current_tree):
		tree_extinguish_completed.emit(_current_tree)
		_current_tree = null
		_is_extinguishing = false
		_extinguish_progress = 0.0

func is_extinguishing() -> bool:
	return _is_extinguishing

func get_extinguish_progress() -> float:
	return _extinguish_progress

func get_current_tree() -> Node3D:
	return _current_tree
