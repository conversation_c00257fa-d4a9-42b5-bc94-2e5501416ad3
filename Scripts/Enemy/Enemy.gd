class_name Enemy
extends CharacterBody3D

# 玩家捕获信号
signal player_captured(player: CharacterBody3D)

# 状态枚举
enum State { PATROL, IDLE, MOVE_TO_FIRE, EXTINGUISH, CHASE_PLAYER, TRAPPED }
var current_state: State = State.IDLE

# 初始化相关变量
var _initialization_timer: float = 0.0
var _initialization_delay: float = 2.0  # 2秒后开始巡逻
var _is_initialized: bool = false

# 巡逻点等待相关变量
var _patrol_wait_timer: float = 0.0
var _patrol_wait_delay: float = 2.0  # 在巡逻点等待2秒
var _is_waiting_at_patrol_point: bool = false

# 组件引用
@onready var movement: EnemyMovementComponent = $MovementComponent
@onready var navigation: EnemyNavigationComponent = $NavigationComponent
@onready var tree_interaction: EnemyTreeInteractionComponent = $TreeInteractionComponent
@onready var player_interaction: EnemyPlayerInteractionComponent = $PlayerInteractionComponent
@onready var item_interaction: EnemyItemInteractionComponent = $ItemInteractionComponent

# 注意：skin的引用在子类中会被重写
var skin

@export var patrol_wait_time := 2.0
var patrol_wait_timer := 0.0

# 添加状态转换的平滑处理
var _state_transition_time := 0.2  # 状态转换的缓冲时间
var _state_transition_timer := 0.0
var _previous_state: State

func _ready() -> void:
	# 设置基本属性
	collision_layer = 4  # 敌人属于第三层
	collision_mask = 11 | 16  # 与地形、玩家、墙体和陷阱碰撞

	# 确保在enemies组中
	if not is_in_group("enemies"):
		add_to_group("enemies")

	# 添加到迷雾对象组
	add_to_group("fog_objects")

	# 获取skin引用
	skin = get_node_or_null("Enemy01")
	# 注释掉警告，因为不是所有敌人都有Skin节点
	# if not skin:
	#	push_warning("Enemy: Skin node not found!")

	# 连接组件信号
	connect_component_signals()

	# 等待导航系统初始化
	await get_tree().create_timer(0.5).timeout

	# 等待导航地图同步
	navigation.setup_navigation_agent()

	# 确保导航地图已同步
	var nav_map = navigation.navigation_agent.get_navigation_map()
	var max_wait_attempts = 10
	var current_attempt = 0

	while NavigationServer3D.map_get_iteration_id(nav_map) == 0 and current_attempt < max_wait_attempts:
		await get_tree().process_frame
		current_attempt += 1

	# 等待PatrolPointManager初始化完成
	var patrol_manager = _find_patrol_point_manager()
	if patrol_manager:
		# 如果PatrolPointManager已经初始化完成
		if not patrol_manager._initialization_complete:
			# 等待PatrolPointManager初始化完成
			await patrol_manager.patrol_points_created

	# 初始化完成
	# 巡逻点分配现在在2秒初始化完成时进行

func _physics_process(delta: float) -> void:
	# 处理初始化延迟
	if not _is_initialized:
		_initialization_timer += delta
		if _initialization_timer >= _initialization_delay:
			_is_initialized = true
			# 2秒后开始巡逻，首先分配巡逻点
			var patrol_manager = _find_patrol_point_manager()
			if patrol_manager and patrol_manager.has_method("assign_patrol_point_to_enemy"):
				# 初始化时不排除当前巡逻点（因为还没有当前巡逻点）
				var success = patrol_manager.assign_patrol_point_to_enemy(self, -1, false)
				if not success:
					_current_patrol_point = global_position
			else:
				_current_patrol_point = global_position

			# 开始巡逻
			set_state(State.PATROL)
		else:
			# 在初始化期间保持IDLE状态，不执行其他逻辑
			move_and_slide()
			return

	# 检查是否被困住
	if item_interaction.is_in_trapped_state():
		movement.stop_movement()
		# 动画由皮肤组件根据状态自动处理
		move_and_slide()
		return

	# 检查玩家
	var player = get_tree().get_first_node_in_group("player")
	var player_detected = player_interaction.check_for_player()

	if player_detected and player:
		# 如果检测到玩家，设置为追踪状态
		player_interaction.detect_player(player)
		if current_state != State.CHASE_PLAYER:
			set_state(State.CHASE_PLAYER)
	elif player_interaction.is_chasing_player():
		# 如果没有检测到玩家但仍在追踪状态，失去追踪
		# 添加一个持久性检查，避免频繁切换状态
		if _state_transition_timer <= 0:
			player_interaction.lose_player(player_interaction.get_current_player())

	# 如果正在状态转换中，更新计时器
	if _state_transition_timer > 0:
		_state_transition_timer -= delta
		if _state_transition_timer <= 0:
			_state_transition_timer = 0
			# 移除多余的代码：_previous_state = current_state

	# 根据状态执行相应行为
	match current_state:
		State.PATROL:
			handle_patrol_state(delta)
		State.IDLE:
			handle_idle_state(delta)
		State.MOVE_TO_FIRE:
			handle_move_to_fire_state(delta)
		State.EXTINGUISH:
			handle_extinguish_state(delta)
		State.CHASE_PLAYER:
			handle_chase_player_state(delta)

	# 应用移动
	move_and_slide()

	# 打印实时速度（每秒只打印一次）
	if Engine.get_frames_drawn() % int(Engine.get_frames_per_second()) == 0:
		var current_speed = Vector2(velocity.x, velocity.z).length()
		var config_speed = 0.0
		if movement:
			config_speed = movement.get_move_speed()

		var raw_velocity = Vector2(velocity.x, velocity.z)
		var direction = raw_velocity.normalized() if raw_velocity.length() > 0.01 else Vector2.ZERO

		# 详细日志
		var _log_str = name + " 详细速度信息:\n"
		_log_str += "- 实时速度: " + str(current_speed) + "\n"
		_log_str += "- 配置速度: " + str(config_speed) + "\n"
		_log_str += "- 原始速度向量: (" + str(velocity.x) + ", " + str(velocity.z) + ")\n"
		_log_str += "- 移动方向: (" + str(direction.x) + ", " + str(direction.y) + ")\n"

		# 获取当前状态
		var state_name = "未知"
		if current_state >= 0 and current_state < State.size():
			state_name = State.keys()[current_state]
		_log_str += "- 当前状态: " + state_name + "\n"

		# 获取导航信息
		if navigation and navigation.is_navigating:
			var target_pos = navigation.get_final_position()
			var distance = global_position.distance_to(target_pos)
			_log_str += "- 导航目标距离: " + str(distance) + "\n"

			var next_pos = navigation.get_next_path_position()
			var next_distance = global_position.distance_to(next_pos)
			_log_str += "- 下一导航点距离: " + str(next_distance) + "\n"

	# 碰撞检测：检查是否与玩家碰撞
	if _state_transition_timer <= 0 and current_state == State.CHASE_PLAYER:
		for i in range(get_slide_collision_count()):
			var collision = get_slide_collision(i)
			var collider = collision.get_collider()

			# 检查碰撞对象是否是玩家
			if collider and collider.is_in_group("player"):
				# 尝试捕获玩家
				var capture_success = player_interaction.try_capture_player(collider)

				# 添加冷却时间，避免连续多次尝试捕获
				_state_transition_timer = 3.0

				# 如果成功捕获玩家，暂时切换到巡逻状态，给玩家一个喘息的机会
				if capture_success:
					set_state(State.PATROL)

				# 只处理第一个玩家碰撞
				break

# 优化状态设置函数
func set_state(new_state: State) -> void:
	if new_state != current_state:
		# 获取调用栈信息，找出调用此方法的函数名
		var stack = get_stack()
		var _caller_info = "unknown"

		# 尝试获取更外层的调用者信息
		if stack.size() > 1:
			var direct_caller = stack[1]["function"]

			# 如果直接调用者是信号处理函数，显示信号名称
			if direct_caller.begins_with("_on_"):
				_caller_info = direct_caller
			else:
				# 尝试获取更外层的调用者
				if stack.size() > 2:
					var outer_caller = stack[2]["function"]
					_caller_info = direct_caller + " <- " + outer_caller
				else:
					_caller_info = direct_caller

		# 状态变化已移除调试日志

		# 更新状态
		_previous_state = current_state
		current_state = new_state
		_state_transition_timer = _state_transition_time

# Handle movement states
var _last_position = Vector3.ZERO
var _stuck_timer = 0.0
var _is_stuck = false
var _stuck_check_interval = 0.5  # 每0.5秒检查一次是否卡住
var _stuck_distance_threshold = 0.1  # 如果移动距离小于此值，认为卡住了
var _detour_attempts = 0  # 记录尝试绕路的次数
var _max_detour_attempts = 10  # 最大尝试次数
var _obstacle_check_timer = 0.0  # 检查是否成功避开障碍物的计时器
var _obstacle_check_interval = 0.5  # 每0.5秒检查一次是否成功避开障碍物

# 当前巡逻点位置
var _current_patrol_point = Vector3.ZERO

# 设置当前巡逻点
func set_current_patrol_point(point_position: Vector3) -> void:
	_current_patrol_point = point_position

func handle_patrol_state(delta: float) -> void:
	# 检查是否有着火的树
	var nearest_tree = tree_interaction.find_nearest_burning_tree(global_position)
	if nearest_tree:
		navigation.set_target_position(nearest_tree.global_position)
		set_state(State.MOVE_TO_FIRE)
		return



	# 改进的卡住检测逻辑
	_stuck_timer += delta

	# 每隔一段时间检查一次是否卡住
	if _stuck_timer >= _stuck_check_interval:
		_stuck_timer = 0.0

		# 计算移动距离
		var movement_distance = global_position.distance_to(_last_position)
		_last_position = global_position

		if not _is_stuck:
			# 如果移动距离小于阈值，认为卡住了
			if movement_distance < _stuck_distance_threshold:
				_is_stuck = true
				_detour_attempts = 1
				# 尝试绕路
				_try_navigate_around_obstacle()
				# 重置检查计时器
				_obstacle_check_timer = 0.0
		else:
			# 如果已经卡住，检查是否成功避开障碍物
			_obstacle_check_timer += _stuck_check_interval

			if movement_distance > _stuck_distance_threshold:
				# 成功移动，重置卡住状态
				_is_stuck = false
				_detour_attempts = 0
			elif _obstacle_check_timer >= _obstacle_check_interval:
				# 仍然卡住，再次尝试绕路
				_obstacle_check_timer = 0.0
				_detour_attempts += 1

				if _detour_attempts <= _max_detour_attempts:
					# 再次尝试绕路
					_try_navigate_around_obstacle()
				else:
					# 达到最大尝试次数，重置并尝试获取新的巡逻点
					_detour_attempts = 0
					_is_stuck = false

					var patrol_manager = _find_patrol_point_manager()
					if patrol_manager and patrol_manager.has_method("assign_patrol_point_to_enemy"):
						# 卡住时获取新巡逻点，排除当前巡逻点
						patrol_manager.assign_patrol_point_to_enemy(self, -1, true)

	# 检查导航状态
	if not navigation.is_navigating:
		# 如果没有在导航中，说明是初始状态或者刚刚完成了等待
		# 尝试获取PatrolPointManager
		var patrol_manager = _find_patrol_point_manager()
		if patrol_manager and patrol_manager.has_method("assign_patrol_point_to_enemy"):
			# 选择一个新的巡逻点，排除当前巡逻点
			patrol_manager.assign_patrol_point_to_enemy(self, -1, true)
		return

	# 检查是否到达了巡逻点
	if navigation.is_navigation_finished():
		# 使用保存的巡逻点位置
		var distance = global_position.distance_to(_current_patrol_point)

		# 只有当距离真的很近时，才认为到达了巡逻点
		if distance <= 3.0:
			# 到达巡逻点，切换到IDLE状态等待
			_is_waiting_at_patrol_point = true
			_patrol_wait_timer = 0.0
			set_state(State.IDLE)
			return
		else:
			# 如果导航系统认为完成了，但实际距离还很远，重新设置导航目标
			navigation.set_target_position(_current_patrol_point)

	# 继续移动到巡逻点
	var next_position = navigation.get_next_path_position()
	movement.move_to_position(next_position, delta)

# 尝试绕过障碍物
func _try_navigate_around_obstacle() -> void:
	# 获取当前朝向
	var forward_dir = Vector3.ZERO
	if has_node("Skin"):
		var skin_node = get_node("Skin")
		forward_dir = Vector3(sin(skin_node.rotation.y), 0, cos(skin_node.rotation.y))
	else:
		# 如果没有Skin节点，使用当前移动方向
		forward_dir = movement.get_movement_direction()

	# 如果没有有效的朝向，使用随机方向
	if forward_dir.length() < 0.1:
		var random_angle = randf() * 2 * PI
		forward_dir = Vector3(sin(random_angle), 0, cos(random_angle))

	# 计算左右方向
	var _right_dir = forward_dir.cross(Vector3.UP).normalized()

	# 根据尝试次数增加绕路角度
	var angle_offset = PI/4 * (1 + _detour_attempts * 0.5)  # 随着尝试次数增加角度

	# 根据尝试次数选择不同的绕路策略
	var detour_dir

	if _detour_attempts <= 2:
		# 前两次尝试，随机选择左右方向
		if randf() > 0.5:
			detour_dir = forward_dir.rotated(Vector3.UP, angle_offset)  # 右转
		else:
			detour_dir = forward_dir.rotated(Vector3.UP, -angle_offset)  # 左转
	elif _detour_attempts <= 4:
		# 第3-4次尝试，尝试更大角度的左右方向
		angle_offset = PI/2  # 90度
		if randf() > 0.5:
			detour_dir = forward_dir.rotated(Vector3.UP, angle_offset)
		else:
			detour_dir = forward_dir.rotated(Vector3.UP, -angle_offset)
	elif _detour_attempts <= 6:
		# 第5-6次尝试，尝试后退
		detour_dir = -forward_dir
	else:
		# 第7次及以后，完全随机方向
		var random_angle = randf() * 2 * PI
		detour_dir = Vector3(sin(random_angle), 0, cos(random_angle))

	# 根据尝试次数增加距离
	var distance = 3.0 + _detour_attempts * 1.0  # 随着尝试次数增加距离
	distance = min(distance, 10.0)  # 最大距离限制为10

	# 设置一个临时目标点
	var detour_pos = global_position + detour_dir * distance

	# 确保目标点在导航网格上
	var navigation_server = NavigationServer3D
	var nav_map = navigation.navigation_agent.get_navigation_map()

	# 只有当导航地图已同步时才进行检查
	if navigation_server.map_get_iteration_id(nav_map) > 0:
		detour_pos = navigation_server.map_get_closest_point(nav_map, detour_pos)

	# 设置导航目标
	navigation.set_target_position(detour_pos)

func handle_idle_state(delta: float) -> void:
	movement.stop_movement()

	# 如果是在巡逻点等待
	if _is_waiting_at_patrol_point:
		_patrol_wait_timer += delta
		if _patrol_wait_timer >= _patrol_wait_delay:
			# 等待时间结束，重新开始巡逻
			_is_waiting_at_patrol_point = false
			_patrol_wait_timer = 0.0

			# 重置导航状态
			navigation.stop_navigation()

			# 请求新的巡逻点，排除当前巡逻点
			var patrol_manager = _find_patrol_point_manager()
			if patrol_manager and patrol_manager.has_method("assign_patrol_point_to_enemy"):
				patrol_manager.assign_patrol_point_to_enemy(self, -1, true)

			# 切换回PATROL状态
			set_state(State.PATROL)
		return

	# 原有的IDLE逻辑：添加一个短暂的等待时间，避免立即切换状态
	if _state_transition_timer <= 0:
		# 检查是否有着火的树
		var nearest_tree = tree_interaction.find_nearest_burning_tree(global_position)
		if nearest_tree:
			navigation.set_target_position(nearest_tree.global_position)
			set_state(State.MOVE_TO_FIRE)
		else:
			set_state(State.PATROL)

func handle_move_to_fire_state(delta: float) -> void:
	if not navigation.is_navigating:
		set_state(State.PATROL)
		return

	var next_position = navigation.get_next_path_position()

	# 确保移动组件正确设置速度
	movement.move_to_position(next_position, delta)

	# 检查是否到达灭火范围
	var target_pos = navigation.get_final_position()
	var distance_to_target = global_position.distance_to(target_pos)

	if distance_to_target < tree_interaction.extinguish_distance:
		set_state(State.EXTINGUISH)
		var tree = tree_interaction.find_nearest_burning_tree(global_position)
		if tree:
			tree_interaction.start_extinguishing(tree)

func handle_extinguish_state(delta: float) -> void:
	movement.stop_movement()

	# 处理灭火过程
	tree_interaction.process_extinguishing(delta)

	# 检查是否还在灭火
	if not tree_interaction.is_extinguishing():
		# 如果不再灭火，但状态仍然是 EXTINGUISH，
		# 可能是灭火过程已经结束，但信号还没有触发
		# 在这种情况下，我们手动设置状态为 PATROL
		if _state_transition_timer <= 0:
			_state_transition_timer = _state_transition_time * 3
			set_state(State.PATROL)

func handle_chase_player_state(delta: float) -> void:
	var player = player_interaction.get_current_player()

	# 只有当玩家为null时才切换回巡逻状态
	if not player:
		set_state(State.PATROL)
		return

	# 更新导航目标为玩家位置
	navigation.set_target_position(player.global_position)
	var next_position = navigation.get_next_path_position()
	movement.move_to_position(next_position, delta)

	# 注意：不再在这里检测距离和尝试捕获玩家
	# 捕获逻辑已移至碰撞检测中

# Signal handlers
func _on_player_captured(player: CharacterBody3D) -> void:
	# 动画由皮肤组件根据状态自动处理
	player_captured.emit(player)

func _on_player_detected(_player: CharacterBody3D) -> void:
	# 重置巡逻等待状态
	_is_waiting_at_patrol_point = false
	_patrol_wait_timer = 0.0
	set_state(State.CHASE_PLAYER)

func _on_player_lost(_player: CharacterBody3D) -> void:
	# 动画由皮肤组件根据状态自动处理
	set_state(State.PATROL)

func _on_tree_extinguish_started(_tree: Node3D) -> void:
	# 重置巡逻等待状态
	_is_waiting_at_patrol_point = false
	_patrol_wait_timer = 0.0
	# 保持在 EXTINGUISH 状态，不切换到 IDLE
	# 如果当前不是 EXTINGUISH 状态，则设置为 EXTINGUISH
	if current_state != State.EXTINGUISH:
		set_state(State.EXTINGUISH)

func _on_tree_extinguish_completed(_tree: Node3D) -> void:
	# 使用正常的状态转换时间
	_state_transition_timer = _state_transition_time
	set_state(State.PATROL)

func _on_trapped_state_changed(trapped: bool) -> void:
	# 重置巡逻等待状态
	_is_waiting_at_patrol_point = false
	_patrol_wait_timer = 0.0
	if trapped:
		set_state(State.TRAPPED)
	else:
		set_state(State.PATROL)

# 连接组件信号
func connect_component_signals() -> void:
	# 玩家交互信号
	player_interaction.player_captured.connect(_on_player_captured)
	player_interaction.player_detected.connect(_on_player_detected)
	player_interaction.player_lost.connect(_on_player_lost)

	# 树木交互信号
	tree_interaction.tree_extinguish_started.connect(_on_tree_extinguish_started)
	tree_interaction.tree_extinguish_completed.connect(_on_tree_extinguish_completed)

	# 道具交互信号
	item_interaction.trapped_state_changed.connect(_on_trapped_state_changed)

# 查找PatrolPointManager的辅助方法
func _find_patrol_point_manager() -> Node:
	# 方法1：在当前场景中查找（与敌人平级）
	var patrol_manager = get_parent().get_node_or_null("PatrolPointManager")
	if patrol_manager:
		return patrol_manager

	# 方法2：查找patrol_managers组中的节点
	var nodes = get_tree().get_nodes_in_group("patrol_managers")
	if not nodes.is_empty():
		return nodes[0]

	return null

# 不再需要递归查找节点的方法
