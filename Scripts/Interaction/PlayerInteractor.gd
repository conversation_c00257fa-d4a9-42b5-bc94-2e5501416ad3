extends Interactor

@export var player: CharacterBody3D

const LONG_PRESS_DURATION = 2.0
var press_time = 0.0
var is_long_press = false

# 添加信号用于转发area信号给player
signal interactor_area_entered(area)
signal interactor_area_exited(area)

var cached_closest: Interactable

func _ready() -> void:
	controller = player

	# 使用内置信号连接到自定义方法
	if not area_entered.is_connected(_on_area_entered):
		area_entered.connect(_on_area_entered)
	if not area_exited.is_connected(_on_area_exited):
		area_exited.connect(_on_area_exited)

func _physics_process(_delta: float) -> void:
	var new_closest: Interactable = get_closest_interactable()
	if new_closest != cached_closest:
		if is_instance_valid(cached_closest):
			unfocus(cached_closest)
		cached_closest = new_closest
		if is_instance_valid(cached_closest):
			focus(cached_closest)

func _input(event: InputEvent) -> void:
	if event.is_action_pressed("interact"):
		# 立即交互
		if is_instance_valid(cached_closest):
			interact(cached_closest)
	elif event.is_action_released("interact"):
		# 长按结束
		if is_long_press and is_instance_valid(cached_closest):
			# 发送长按结束信号
			cached_closest.interacted.emit(self)
		press_time = 0.0
		is_long_press = false

func _process(delta: float) -> void:
	# 处理长按逻辑
	if Input.is_action_pressed("interact"):
		press_time += delta
		if press_time >= LONG_PRESS_DURATION and not is_long_press:
			is_long_press = true
			if is_instance_valid(cached_closest):
				# 发送长按开始信号
				cached_closest.interacted.emit(self)

# 转发area_entered信号
func _on_area_entered(area: Area3D) -> void:
	interactor_area_entered.emit(area)

# 增强area_exited函数，转发信号
func _on_area_exited(area: Area3D) -> void:
	interactor_area_exited.emit(area)

	if cached_closest == area:
		unfocus(area)
		if is_long_press:
			# 如果在长按过程中离开区域，发送长按结束信号
			area.interacted.emit(self)
			press_time = 0.0
			is_long_press = false
