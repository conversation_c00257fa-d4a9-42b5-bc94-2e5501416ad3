extends Interactor

@export var enemy: CharacterBody3D

const LONG_PRESS_DURATION = 2.0
var press_time = 0.0
var is_long_press = false

var cached_closest: Interactable

func _ready() -> void:
	controller = enemy

	# 设置碰撞层和掩码
	collision_layer = 4  # 敌人层
	collision_mask = 2   # 玩家层

	# 强制设置碰撞层和掩码，确保覆盖场景中的设置
	set_collision_layer(4)
	set_collision_mask(2)

	# 连接信号
	if not body_entered.is_connected(_on_body_entered):
		body_entered.connect(_on_body_entered)

func _on_body_entered(body: Node3D) -> void:
	# 检查是否是玩家
	if body.is_in_group("player"):
		# 获取玩家交互组件
		var player_interaction = enemy.get_node_or_null("PlayerInteractionComponent")
		if player_interaction:
			player_interaction.check_for_player()

func _physics_process(_delta: float) -> void:
	var new_closest: Interactable = get_closest_interactable()
	if new_closest != cached_closest:
		if is_instance_valid(cached_closest):
			unfocus(cached_closest)

		cached_closest = new_closest

func _process(delta: float) -> void:
	# 如果按键正在按住，持续增加计时
	if Input.is_action_pressed("interact"):
		press_time += delta
		if press_time >= LONG_PRESS_DURATION and not is_long_press:
			is_long_press = true
			if is_instance_valid(cached_closest):
				interact(cached_closest)

	else:
		# 如果按键松开，重置计时
		press_time = 0.0
		is_long_press = false

func _on_area_exited(area: Interactable) -> void:
	if cached_closest == area:
		unfocus(area)
