extends Node3D

# 预加载所有关卡场景
const LEVELS = {
	"level_01": preload("res://Scenes/levels/Level01.tscn"),
	"level_02": preload("res://Scenes/levels/Level02.tscn"),
	# 添加更多关卡...
}

# 游戏设置
# @export var game_duration := 600.0  # 游戏持续时间（秒）

# 使用get_node而不是@onready，避免在节点不存在时报错
var level_manager: Node
var game_over_ui
var game_win_ui

func _ready() -> void:
	# 等待一帧确保所有节点都已准备就绪
	await get_tree().process_frame

	# 初始化节点引用
	level_manager = get_node_or_null("LevelManager")
	game_over_ui = get_node_or_null("GameOverUI")
	game_win_ui = get_node_or_null("GameWinUI")

	# 如果level_manager不存在，创建一个
	if level_manager == null:
		level_manager = Node.new()
		level_manager.name = "LevelManager"
		add_child(level_manager)

	# 检查是否有指定的关卡配置
	var level_to_load = "level_01"  # 默认加载第一个关卡
	var level_config_path = ""

	# 从全局游戏数据中获取关卡信息
	var game_data = get_node_or_null("/root/GameData")
	if game_data:
		if game_data.has_meta("current_level_id"):
			level_to_load = game_data.get_meta("current_level_id")
			# 清除元数据，避免重复使用
			game_data.remove_meta("current_level_id")

		if game_data.has_meta("current_level_config_path"):
			level_config_path = game_data.get_meta("current_level_config_path")
			# 清除元数据，避免重复使用
			game_data.remove_meta("current_level_config_path")

	# 加载指定的关卡
	load_level(level_to_load, level_config_path)

	# 链接树木燃尽信号
	connect_tree_signals()

func connect_tree_signals() -> void:
	# 确保TreeSignals已加载
	var tree_signals = get_node_or_null("/root/TreeSignals")
	if not tree_signals:
		await get_tree().process_frame
		tree_signals = get_node_or_null("/root/TreeSignals")
		if not tree_signals:
			push_warning("Main: 无法找到TreeSignals单例")
			return

	# 连接树木燃尽信号
	if tree_signals.is_connected("tree_burned_out", _on_tree_burned_out):
		tree_signals.tree_burned_out.disconnect(_on_tree_burned_out)
	tree_signals.tree_burned_out.connect(_on_tree_burned_out)

# 处理树木燃尽信号
func _on_tree_burned_out(_tree: Node3D) -> void:
	# 检查是否所有树木都已燃尽
	check_all_trees_burned_out()

# 检查是否所有树木都已燃尽
func check_all_trees_burned_out() -> void:
	# 获取所有树木
	var all_trees = get_tree().get_nodes_in_group("trees")
	if all_trees.is_empty():
		return

	# 检查每棵树的状态
	var all_burned_out = true
	for tree in all_trees:
		if is_instance_valid(tree):
			# 检查树木是否燃尽
			var is_burnt_out = false
			if "current_state" in tree and tree.current_state == 5:  # TreeState.BURNT_OUT = 5
				is_burnt_out = true
			elif tree.has_method("get_state") and tree.get_state() == "burnt_out":
				is_burnt_out = true

			# 如果有任何一棵树未燃尽，则标记为false
			if not is_burnt_out:
				all_burned_out = false
				break

	# 如果所有树木都已燃尽，显示游戏胜利界面
	if all_burned_out:
		if game_win_ui:
			game_win_ui.show_game_win()
			return

func load_level(level_name: String, config_path: String = "") -> void:
	if not LEVELS.has(level_name):
		push_warning("关卡 %s 不存在！" % level_name)
		return

	# 确保level_manager存在
	if level_manager == null:
		level_manager = Node.new()
		level_manager.name = "LevelManager"
		add_child(level_manager)

	# 清除当前关卡
	for child in level_manager.get_children():
		child.queue_free()

	# 实例化新关卡
	var level_instance = LEVELS[level_name].instantiate()

	# 如果有指定的关卡配置，设置到关卡生成器
	if not config_path.is_empty() and level_instance.has_node("ModularLevelGenerator"):
		var generator = level_instance.get_node("ModularLevelGenerator")
		if generator and ResourceLoader.exists(config_path):
			var config = ResourceLoader.load(config_path)
			if config:
				generator.level_config = config

	level_manager.add_child(level_instance)

	# 确保关卡被正确设置为场景树的一部分
	if Engine.is_editor_hint():
		level_instance.set_owner(get_tree().edited_scene_root)

	# 每次加载新关卡时重置游戏时间
	if game_over_ui:
		game_over_ui.start_timer()

# 切换到下一个关卡
func next_level() -> void:
	# 确保level_manager存在
	if level_manager == null:
		level_manager = Node.new()
		level_manager.name = "LevelManager"
		add_child(level_manager)
		load_level("level_01")
		return

	# 检查当前关卡
	if level_manager.get_child_count() == 0:
		load_level("level_01")
		return

	var current_level = level_manager.get_child(0)
	if not current_level:
		load_level("level_01")
		return

	var current_level_name = current_level.scene_file_path.get_file().trim_suffix(".tscn")

	var level_number = 1
	if current_level_name.begins_with("level_"):
		var parts = current_level_name.split("_")
		if parts.size() >= 2:
			level_number = parts[1].to_int()

	var next_level_name = "level_%02d" % (level_number + 1)

	if LEVELS.has(next_level_name):
		load_level(next_level_name)
	else:
		push_warning("游戏通关！")

func _input(event: InputEvent) -> void:
	# 处理鼠标点击
	if event is InputEventMouseButton:
		if event.button_index == MOUSE_BUTTON_LEFT and event.pressed:
			# 点击时捕获鼠标
			Input.mouse_mode = Input.MOUSE_MODE_CAPTURED
			get_viewport().set_input_as_handled()

	# 处理ESC键
	if event.is_action_pressed("ui_cancel"):
		# 阻止事件继续传播
		get_viewport().set_input_as_handled()
		# 显示鼠标
		Input.mouse_mode = Input.MOUSE_MODE_VISIBLE

	# 用于测试：按N键切换到下一关
	if OS.is_debug_build() and event.is_action_pressed("next_level"):
		next_level()

	# 用于测试：按G键立即显示游戏结束界面
	if OS.is_debug_build() and event.is_action_pressed("game_over_test"):
		if game_over_ui:
			game_over_ui.show_game_over(true) # 测试时强制暂停游戏

	# 用于测试：按W键立即显示游戏胜利界面
	if OS.is_debug_build() and event.is_action_pressed("ui_up"):
		if game_win_ui:
			game_win_ui.show_game_win()
