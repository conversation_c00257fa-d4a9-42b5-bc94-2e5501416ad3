extends Node

# 配置
@export_file("*.tres") var level_config_path := "res://Resources/LevelConfigs/demo_level.tres"
@export var auto_generate := true
@export var wait_time := 0.5

# 节点引用
var level_generator: Node = null

func _ready() -> void:
	# 查找关卡生成器节点
	level_generator = get_parent().get_node_or_null("ModularLevelGenerator")
	if not level_generator:
		push_error("[LevelGeneratorStarter] 错误：找不到ModularLevelGenerator节点！")
		return
	
	if auto_generate:
		# 延迟一点时间，确保场景完全加载
		await get_tree().create_timer(wait_time).timeout
		generate_level()

# 生成关卡
func generate_level() -> void:
	if not level_generator:
		push_error("[LevelGeneratorStarter] 错误：找不到ModularLevelGenerator节点！")
		return
	
	# 加载关卡配置
	var config = load_level_config()
	if config:
		# 设置关卡配置
		level_generator.level_config = config
		
		# 生成关卡
		level_generator.generate_level()


# 加载关卡配置
func load_level_config() -> Resource:
	if not level_config_path.is_empty() and ResourceLoader.exists(level_config_path):
		var config = ResourceLoader.load(level_config_path)
		if config and config is LevelConfig:
			return config
	
	push_error("[LevelGeneratorStarter] 错误：无法加载关卡配置：" + level_config_path)
	return null

# 随机选择关卡配置 (从目录中选择一个)
func choose_random_level_config() -> Resource:
	var dir = DirAccess.open("res://Resources/LevelConfigs")
	if not dir:
		push_error("[LevelGeneratorStarter] 错误：无法打开关卡配置目录！")
		return null
	
	var config_files = []
	dir.list_dir_begin()
	var file = dir.get_next()
	while file != "":
		if file.ends_with(".tres"):
			config_files.append("res://Resources/LevelConfigs/" + file)
		file = dir.get_next()
	dir.list_dir_end()
	
	if config_files.size() > 0:
		# 随机选择一个配置文件
		var random_config_path = config_files[randi() % config_files.size()]
		return load_level_config_from_path(random_config_path)
	
	push_error("[LevelGeneratorStarter] 错误：配置目录中没有找到任何关卡配置！")
	return null

# 从指定路径加载关卡配置
func load_level_config_from_path(path: String) -> Resource:
	if ResourceLoader.exists(path):
		var config = ResourceLoader.load(path)
		if config and config is LevelConfig:
			return config
	
	push_error("[LevelGeneratorStarter] 错误：无法加载关卡配置：" + path)
	return null 
