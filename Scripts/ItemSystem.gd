@tool # 允许在编辑器中工作
extends Node

# 游戏系统 - 道具系统
# 负责管理所有道具的创建、分配和交互

@export_group("调试选项")
@export var debug_add_gas_to_player: bool = false  # 此项将被禁用，不再自动添加道具

# 预加载道具资源
var gas_item_resource: Resource

func _ready():

	
	# 连接到场景树的node_added信号
	if not get_tree().node_added.is_connected(_on_node_added):
		get_tree().node_added.connect(_on_node_added)
	
	# 预加载GasItem资源
	gas_item_resource = preload("res://Resources/GasItem.tres")

	
	# 禁用自动添加GAS道具
	debug_add_gas_to_player = false
	
	# 清空所有道具
	clear_all_items()
	
	# 延迟连接到所有道具拾取物
	call_deferred("connect_to_all_item_pickups")
	


# 更新方法：只处理UI更新和拾取物连接，不再添加默认道具
func _process(_delta) -> void:
	# 周期性检查是否有新的拾取物体加入场景
	# 不再每帧强制更新UI，减少性能开销
	pass

# 新方法：注册GasItem资源
func register_gas_item(item_resource: Resource) -> void:
	if item_resource and item_resource is Item:
		gas_item_resource = item_resource
		
		# 由于资源已更新，尝试预热UI系统
		call_deferred("_preheat_ui_system")
	else:
		push_error("ItemSystem: 尝试注册无效的道具资源")

# 预热UI系统，确保UI在游戏启动时正确显示
func _preheat_ui_system() -> void:
	# 给UI系统一点时间初始化
	await get_tree().create_timer(0.1).timeout
	
	# 连接到所有拾取物体
	connect_to_all_item_pickups()

# 直接更新UI，绕过玩家节点
func update_item_boxes_ui() -> void:
	var item_boxes = get_tree().get_nodes_in_group("item_boxes_ui")
	if item_boxes.size() > 0:
		var item_ui = item_boxes[0]
		if item_ui.has_method("update_items"):
			# 不再向UI发送默认道具，传递空数组表示没有道具
			item_ui.update_items([])


# 连接到场景中所有的道具拾取物体
func connect_to_all_item_pickups() -> void:
	var pickups = get_tree().get_nodes_in_group("item_pickup")
	
	for pickup in pickups:
		if pickup.has_signal("picked_up"):
			# 先断开以前的连接，避免重复连接
			if pickup.is_connected("picked_up", _on_item_picked_up):
				pickup.disconnect("picked_up", _on_item_picked_up)
				
			# 建立新连接
			pickup.connect("picked_up", _on_item_picked_up)
		else:
			push_error("ItemSystem: 道具拾取物缺少picked_up信号: " + pickup.name)

# 清空所有道具
func clear_all_items() -> void:
	# 清空玩家道具
	var players = get_tree().get_nodes_in_group("player")
	for player in players:
		if player.has_method("clear_items"):
			player.clear_items()
	
	# 清空场景中的道具拾取物
	var pickups = get_tree().get_nodes_in_group("item_pickup")
	for pickup in pickups:
		pickup.queue_free()
	


# 当玩家拾取道具时的回调
func _on_item_picked_up(item_resource: Resource, player: Node) -> void:
	if not player or not item_resource:
		push_error("ItemSystem: 无效的道具或玩家")
		return
	
	# 检查道具资源是否有效
	if not item_resource is Item:
		push_error("ItemSystem: 拾取的不是有效道具")
		return
	
	# 确保玩家有添加道具的方法
	if player.has_method("add_item"):
		# 为拾取的道具创建一个新实例，避免直接使用资源引用
		var picked_item = item_resource.duplicate(true)  # 使用深度复制
		
		# 添加道具到玩家
		var success = player.add_item(picked_item)
		if success:
			# 更新UI
			update_item_boxes_ui_from_player(player)

# 从玩家的道具列表更新道具栏UI
func update_item_boxes_ui_from_player(player: Node) -> void:
	# 等待一帧确保道具已添加到玩家
	await get_tree().process_frame
	
	# 获取玩家的道具列表
	var player_items = []
	if player.has_method("get_items"):
		player_items = player.get_items()
	
	# 查找道具栏UI
	var item_boxes = get_tree().get_nodes_in_group("item_boxes_ui")
	if item_boxes.size() > 0:
		var item_ui = item_boxes[0]
		if item_ui.has_method("update_items"):
			item_ui.update_items(player_items)


# 当一个新节点被添加到场景树时的回调
func _on_node_added(node: Node) -> void:

	
	# 检查是否为道具拾取物
	if node.is_in_group("item_pickup"):
		call_deferred("connect_to_all_item_pickups")
	
	# 检查是否为道具栏UI
	if node.is_in_group("item_boxes_ui"):
		call_deferred("_preheat_ui_system") 

# 当玩家使用道具时的回调
func _on_item_used(player: Node, item: Item, success: bool) -> void:
	if not player or not item:
		return
	
	if success:
		# 如果是一次性道具，从玩家背包中移除
		if item.is_one_time_use:
			# 更新UI
			update_item_boxes_ui_from_player(player)
