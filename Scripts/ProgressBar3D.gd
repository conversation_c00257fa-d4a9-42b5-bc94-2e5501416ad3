extends Node3D

@export var progress := 0.0:
	set(value):
		progress = clamp(value, 0.0, 1.0)
		if progress_bar:
			progress_bar.value = progress * 100

@onready var progress_bar = $SubViewport/ProgressBar
@onready var sprite_3d = $Sprite3D
@onready var sub_viewport = $SubViewport

var _current_value := 0.0
var _target_value := 0.0
var _smooth_speed := 10.0  # 平滑过渡速度
var _offset = Vector3(0, 2.0, 0)  # 相对于树的偏移

func _ready() -> void:
	
	
	# 确保所有节点都存在
	if not progress_bar or not sprite_3d or not sub_viewport:
		push_error("[ProgressBar3D] 错误：缺少必要节点！")
		return
		
	# 确保所有节点都可见
	visible = false  # 默认隐藏
	sprite_3d.visible = true
	progress_bar.visible = true
	
	# 设置SubViewport属性
	sub_viewport.render_target_update_mode = SubViewport.UPDATE_ALWAYS
	
	# 确保Sprite3D使用SubViewport的纹理
	sprite_3d.texture = sub_viewport.get_texture()
	
	# 设置Sprite3D的属性
	sprite_3d.billboard = true
	sprite_3d.transparent = true
	sprite_3d.no_depth_test = true
	sprite_3d.pixel_size = 0.005
	sprite_3d.modulate.a = 1.0
	
	# 设置SubViewport大小
	sub_viewport.size = Vector2i(200, 20)
	
	# 设置初始值
	set_value(0)

func _process(delta: float) -> void:
	# 平滑更新进度值
	if _current_value != _target_value:
		_current_value = lerp(_current_value, _target_value, delta * _smooth_speed)
		progress_bar.value = _current_value
	
	# 如果是top_level，需要手动更新位置
	if get_parent() and top_level:
		global_position = get_parent().global_position + _offset

func set_value(value: float) -> void:
	_target_value = clamp(value, 0, 100)
	_current_value = _target_value
	if progress_bar:
		progress_bar.value = _current_value

func set_height(height: float) -> void:
	_offset.y = height  # 更新偏移
	if get_parent() and top_level:
		global_position = get_parent().global_position + _offset

func set_color(color: Color) -> void:
	if not progress_bar:
		return
		
	# 设置填充样式
	var fill_style = StyleBoxFlat.new()
	fill_style.bg_color = color
	fill_style.corner_radius_top_left = 4
	fill_style.corner_radius_top_right = 4
	fill_style.corner_radius_bottom_right = 4
	fill_style.corner_radius_bottom_left = 4
	progress_bar.add_theme_stylebox_override("fill", fill_style)
	
	# 设置背景样式
	var bg_style = StyleBoxFlat.new()
	bg_style.bg_color = Color(0, 0, 0, 0.8)
	bg_style.corner_radius_top_left = 4
	bg_style.corner_radius_top_right = 4
	bg_style.corner_radius_bottom_right = 4
	bg_style.corner_radius_bottom_left = 4
	progress_bar.add_theme_stylebox_override("background", bg_style)
	
