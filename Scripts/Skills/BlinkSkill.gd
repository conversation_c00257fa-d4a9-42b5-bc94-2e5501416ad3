extends "res://Scripts/Skills/Skill.gd"
class_name BlinkSkill

# 闪烁技能专有属性
@export var blink_distance: float = 5.0  # 闪烁距离（米）
@export var effect_scene: PackedScene  # 可选的特效场景
@export var camera_transition_time: float = 0.3  # 相机过渡时间（秒）

func _init() -> void:
	skill_name = "BLINK"
	cooldown_time = 10.0
	skill_description = "向当前朝向瞬间移动" + str(blink_distance) + "米"

# 实现激活方法
func activate() -> bool:
	# 尝试从全局获取玩家节点
	var player = find_player()
	if not player:
		return false

	# 获取玩家的前向方向
	var forward_direction = Vector3.BACK
	if player.has_method("get_forward_direction"):
		forward_direction = player.get_forward_direction()
	elif player.get("_last_input_direction") != null:
		# 检查_last_input_direction的类型
		if player._last_input_direction is Vector2:
			# 将Vector2转换为Vector3
			var dir_2d = player._last_input_direction
			forward_direction = Vector3(dir_2d.x, 0, dir_2d.y)
		else:
			forward_direction = player._last_input_direction

	# 获取玩家的交互器（现在直接在Player下）
	var interactor = player.get_node_or_null("PlayerInteractor")

	# 保存原始位置
	var original_player_pos = player.global_position

	# 计算闪烁目标位置
	var target_position = original_player_pos + forward_direction * blink_distance

	# 添加射线检测，防止穿墙
	var space_state = player.get_world_3d().direct_space_state
	var query = PhysicsRayQueryParameters3D.create(original_player_pos, target_position)
	query.exclude = [player]  # 排除玩家自身
	var result = space_state.intersect_ray(query)

	# 如果检测到障碍物，调整闪烁距离
	if result:
		# 找到的碰撞点向后移动一点，避免卡在墙里
		var safe_distance = 0.5  # 安全距离
		var _collision_normal = result.get("normal", Vector3.ZERO)
		target_position = result.position - forward_direction * safe_distance

	# 生成特效
	spawn_effect(original_player_pos, target_position)

	# 在闪烁前暂时禁用交互器
	if interactor and interactor is Area3D:
		interactor.monitoring = false
		interactor.monitorable = false

	# 执行闪烁 - 直接设置玩家的位置
	player.global_position = target_position

	# 固定Camera会自动跟随，不需要手动处理Camera过渡

	# 直接设置交互器位置
	if interactor:
		# 直接将交互器设置到玩家位置
		interactor.global_position = player.global_position

		# 重新启用交互器
		interactor.monitoring = true
		interactor.monitorable = true

	# 通知调试器
	notify_debugger(player)

	# 启动冷却
	start_cooldown()

	return true

# 找到玩家节点的辅助函数 - 简化版本
func find_player() -> Node3D:
	# 直接从场景树中查找player组的第一个节点
	var scene_tree = Engine.get_main_loop()
	if scene_tree and scene_tree is SceneTree:
		var player = scene_tree.get_first_node_in_group("player")
		if player and player is Node3D:
			return player

	return null

# 辅助函数：递归查找指定组的节点
func _find_nodes_in_group(node: Node, group_name: String, result: Array) -> void:
	if node.is_in_group(group_name):
		result.append(node)

	for child in node.get_children():
		_find_nodes_in_group(child, group_name, result)

# 修改：确保交互器正确更新，不使用信号或延迟
func update_interactor(_player: Node3D) -> void:
	# 逻辑已移到activate函数中，这是为了保持兼容性
	pass

# 平滑相机过渡 - 固定Camera不需要此功能
func smooth_camera_transition(_camera_pivot: Node3D, _start_position: Vector3, _target_position: Vector3) -> void:
	# 固定Camera会自动跟随Player，不需要手动过渡
	pass

# 生成闪烁特效 - 简化版本
func spawn_effect(start_pos: Vector3, end_pos: Vector3) -> void:
	if effect_scene:
		# 直接从场景树获取当前场景
		var scene_tree = Engine.get_main_loop()
		if not scene_tree or not scene_tree is SceneTree:
			return

		# 获取当前场景
		var current_scene = scene_tree.get_root()
		if not current_scene:
			return

		# 在起点和终点生成特效
		var start_effect = effect_scene.instantiate()
		var end_effect = effect_scene.instantiate()

		current_scene.add_child(start_effect)
		current_scene.add_child(end_effect)

		# 确保效果节点是3D节点
		if start_effect is Node3D and end_effect is Node3D:
			start_effect.global_position = start_pos
			end_effect.global_position = end_pos
	else:
		# 没有特效场景时，可以添加简单的粒子或其他视觉反馈
		pass

# 新增：通知交互调试器玩家已闪烁 - 简化版本
func notify_debugger(player: Node3D) -> void:
	# 查找所有交互调试器节点
	if not player:
		return

	# 使用Engine获取场景树
	var scene_tree = Engine.get_main_loop()
	if scene_tree and scene_tree is SceneTree:
		# 获取所有交互调试器节点
		var debuggers = scene_tree.get_nodes_in_group("interaction_debuggers")

		# 通知所有调试器
		for debugger in debuggers:
			if debugger.has_method("_on_player_teleported"):
				debugger.call("_on_player_teleported")

# 新的辅助类，用于延迟重新激活交互器 - 简化版
class DelayedReactivator extends Node:
	var interactor: Area3D
	var elapsed: float = 0
	var delay: float = 0.05

	func _ready() -> void:
		# 设置为一次性使用
		set_process(true)

	func _process(delta: float) -> void:
		elapsed += delta
		if elapsed >= delay:
			# 当足够时间后，重新激活交互器
			reactivate_interactor()
			# 移除自身
			queue_free()

	func reactivate_interactor() -> void:
		if not interactor or not is_instance_valid(interactor):
			return

		# 确保我们有玩家引用
		var player = get_parent()
		if not player or not player is Node3D or not player.is_in_group("player"):
			return

		# 重新启用交互器
		if interactor is Area3D:
			interactor.monitoring = true
			interactor.monitorable = true

		# 最简单的方法：直接将交互器设置到玩家位置
		if interactor is Node3D and player is Node3D:
			interactor.global_position = player.global_position
