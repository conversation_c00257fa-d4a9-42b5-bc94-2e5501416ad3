extends "res://Scripts/Skills/Skill.gd"
class_name BurningSpiritSkill

# 技能特有属性
@export var duration: float = 5.0  # 速度提升持续时间（秒）
@export var speed_multiplier: float = 1.5  # 速度提升倍数

# 内部状态
var _is_triggered: bool = false  # 技能是否已被触发
var _is_active: bool = false  # 速度提升效果是否激活
var _is_invincible: bool = false  # 玩家是否处于无敌状态
var _timer: Timer = null  # 计时器
var _original_speed: float = 0.0  # 玩家原始速度
var _is_used: bool = false  # 技能是否已被使用过（一次性使用）

# 初始化技能属性
func _init() -> void:
	skill_name = "BURNING SPIRIT"
	cooldown_time = 5.0  # 冷却时间5秒
	skill_description = "被动，可以抵御一次敌人抓捕。抵御后，player速度变为1.5倍，持续5秒"
	is_teleport_skill = false
	skill_category = "passive"  # 设置技能类别为"passive"

# 重置技能状态
func reset() -> void:
	_is_triggered = false
	_is_active = false
	_is_invincible = false
	if _timer and _timer.is_inside_tree():
		_timer.stop()

	# 如果速度提升效果正在生效，恢复玩家原始速度
	if _is_active and owner and "move_speed" in owner:
		owner.move_speed = _original_speed
		_is_active = false

	# 注意：不重置_is_used状态，因为这是一次性技能

# 检查技能是否已被触发
func is_triggered() -> bool:
	return _is_triggered

# 检查玩家是否处于无敌状态
func is_invincible() -> bool:
	return _is_invincible

# 检查技能是否已被使用过（一次性使用）
func is_used() -> bool:
	return _is_used

# 触发技能效果
func trigger() -> void:
	if _is_triggered or _is_used:
		return  # 技能已被触发或已被使用过，不能再次触发

	# 标记技能为已触发和已使用
	_is_triggered = true
	_is_used = true
	# 设置无敌状态
	_is_invincible = true

	# 获取玩家引用
	var player = owner
	if not player:
		return

	# 保存玩家原始速度
	if "move_speed" in player:
		_original_speed = player.move_speed

		# 增加玩家速度
		player.move_speed = _original_speed * speed_multiplier
		_is_active = true

		# 创建计时器
		if not _timer:
			_timer = Timer.new()
			_timer.one_shot = true
			_timer.timeout.connect(_on_timer_timeout)
			player.add_child(_timer)

		# 启动计时器
		_timer.wait_time = duration
		_timer.start()

		# 添加视觉效果
		_add_visual_effects()



# 计时器超时回调
func _on_timer_timeout() -> void:
	# 恢复玩家原始速度
	if owner and "move_speed" in owner and _is_active:
		owner.move_speed = _original_speed
		_is_active = false

		# 结束无敌状态
	_is_invincible = false

	# 移除视觉效果
	_remove_visual_effects()

# 添加视觉效果
func _add_visual_effects() -> void:
	var player = owner
	if not player:
		return

	# 创建火焰粒子效果
	var particles = GPUParticles3D.new()
	particles.name = "BurningSpiritParticles"
	player.add_child(particles)

	# 设置粒子属性
	var material = ParticleProcessMaterial.new()
	material.emission_shape = ParticleProcessMaterial.EMISSION_SHAPE_SPHERE
	material.emission_sphere_radius = 1.0
	material.direction = Vector3(0, 1, 0)
	material.spread = 45.0
	material.gravity = Vector3(0, 0.5, 0)
	material.initial_velocity_min = 1.0
	material.initial_velocity_max = 2.0
	material.scale_min = 0.1
	material.scale_max = 0.3
	material.color = Color(1.0, 0.3, 0.0, 0.7)  # 红色火焰
	particles.process_material = material

	# 设置粒子网格
	var mesh = SphereMesh.new()
	mesh.radius = 0.05
	mesh.height = 0.1
	particles.draw_pass_1 = mesh

	# 启动粒子效果
	particles.emitting = true
	particles.amount = 50
	particles.lifetime = 1.0

# 移除视觉效果
func _remove_visual_effects() -> void:
	var player = owner
	if not player:
		return

	# 查找并移除粒子效果
	var particles = player.get_node_or_null("BurningSpiritParticles")
	if particles:
		particles.emitting = false

		# 使用计时器延迟移除，让剩余粒子消失
		var timer = Timer.new()
		player.add_child(timer)
		timer.wait_time = 1.0  # 等待1秒，让粒子消失
		timer.one_shot = true
		timer.timeout.connect(func():
			if is_instance_valid(particles):
				particles.queue_free()
			timer.queue_free()
		)
		timer.start()

# 更新方法，处理冷却
func update(delta: float) -> void:
	# 调用父类的update方法处理冷却
	super.update(delta)
