extends "res://Scripts/Skills/Skill.gd"
class_name FireSkill

# 点火技能专有属性
@export var cast_time: float = 5.0  # 施法时间（秒），这是所有点火相关时间的单一数据源

# 获取点火时间（供其他组件调用）
static func get_fire_cast_time() -> float:
	# 尝试从SkillManager中获取FireSkill资源
	var skill_manager = null

	# 尝试使用全局路径
	if Engine.has_singleton("SkillManager"):
		skill_manager = Engine.get_singleton("SkillManager")
	else:
		# 尝试使用节点路径
		var root = Engine.get_main_loop().get_root()
		if root:
			skill_manager = root.get_node_or_null("/root/SkillManager")

	if skill_manager and skill_manager.has_method("get_skill_resource"):
		var fire_skill = skill_manager.get_skill_resource("fire")
		if fire_skill and fire_skill.get("cast_time") != null:
			return fire_skill.cast_time

	# 如果无法获取，返回默认值
	return 5.0

# 内部状态
var _is_casting: bool = false  # 是否正在施法
var _cast_progress: float = 0.0  # 施法进度（0-1）
var _current_tree = null  # 当前目标树木
var _real_cast_time: float = 0.0  # 实际施法时间（用于调试）

# 初始化函数
func _init() -> void:
	skill_name = "FIRE"
	cooldown_time = 0.0  # 无冷却时间
	skill_description = "施法5秒，点燃一棵树木"
	is_teleport_skill = false
	# 设置技能类别为“fire”
	skill_category = "fire"

# 实现激活方法
func activate() -> bool:
	# 获取玩家引用
	var player = owner
	if not player:
		return false

	# 不再需要检查cast_time

	# 获取玩家的交互器
	var interactor = null
	var camera_pivot = player.get_node_or_null("%CameraPivot")
	if camera_pivot:
		interactor = camera_pivot.get_node_or_null("PlayerInteractor")

	if not interactor:
		return false

	# 获取当前交互的树木
	# 使用玩家的_current_tree变量，这个变量由交互器设置
	_current_tree = player._current_tree
	if not _current_tree or not _current_tree.is_in_group("trees"):
		print("\n\n[FireSkill] 没有可以点火的树木\n\n")
		return false

	# 开始施法过程
	_is_casting = true
	_cast_progress = 0.0
	_real_cast_time = 0.0  # 重置实际施法时间

	# 不再需要打印施法时间

	# 开始点火动画
	if player.has_method("play_fire_animation"):
		player.play_fire_animation()

	# 通知树木开始被点燃
	# 调用start_igniting方法，并传递点火时间和技能对象
	if _current_tree.has_method("start_igniting_with_duration"):
		_current_tree.start_igniting_with_duration(cast_time, self)
	elif _current_tree.has_method("start_igniting"):
		# 兼容旧版本
		_current_tree.start_igniting(self)

	return true

# 取消施法
func cancel() -> void:
	if not _is_casting:
		return

	_is_casting = false
	_cast_progress = 0.0

	# 停止点火动画
	var player = owner
	if player and player.has_method("stop_fire_animation"):
		player.stop_fire_animation()

	# 通知树木停止点火
	if _current_tree:
		if _current_tree.has_method("update_ignite_time"):
			# 重置树木的点火时间
			_current_tree.update_ignite_time(0.0)

		if _current_tree.has_method("stop_igniting"):
			_current_tree.stop_igniting()

	_current_tree = null

# 重写更新方法，处理施法进度
func update(delta: float) -> void:
	# 首先调用父类的update方法处理冷却
	super.update(delta)

	# 如果不在施法中，直接返回
	if not _is_casting:
		return

	# 检查目标树木是否仍然有效
	if not _current_tree or not is_instance_valid(_current_tree):
		cancel()
		return

	# 获取玩家引用
	var player = owner
	if not player:
		cancel()
		return

	# 检查玩家是否仍然按住技能键
	# 如果玩家松开按键，则取消施法
	if not (Input.is_action_pressed("skill_1") or Input.is_action_pressed("skill_2")):
		# 玩家松开了按键，取消施法
		cancel()
		return

	# 检查玩家是否移动，如果移动则取消施法
	if player.is_moving():
		# 玩家移动了，取消施法
		cancel()
		return

	# 更新实际经过的时间
	_real_cast_time += delta

	# 直接使用实际经过的时间来计算施法进度
	_cast_progress = _real_cast_time / cast_time

	# 直接更新树木的点火进度
	if _current_tree and _current_tree.has_method("update_ignite_time"):
		_current_tree.update_ignite_time(_real_cast_time)

	# 不再需要显示进度日志

	# 不再显示点火进度日志，改为在树木进度条中显示

	# 如果实际经过的时间大于等于施法时间，则施法完成
	if _real_cast_time >= cast_time:
		_is_casting = false
		_cast_progress = 0.0

		# 不再需要显示点火完成日志

		# 点燃树木
		if _current_tree.has_method("start_burning"):
			# 直接开始燃烧，不再调用set_ignite_time
			_current_tree.start_burning()
		elif _current_tree.has_method("ignite"):
			_current_tree.ignite()

		# 停止点火动画
		if player.has_method("stop_fire_animation"):
			player.stop_fire_animation()

		_current_tree = null

		# 开始冷却
		start_cooldown()

# 获取施法进度
func get_cast_progress() -> float:
	return _cast_progress

# 是否正在施法
func is_casting() -> bool:
	return _is_casting
