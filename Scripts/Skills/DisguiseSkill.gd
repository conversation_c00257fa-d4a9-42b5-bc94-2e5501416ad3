extends "res://Scripts/Skills/Skill.gd"
class_name DisguiseSkill

# 伪装技能专有属性
@export var disguise_duration: float = 5.0  # 伪装持续时间改为5秒
@export var tree_model_scene: PackedScene  # 用于替换玩家模型的树模型场景

# 内部状态
var _is_disguised: bool = false  # 是否处于伪装状态
var _disguise_timer: float = 0.0  # 伪装计时器
var _original_player_model: Node3D = null  # 原始玩家模型
var _disguise_model: Node3D = null  # 伪装模型
var _original_collision_layer: int = 0  # 原始碰撞层
var _original_collision_mask: int = 0  # 原始碰撞掩码
var _original_velocity: Vector3 = Vector3.ZERO  # 保存原始速度
var _original_process_mode: int = 0  # 原始处理模式

func _init() -> void:
	skill_name = "DISGUISE"
	cooldown_time = 20.0
	skill_description = "伪装成树木，持续5秒。敌人将无法识别你。"
	_is_disguised = false  # 确保初始状态为未伪装
	_disguise_timer = 0.0  # 初始化计时器

# 尝试激活技能 - 重构版本
func try_activate(skill_owner = null) -> bool:
	# 如果提供了skill_owner参数，更新owner
	if skill_owner != null:
		owner = skill_owner

	# 获取玩家引用
	var player = owner
	if not player:
		return false

	# 如果已经在伪装状态，则取消伪装
	if _is_disguised:
		# 直接调用undisguise，而不经过activate
		undisguise(player)
		return true

	# 如果在冷却中，不能激活
	if is_on_cooldown:
		return false

	# 执行激活 - 直接调用apply_disguise，而不经过activate
	var success = apply_disguise(player)

	# 如果激活成功，不需要开始冷却，因为冷却将在解除伪装时开始
	return success

# 实现激活方法 - 不再直接使用，保留为兼容性
func activate() -> bool:
	# 调用新的实现
	return try_activate()

# 更新技能状态
func update(delta: float) -> void:
	# 调用父类的更新方法处理冷却
	super.update(delta)

	# 如果处于伪装状态，更新计时器
	if _is_disguised and owner:
		_disguise_timer -= delta

		# 如果时间到，解除伪装
		if _disguise_timer <= 0:
			undisguise(owner)

		# 确保玩家在伪装状态下不会移动
		enforce_disguise_state(owner)

# 应用伪装
func apply_disguise(player: Node3D) -> bool:
	# 确保有玩家引用
	if not player:
		return false

	# 首先停止抓捕效果 (闪烁+加速+无敌)
	if player.has_method("stop_capture_effects"):
		print("[DisguiseSkill] 停止抓捕效果，准备伪装")
		player.stop_capture_effects()

	# 检查玩家模型
	var skin_node = player.get_node_or_null("Player01")
	if not skin_node:
		# 尝试获取玩家的_skin属性
		if player.has_method("get") and player.get("_skin"):
			skin_node = player.get("_skin")
		else:
			return false

	# 保存原始玩家模型引用
	_original_player_model = skin_node

	# 保存原始碰撞层和掩码
	_original_collision_layer = player.collision_layer
	_original_collision_mask = player.collision_mask

	# 保存原始速度
	if "velocity" in player:
		_original_velocity = player.velocity
		player.velocity = Vector3.ZERO  # 停止玩家移动

	# 创建树模型
	var tree_model = null

	# 直接使用硬编码的路径加载树模型场景
	var tree_scene_path = "res://Scenes/Prefabs/Tree.tscn"

	if ResourceLoader.exists(tree_scene_path):
		var scene = load(tree_scene_path)
		tree_model = scene.instantiate()
	else:
		# 如果直接加载失败，尝试使用预设场景
		if tree_model_scene:
			tree_model = tree_model_scene.instantiate()
		else:
			# 尝试从项目中加载备选树模型
			var backup_paths = [
				"res://Scenes/props/tree.tscn",
				"res://Scenes/Tree.tscn"
			]

			for path in backup_paths:
				if ResourceLoader.exists(path):
					var backup_scene = load(path)
					tree_model = backup_scene.instantiate()
					break

	# 如果所有尝试都失败，创建一个简单的替代模型
	if not tree_model:
		tree_model = Node3D.new()

		# 添加一个简单的立方体作为视觉提示
		var mesh_instance = MeshInstance3D.new()
		var cube_mesh = BoxMesh.new()
		cube_mesh.size = Vector3(1, 3, 1)
		mesh_instance.mesh = cube_mesh

		# 设置绿色材质
		var material = StandardMaterial3D.new()
		material.albedo_color = Color(0.2, 0.8, 0.2)
		mesh_instance.material_override = material

		tree_model.add_child(mesh_instance)
		mesh_instance.position.y = 1.5  # 将立方体向上移动一半高度

	# 隐藏原始模型并禁用动画
	_original_player_model.visible = false

	# 修改碰撞层，使敌人忽略玩家，但保留与环境的碰撞
	player.collision_layer = 8  # 假设8是树木的碰撞层

	# 禁用树模型中的所有碰撞形状，防止与玩家碰撞冲突
	disable_collisions_in_node(tree_model)

	# 添加树模型到玩家
	# 使用正确的场景获取方式，避免错误地添加到UI节点
	var current_scene = player.get_tree().current_scene
	if not current_scene:
		# 如果current_scene为空，尝试通过NavigationRegion3D查找场景
		var nav_regions = player.get_tree().get_nodes_in_group("navigation_region")
		if nav_regions.size() > 0:
			current_scene = nav_regions[0]
		else:
			# 最后的备选方案：直接添加到player的父节点
			current_scene = player.get_parent()

	# 先从当前场景中移除已有的DisguiseModel（如果有）
	var existing_model = current_scene.get_node_or_null("DisguiseModel")
	if existing_model:
		existing_model.queue_free()

	# 添加到当前场景
	current_scene.add_child(tree_model)
	tree_model.name = "DisguiseModel"

	# 将树模型位置设置为玩家位置
	tree_model.global_position = player.global_position

	# 确保树模型可见
	tree_model.visible = true

	# 调整树模型的缩放，使其更容易看到
	tree_model.scale = Vector3(1.5, 1.5, 1.5)

	# 随机旋转树模型，使其看起来更自然
	var random_rotation = randf_range(0, 2 * PI)
	tree_model.rotation.y = random_rotation

	# 保存伪装模型引用
	_disguise_model = tree_model

	# 设置伪装状态和计时器
	_is_disguised = true
	_disguise_timer = disguise_duration

	# 如果玩家有_physics_process或_process，保存并冻结物理处理
	freeze_player_movement(player)

	return true

# 解除伪装
func undisguise(player: Node3D) -> void:
	if not player or not _is_disguised:
		return

	# 恢复原始模型可见性
	if _original_player_model:
		_original_player_model.visible = true

	# 恢复原始碰撞层和掩码
	player.collision_layer = _original_collision_layer
	player.collision_mask = _original_collision_mask

	# 恢复玩家的移动处理
	unfreeze_player_movement(player)

	# 恢复原始速度，但设置为较低值防止突然弹射
	if "velocity" in player:
		player.velocity = _original_velocity * 0.1  # 减小速度以防止弹射

	# 移除伪装模型 - 使用正确的场景获取方式
	var current_scene = player.get_tree().current_scene
	if not current_scene:
		# 如果current_scene为空，尝试通过NavigationRegion3D查找场景
		var nav_regions = player.get_tree().get_nodes_in_group("navigation_region")
		if nav_regions.size() > 0:
			current_scene = nav_regions[0]
		else:
			# 最后的备选方案：直接从player的父节点查找
			current_scene = player.get_parent()

	var disguise_model = current_scene.get_node_or_null("DisguiseModel")

	if disguise_model:
		disguise_model.queue_free()
	elif _disguise_model and is_instance_valid(_disguise_model):
		_disguise_model.queue_free()

	_disguise_model = null

	# 重置伪装状态
	_is_disguised = false
	_disguise_timer = 0.0

	# 开始冷却
	start_cooldown()

# 禁用节点中的所有碰撞形状
func disable_collisions_in_node(node: Node) -> void:
	# 检查节点是否有碰撞形状
	if node is CollisionShape3D or node is CollisionPolygon3D:
		node.disabled = true

	# 检查节点是否有刚体组件
	if node is RigidBody3D:
		node.freeze = true
		node.collision_layer = 0
		node.collision_mask = 0

	# 递归处理所有子节点
	for child in node.get_children():
		disable_collisions_in_node(child)

# 冻结玩家移动
func freeze_player_movement(player: Node3D) -> void:
	# 保存玩家的处理模式
	_original_process_mode = player.process_mode

	# 设置自定义处理模式，允许渲染但禁止物理
	if player is CharacterBody3D:
		# 不能完全禁用处理，但可以添加一个标志来防止移动
		if not player.has_meta("disguised"):
			player.set_meta("disguised", true)

	# 如果玩家有move_and_slide方法，我们可以监听但不实际移动
	if player.has_method("move_and_slide"):
		# 可以通过设置质量等参数使玩家静止
		player.floor_stop_on_slope = true
		player.floor_constant_speed = true

# 解冻玩家移动
func unfreeze_player_movement(player: Node3D) -> void:
	# 恢复原始处理模式 - 使用枚举值而不是整数
	# 根据Godot文档，ProcessMode是一个带有INHERIT、PAUSABLE、DISABLED和ALWAYS四个值的枚举
	var process_values = [Node.PROCESS_MODE_INHERIT, Node.PROCESS_MODE_PAUSABLE,
						  Node.PROCESS_MODE_DISABLED, Node.PROCESS_MODE_ALWAYS]

	if _original_process_mode >= 0 and _original_process_mode < process_values.size():
		player.process_mode = process_values[_original_process_mode]
	else:
		# 如果超出范围，使用默认值INHERIT
		player.process_mode = Node.PROCESS_MODE_INHERIT

	# 移除伪装标志
	if player.has_meta("disguised"):
		player.remove_meta("disguised")

	# 恢复正常的移动参数
	if player is CharacterBody3D:
		player.floor_stop_on_slope = false
		player.floor_constant_speed = false

# 确保伪装状态下的限制
func enforce_disguise_state(player: Node3D) -> void:
	if not _is_disguised or not player:
		return

	# 确保玩家不会移动
	if "velocity" in player:
		player.velocity = Vector3.ZERO

	# 如果玩家尝试移动，阻止它
	if player.has_meta("disguised") and player is CharacterBody3D:
		player.velocity = Vector3.ZERO

	# 更新树模型位置，确保它跟随玩家
	var current_scene = player.get_tree().current_scene
	if not current_scene:
		# 如果current_scene为空，尝试通过NavigationRegion3D查找场景
		var nav_regions = player.get_tree().get_nodes_in_group("navigation_region")
		if nav_regions.size() > 0:
			current_scene = nav_regions[0]
		else:
			# 最后的备选方案：直接从player的父节点查找
			current_scene = player.get_parent()

	var disguise_model = current_scene.get_node_or_null("DisguiseModel")

	if disguise_model:
		# 更新树模型位置为玩家位置
		disguise_model.global_position = player.global_position

# 获取当前伪装状态
func is_disguised() -> bool:
	return _is_disguised

# 获取剩余伪装时间
func get_disguise_time_remaining() -> float:
	return _disguise_timer if _is_disguised else 0.0

# 获取伪装时间百分比
func get_disguise_percent() -> float:
	return _disguise_timer / disguise_duration if _is_disguised else 0.0

# 继承并扩展重置方法
func reset() -> void:
	# 调用父类的重置方法
	super.reset()

	# 重置伪装状态变量，但不强制解除伪装
	# 这样可以避免在初始化时意外解除伪装
	_is_disguised = false
	_disguise_timer = 0.0

	# 注释掉这部分，避免在初始化时意外解除伪装
	# 如果有玩家引用且可能仍在伪装状态，强制解除伪装
	# if owner and (owner.has_node("DisguiseModel") or (owner.has_meta("disguised") and owner.get_meta("disguised"))):
	# 	undisguise(owner)

# 获取技能状态信息 - 用于UI显示
func get_skill_status() -> Dictionary:
	var status = {
		"is_active": _is_disguised,            # 技能是否激活中
		"active_remaining": _disguise_timer,   # 剩余持续时间
		"active_total": disguise_duration,     # 总持续时间
		"is_on_cooldown": is_on_cooldown,      # 是否在冷却中
		"cooldown_remaining": cooldown_remaining, # 剩余冷却时间
		"cooldown_total": cooldown_time        # 总冷却时间
	}
	return status

# 获取持续时间百分比 - 用于UI显示
func get_active_percent() -> float:
	if not _is_disguised or disguise_duration <= 0:
		return 0.0
	return _disguise_timer / disguise_duration
