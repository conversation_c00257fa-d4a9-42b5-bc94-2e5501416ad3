extends "res://Scripts/Skills/Skill.gd"
class_name FlameWaltzSkill

# 被动技能：当一颗树燃尽后，距离它最近的一颗未点燃/熄灭的树木自动被引燃

func _init() -> void:
	skill_name = "FLAME WALTZ"
	cooldown_time = 0.0  # 无冷却时间
	skill_description = "被动技能，当一颗tree燃尽后，距离它最近的一颗未点燃/熄灭的树木自动被引燃。"
	is_teleport_skill = false

# 在技能被初始化时调用
func initialize(owner_node: Node) -> void:
	# 连接到TreeSignals的tree_burned_out信号
	var tree_signals = owner_node.get_node("/root/TreeSignals")
	if tree_signals and not tree_signals.is_connected("tree_burned_out", _on_tree_burned_out):
		tree_signals.connect("tree_burned_out", _on_tree_burned_out)

# 当树木燃尽时触发
func _on_tree_burned_out(burned_tree: Node3D) -> void:
	# 寻找最近的未点燃/熄灭树木并点燃
	var nearest_tree = find_nearest_unburned_tree(burned_tree)
	if nearest_tree:
		# 创建火焰传递效果
		create_flame_path(burned_tree, nearest_tree)
		# 点燃最近的树木
		nearest_tree.start_burning()

# 寻找最近的未点燃/熄灭树木
func find_nearest_unburned_tree(source_tree: Node3D) -> Node3D:
	# 使用SceneTree单例获取树木
	var scene_tree = source_tree.get_tree()
	if not scene_tree:
		return null

	var trees = scene_tree.get_nodes_in_group("trees")
	var nearest_tree = null
	var min_distance = INF

	for tree in trees:
		# 检查树木是否可以被点燃（未点燃或已熄灭）
		if tree != source_tree and tree.has_method("can_interact") and tree.can_interact():
			var distance = source_tree.global_position.distance_to(tree.global_position)
			if distance < min_distance:
				min_distance = distance
				nearest_tree = tree

	return nearest_tree

# 创建火焰传递的视觉效果
func create_flame_path(source_tree: Node3D, target_tree: Node3D) -> void:
	# 获取场景根节点
	var scene_tree = source_tree.get_tree()
	if not scene_tree:
		return

	var root = scene_tree.root
	if not root:
		return

	# 创建一个简单的粒子效果来显示火焰传递
	var particles = GPUParticles3D.new()
	root.add_child(particles)

	# 设置粒子属性
	var material = ParticleProcessMaterial.new()
	material.emission_shape = ParticleProcessMaterial.EMISSION_SHAPE_SPHERE
	material.emission_sphere_radius = 0.1
	material.direction = Vector3(0, 1, 0)
	material.spread = 45.0
	material.gravity = Vector3(0, -1, 0)
	material.initial_velocity_min = 2.0
	material.initial_velocity_max = 5.0
	material.scale_min = 0.1
	material.scale_max = 0.3
	material.color = Color(1.0, 0.5, 0.0, 1.0)  # 橙色火焰
	particles.process_material = material

	# 设置粒子网格
	var mesh = SphereMesh.new()
	mesh.radius = 0.05
	mesh.height = 0.1
	particles.draw_pass_1 = mesh

	# 设置粒子位置和方向
	particles.global_position = source_tree.global_position + Vector3(0, 1, 0)  # 从树的顶部开始
	particles.look_at(target_tree.global_position + Vector3(0, 1, 0), Vector3.UP)

	# 启动粒子效果
	particles.emitting = true
	particles.one_shot = true
	particles.amount = 50

	# 设置自动销毁
	var timer = Timer.new()
	particles.add_child(timer)
	timer.wait_time = 2.0  # 2秒后销毁粒子效果
	timer.one_shot = true
	timer.timeout.connect(func(): particles.queue_free())
	timer.start()

# 实现激活方法（被动技能不需要主动激活，但需要实现这个方法）
func activate() -> bool:
	# 被动技能，不需要主动激活
	# 打印日志，提示玩家这是被动技能
	print("FLAME WALTZ 是被动技能，不需要主动激活")
	return false

# 实现更新方法
func update(_delta: float) -> void:
	# 被动技能，不需要在update中处理逻辑
	pass
