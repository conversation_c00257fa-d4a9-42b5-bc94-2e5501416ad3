extends "res://Scripts/Skills/Skill.gd"
class_name SacredBlazeSkill

# 施法相关变量
var _is_casting: bool = false  # 是否正在施法
var _cast_progress: float = 0.0  # 施法进度（0-1）
var _real_cast_time: float = 0.0  # 实际施法时间
var cast_time: float = 3.5  # 施法时间（秒）
var _trees_to_ignite = []  # 要点燃的树木列表
var _current_tree = null  # 当前正在点燃的树木（用于显示进度条）

# 初始化技能属性
func _init() -> void:
	skill_name = "SACRED BLAZE"
	cooldown_time = 10.0  # 冷却时间10秒
	skill_description = "原地施法5秒，点燃全图的所有树木。"
	is_teleport_skill = false
	skill_category = "fire"  # 设置技能类别为“fire”

# 尝试激活技能
func try_activate(skill_owner = null) -> bool:
	# 如果提供了skill_owner参数，更新owner
	if skill_owner != null:
		owner = skill_owner

	# 检查冷却状态
	if is_on_cooldown:
		return false

	# 激活技能
	var success = activate()

	return success

# 激活技能
func activate() -> bool:
	# 获取玩家引用
	var player = owner
	if not player:
		return false

	# 获取场景中所有树木
	var scene_tree = player.get_tree()
	if not scene_tree:
		return false

	_trees_to_ignite.clear()
	var trees = scene_tree.get_nodes_in_group("trees")

	for tree in trees:
		if tree.has_method("can_interact") and tree.can_interact():
			_trees_to_ignite.append(tree)

	# 如果没有可点燃的树木，直接返回
	if _trees_to_ignite.is_empty():
		return false

	# 选择第一棵树木作为当前树木（用于显示进度条）
	_current_tree = _trees_to_ignite[0]

	# 开始施法
	_is_casting = true
	_cast_progress = 0.0
	_real_cast_time = 0.0

	# 播放fire动画
	if player.has_method("play_fire_animation"):
		player.play_fire_animation()

	# 显示进度条，传递技能对象
	if _current_tree.has_method("start_igniting_with_duration"):
		_current_tree.start_igniting_with_duration(cast_time, self)
	elif _current_tree.has_method("start_igniting"):
		_current_tree.start_igniting(self)

	return true

# 取消施法
func cancel() -> void:
	# 停止施法
	_is_casting = false
	_cast_progress = 0.0
	_real_cast_time = 0.0

	# 停止fire动画
	var player = owner
	if player and player.has_method("stop_fire_animation"):
		player.stop_fire_animation()

	# 停止树木的点火进度条
	if _current_tree:
		if _current_tree.has_method("update_ignite_time"):
			# 重置树木的点火时间
			_current_tree.update_ignite_time(0.0)

		if _current_tree.has_method("stop_igniting"):
			_current_tree.stop_igniting()

	_current_tree = null
	_trees_to_ignite.clear()

# 更新方法，处理施法进度和冷却
func update(delta: float) -> void:
	# 首先调用父类的update方法处理冷却
	super.update(delta)

	# 如果不在施法中，直接返回
	if not _is_casting:
		return

	# 获取玩家引用
	var player = owner
	if not player:
		cancel()
		return

	# 检查玩家是否仍然按住技能键
	# 如果玩家松开按键，则取消施法
	if not (Input.is_action_pressed("skill_1") or Input.is_action_pressed("skill_2")):
		cancel()
		return

	# 检查玩家是否移动，如果移动则取消施法
	if player.has_method("is_moving") and player.is_moving():
		cancel()
		return

	# 更新实际经过的时间
	_real_cast_time += delta

	# 更新施法进度
	_cast_progress = _real_cast_time / cast_time

	# 更新树木的点火进度
	if _current_tree and _current_tree.has_method("update_ignite_time"):
		_current_tree.update_ignite_time(_real_cast_time)

	# 如果施法完成
	if _real_cast_time >= cast_time:
		# 完成施法
		complete_cast()

		# 停止施法状态
		_is_casting = false
		_cast_progress = 0.0
		_real_cast_time = 0.0

		# 进入冷却状态
		start_cooldown()

# 完成施法，点燃全图树木
func complete_cast() -> void:
	# 停止点火动画
	var player = owner
	if player and player.has_method("stop_fire_animation"):
		player.stop_fire_animation()

	# 停止树木的点火进度条
	if _current_tree:
		if _current_tree.has_method("stop_igniting"):
			_current_tree.stop_igniting()

	# 点燃所有树木
	for tree in _trees_to_ignite:
		if tree and is_instance_valid(tree) and tree.has_method("start_burning"):
			tree.start_burning()

			# 创建火焰特效
			create_flame_effect(tree)

	# 创建全局火焰特效
	create_global_flame_effect()

	# 清空树木列表
	_current_tree = null
	_trees_to_ignite.clear()

# 创建火焰特效
func create_flame_effect(tree: Node3D) -> void:
	# 创建一个简单的粒子效果
	var particles = GPUParticles3D.new()
	tree.add_child(particles)

	# 设置粒子属性
	var material = ParticleProcessMaterial.new()
	material.emission_shape = ParticleProcessMaterial.EMISSION_SHAPE_SPHERE
	material.emission_sphere_radius = 0.5
	material.direction = Vector3(0, 1, 0)
	material.spread = 45.0
	material.gravity = Vector3(0, -1, 0)
	material.initial_velocity_min = 2.0
	material.initial_velocity_max = 5.0
	material.scale_min = 0.1
	material.scale_max = 0.3
	material.color = Color(1.0, 0.5, 0.0, 1.0)  # 橙色火焰
	particles.process_material = material

	# 设置粒子网格
	var mesh = SphereMesh.new()
	mesh.radius = 0.05
	mesh.height = 0.1
	particles.draw_pass_1 = mesh

	# 设置粒子位置
	particles.position = Vector3(0, 1, 0)  # 在树的顶部

	# 启动粒子效果
	particles.emitting = true
	particles.one_shot = true
	particles.amount = 30

	# 设置自动销毁
	var timer = Timer.new()
	particles.add_child(timer)
	timer.wait_time = 2.0  # 2秒后销毁粒子效果
	timer.one_shot = true
	timer.timeout.connect(func(): particles.queue_free())
	timer.start()

# 创建全局火焰特效
func create_global_flame_effect() -> void:
	# 获取场景树
	var player = owner
	if not player:
		return

	var scene_tree = player.get_tree()
	if not scene_tree:
		return

	# 创建一个全屏粒子效果
	var particles = GPUParticles3D.new()
	scene_tree.root.add_child(particles)

	# 设置粒子属性
	var material = ParticleProcessMaterial.new()
	material.emission_shape = ParticleProcessMaterial.EMISSION_SHAPE_BOX
	material.emission_box_extents = Vector3(50, 10, 50)  # 覆盖整个场景
	material.direction = Vector3(0, 1, 0)
	material.spread = 180.0
	material.gravity = Vector3(0, -0.5, 0)
	material.initial_velocity_min = 1.0
	material.initial_velocity_max = 3.0
	material.scale_min = 0.2
	material.scale_max = 0.5
	material.color = Color(1.0, 0.3, 0.0, 0.7)  # 橙红色火焰
	particles.process_material = material

	# 设置粒子网格
	var mesh = SphereMesh.new()
	mesh.radius = 0.1
	mesh.height = 0.2
	particles.draw_pass_1 = mesh

	# 设置粒子位置（在场景中心上方）
	particles.position = Vector3(0, 5, 0)

	# 启动粒子效果
	particles.emitting = true
	particles.one_shot = true
	particles.amount = 200

	# 设置自动销毁
	var timer = Timer.new()
	particles.add_child(timer)
	timer.wait_time = 5.0  # 5秒后销毁粒子效果
	timer.one_shot = true
	timer.timeout.connect(func(): particles.queue_free())
	timer.start()
