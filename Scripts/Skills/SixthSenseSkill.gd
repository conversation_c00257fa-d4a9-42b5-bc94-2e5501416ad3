extends "res://Scripts/Skills/Skill.gd"
class_name SixthSenseSkill

# 技能特有属性
@export var duration: float = 3.0  # 技能持续时间（秒）

# 内部状态
var _is_active: bool = false  # 技能是否激活
var _affected_enemies: Array = []  # 受影响的敌人
var _original_states: Dictionary = {}  # 存储敌人的原始状态
var _timer: Timer = null  # 计时器

# 初始化技能属性
func _init() -> void:
	skill_name = "SIXTH SENSE"
	cooldown_time = 5.0  # 冷却时间5秒
	skill_description = "使用意念感受敌人的位置，在小地图中现实，持续3秒"
	is_teleport_skill = false

# 尝试激活技能
func try_activate(skill_owner = null) -> bool:
	# 如果提供了skill_owner参数，更新owner
	if skill_owner != null:
		owner = skill_owner

	# 检查冷却状态
	if is_on_cooldown:
		return false

	# 激活技能
	var success = activate()

	return success

# 激活技能
func activate() -> bool:
	# 获取玩家引用
	var player = owner
	if not player:
		return false

	# 获取所有敌人
	var enemies = player.get_tree().get_nodes_in_group("enemies")
	if enemies.is_empty():
		return false

	# 保存受影响的敌人
	_affected_enemies.clear()

	# 遍历所有敌人，修改其可见性状态
	for enemy in enemies:
		if is_instance_valid(enemy):
			_affected_enemies.append(enemy)

			# 使用GameManager设置效果状态
			if Engine.has_singleton("GameManager"):
				var game_manager = Engine.get_singleton("GameManager")
				if game_manager.has_method("add_status_effect"):
					# 保存当前状态
					var current_fog_state = enemy.get_fog_state() if enemy.has_method("get_fog_state") else "unexplored"
					var data = {
						"original_fog_state": current_fog_state,
						"visible": enemy.visible if "visible" in enemy else false
					}
					game_manager.add_status_effect(enemy, "sixth_sense", data)

			# 设置敌人为可见状态
			if enemy.has_method("set_fog_state"):
				enemy.set_fog_state(2)  # 2对应FogState.VISIBLE
			else:
				enemy.visible = true

	# 创建技能效果
	create_visual_effect(player)

	# 创建计时器
	if not _timer:
		_timer = Timer.new()
		_timer.one_shot = true
		_timer.timeout.connect(_on_timer_timeout)
		player.add_child(_timer)

	# 启动计时器
	_timer.wait_time = duration
	_timer.start()

	# 设置技能为激活状态
	_is_active = true

	# 进入冷却状态
	start_cooldown()

	return true

# 计时器超时回调
func _on_timer_timeout() -> void:
	# 恢复敌人的原始状态
	restore_enemy_states()

	# 设置技能为非激活状态
	_is_active = false

# 恢复敌人的原始状态
func restore_enemy_states() -> void:
	# 获取GameManager引用
	var game_manager = null
	if Engine.has_singleton("GameManager"):
		game_manager = Engine.get_singleton("GameManager")

	for enemy in _affected_enemies:
		if is_instance_valid(enemy):
			if game_manager and game_manager.has_method("get_status_effect_data"):
				var effect_data = game_manager.get_status_effect_data(enemy, "sixth_sense")
				if effect_data.has("original_fog_state"):
					# 恢复原始迷雾状态
					if enemy.has_method("set_fog_state"):
						match effect_data.original_fog_state:
							"unexplored": enemy.set_fog_state(0)
							"explored": enemy.set_fog_state(1)
							"visible": enemy.set_fog_state(2)
							_: enemy.set_fog_state(0)

			# 移除状态效果
			if game_manager and game_manager.has_method("remove_status_effect"):
				game_manager.remove_status_effect(enemy, "sixth_sense")

	# 清空数组和字典
	_affected_enemies.clear()
	_original_states.clear()

# 创建视觉效果
func create_visual_effect(player: Node3D) -> void:
	# 创建一个简单的粒子效果
	var particles = GPUParticles3D.new()
	player.add_child(particles)

	# 设置粒子属性
	var material = ParticleProcessMaterial.new()
	material.emission_shape = ParticleProcessMaterial.EMISSION_SHAPE_SPHERE
	material.emission_sphere_radius = 1.0
	material.direction = Vector3(0, 1, 0)
	material.spread = 180.0
	material.gravity = Vector3(0, 0.5, 0)
	material.initial_velocity_min = 1.0
	material.initial_velocity_max = 2.0
	material.scale_min = 0.1
	material.scale_max = 0.2
	material.color = Color(0.5, 0.5, 1.0, 0.7)  # 蓝色粒子
	particles.process_material = material

	# 设置粒子网格
	var mesh = SphereMesh.new()
	mesh.radius = 0.05
	mesh.height = 0.1
	particles.draw_pass_1 = mesh

	# 设置粒子位置
	particles.position = Vector3(0, 1.0, 0)  # 在玩家头部上方

	# 启动粒子效果
	particles.emitting = true
	particles.one_shot = true
	particles.amount = 50

	# 设置自动销毁
	var timer = Timer.new()
	particles.add_child(timer)
	timer.wait_time = duration  # 与技能持续时间相同
	timer.one_shot = true
	timer.timeout.connect(func(): particles.queue_free())
	timer.start()



# 更新方法，处理冷却
func update(delta: float) -> void:
	# 调用父类的update方法处理冷却
	super.update(delta)

	# 如果技能处于激活状态，但计时器已经停止，强制恢复敌人状态
	if _is_active and _timer and not _timer.is_stopped():
		# 技能仍在生效中
		pass
	elif _is_active:
		# 计时器已停止但技能仍标记为激活，强制恢复
		restore_enemy_states()
		_is_active = false
