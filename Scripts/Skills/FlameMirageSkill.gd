extends "res://Scripts/Skills/Skill.gd"
class_name FlameMirageSkill

# 技能特有属性
@export var duration: float = 10.0  # 分身持续时间（秒）

# 内部状态
var _current_mirage: Node3D = null  # 当前分身实例
var _timer: Timer = null  # 计时器

# 初始化技能属性
func _init() -> void:
	skill_name = "FLAME MIRAGE"
	cooldown_time = 20.0  # 冷却时间20秒
	skill_description = "召唤出一个和player一样大的分身，操作以及fire技能同player一样，速度等于player的速度+1，被enemy抓捕或者10秒后，分身消失"
	is_teleport_skill = false
	skill_category = "fire"  # 设置技能类别为"fire"

# 激活技能
func activate() -> bool:
	# 获取玩家引用
	var player = owner
	if not player:
		return false

	# 如果已有分身，先销毁
	if _current_mirage and is_instance_valid(_current_mirage):
		_current_mirage.queue_free()
		_current_mirage = null

	# 创建分身
	_current_mirage = create_mirage(player)
	if not _current_mirage:
		return false

	# 创建计时器
	if not _timer:
		_timer = Timer.new()
		_timer.one_shot = true
		_timer.timeout.connect(_on_timer_timeout)
		player.add_child(_timer)

	# 启动计时器
	_timer.wait_time = duration
	_timer.start()

	# 进入冷却状态
	start_cooldown()

	return true

# 创建分身 - 重构版本，避免依赖于CharacterBase的@onready变量
func create_mirage(player: Node3D) -> Node3D:
	# 创建分身节点
	var mirage = CharacterBody3D.new()
	mirage.set_script(load("res://Scripts/MirageClone.gd"))
	mirage.name = "MirageClone"

	# 注意：碰撞层和掩码将在MirageClone的initialize方法中从玩家复制
	# 这里不再设置

	# 添加碰撞形状
	var collision_shape = CollisionShape3D.new()
	var capsule_shape = CapsuleShape3D.new()
	capsule_shape.radius = 0.4
	capsule_shape.height = 2.2
	collision_shape.shape = capsule_shape
	collision_shape.transform.origin.y = 1.1  # 与玩家碰撞形状位置一致
	mirage.add_child(collision_shape)

	# 添加必要的子节点，但不使用unique_name_in_owner
	# 这些节点只是为了结构完整性，MirageClone不会实际使用它们
	var camera_pivot = Node3D.new()
	camera_pivot.name = "CameraPivot"
	mirage.add_child(camera_pivot)

	# 注意：不在这里创建交互器，而是在MirageClone的initialize_interaction方法中创建
	# 这样可以确保交互器的信号正确连接到MirageClone的方法
	# 在MirageClone的_ready方法中会调用initialize_interaction

	# 不再需要添加交互器碰撞形状，因为交互器在MirageClone中创建
	# 这样可以确保交互器的信号正确连接到MirageClone的方法

	# 添加粒子效果节点（空的，只为了结构完整）
	var dust_particles = GPUParticles3D.new()
	dust_particles.name = "DustParticles"
	dust_particles.emitting = false
	mirage.add_child(dust_particles)

	# 添加音效节点（空的，只为了结构完整）
	var landing_sound = AudioStreamPlayer3D.new()
	landing_sound.name = "LandingSound"
	landing_sound.volume_db = -20  # 设置很低的音量，实际上不会播放
	mirage.add_child(landing_sound)

	# 复制玩家模型
	var player_model = player.get_node_or_null("Player01")
	if player_model and is_instance_valid(player_model):
		# 使用PackedScene来正确复制模型
		var model_scene = PackedScene.new()
		var error = model_scene.pack(player_model)

		if error == OK:
			var mirage_model = model_scene.instantiate()
			mirage_model.name = "Player01"
			mirage.add_child(mirage_model)
		else:
			# 如果无法打包，使用简单的可见节点
			_create_placeholder_model(mirage)
	else:
		# 如果无法找到玩家模型，使用简单的可见节点
		_create_placeholder_model(mirage)

	# 添加到场景
	var current_scene = player.get_tree().current_scene
	if current_scene:
		current_scene.add_child(mirage)
	else:
		player.get_parent().add_child(mirage)

	# 设置分身位置和朝向
	# 使用安全的方法获取玩家朝向
	var player_forward = Vector3.FORWARD
	if player.has_method("get_forward_direction"):
		player_forward = player.get_forward_direction()
	elif "_last_input_direction" in player:
		var direction = player._last_input_direction
		if direction is Vector2 and direction != Vector2.ZERO:
			player_forward = Vector3(direction.x, 0, direction.y).normalized()
		elif direction is Vector3 and direction != Vector3.ZERO:
			player_forward = Vector3(direction.x, 0, direction.z).normalized()

	# 设置位置在玩家前方2米处
	mirage.global_position = player.global_position + player_forward * 2.0

	# 初始化分身
	mirage.initialize(player)

	return mirage

# 创建占位模型
func _create_placeholder_model(mirage: Node3D) -> void:
	var placeholder = MeshInstance3D.new()
	var capsule_mesh = CapsuleMesh.new()
	capsule_mesh.radius = 0.4
	capsule_mesh.height = 2.0
	placeholder.mesh = capsule_mesh
	placeholder.name = "Player01"  # 使用相同的名称，便于查找
	mirage.add_child(placeholder)

	# 设置材质
	var material = StandardMaterial3D.new()
	material.albedo_color = Color(1, 0.5, 0, 0.8)  # 橙色半透明
	placeholder.material_override = material

# 计时器超时回调
func _on_timer_timeout() -> void:
	# 销毁分身
	if _current_mirage and is_instance_valid(_current_mirage):
		_current_mirage.queue_free()
		_current_mirage = null

# 重写更新方法
func update(delta: float) -> void:
	# 调用父类的update方法处理冷却
	super.update(delta)

	# 检查分身是否仍然存在
	if _current_mirage and not is_instance_valid(_current_mirage):
		_current_mirage = null
