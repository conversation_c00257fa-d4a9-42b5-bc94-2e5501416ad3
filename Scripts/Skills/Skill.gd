extends Resource
class_name Skill

# 基本技能属性
@export var skill_name: String = "未命名技能"
@export var cooldown_time: float = 1.0  # 冷却时间（秒）
@export var icon: Texture2D  # 技能图标
@export var skill_description: String = ""  # 技能描述
@export var is_teleport_skill: bool = false  # 标记是否为瞬移类技能
@export var skill_category: String = ""  # 技能类别，如"fire"、"water"等

# 内部状态
var is_on_cooldown: bool = false
var cooldown_remaining: float = 0.0
var owner = null  # 技能的拥有者，通常是Player

# 初始化函数
func initialize(skill_owner) -> void:
	owner = skill_owner

# 尝试激活技能
func try_activate(skill_owner = null) -> bool:
	# 如果提供了skill_owner参数，更新owner
	if skill_owner != null:
		owner = skill_owner

	# 检查owner是否有效
	if owner == null:
		return false

	# 检查冷却状态
	if is_on_cooldown:
		return false

	# 执行技能激活
	var success = activate()

	if success:
		# 激活成功，开始冷却
		start_cooldown()

		# 对于瞬移类技能，确保玩家的相机和交互器同步
		if is_teleport_skill and owner and owner.has_method("ensure_camera_interactor_sync"):
			# 在下一帧延迟调用同步函数，给技能激活有时间完成
			if owner.has_method("create_timer"):
				var timer = owner.create_timer(0.05)  # 短暂延迟
				timer.timeout.connect(Callable(owner, "ensure_camera_interactor_sync"))

	return success

# 由子类重写的激活方法
func activate() -> bool:
	push_error("Skill.activate() 方法必须由子类重写！")
	return false

# 开始技能冷却
func start_cooldown() -> void:
	is_on_cooldown = true
	cooldown_remaining = cooldown_time

# 更新技能状态（每帧调用）
func update(delta: float) -> void:
	if is_on_cooldown:
		cooldown_remaining -= delta

		if cooldown_remaining <= 0:
			is_on_cooldown = false
			cooldown_remaining = 0


# 获取冷却百分比（0-1之间）
func get_cooldown_percent() -> float:
	if not is_on_cooldown or cooldown_time <= 0:
		return 0.0
	return cooldown_remaining / cooldown_time

# 新增：重置技能状态
func reset() -> void:
	is_on_cooldown = false
	cooldown_remaining = 0.0
