extends "res://Scripts/Skills/Skill.gd"
class_name MoltenVeilSkill

# 技能特有属性
@export var duration: float = 10.0  # 隐身持续时间（秒）
@export var speed_multiplier: float = 0.8  # 移动速度倍率

# 内部状态
var _is_active: bool = false  # 技能是否激活
var _timer: Timer = null  # 计时器
var _original_speed: float = 0.0  # 原始移动速度
var _original_materials: Dictionary = {}  # 原始材质
var _veil_effect: GPUParticles3D = null  # 隐身效果粒子

# 初始化技能属性
func _init() -> void:
	skill_name = "MOLTEN VEIL"
	cooldown_time = 5.0  # 冷却时间5秒
	skill_description = "使player处于隐身状态，该状态下player移动速度下降至0.8倍，但enemy无法感知player，持续10秒"
	is_teleport_skill = false
	skill_category = "stealth"  # 设置技能类别为"stealth"
	icon = load("res://Icon/SkillIcon/MoltenVeil.png")

# 激活技能
func activate() -> bool:
	# 获取玩家引用
	var player = owner
	if not player:
		return false
	
	# 如果已经处于隐身状态，刷新持续时间
	if _is_active:
		if _timer:
			_timer.wait_time = duration
			_timer.start()
		return true
	
	# 保存原始移动速度
	_original_speed = player.move_speed
	
	# 降低移动速度
	player.move_speed = _original_speed * speed_multiplier
	
	# 创建计时器
	if not _timer:
		_timer = Timer.new()
		_timer.one_shot = true
		_timer.timeout.connect(_on_timer_timeout)
		player.add_child(_timer)
	
	# 启动计时器
	_timer.wait_time = duration
	_timer.start()
	
	# 应用隐身效果
	apply_veil_effect(player)
	
	# 设置玩家的隐身状态元数据
	player.set_meta("molten_veil_active", true)
	
	# 设置状态为激活
	_is_active = true
	
	# 进入冷却状态
	start_cooldown()
	
	return true

# 应用隐身效果
func apply_veil_effect(player: Node3D) -> void:
	# 保存原始材质
	save_original_materials(player)
	
	# 应用半透明材质
	apply_transparent_materials(player)
	
	# 创建粒子效果
	create_particle_effect(player)

# 保存原始材质
func save_original_materials(player: Node3D) -> void:
	_original_materials.clear()
	
	# 查找玩家模型
	var player_model = player.get_node_or_null("Player01")
	if not player_model:
		return
	
	# 递归保存所有MeshInstance3D的材质
	save_materials_recursive(player_model)

# 递归保存材质
func save_materials_recursive(node: Node) -> void:
	if node is MeshInstance3D:
		var mesh_instance = node as MeshInstance3D
		var materials = []
		
		# 保存所有表面的材质
		for i in range(mesh_instance.get_surface_override_material_count()):
			var material = mesh_instance.get_surface_override_material(i)
			materials.append(material)
		
		_original_materials[node.get_path()] = materials
	
	# 递归处理子节点
	for child in node.get_children():
		save_materials_recursive(child)

# 应用半透明材质
func apply_transparent_materials(player: Node3D) -> void:
	# 查找玩家模型
	var player_model = player.get_node_or_null("Player01")
	if not player_model:
		return
	
	# 递归应用半透明材质
	apply_transparent_materials_recursive(player_model)

# 递归应用半透明材质
func apply_transparent_materials_recursive(node: Node) -> void:
	if node is MeshInstance3D:
		var mesh_instance = node as MeshInstance3D
		
		# 为每个表面创建半透明材质
		for i in range(mesh_instance.get_surface_override_material_count()):
			var original_material = mesh_instance.get_surface_override_material(i)
			var transparent_material = StandardMaterial3D.new()
			
			if original_material is StandardMaterial3D:
				# 复制原始材质的属性
				transparent_material.albedo_color = original_material.albedo_color
				transparent_material.albedo_texture = original_material.albedo_texture
				transparent_material.metallic = original_material.metallic
				transparent_material.roughness = original_material.roughness
			else:
				# 默认半透明材质
				transparent_material.albedo_color = Color(0.7, 0.3, 0.1, 0.5)  # 熔岩红色半透明
			
			# 设置透明度
			transparent_material.transparency = BaseMaterial3D.TRANSPARENCY_ALPHA
			transparent_material.albedo_color.a = 0.5  # 50%透明度
			
			# 添加发光效果
			transparent_material.emission_enabled = true
			transparent_material.emission = Color(0.8, 0.3, 0.1, 1.0)  # 熔岩红色发光
			transparent_material.emission_energy = 0.5
			
			# 应用材质
			mesh_instance.set_surface_override_material(i, transparent_material)
	
	# 递归处理子节点
	for child in node.get_children():
		apply_transparent_materials_recursive(child)

# 创建粒子效果
func create_particle_effect(player: Node3D) -> void:
	# 如果已经有粒子效果，先移除
	if _veil_effect and is_instance_valid(_veil_effect):
		_veil_effect.queue_free()
	
	# 创建新的粒子效果
	_veil_effect = GPUParticles3D.new()
	_veil_effect.name = "MoltenVeilEffect"
	
	# 设置粒子材质
	var particle_material = ParticleProcessMaterial.new()
	particle_material.emission_shape = ParticleProcessMaterial.EMISSION_SHAPE_SPHERE
	particle_material.emission_sphere_radius = 1.0
	particle_material.direction = Vector3(0, 1, 0)
	particle_material.spread = 45.0
	particle_material.gravity = Vector3(0, -0.5, 0)
	particle_material.initial_velocity_min = 0.2
	particle_material.initial_velocity_max = 0.5
	particle_material.scale_min = 0.05
	particle_material.scale_max = 0.1
	particle_material.color = Color(1.0, 0.4, 0.1, 0.7)  # 熔岩红色
	_veil_effect.process_material = particle_material
	
	# 设置粒子网格
	var particle_mesh = SphereMesh.new()
	particle_mesh.radius = 0.05
	particle_mesh.height = 0.1
	_veil_effect.draw_pass_1 = particle_mesh
	
	# 设置粒子属性
	_veil_effect.amount = 30
	_veil_effect.lifetime = 1.5
	_veil_effect.explosiveness = 0.0
	_veil_effect.randomness = 0.5
	_veil_effect.visibility_aabb = AABB(Vector3(-2, -2, -2), Vector3(4, 4, 4))
	_veil_effect.emitting = true
	
	# 添加到玩家
	player.add_child(_veil_effect)

# 恢复原始材质
func restore_original_materials(player: Node3D) -> void:
	# 遍历保存的原始材质
	for node_path in _original_materials.keys():
		var node = player.get_node_or_null(node_path)
		if node and node is MeshInstance3D:
			var materials = _original_materials[node_path]
			
			# 恢复所有表面的材质
			for i in range(min(node.get_surface_override_material_count(), materials.size())):
				node.set_surface_override_material(i, materials[i])
	
	# 清空保存的材质
	_original_materials.clear()

# 移除粒子效果
func remove_particle_effect() -> void:
	if _veil_effect and is_instance_valid(_veil_effect):
		_veil_effect.emitting = false
		_veil_effect.queue_free()
		_veil_effect = null

# 计时器超时回调
func _on_timer_timeout() -> void:
	deactivate()

# 停用技能
func deactivate() -> void:
	if not _is_active:
		return
	
	var player = owner
	if not player:
		return
	
	# 恢复原始移动速度
	player.move_speed = _original_speed
	
	# 恢复原始材质
	restore_original_materials(player)
	
	# 移除粒子效果
	remove_particle_effect()
	
	# 移除玩家的隐身状态元数据
	if player.has_meta("molten_veil_active"):
		player.remove_meta("molten_veil_active")
	
	# 设置状态为未激活
	_is_active = false

# 重写更新方法
func update(delta: float) -> void:
	# 调用父类的update方法处理冷却
	super.update(delta)
	
	# 如果技能已激活但玩家不存在，停用技能
	if _is_active:
		var player = owner
		if not player or not is_instance_valid(player):
			deactivate()

# 检查技能是否激活
func is_active() -> bool:
	return _is_active

# 获取剩余持续时间
func get_remaining_duration() -> float:
	if _is_active and _timer and is_instance_valid(_timer):
		return _timer.time_left
	return 0.0

# 重置技能状态
func reset() -> void:
	# 停用技能
	deactivate()
	
	# 重置冷却
	super.reset()
