extends "res://Scripts/Skills/Skill.gd"
class_name WallofSighsSkill

# 技能特有属性
@export var duration: float = 5.0  # 技能持续时间（秒）
@export var wall_distance: float = 5.0  # 墙生成的距离
@export var wall_width: float = 5.0  # 墙的宽度
@export var wall_height: float = 2.0  # 墙的高度

# 内部状态
var _is_active: bool = false  # 技能是否激活
var _wall_instance: Node3D = null  # 墙实例
var _timer: Timer = null  # 计时器
var _particles: Array[GPUParticles3D] = []  # 粒子效果数组

# 初始化技能属性
func _init() -> void:
	skill_name = "WALL OF SIGHS"
	cooldown_time = 20.0  # 冷却时间20秒
	skill_description = "召唤一面火焰墙，敌人无法通过，玩家可以穿过，持续时间5秒"
	is_teleport_skill = false
	skill_category = "fire"  # 设置技能类别为"fire"

# 尝试激活技能
func try_activate(skill_owner = null) -> bool:
	# 如果提供了skill_owner参数，更新owner
	if skill_owner != null:
		owner = skill_owner

	# 检查冷却状态
	if is_on_cooldown:
		return false

	# 激活技能
	var success = activate()

	return success

# 激活技能
func activate() -> bool:
	# 获取玩家引用
	var player = owner
	if not player:
		return false

	# 创建墙
	create_wall(player)

	# 创建计时器
	if not _timer:
		_timer = Timer.new()
		_timer.one_shot = true
		_timer.timeout.connect(_on_timer_timeout)
		player.add_child(_timer)

	# 启动计时器
	_timer.wait_time = duration
	_timer.start()

	# 设置技能为激活状态
	_is_active = true

	# 进入冷却状态
	start_cooldown()

	return true

# 计时器超时回调
func _on_timer_timeout() -> void:
	# 移除墙
	remove_wall()

	# 设置技能为非激活状态
	_is_active = false

# 创建墙
func create_wall(player: Node3D) -> void:
	# 获取场景树
	var scene_tree = player.get_tree()
	if not scene_tree:
		return

	# 加载预制墙体场景
	var wall_scene = load("res://Scenes/WallOfSighsWall.tscn")
	if not wall_scene:
		push_error("无法加载墙体预制场景")
		return

	# 实例化墙体场景
	_wall_instance = wall_scene.instantiate()
	_wall_instance.name = "WallOfSighs"

	# 计算墙的位置和旋转
	# 使用player脚本中的_last_input_direction获取玩家的真实朝向
	var player_forward = Vector3.ZERO

	# 检查玩家是否有_last_input_direction变量
	if "_last_input_direction" in player:
		# 将Vector2转换为Vector3，因为在CharacterBase.gd中_last_input_direction是Vector2类型
		var direction_2d = player._last_input_direction
		player_forward = Vector3(direction_2d.x, 0, direction_2d.y).normalized()
	else:
		# 如果没有，则使用默认方向
		player_forward = -player.global_transform.basis.z.normalized()

	# 计算墙的位置，在玩家前方wall_distance距离处
	var wall_position = player.global_position + player_forward * wall_distance
	wall_position.y = 0  # 确保墙在地面上

	# 添加到场景，先添加再设置位置
	scene_tree.root.add_child(_wall_instance)

	# 重置墙体的变换，确保没有继承预制场景的变换
	_wall_instance.transform = Transform3D.IDENTITY

	# 设置墙的位置（全局坐标）
	_wall_instance.global_position = wall_position

	# 计算旋转角度，使墙体与玩家前进方向平行
	var wall_angle = atan2(player_forward.x, player_forward.z)
	_wall_instance.global_rotation = Vector3(0, wall_angle, 0)

	# 添加火焰粒子效果
	create_fire_particles()

# 创建火焰粒子效果
func create_fire_particles() -> void:
	if not _wall_instance:
		return

	# 在墙的顶部创建火焰粒子
	var top_particles = GPUParticles3D.new()
	top_particles.name = "TopFireParticles"
	_wall_instance.add_child(top_particles)

	# 设置粒子位置在墙的顶部
	top_particles.position = Vector3(0, 2.0, 0)  # 使用固定高度，与预制场景匹配

	# 设置粒子属性
	var material = ParticleProcessMaterial.new()
	material.emission_shape = ParticleProcessMaterial.EMISSION_SHAPE_BOX
	material.emission_box_extents = Vector3(wall_width / 2, 0.1, 0.2)
	material.direction = Vector3(0, 1, 0)
	material.spread = 15.0
	material.gravity = Vector3(0, 0.5, 0)
	material.initial_velocity_min = 1.0
	material.initial_velocity_max = 2.0
	material.scale_min = 0.1
	material.scale_max = 0.3
	material.color = Color(1.0, 0.3, 0.0, 0.7)  # 红色火焰
	top_particles.process_material = material

	# 设置粒子网格
	var mesh = SphereMesh.new()
	mesh.radius = 0.05
	mesh.height = 0.1
	top_particles.draw_pass_1 = mesh

	# 启动粒子效果
	top_particles.emitting = true
	top_particles.amount = 100
	top_particles.lifetime = 1.0

	# 将粒子添加到数组中，以便后续清理
	_particles.append(top_particles)

# 移除墙
func remove_wall() -> void:
	if _wall_instance and is_instance_valid(_wall_instance):
		# 停止粒子效果
		for particle in _particles:
			if is_instance_valid(particle):
				particle.emitting = false

		# 先禁用碰撞体，避免淡出过程中的物理异常
		var collision_body = _wall_instance.get_node_or_null("WallCollision")
		if collision_body and collision_body is StaticBody3D:
			# 禁用碰撞
			collision_body.collision_layer = 0
			collision_body.collision_mask = 0

		# 使用淡出效果移除墙
		var tween = _wall_instance.create_tween()
		# 使用透明度淡出而不是缩放，避免矩阵奇异性问题
		# 首先获取所有网格实例
		var meshes = []
		find_all_mesh_instances(_wall_instance, meshes)

		# 对每个网格实例设置淡出效果
		for mesh in meshes:
			if mesh.material_override == null:
				# 创建新材质
				var material = StandardMaterial3D.new()
				material.transparency = BaseMaterial3D.TRANSPARENCY_ALPHA
				mesh.material_override = material

				# 添加透明度动画
				tween.parallel().tween_property(material, "albedo_color:a", 0.0, 0.5)

		# 使用tween的完成回调来移除墙体
		tween.tween_callback(func():
			if is_instance_valid(_wall_instance):
				_wall_instance.queue_free()
		).set_delay(0.6)

		# 清空粒子数组
		_particles.clear()

# 递归查找MeshInstance3D节点
func find_all_mesh_instances(node: Node, result: Array) -> void:
	if node is MeshInstance3D:
		result.append(node)

	for child in node.get_children():
		find_all_mesh_instances(child, result)

# 更新方法，处理冷却
func update(delta: float) -> void:
	# 调用父类的update方法处理冷却
	super.update(delta)

	# 如果技能处于激活状态，但计时器已经停止，强制移除墙
	if _is_active and _timer and not _timer.is_stopped():
		# 技能仍在生效中
		pass
	elif _is_active:
		# 计时器已停止但技能仍标记为激活，强制移除
		remove_wall()
		# 立即设置为非激活状态
		_is_active = false
