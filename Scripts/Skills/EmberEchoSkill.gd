extends "res://Scripts/Skills/Skill.gd"
class_name EmberEchoSkill

# EMBER ECHO技能专有属性
@export var cast_time: float = 5.0  # 施法时间（秒）
@export var ignite_radius: float = 15.0  # 点燃范围（米）

# 内部状态
var _is_casting: bool = false  # 是否正在施法
var _cast_progress: float = 0.0  # 施法进度（0-1）
var _current_tree = null  # 当前目标树木
var _real_cast_time: float = 0.0  # 实际施法时间

func _init() -> void:
	skill_name = "EMBER ECHO"
	cooldown_time = 5.0  # 冷却时间5秒
	skill_description = "点燃范围小幅扩大，点燃高亮树木周围15米内的多棵树"
	is_teleport_skill = false
	skill_category = "fire"  # 设置技能类别为“fire”

# 实现激活方法
func activate() -> bool:
	# 获取玩家引用
	var player = owner
	if not player:
		return false

	# 获取玩家的交互器（现在直接在Player下）
	var interactor = player.get_node_or_null("PlayerInteractor")
	if not interactor:
		return false

	# 获取当前交互的树木
	# 使用玩家的_current_tree变量，这个变量由交互器设置
	_current_tree = player._current_tree
	if not _current_tree or not _current_tree.is_in_group("trees"):
		print("\n\n[EmberEchoSkill] 没有可以点火的树木\n\n")
		return false

	# 开始施法过程
	_is_casting = true
	_cast_progress = 0.0
	_real_cast_time = 0.0

	# 开始点火动画
	if player.has_method("play_fire_animation"):
		player.play_fire_animation()

	# 通知树木开始被点燃，传递技能对象
	if _current_tree.has_method("start_igniting_with_duration"):
		_current_tree.start_igniting_with_duration(cast_time, self)
	elif _current_tree.has_method("start_igniting"):
		_current_tree.start_igniting(self)

	return true

# 取消施法
func cancel() -> void:
	if not _is_casting:
		return

	_is_casting = false
	_cast_progress = 0.0

	# 停止点火动画
	var player = owner
	if player and player.has_method("stop_fire_animation"):
		player.stop_fire_animation()

	# 通知树木停止点火
	if _current_tree:
		if _current_tree.has_method("update_ignite_time"):
			# 重置树木的点火时间
			_current_tree.update_ignite_time(0.0)

		if _current_tree.has_method("stop_igniting"):
			_current_tree.stop_igniting()

	_current_tree = null

# 重写更新方法，处理施法进度
func update(delta: float) -> void:
	# 首先调用父类的update方法处理冷却
	super.update(delta)

	# 如果不在施法中，直接返回
	if not _is_casting:
		return

	# 检查目标树木是否仍然有效
	if not _current_tree or not is_instance_valid(_current_tree):
		cancel()
		return

	# 获取玩家引用
	var player = owner
	if not player:
		cancel()
		return

	# 检查玩家是否仍然按住技能键
	# 如果玩家松开按键，则取消施法
	if not (Input.is_action_pressed("skill_1") or Input.is_action_pressed("skill_2")):
		cancel()
		return

	# 检查玩家是否移动，如果移动则取消施法
	if player.is_moving():
		cancel()
		return

	# 更新实际经过的时间
	_real_cast_time += delta

	# 直接使用实际经过的时间来计算施法进度
	_cast_progress = _real_cast_time / cast_time

	# 直接更新树木的点火进度
	if _current_tree and _current_tree.has_method("update_ignite_time"):
		_current_tree.update_ignite_time(_real_cast_time)

	# 如果实际经过的时间大于等于施法时间，则施法完成
	if _real_cast_time >= cast_time:
		_is_casting = false
		_cast_progress = 0.0

		# 点燃范围内的所有树木
		ignite_trees_in_radius()

		# 停止点火动画
		if player.has_method("stop_fire_animation"):
			player.stop_fire_animation()

		_current_tree = null

		# 开始冷却
		start_cooldown()

# 点燃范围内的所有树木
func ignite_trees_in_radius() -> void:
	if not _current_tree or not is_instance_valid(_current_tree):
		return

	# 获取场景中所有树木
	var scene_tree = _current_tree.get_tree()
	if not scene_tree:
		return

	var trees = scene_tree.get_nodes_in_group("trees")
	if trees.is_empty():
		return

	# 获取目标树木的位置
	var target_position = _current_tree.global_position

	# 创建一个范围指示效果
	create_radius_indicator(target_position, ignite_radius)

	# 首先点燃圆心的树
	if _current_tree.has_method("start_burning"):
		_current_tree.start_burning()

		# 创建火焰特效
		create_flame_effect(_current_tree)

	# 点燃范围内的所有树木
	for tree in trees:
		if tree and is_instance_valid(tree) and tree != _current_tree and tree.has_method("can_interact") and tree.can_interact():
			# 计算树木与目标树木的距离
			var distance = target_position.distance_to(tree.global_position)

			# 如果在范围内，则点燃
			if distance <= ignite_radius:
				if tree.has_method("start_burning"):
					tree.start_burning()

					# 创建火焰特效
					create_flame_effect(tree)

# 创建范围指示效果
func create_radius_indicator(position: Vector3, radius: float) -> void:
	# 获取场景树
	var scene_tree = _current_tree.get_tree()
	if not scene_tree:
		return

	# 创建一个MeshInstance3D节点
	var indicator = MeshInstance3D.new()
	scene_tree.root.add_child(indicator)

	# 创建一个圆环网格
	var mesh = CylinderMesh.new()
	mesh.top_radius = radius
	mesh.bottom_radius = radius
	mesh.height = 0.1
	indicator.mesh = mesh

	# 设置位置
	indicator.global_position = position + Vector3(0, 0.05, 0)  # 稍微抬高一点，避免与地面重叠

	# 创建一个半透明的材质
	var material = StandardMaterial3D.new()
	material.albedo_color = Color(1.0, 0.5, 0.0, 0.3)  # 橙色，半透明
	material.emission_enabled = true
	material.emission = Color(1.0, 0.5, 0.0, 0.5)
	material.emission_energy = 1.0
	material.transparency = BaseMaterial3D.TRANSPARENCY_ALPHA
	indicator.material_override = material

	# 设置自动销毁
	var timer = Timer.new()
	indicator.add_child(timer)
	timer.wait_time = 2.0  # 2秒后销毁
	timer.one_shot = true
	timer.timeout.connect(func(): indicator.queue_free())
	timer.start()

# 创建火焰特效
func create_flame_effect(tree: Node3D) -> void:
	# 创建一个简单的粒子效果
	var particles = GPUParticles3D.new()
	tree.add_child(particles)

	# 设置粒子属性
	var material = ParticleProcessMaterial.new()
	material.emission_shape = ParticleProcessMaterial.EMISSION_SHAPE_SPHERE
	material.emission_sphere_radius = 0.5
	material.direction = Vector3(0, 1, 0)
	material.spread = 45.0
	material.gravity = Vector3(0, -1, 0)
	material.initial_velocity_min = 2.0
	material.initial_velocity_max = 5.0
	material.scale_min = 0.1
	material.scale_max = 0.3
	material.color = Color(1.0, 0.5, 0.0, 1.0)  # 橙色火焰
	particles.process_material = material

	# 设置粒子网格
	var mesh = SphereMesh.new()
	mesh.radius = 0.05
	mesh.height = 0.1
	particles.draw_pass_1 = mesh

	# 设置粒子位置
	particles.position = Vector3(0, 1, 0)  # 在树的顶部

	# 启动粒子效果
	particles.emitting = true
	particles.one_shot = true
	particles.amount = 30

	# 设置自动销毁
	var timer = Timer.new()
	particles.add_child(timer)
	timer.wait_time = 2.0  # 2秒后销毁粒子效果
	timer.one_shot = true
	timer.timeout.connect(func(): particles.queue_free())
	timer.start()
