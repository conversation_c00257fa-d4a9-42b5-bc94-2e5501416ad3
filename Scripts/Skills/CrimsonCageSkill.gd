extends "res://Scripts/Skills/Skill.gd"
class_name CrimsonCageSkill

# 技能特有属性
@export var duration: float = 3.0  # 技能持续时间（秒）
@export var detection_radius: float = 10.0  # 检测敌人的半径（米）

# 内部状态
var _is_active: bool = false  # 技能是否激活
var _affected_enemy = null  # 受影响的敌人
var _timer: Timer = null  # 计时器
var _particles: GPUParticles3D = null  # 粒子效果

# 初始化技能属性
func _init() -> void:
	skill_name = "CRIMSON CAGE"
	cooldown_time = 5.0  # 冷却时间5秒
	skill_description = "召唤火焰将敌人包围，自动选择10米范围内最近的敌人，将敌人短暂困在原地，持续5秒"
	is_teleport_skill = false
	# 不设置技能类别，保持默认的空字符串

# 尝试激活技能
func try_activate(skill_owner = null) -> bool:
	# 如果提供了skill_owner参数，更新owner
	if skill_owner != null:
		owner = skill_owner

	# 检查冷却状态
	if is_on_cooldown:
		return false

	# 激活技能
	var success = activate()

	return success

# 激活技能
func activate() -> bool:
	# 获取玩家引用
	var player = owner
	if not player:
		return false

	# 自动选择范围内最近的敌人
	var nearest_enemy = find_nearest_enemy(player)

	if not nearest_enemy:
		print("范围内没有敌人，无法使用CRIMSON CAGE技能")
		return false

	# 保存受影响的敌人
	_affected_enemy = nearest_enemy

	# 禁用敌人移动
	disable_enemy_movement(_affected_enemy)

	# 创建火焰粒子效果
	create_fire_particles(_affected_enemy)

	# 创建计时器
	if not _timer:
		_timer = Timer.new()
		_timer.one_shot = true
		_timer.timeout.connect(_on_timer_timeout)
		player.add_child(_timer)

	# 启动计时器
	_timer.wait_time = duration
	_timer.start()

	# 设置技能为激活状态
	_is_active = true

	# 打印调试信息
	print("CRIMSON CAGE技能已激活，目标：", _affected_enemy.name, "，持续时间：", duration, "秒")

	# 进入冷却状态
	start_cooldown()

	return true

# 查找范围内最近的敌人
func find_nearest_enemy(player: Node3D) -> Node3D:
	# 获取所有敌人
	var enemies = player.get_tree().get_nodes_in_group("enemies")
	if enemies.is_empty():
		return null

	# 存储范围内的敌人
	var enemies_in_range = []

	# 检查每个敌人是否在范围内
	for enemy in enemies:
		var distance = player.global_position.distance_to(enemy.global_position)
		if distance <= detection_radius:
			enemies_in_range.append({"enemy": enemy, "distance": distance})

	# 如果没有范围内的敌人，返回null
	if enemies_in_range.is_empty():
		return null

	# 按距离排序
	enemies_in_range.sort_custom(func(a, b): return a["distance"] < b["distance"])

	# 获取最小距离
	var min_distance = enemies_in_range[0]["distance"]

	# 找出所有具有最小距离的敌人
	var closest_enemies = []
	for enemy_data in enemies_in_range:
		if enemy_data["distance"] <= min_distance + 0.1: # 添加一点容差
			closest_enemies.append(enemy_data["enemy"])

	# 如果有多个最近的敌人，随机选择一个
	if closest_enemies.size() > 1:
		return closest_enemies[randi() % closest_enemies.size()]
	else:
		return closest_enemies[0]

# 计时器超时回调
func _on_timer_timeout() -> void:
	# 恢复敌人的移动能力
	restore_enemy_movement()

	# 移除粒子效果
	remove_fire_particles()

	# 设置技能为非激活状态
	_is_active = false

	# 打印调试信息
	print("CRIMSON CAGE技能效果已结束")

# 禁用敌人移动
func disable_enemy_movement(enemy) -> void:
	if not is_instance_valid(enemy):
		return

	# 使用GameManager保存状态
	if Engine.has_singleton("GameManager"):
		var game_manager = Engine.get_singleton("GameManager")
		if game_manager.has_method("add_status_effect"):
			# 保存当前状态
			var data = {
				"previous_state": enemy.current_state if "current_state" in enemy else null,
				"previous_velocity": enemy.velocity if "velocity" in enemy else Vector3.ZERO
			}
			game_manager.add_status_effect(enemy, "crimson_cage", data)

	# 禁用敌人移动
	if "velocity" in enemy:
		enemy.velocity = Vector3.ZERO

	# 如果敌人有特定的状态枚举，设置为IDLE状态
	if "current_state" in enemy and "State" in enemy:
		if enemy.State.has("IDLE"):
			enemy.current_state = enemy.State.IDLE

	print("敌人已被CRIMSON CAGE禁用移动")

# 恢复敌人的移动能力
func restore_enemy_movement() -> void:
	if not is_instance_valid(_affected_enemy):
		return

	if Engine.has_singleton("GameManager"):
		var game_manager = Engine.get_singleton("GameManager")
		if game_manager.has_method("get_status_effect_data"):
			var effect_data = game_manager.get_status_effect_data(_affected_enemy, "crimson_cage")

			# 恢复之前的状态
			if effect_data.has("previous_state") and "current_state" in _affected_enemy:
				_affected_enemy.current_state = effect_data.previous_state

			# 移除状态效果
			if game_manager.has_method("remove_status_effect"):
				game_manager.remove_status_effect(_affected_enemy, "crimson_cage")

	print("敌人的移动能力已恢复")

# 创建火焰粒子效果
func create_fire_particles(enemy) -> void:
	if not is_instance_valid(enemy):
		return

	# 创建粒子效果
	_particles = GPUParticles3D.new()
	enemy.add_child(_particles)

	# 设置粒子属性
	var material = ParticleProcessMaterial.new()
	material.emission_shape = ParticleProcessMaterial.EMISSION_SHAPE_SPHERE
	material.emission_sphere_radius = 1.0
	material.direction = Vector3(0, 1, 0)
	material.spread = 180.0
	material.gravity = Vector3(0, 0.5, 0)
	material.initial_velocity_min = 1.0
	material.initial_velocity_max = 2.0
	material.scale_min = 0.1
	material.scale_max = 0.2
	material.color = Color(1.0, 0.3, 0.0, 0.7)  # 红色火焰
	_particles.process_material = material

	# 设置粒子网格
	var mesh = SphereMesh.new()
	mesh.radius = 0.05
	mesh.height = 0.1
	_particles.draw_pass_1 = mesh

	# 设置粒子位置
	_particles.position = Vector3(0, 1.0, 0)  # 在敌人头部上方

	# 启动粒子效果
	_particles.emitting = true
	_particles.amount = 50

	print("火焰粒子效果已创建")

# 移除粒子效果
func remove_fire_particles() -> void:
	if _particles and is_instance_valid(_particles):
		_particles.emitting = false
		_particles.queue_free()
		_particles = null

	print("火焰粒子效果已移除")

# 更新方法，处理冷却
func update(delta: float) -> void:
	# 调用父类的update方法处理冷却
	super.update(delta)

	# 如果技能处于激活状态，但计时器已经停止，强制恢复敌人状态
	if _is_active and _timer and not _timer.is_stopped():
		# 技能仍在生效中
		pass
	elif _is_active:
		# 计时器已停止但技能仍标记为激活，强制恢复
		restore_enemy_movement()
		remove_fire_particles()
		_is_active = false
