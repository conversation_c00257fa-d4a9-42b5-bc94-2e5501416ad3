extends Node3D

# 交互调试器 - 用于可视化交互范围的辅助工具

@export var target_player_path: NodePath
@export var draw_interaction_area: bool = true
@export var interaction_area_color: Color = Color(0, 1, 0, 0.3)  # 半透明绿色
@export var draw_always: bool = true  # 是否总是绘制，即使在游戏运行时
@export var draw_position_lines: bool = true  # 是否绘制位置关系线
@export var position_line_color: Color = Color(1, 0, 0, 0.8)  # 红色

var player: Node3D
var interactor: Area3D
var collision_shape: CollisionShape3D
var mesh_instance: MeshInstance3D
var position_line: MeshInstance3D  # 新增：显示位置关系的线

func _ready() -> void:
	# 添加到交互调试器组
	add_to_group("interaction_debuggers")
	
	# 延迟一帧初始化，确保所有节点都已加载
	await get_tree().process_frame
	initialize()

func initialize() -> void:
	# 获取玩家节点
	if target_player_path:
		player = get_node(target_player_path)
	else:
		# 尝试在场景中查找玩家
		var players = get_tree().get_nodes_in_group("player")
		if players.size() > 0:
			player = players[0]
	
	if not player:
		push_error("InteractionDebugger: 无法找到玩家节点")
		return
	
	# 寻找交互器节点（现在直接在Player下）
	interactor = player.get_node_or_null("PlayerInteractor")

	if not interactor:
		push_error("InteractionDebugger: 无法找到交互器节点")
		return
	
	# 获取碰撞形状
	collision_shape = interactor.get_node_or_null("CollisionShape3D")
	if not collision_shape:
		push_error("InteractionDebugger: 无法找到碰撞形状")
		return
	
	# 创建可视化网格
	create_visualization_mesh()
	
	# 创建位置关系线
	create_position_line()
	
	# 每次玩家移动后更新可视化
	set_process(true)

func create_visualization_mesh() -> void:
	# 删除现有的网格实例（如果有）
	if mesh_instance and is_instance_valid(mesh_instance):
		mesh_instance.queue_free()
	
	# 获取碰撞形状信息
	var shape = collision_shape.shape
	if not shape:
		push_error("InteractionDebugger: 碰撞形状未定义")
		return
	
	# 创建新的网格实例
	mesh_instance = MeshInstance3D.new()
	add_child(mesh_instance)
	
	# 根据碰撞形状类型创建不同的网格
	if shape is BoxShape3D:
		# 创建立方体网格
		var box_mesh = BoxMesh.new()
		box_mesh.size = shape.size
		mesh_instance.mesh = box_mesh
	elif shape is SphereShape3D:
		# 创建球体网格
		var sphere_mesh = SphereMesh.new()
		sphere_mesh.radius = shape.radius
		sphere_mesh.height = shape.radius * 2
		mesh_instance.mesh = sphere_mesh
	elif shape is CapsuleShape3D:
		# 创建胶囊网格
		var capsule_mesh = CapsuleMesh.new()
		capsule_mesh.radius = shape.radius
		capsule_mesh.height = shape.height
		mesh_instance.mesh = capsule_mesh
	else:
		# 对于其他形状，使用通用的立方体
		var box_mesh = BoxMesh.new()
		box_mesh.size = Vector3(1, 1, 1)
		mesh_instance.mesh = box_mesh
		push_warning("InteractionDebugger: 未支持的碰撞形状类型，使用默认立方体")
	
	# 设置半透明材质
	var material = StandardMaterial3D.new()
	material.albedo_color = interaction_area_color
	material.transparency = BaseMaterial3D.TRANSPARENCY_ALPHA
	mesh_instance.material_override = material

# 新增：创建位置关系线
func create_position_line() -> void:
	# 删除现有的线（如果有）
	if position_line and is_instance_valid(position_line):
		position_line.queue_free()
	
	# 创建线的网格实例
	position_line = MeshInstance3D.new()
	add_child(position_line)
	
	# 创建线的材质
	var material = StandardMaterial3D.new()
	material.albedo_color = position_line_color
	material.shading_mode = BaseMaterial3D.SHADING_MODE_UNSHADED
	position_line.material_override = material

func _process(_delta: float) -> void:
	# 每帧更新可视化网格的位置
	if interactor and mesh_instance and is_instance_valid(mesh_instance):
		# 将可视化网格与交互器对齐
		mesh_instance.global_transform = interactor.global_transform
		mesh_instance.visible = draw_interaction_area
	
	# 更新位置关系线
	update_position_line()

# 新增：更新位置关系线
func update_position_line() -> void:
	if not draw_position_lines or not player or not interactor or not position_line:
		if position_line:
			position_line.visible = false
		return
	
	position_line.visible = true
	
	# 获取玩家和交互器的位置
	var player_pos = player.global_position
	var interactor_pos = interactor.global_position
	
	# 创建从玩家到交互器的线
	var immediate_mesh = ImmediateMesh.new()
	position_line.mesh = immediate_mesh
	
	immediate_mesh.clear_surfaces()
	immediate_mesh.surface_begin(Mesh.PRIMITIVE_LINES)
	
	# 添加玩家位置点（稍微偏移以便可见）
	immediate_mesh.surface_add_vertex(Vector3.ZERO)  # 玩家位置（相对于世界原点）
	immediate_mesh.surface_add_vertex(interactor_pos - player_pos)  # 交互器位置（相对于玩家）
	
	immediate_mesh.surface_end()
	
	# 将线的原点设置在玩家位置
	position_line.global_position = player_pos
	
	# 在屏幕上显示位置差异的文本信息
	var _offset = interactor_pos - player_pos


func _on_player_teleported() -> void:
	# 玩家瞬移后，确保可视化更新
	if interactor and mesh_instance and is_instance_valid(mesh_instance):
		mesh_instance.global_transform = interactor.global_transform
	
	# 显示瞬移后的位置关系
	if player and interactor:
		var player_pos = player.global_position
		var interactor_pos = interactor.global_position
		var _offset = interactor_pos - player_pos
