extends Node3D

@export var minimap_scene: PackedScene
@export var player_avatar_scene: PackedScene
@export var item_boxes_scene: PackedScene
@export var skill_box_ui_scene: PackedScene
@export var debug_interaction: bool = true  # 是否启用交互范围调试

# UIManager引用
var ui_manager: UIManager

func _ready() -> void:
	# 等待一帧确保所有节点都已准备就绪
	await get_tree().process_frame

	# 初始化UIManager
	initialize_ui_manager()

	# 动态添加UI组件（现在通过UIManager管理）
	add_ui_components()

	# 为玩家装备技能
	equip_player_skills()

	# 初始化被动技能
	initialize_passive_skills()

	# 如果启用了调试，添加交互范围可视化
	if debug_interaction:
		add_interaction_debugger()

	# 连接敌人信号
	connect_enemy_signals()

# 初始化UIManager
func initialize_ui_manager() -> void:
	ui_manager = get_node_or_null("UIManager")
	if not ui_manager:
		push_error("[Level01] 未找到UIManager节点")
		return

	print("[Level01] UIManager初始化完成")

# 添加UI组件（通过UIManager管理）
func add_ui_components() -> void:
	if not ui_manager:
		push_error("[Level01] UIManager未初始化，无法添加UI组件")
		return

	# 创建UI组件实例
	var minimap_instance = create_minimap()
	var player_avatar_instance = create_player_avatar()
	var item_boxes_instance = create_item_boxes()
	var skill_box_instance = create_skill_box_ui()

	# 注册到UIManager
	ui_manager.register_game_ui_components(
		skill_box_instance,
		item_boxes_instance,
		player_avatar_instance,
		minimap_instance
	)

# 创建小地图实例
func create_minimap() -> Control:
	if not minimap_scene:
		push_error("Level01: 未设置minimap_scene")
		return null

	var minimap_instance = minimap_scene.instantiate()
	minimap_instance.add_to_group("minimap")

	# 设置FogOfWar（延迟到下一帧）
	call_deferred("_setup_minimap_fog_of_war", minimap_instance)

	return minimap_instance

# 设置小地图的FogOfWar
func _setup_minimap_fog_of_war(minimap_instance: Control) -> void:
	# 获取FogOfWar节点
	var fog_of_war = get_node_or_null("FogOfWar")
	if not fog_of_war:
		var fog_nodes = get_tree().get_nodes_in_group("fog_of_war")
		if fog_nodes.size() > 0:
			fog_of_war = fog_nodes[0]

	if fog_of_war and minimap_instance.has_method("set_fog_of_war"):
		minimap_instance.set_fog_of_war(fog_of_war)
	else:
		print("[LEVEL01] 未找到FogOfWar节点")

# 创建玩家头像实例
func create_player_avatar() -> Control:
	if not player_avatar_scene:
		if ResourceLoader.exists("res://Scenes/PlayerAvatar.tscn"):
			player_avatar_scene = load("res://Scenes/PlayerAvatar.tscn")
		else:
			push_error("Level01: 未设置player_avatar_scene且无法自动加载")
			return null

	var avatar_instance = player_avatar_scene.instantiate()
	avatar_instance.add_to_group("avatar")
	return avatar_instance



# 创建道具框实例
func create_item_boxes() -> Control:
	if not item_boxes_scene:
		if ResourceLoader.exists("res://Scenes/ItemBoxes.tscn"):
			item_boxes_scene = load("res://Scenes/ItemBoxes.tscn")
		else:
			push_error("Level01: 未设置item_boxes_scene且无法自动加载")
			return null

	var item_boxes_instance = item_boxes_scene.instantiate()
	item_boxes_instance.add_to_group("item_boxes")
	return item_boxes_instance



# 创建技能框实例
func create_skill_box_ui() -> Control:
	if not skill_box_ui_scene:
		if ResourceLoader.exists("res://Scenes/SkillBoxUI.tscn"):
			skill_box_ui_scene = load("res://Scenes/SkillBoxUI.tscn")
		else:
			push_error("Level01: 未设置skill_box_ui_scene且无法自动加载")
			return null

	var skill_box_ui_instance = skill_box_ui_scene.instantiate()
	skill_box_ui_instance.add_to_group("skill_ui")
	return skill_box_ui_instance


func equip_player_skills() -> void:
	# 等待一帧，确保玩家已经完全初始化
	await get_tree().process_frame

	# 查找玩家节点
	var players = get_tree().get_nodes_in_group("player")
	if players.size() == 0:
		push_error("Level01: 未找到玩家节点，无法装备技能")
		return

	var player = players[0]

	# 检查玩家是否有initialize_skills方法
	if not player.has_method("initialize_skills"):
		push_error("玩家没有initialize_skills方法")
		return

	# 从技能管理器获取当前装备的技能
	var skill_manager = get_node_or_null("/root/SkillManager")
	if not skill_manager:
		push_error("SkillManager单例不存在")
		return

	# 使用玩家在技能配置界面中选择的技能
	# 技能已经在SkillManager中设置，不需要在这里确定默认值

	# 初始化玩家技能
	player.initialize_skills()

# 初始化被动技能
func initialize_passive_skills() -> void:
	# 等待一帧，确保所有节点都已准备就绪
	await get_tree().process_frame

	# 检查是否已有被动技能初始化器
	var existing_initializers = get_tree().get_nodes_in_group("passive_skill_initializers")
	if existing_initializers.size() > 0:
		return

	# 创建被动技能初始化器
	var initializer = Node.new()
	initializer.name = "PassiveSkillInitializer"
	initializer.add_to_group("passive_skill_initializers")
	initializer.set_script(load("res://Scripts/PassiveSkillInitializer.gd"))

	# 添加到场景中
	add_child(initializer)

# 新增：添加交互范围调试器
func add_interaction_debugger() -> void:
	# 检查场景中是否已有交互调试器，避免重复添加
	var existing_debuggers = get_tree().get_nodes_in_group("interaction_debuggers")
	if existing_debuggers.size() > 0:

		return

	# 尝试从路径加载调试器场景
	var debugger_scene_path = "res://Scenes/InteractionDebugger.tscn"
	if not ResourceLoader.exists(debugger_scene_path):

		return

	var debugger_scene = load(debugger_scene_path)
	var debugger_instance = debugger_scene.instantiate()

	# 将调试器添加到场景中
	add_child(debugger_instance)

# 连接敌人的信号
func connect_enemy_signals() -> void:
	var enemies = get_tree().get_nodes_in_group("enemies")
	for enemy in enemies:
		if not enemy.player_captured.is_connected(_on_enemy_captured_player):
			enemy.player_captured.connect(_on_enemy_captured_player)

# 处理玩家被捕获事件
func _on_enemy_captured_player(_player: CharacterBody3D) -> void:
	# 检查玩家血量，只有当血量为0时才显示游戏结束界面
	if _player.health <= 0:
		# 显示游戏结束界面
		show_game_over_ui()

# 显示游戏结束界面
func show_game_over_ui() -> void:
	if Engine.has_singleton("GameManager"):
		var game_manager = Engine.get_singleton("GameManager")
		game_manager.show_game_over(true)  # true 表示强制暂停游戏
		return

	# 如果没有 GameManager，尝试直接显示游戏结束UI
	var game_over_ui = get_node_or_null("GameOverUI")
	if not game_over_ui:
		game_over_ui = get_tree().root.get_node_or_null("GameOverUI")

	if game_over_ui:
		game_over_ui.show_game_over(true)
		return

	# 如果找不到游戏结束UI，尝试加载并实例化它
	var game_over_ui_path = "res://Scenes/UI/GameOverUI.tscn"
	if ResourceLoader.exists(game_over_ui_path):
		var game_over_scene = load(game_over_ui_path)
		game_over_ui = game_over_scene.instantiate()
		add_child(game_over_ui)
		game_over_ui.show_game_over(true)
