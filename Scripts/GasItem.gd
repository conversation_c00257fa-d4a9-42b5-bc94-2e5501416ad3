extends Item
class_name GasItem

# GAS道具固定属性 - 确保所有实例都使用相同的值
var flame_multiplier: float = 2.0  # 火焰效果倍数 - 用于内部逻辑
var burn_time_reduction_factor: float = 0.5  # 燃烧时间减半(固定值)
var fire_size_multiplier: float = 1.5  # 火焰大小放大倍数(固定值)
var highlight_color: Color = Color(1.0, 0.3, 0.0, 0.7)  # 高亮颜色，橙红色半透明
var interaction_distance: float = 3.0  # 交互距离

# 每个实例独立的变量
var _current_tree: Node = null  # 当前可交互的树
var _gas_highlight_material: StandardMaterial3D = null  # GAS专用高亮材质
var original_fire_scale: Vector3 = Vector3(1, 1, 1)  # 保存原始火焰大小
var original_fire_amount: float = 1.0  # 保存原始火焰数量
var original_extents: Vector3 = Vector3(5, 5, 5)  # 保存原始火焰范围
var effect_sound: AudioStream = null  # 保存效果音效
var effect_sound_player: AudioStreamPlayer3D = null  # 效果音效播放器

func _init() -> void:
	# 设置道具基本属性
	item_name = "汽油"
	item_description = "使燃烧中的树木燃烧时间缩短一半，同时火焰变大1.5倍"
	is_one_time_use = true
	cooldown_time = 0.0  # 一次性道具，无冷却时间
	item_category = "utility"

	# 创建高亮材质
	_gas_highlight_material = StandardMaterial3D.new()
	_gas_highlight_material.albedo_color = Color(0.9, 0.4, 0.0, 0.6)  # 橙色半透明
	_gas_highlight_material.emission_enabled = true
	_gas_highlight_material.emission = Color(0.9, 0.4, 0.0)
	_gas_highlight_material.emission_energy_multiplier = 0.5

	# 安全地加载音效 - 目前已注释，等待后续开发
	# var sound_path = "res://sounds/gas_effect.ogg"

# 覆盖初始化函数
func initialize(item_owner = null) -> void:
	# 调用父类的初始化方法
	super.initialize(item_owner)

# 使用道具的逻辑
func use() -> bool:
	# 首先检查是否有目标树木
	if not find_target_tree():
		return false

	# 确认树是否正在燃烧
	if not _current_tree.has_method("is_burning") or not _current_tree.is_burning():
		return false

	# 应用GAS效果
	apply_gas_effect(_current_tree)

	# 显示使用效果（例如粒子、声音等）
	show_use_effects()

	# 清除当前树引用，以便下次使用
	clear_current_tree()

	# 返回使用成功
	return true

# 查找最近的目标树木
func find_target_tree() -> bool:
	if _current_tree:
		return true  # 已经找到了目标树

	# 如果没有预设目标，尝试查找附近的树
	if owner and owner.has_method("get_nearest_tree"):
		var nearest_tree = owner.get_nearest_tree(interaction_distance)
		if nearest_tree:
			set_current_tree(nearest_tree)
			return true

	return false

# 显示使用效果
func show_use_effects() -> void:
	if not _current_tree:
		return

	# 创建粒子效果
	var effect = GPUParticles3D.new()
	_current_tree.add_child(effect)

	# 设置粒子属性
	# NOTE: 此处可能需要根据项目实际情况调整
	effect.emitting = true
	effect.one_shot = true
	effect.explosiveness = 0.8

	# 确保粒子系统自动清理
	effect.finished.connect(Callable(effect, "queue_free"))

	# 如果粒子系统初始化失败，使用计时器清理
	var timer = _current_tree.get_tree().create_timer(3.0)
	timer.timeout.connect(Callable(effect, "queue_free"))

# 应用GAS效果到树上
func apply_gas_effect(tree_node) -> void:
	# 保存当前树引用
	_current_tree = tree_node

	# 获取火焰节点
	var fire_node = null
	if tree_node.has_method("get_fire_node"):
		fire_node = tree_node.get_fire_node()
	else:
		return

	if not fire_node:
		return

	# 保存原始参数
	if fire_node.has_meta("gas_effect_applied"):
		return

	# 保存原始火焰参数
	if fire_node.has_method("get_original_fire_scale"):
		original_fire_scale = fire_node.get_original_fire_scale()
	else:
		# 如果没有获取原始大小的方法，直接用当前大小
		if "scale" in fire_node:
			original_fire_scale = fire_node.scale
		else:
			original_fire_scale = Vector3(1, 1, 1)  # 默认值

	if fire_node.has_method("get_original_fire_amount"):
		original_fire_amount = fire_node.get_original_fire_amount()
	else:
		if "fire_amount" in fire_node:
			original_fire_amount = fire_node.fire_amount
		else:
			original_fire_amount = 1.0  # 默认值

	if fire_node.has_method("get_original_extents"):
		original_extents = fire_node.get_original_extents()
	else:
		if "extents" in fire_node:
			original_extents = fire_node.extents
		else:
			original_extents = Vector3(5, 5, 5)  # 默认值

	# 标记已经施加效果
	fire_node.set_meta("gas_effect_applied", true)

	# 应用倍增效果 - 确保匹配Vector3类型
	var new_scale = original_fire_scale * fire_size_multiplier
	var new_amount = original_fire_amount * fire_size_multiplier
	var new_extents = original_extents * fire_size_multiplier

	# 设置新参数 - 确保类型匹配
	if "scale" in fire_node:
		# 确保类型匹配
		if fire_node.scale is Vector3:
			fire_node.scale = new_scale
		elif fire_node.scale is Vector2:
			# 如果是Vector2，只应用x和y
			fire_node.scale = Vector2(new_scale.x, new_scale.y)

	if "fire_amount" in fire_node:
		fire_node.fire_amount = new_amount

	if "extents" in fire_node:
		# 确保类型匹配
		if fire_node.extents is Vector3:
			fire_node.extents = new_extents
		elif fire_node.extents is Vector2:
			# 如果是Vector2，只应用x和y
			fire_node.extents = Vector2(new_extents.x, new_extents.y)

	# 减少剩余燃烧时间
	if tree_node.has_method("get_remaining_burn_time") and tree_node.has_method("set_remaining_burn_time"):
		var current_burn_time = tree_node.get_remaining_burn_time()
		var new_burn_time = current_burn_time * burn_time_reduction_factor  # 减少到原来的一半

		tree_node.set_remaining_burn_time(new_burn_time)
	else:
		# 尝试其他可能存在的属性
		if "burn_time" in tree_node:
			var original_time = tree_node.burn_time
			tree_node.burn_time = original_time * burn_time_reduction_factor

	# 播放效果音效
	if effect_sound_player and effect_sound:
		effect_sound_player.stream = effect_sound
		effect_sound_player.play()

# 播放效果音效
func play_effect_sound(tree: Node) -> void:
	# 创建临时音效节点
	var audio_player = AudioStreamPlayer3D.new()
	tree.add_child(audio_player)

	# 使用计时器在没有音效的情况下自动清理
	var timer = tree.get_tree().create_timer(1.0)
	timer.timeout.connect(Callable(audio_player, "queue_free"))

# 设置当前可交互的树
func set_current_tree(tree: Node) -> void:
	# 如果是同一棵树，不做任何事
	if _current_tree == tree:
		return

	# 清除上一棵树的高亮
	clear_current_tree()

	# 设置新的树
	_current_tree = tree

	# 如果新树是有效的且正在燃烧，应用GAS专用高亮
	if _current_tree and _current_tree.has_method("is_burning") and _current_tree.is_burning():
		apply_gas_highlight()

# 清除当前树的引用和高亮
func clear_current_tree() -> void:
	if _current_tree:
		remove_gas_highlight()
		_current_tree = null

# 应用GAS专用高亮
func apply_gas_highlight() -> void:
	if not _current_tree:
		return

	var tree_mesh = _current_tree.get_node_or_null("pine-crooked")
	if tree_mesh and tree_mesh is MeshInstance3D:
		tree_mesh.material_overlay = _gas_highlight_material

# 移除GAS专用高亮
func remove_gas_highlight() -> void:
	if not _current_tree:
		return

	var tree_mesh = _current_tree.get_node_or_null("pine-crooked")
	if tree_mesh and tree_mesh is MeshInstance3D:
		tree_mesh.material_overlay = null
