extends Control

# 配置选项
@export var avatar_size: float = 150.0  # 头像大小
@export var background_color_inner: Color = Color(0.3, 0.3, 0.3, 0.8)  # 内部背景颜色
@export var background_color_outer: Color = Color(0.2, 0.2, 0.2, 0.6)  # 外部背景颜色
@export var border_color: Color = Color(1, 1, 1, 0.5)  # 边框颜色
@export var border_width: float = 2.0  # 边框宽度

# 引用玩家头像纹理
var player_avatar: Texture2D
var is_initialized := false
var font: Font

func _ready() -> void:
	# 确保控件可见
	visible = true
	show()

	# 加载默认字体
	font = ThemeDB.fallback_font

	# 延迟初始化，确保所有节点都已准备就绪
	await get_tree().process_frame
	await get_tree().process_frame

	# 查找小地图并获取玩家图标
	find_player_icon()

	# 设置位置和大小
	update_position_and_size()

	# 连接信号，处理窗口大小改变
	get_tree().root.size_changed.connect(_on_window_size_changed)

	is_initialized = true
	queue_redraw()

func _on_window_size_changed() -> void:
	update_position_and_size()
	queue_redraw()

func update_position_and_size() -> void:
	# 获取视口大小
	var viewport_size = get_viewport_rect().size

	# 计算缩放比例（基于屏幕高度）
	var scale_factor = viewport_size.y / 1080.0  # 1080p作为基准分辨率

	# 计算头像大小
	var new_size = Vector2(avatar_size, avatar_size) * scale_factor

	# 确保头像不会太大，但允许在PC上显示更大的头像
	var max_size = min(viewport_size.x * 0.25, viewport_size.y * 0.25)
	new_size = new_size.clamp(Vector2.ZERO, Vector2(max_size, max_size))

	# 设置控件大小 - 现在只需要头像大小的空间
	custom_minimum_size = new_size
	size = custom_minimum_size

	# 放置在左下角
	var margin = 20.0 * scale_factor
	position = Vector2(margin, viewport_size.y - size.y - margin)

func find_player_icon() -> void:
	# 直接加载指定的玩家头像
	if ResourceLoader.exists("res://Icon/MainIcon/PlayerHead.png"):
		player_avatar = load("res://Icon/MainIcon/PlayerHead.png")
		return

	# 如果指定的头像不存在，尝试从小地图获取
	var minimaps = get_tree().get_nodes_in_group("minimap")
	for minimap in minimaps:
		if is_instance_valid(minimap) and minimap.has_method("get") and minimap.get("player_icon"):
			player_avatar = minimap.player_icon
			return

	# 如果还没找到，尝试加载备用图标
	if ResourceLoader.exists("res://icon.png"):
		player_avatar = load("res://icon.png")
	elif ResourceLoader.exists("res://Assets/UI/player_icon.png"):
		player_avatar = load("res://Assets/UI/player_icon.png")

	# 如果还没找到，在下一帧再试一次
	if not player_avatar:
		await get_tree().process_frame
		find_player_icon()

func _draw() -> void:
	if not is_initialized:
		return

	# 绘制玩家头像
	draw_player_avatar()

# 绘制玩家头像
func draw_player_avatar() -> void:
	# 获取头像绘制区域
	var avatar_radius = min(size.x, size.y) / 2
	var avatar_center = Vector2(avatar_radius, size.y / 2)

	# 绘制头像背景
	draw_circle(avatar_center, avatar_radius, background_color_outer)
	draw_circle(avatar_center, avatar_radius * 0.85, background_color_inner)

	# 绘制头像边框
	draw_arc(avatar_center, avatar_radius, 0, 2 * PI, 32, border_color, border_width)

	# 绘制玩家头像
	if player_avatar:
		# 计算绘制区域
		var avatar_rect = Rect2(
			avatar_center - Vector2(avatar_radius * 0.7, avatar_radius * 0.7),
			Vector2(avatar_radius * 1.4, avatar_radius * 1.4)
		)

		# 绘制头像（使用纹理矩形区域以保持原始纹理比例）
		draw_texture_rect(player_avatar, avatar_rect, false)

# 绘制圆角矩形
func draw_rounded_rectangle(rect: Rect2, radius: float, color: Color) -> void:
	# 绘制圆角矩形的中心部分
	var center_rect = Rect2(
		rect.position.x + radius,
		rect.position.y,
		rect.size.x - 2 * radius,
		rect.size.y
	)
	draw_rect(center_rect, color)

	# 绘制左右两侧部分
	var left_rect = Rect2(
		rect.position.x,
		rect.position.y + radius,
		radius,
		rect.size.y - 2 * radius
	)
	draw_rect(left_rect, color)

	var right_rect = Rect2(
		rect.position.x + rect.size.x - radius,
		rect.position.y + radius,
		radius,
		rect.size.y - 2 * radius
	)
	draw_rect(right_rect, color)

	# 绘制四个角落的圆弧
	# 左上角
	draw_circle(Vector2(rect.position.x + radius, rect.position.y + radius), radius, color)
	# 右上角
	draw_circle(Vector2(rect.position.x + rect.size.x - radius, rect.position.y + radius), radius, color)
	# 左下角
	draw_circle(Vector2(rect.position.x + radius, rect.position.y + rect.size.y - radius), radius, color)
	# 右下角
	draw_circle(Vector2(rect.position.x + rect.size.x - radius, rect.position.y + rect.size.y - radius), radius, color)

# 绘制圆角矩形边框
func draw_rounded_rectangle_outline(rect: Rect2, radius: float, color: Color, width: float) -> void:
	# 暂时简化为直接绘制4条线和4个圆弧

	# 上边线
	draw_line(Vector2(rect.position.x + radius, rect.position.y),
		Vector2(rect.position.x + rect.size.x - radius, rect.position.y), color, width)

	# 下边线
	draw_line(Vector2(rect.position.x + radius, rect.position.y + rect.size.y),
		Vector2(rect.position.x + rect.size.x - radius, rect.position.y + rect.size.y), color, width)

	# 左边线
	draw_line(Vector2(rect.position.x, rect.position.y + radius),
		Vector2(rect.position.x, rect.position.y + rect.size.y - radius), color, width)

	# 右边线
	draw_line(Vector2(rect.position.x + rect.size.x, rect.position.y + radius),
		Vector2(rect.position.x + rect.size.x, rect.position.y + rect.size.y - radius), color, width)

	# 四个角落的圆弧
	# 左上角
	draw_arc(Vector2(rect.position.x + radius, rect.position.y + radius), radius, PI, 1.5 * PI, 16, color, width)
	# 右上角
	draw_arc(Vector2(rect.position.x + rect.size.x - radius, rect.position.y + radius), radius, 1.5 * PI, 2 * PI, 16, color, width)
	# 左下角
	draw_arc(Vector2(rect.position.x + radius, rect.position.y + rect.size.y - radius), radius, 0.5 * PI, PI, 16, color, width)
	# 右下角
	draw_arc(Vector2(rect.position.x + rect.size.x - radius, rect.position.y + rect.size.y - radius), radius, 0, 0.5 * PI, 16, color, width)
