extends Node
class_name UIManager

## UI层级管理器
## 统一管理所有UI层级，确保UI不受3D Camera影响，完全独立于碰撞系统

# UI层级定义
enum UILayer {
	BORDER_DECORATION = 0,  # 地图边界装饰
	GAME_UI = 1,           # 游戏UI（技能、道具、血量等）
	MENU_UI = 2,           # 菜单UI（暂停、设置等）
	POPUP_UI = 3           # 弹窗UI（游戏结束、对话等）
}

# UI层级节点引用
var ui_layers: Dictionary = {}
var border_decoration: CanvasLayer
var game_ui: CanvasLayer
var menu_ui: CanvasLayer
var popup_ui: CanvasLayer

# UI组件引用
var skill_box_ui: Control
var item_boxes_ui: Control
var player_avatar_ui: Control
var minimap_ui: Control

# 边界装饰配置
@export_group("边界装饰配置")
@export var border_enabled: bool = true
@export var border_width: float = 50.0
@export var border_color: Color = Color(0.1, 0.1, 0.2, 0.8)
@export var border_pattern_color: Color = Color(0.3, 0.3, 0.5, 0.6)

func _ready() -> void:
	# 确保UIManager在场景树中的位置正确
	if get_parent() != get_tree().current_scene:
		print("[UIManager] 警告：UIManager应该直接添加到场景根节点")
	
	# 初始化UI层级
	_initialize_ui_layers()
	
	# 创建边界装饰
	if border_enabled:
		_create_border_decoration()
	
	print("[UIManager] UI管理器初始化完成")

# 初始化所有UI层级
func _initialize_ui_layers() -> void:
	# 创建边界装饰层 (Layer 0)
	border_decoration = CanvasLayer.new()
	border_decoration.name = "BorderDecoration"
	border_decoration.layer = UILayer.BORDER_DECORATION
	add_child(border_decoration)
	ui_layers[UILayer.BORDER_DECORATION] = border_decoration
	
	# 创建游戏UI层 (Layer 1)
	game_ui = CanvasLayer.new()
	game_ui.name = "GameUI"
	game_ui.layer = UILayer.GAME_UI
	add_child(game_ui)
	ui_layers[UILayer.GAME_UI] = game_ui
	
	# 创建菜单UI层 (Layer 2)
	menu_ui = CanvasLayer.new()
	menu_ui.name = "MenuUI"
	menu_ui.layer = UILayer.MENU_UI
	add_child(menu_ui)
	ui_layers[UILayer.MENU_UI] = menu_ui
	
	# 创建弹窗UI层 (Layer 3)
	popup_ui = CanvasLayer.new()
	popup_ui.name = "PopupUI"
	popup_ui.layer = UILayer.POPUP_UI
	add_child(popup_ui)
	ui_layers[UILayer.POPUP_UI] = popup_ui
	
	print("[UIManager] UI层级创建完成：4个CanvasLayer")

# 创建边界装饰
func _create_border_decoration() -> void:
	if not border_decoration:
		push_error("[UIManager] 边界装饰层未初始化")
		return
	
	# 创建边界装饰控件
	var border_control = Control.new()
	border_control.name = "BorderControl"
	border_control.set_anchors_and_offsets_preset(Control.PRESET_FULL_RECT)
	border_control.mouse_filter = Control.MOUSE_FILTER_IGNORE  # 不拦截鼠标事件
	
	# 连接绘制信号
	border_control.draw.connect(_draw_border_decoration.bind(border_control))
	
	border_decoration.add_child(border_control)
	
	print("[UIManager] 边界装饰创建完成")

# 绘制边界装饰
func _draw_border_decoration(control: Control) -> void:
	var viewport_size = get_viewport().get_visible_rect().size
	
	# 绘制四个边界矩形
	var border_rects = [
		Rect2(0, 0, viewport_size.x, border_width),  # 上边界
		Rect2(0, viewport_size.y - border_width, viewport_size.x, border_width),  # 下边界
		Rect2(0, 0, border_width, viewport_size.y),  # 左边界
		Rect2(viewport_size.x - border_width, 0, border_width, viewport_size.y)   # 右边界
	]
	
	# 绘制边界背景
	for rect in border_rects:
		control.draw_rect(rect, border_color)
	
	# 绘制装饰花纹（简单的线条图案）
	_draw_border_patterns(control, viewport_size)

# 绘制边界花纹
func _draw_border_patterns(control: Control, viewport_size: Vector2) -> void:
	var pattern_width = 2.0
	var pattern_spacing = 20.0
	
	# 上边界花纹
	for x in range(0, int(viewport_size.x), int(pattern_spacing)):
		var start_pos = Vector2(x, border_width * 0.2)
		var end_pos = Vector2(x, border_width * 0.8)
		control.draw_line(start_pos, end_pos, border_pattern_color, pattern_width)
	
	# 下边界花纹
	for x in range(0, int(viewport_size.x), int(pattern_spacing)):
		var start_pos = Vector2(x, viewport_size.y - border_width * 0.8)
		var end_pos = Vector2(x, viewport_size.y - border_width * 0.2)
		control.draw_line(start_pos, end_pos, border_pattern_color, pattern_width)
	
	# 左边界花纹
	for y in range(0, int(viewport_size.y), int(pattern_spacing)):
		var start_pos = Vector2(border_width * 0.2, y)
		var end_pos = Vector2(border_width * 0.8, y)
		control.draw_line(start_pos, end_pos, border_pattern_color, pattern_width)
	
	# 右边界花纹
	for y in range(0, int(viewport_size.y), int(pattern_spacing)):
		var start_pos = Vector2(viewport_size.x - border_width * 0.8, y)
		var end_pos = Vector2(viewport_size.x - border_width * 0.2, y)
		control.draw_line(start_pos, end_pos, border_pattern_color, pattern_width)

# 获取指定层级的CanvasLayer
func get_ui_layer(layer: UILayer) -> CanvasLayer:
	return ui_layers.get(layer)

# 将UI组件添加到指定层级
func add_ui_to_layer(ui_component: Control, layer: UILayer) -> void:
	var canvas_layer = get_ui_layer(layer)
	if canvas_layer:
		canvas_layer.add_child(ui_component)
		print("[UIManager] UI组件 ", ui_component.name, " 已添加到层级 ", layer)
	else:
		push_error("[UIManager] 无法找到UI层级: " + str(layer))

# 移除UI组件
func remove_ui_from_layer(ui_component: Control) -> void:
	if ui_component and ui_component.get_parent():
		ui_component.get_parent().remove_child(ui_component)
		print("[UIManager] UI组件 ", ui_component.name, " 已移除")

# 显示/隐藏指定层级
func set_layer_visible(layer: UILayer, visible: bool) -> void:
	var canvas_layer = get_ui_layer(layer)
	if canvas_layer:
		canvas_layer.visible = visible
		print("[UIManager] 层级 ", layer, " 可见性设置为: ", visible)

# 设置边界装饰可见性
func set_border_visible(visible: bool) -> void:
	if border_decoration:
		border_decoration.visible = visible
		print("[UIManager] 边界装饰可见性设置为: ", visible)

# 刷新边界装饰（当窗口大小改变时）
func refresh_border_decoration() -> void:
	if border_decoration and border_decoration.get_child_count() > 0:
		var border_control = border_decoration.get_child(0)
		if border_control:
			border_control.queue_redraw()

# 响应窗口大小变化
func _notification(what: int) -> void:
	if what == NOTIFICATION_RESIZED:
		refresh_border_decoration()

# 注册游戏UI组件
func register_game_ui_components(skill_box: Control, item_boxes: Control, player_avatar: Control, minimap: Control) -> void:
	skill_box_ui = skill_box
	item_boxes_ui = item_boxes
	player_avatar_ui = player_avatar
	minimap_ui = minimap
	
	# 将这些组件移动到游戏UI层
	if skill_box_ui:
		add_ui_to_layer(skill_box_ui, UILayer.GAME_UI)
	if item_boxes_ui:
		add_ui_to_layer(item_boxes_ui, UILayer.GAME_UI)
	if player_avatar_ui:
		add_ui_to_layer(player_avatar_ui, UILayer.GAME_UI)
	if minimap_ui:
		add_ui_to_layer(minimap_ui, UILayer.GAME_UI)
	
	print("[UIManager] 游戏UI组件注册完成")

# 获取游戏UI的有效区域（排除边界装饰）
func get_game_ui_rect() -> Rect2:
	var viewport_size = get_viewport().get_visible_rect().size
	if border_enabled:
		return Rect2(
			border_width, 
			border_width, 
			viewport_size.x - border_width * 2, 
			viewport_size.y - border_width * 2
		)
	else:
		return Rect2(Vector2.ZERO, viewport_size)
