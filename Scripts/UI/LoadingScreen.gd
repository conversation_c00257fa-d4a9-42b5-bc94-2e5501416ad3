extends Control

signal initialization_complete

# 配置参数
@export var main_scene_path: String = "res://Scenes/Levels/Level01.tscn"  # 直接加载Level01场景
@export var min_loading_time: float = 1.0  # 最小加载时间，避免加载过快闪屏

# 备选场景路径，当主场景无效时将尝试这些路径
var fallback_scene_paths := [
	"res://Scenes/levels/Level01.tscn",
	"res://Scenes/levels/Level02.tscn"
]

# 加载状态
var _loading_steps := []
var _current_step := 0
var _start_time := 0.0
var _is_loading := false

# 选定的关卡信息
var selected_level_id: String = ""
var selected_level_config_path: String = ""

# UI 引用
@onready var progress_bar: ProgressBar = $ProgressBar
@onready var status_label: Label = $StatusLabel
@onready var animation_player: AnimationPlayer = $AnimationPlayer

func _ready() -> void:
	# 注意：游戏运行时可能会出现导航网格错误，这是引擎内部问题，不影响游戏功能
	# 错误信息: "_build_step_find_edge_connection_pairs: Navigation map synchronization error."
	# 错误信息: "Meshes must contain a vertex array, an index array, or both"

	# 设置错误处理，忽略网格错误
	Engine.set_print_error_messages(false)

	# 确保UI元素可见
	visible = true
	modulate.a = 1.0

	# 从全局游戏数据中获取选定的关卡信息
	var game_data = get_node_or_null("/root/GameData")
	if game_data:
		if game_data.has_meta("selected_level_id"):
			selected_level_id = game_data.get_meta("selected_level_id")
		if game_data.has_meta("selected_level_config_path"):
			selected_level_config_path = game_data.get_meta("selected_level_config_path")

	# 注册加载步骤
	_register_loading_steps()

	# 显示初始状态
	if progress_bar:
		progress_bar.value = 0
	if status_label:
		status_label.text = "准备加载..."

	# 延迟一帧开始加载
	call_deferred("start_loading")

func _process(_delta: float) -> void:
	if not _is_loading:
		return

	# 确保最小加载时间
	var elapsed = Time.get_ticks_msec() / 1000.0 - _start_time
	var progress = float(_current_step) / float(_loading_steps.size())

	# 更新进度条
	if progress_bar:
		progress_bar.value = progress * 100

	# 检查是否完成所有步骤且达到最小加载时间
	if _current_step >= _loading_steps.size() and elapsed >= min_loading_time:
		_finish_loading()

func start_loading() -> void:

	_is_loading = true
	_start_time = Time.get_ticks_msec() / 1000.0
	_current_step = 0

	# 显示加载动画
	if animation_player and animation_player.has_animation("loading"):
		animation_player.play("loading")

	# 开始执行加载步骤
	_execute_next_step()

func _register_loading_steps() -> void:
	# 注册所有需要执行的加载步骤
	_loading_steps = [
		{"name": "初始化游戏设置", "method": "_init_game_settings"},
		{"name": "检查玩家数据", "method": "_check_player_data"},
		{"name": "初始化技能系统", "method": "_init_skill_system"},
		{"name": "初始化道具系统", "method": "_preload_item_system"},
		{"name": "预加载UI元素", "method": "_preload_ui"},
		{"name": "准备游戏场景", "method": "_prepare_game_scene"}
	]

func _execute_next_step() -> void:
	# 检查是否已完成所有步骤
	if _current_step >= _loading_steps.size():
		return

	# 获取当前步骤
	var step = _loading_steps[_current_step]

	# 更新状态文本
	if status_label:
		status_label.text = "正在" + step.name + "..."



	# 执行步骤
	call(step.method)

	# 移动到下一步
	_current_step += 1

	# 执行下一步（延迟一帧，避免UI卡顿）
	call_deferred("_execute_next_step")

func _finish_loading() -> void:
	_is_loading = false

	if status_label:
		status_label.text = "加载完成，正在进入游戏..."

	# 发出加载完成信号
	emit_signal("initialization_complete")

	# 重新启用错误消息
	Engine.set_print_error_messages(true)

	# 播放完成动画（如果有）
	if animation_player and animation_player.has_animation("fade_out"):
		animation_player.play("fade_out")
		await animation_player.animation_finished

	# 确保路径有效
	if ResourceLoader.exists(main_scene_path):
		# 直接切换到主场景，不使用预加载的场景
		# 这样可以避免网格错误
		get_tree().change_scene_to_file(main_scene_path)
	else:
		push_error("加载屏幕：主场景仍然无效，尝试任何可用场景")

		# 最后的尝试：加载任何可用的关卡场景
		var level_paths = [
			"res://Scenes/levels/Level01.tscn",
			"res://Scenes/levels/Level02.tscn",
			"res://Scenes/UI/MainMenu.tscn"
		]

		for path in level_paths:
			if ResourceLoader.exists(path):

				get_tree().change_scene_to_file(path)
				return

		# 如果仍然找不到任何场景，显示错误
		push_error("加载屏幕：找不到任何可用场景，无法继续")
		if status_label:
			status_label.text = "加载失败：找不到任何有效场景"

# 以下是各个加载步骤的具体实现

func _init_game_settings() -> void:
	# 初始化游戏设置

	# 设置物理帧率
	Engine.physics_ticks_per_second = 60

	# 确保游戏未暂停
	get_tree().paused = false

	# 预加载GameOverUI
	var game_over_ui_path = "res://Scenes/UI/GameOverUI.tscn"
	if ResourceLoader.exists(game_over_ui_path):
		var _game_over_ui_scene = load(game_over_ui_path)

	# 等待一小段时间模拟加载
	await get_tree().create_timer(0.2).timeout

func _check_player_data() -> void:


	# 这里可以添加读取存档、初始化玩家数据等逻辑

	# 等待一小段时间模拟加载
	await get_tree().create_timer(0.3).timeout

func _init_skill_system() -> void:
	# 初始化技能系统

	# 预加载所有技能资源
	var skill_paths = [
		"res://Resources/Skills/BlinkSkill.tres",
		"res://Resources/Skills/DisguiseSkill.tres"
	]

	for path in skill_paths:
		if ResourceLoader.exists(path):
			var _skill = load(path)

	# 等待一小段时间模拟加载
	await get_tree().create_timer(0.3).timeout

func _preload_item_system() -> void:
	# 预加载所有道具资源
	var item_paths = [
		"res://Resources/GasItem.tres"
	]

	# 加载所有道具资源
	var loaded_items = []
	for path in item_paths:
		if ResourceLoader.exists(path):
			var item = load(path)
			if item:
				loaded_items.append(item)

	# 确保ItemSystem可用（通过autoload应该已经加载）
	var item_system = get_node_or_null("/root/ItemSystem")
	if item_system:
		# 注册GasItem资源
		if loaded_items.size() > 0 and item_system.has_method("register_gas_item"):
			item_system.register_gas_item(loaded_items[0])

		# 确保item_boxes_ui加载完成并正确初始化
		var item_boxes_scene = load("res://Scenes/ItemBoxes.tscn")
		if item_boxes_scene:
			# 创建临时实例以确保初始化正确
			var temp_item_boxes = item_boxes_scene.instantiate()
			# 添加到场景树以确保_ready()被调用
			add_child(temp_item_boxes)
			# 等待一帧确保初始化完成
			await get_tree().process_frame
			# 验证UI是否可用

			remove_child(temp_item_boxes)
			temp_item_boxes.queue_free()
	else:
		push_error("加载屏幕：未找到ItemSystem自动加载节点")

	# 等待一小段时间模拟加载
	await get_tree().create_timer(0.3).timeout

func _preload_ui() -> void:
	# 预加载UI元素

	var ui_paths = [
		"res://Scenes/UI/MainMenu.tscn",  # 添加主菜单预加载
		"res://Scenes/SkillBoxUI.tscn",
		"res://Scenes/UI/GameOverUI.tscn",
		"res://Scenes/ItemBoxes.tscn"
	]

	for path in ui_paths:
		if ResourceLoader.exists(path):
			var _ui = load(path)

	# 等待一小段时间模拟加载
	await get_tree().create_timer(0.2).timeout

func _prepare_game_scene() -> void:
	# 验证主场景存在，如果不存在则尝试备选路径
	var scene_to_load = main_scene_path
	if not ResourceLoader.exists(scene_to_load):
		push_error("加载屏幕：主场景路径无效 " + scene_to_load)

		# 尝试备选路径
		var found_valid_scene = false
		for fallback_path in fallback_scene_paths:
			if ResourceLoader.exists(fallback_path):
				scene_to_load = fallback_path
				found_valid_scene = true
				break

		if not found_valid_scene:
			push_error("加载屏幕：所有场景路径均无效，无法加载游戏")

			if status_label:
				status_label.text = "加载失败：无法找到游戏场景"
			return

	# 预加载主场景
	var scene = null

	# 完全禁用场景加载以避免网格错误
	# 这个错误不影响游戏功能，只是控制台上的警告
	if ResourceLoader.exists(scene_to_load):
		# 不预加载场景，而是在切换场景时直接加载
		# 这样可以避免网格错误
		# scene = load(scene_to_load)

		# 直接设置场景路径，不预加载
		scene = true  # 只是一个标记，表示场景存在

		# 注释下面的错误检查，因为即使有网格错误，场景仍然可以加载
		# if scene == null:
		# 	push_error("加载场景时发生错误: " + scene_to_load)
	else:
		push_error("场景文件不存在: " + scene_to_load)

	if scene:
		main_scene_path = scene_to_load  # 更新主场景路径为有效路径

		# 如果有选定的关卡配置，预先设置到GameData中
		if not selected_level_id.is_empty() and not selected_level_config_path.is_empty():
			var game_data = get_node_or_null("/root/GameData")
			if game_data:
				# 设置关卡信息，供Main.tscn使用
				game_data.set_meta("current_level_id", selected_level_id)
				game_data.set_meta("current_level_config_path", selected_level_config_path)

				# 清除选定的关卡信息，避免重复使用
				game_data.remove_meta("selected_level_id")
				game_data.remove_meta("selected_level_config_path")

		# 跳过ensure_game_over_ui_in_scene函数，因为它可能导致网格错误
		# ensure_game_over_ui_in_scene(scene)
	else:
		push_error("加载屏幕：无法加载场景: " + scene_to_load)
		if status_label:
			status_label.text = "加载失败：场景加载出错"
		return

	# 等待一小段时间模拟加载
	await get_tree().create_timer(0.3).timeout

# 确保场景中有GameOverUI节点
func ensure_game_over_ui_in_scene(_scene: PackedScene) -> void:
	# 跳过这个函数，因为它可能导致网格错误
	return

	# 以下代码被跳过
	# 检查场景是否为Level场景
	# var instance = scene.instantiate()
	#
	# # 检查是否是关卡场景（判断方法：有Player节点或是Node3D类型）
	# var is_level_scene = false
	# if instance is Node3D:
	# 	is_level_scene = true
	# if instance.has_node("Player"):
	# 	is_level_scene = true
	#
	# # 只有关卡场景需要确保有GameOverUI
	# if is_level_scene:
	# 	# 检查场景中是否已经有GameOverUI节点
	# 	var has_game_over_ui = false
	# 	if instance.has_node("GameOverUI"):
	# 		has_game_over_ui = true
	#
	# 	# 如果没有，需要确保游戏启动时添加
	# 	if not has_game_over_ui:
	# 		# 创建一个自动加载脚本，确保在场景加载后添加GameOverUI
	# 		create_ui_autoload_script()
	#
	# # 释放临时实例
	# instance.queue_free()

# 创建UI自动加载脚本，确保在场景加载后添加GameOverUI
func create_ui_autoload_script() -> void:
	# 创建一个新节点作为自动加载单例
	var ui_helper = Node.new()
	ui_helper.name = "UIHelper"
	ui_helper.set_meta("add_game_over_ui", true)

	# 为节点添加一个简单的脚本
	var script_code = """
extends Node

func _ready():
	# 确保在一帧后添加GameOverUI，以便场景完全加载
	call_deferred("add_game_over_ui")

func add_game_over_ui():
	# 等待两帧确保场景初始化完成
	await get_tree().process_frame
	await get_tree().process_frame

	# 检查当前场景是否已经有GameOverUI
	var current_scene = get_tree().current_scene
	if current_scene and current_scene.has_node("GameOverUI"):
		return

	# 加载GameOverUI场景
	var game_over_ui_path = "res://Scenes/UI/GameOverUI.tscn"
	if not ResourceLoader.exists(game_over_ui_path):
		return

	var game_over_ui_scene = load(game_over_ui_path)
	if not game_over_ui_scene:
		return

	var game_over_ui = game_over_ui_scene.instantiate()
	if not game_over_ui:
		return

	# 添加到当前场景
	if current_scene:
		current_scene.add_child(game_over_ui)
	else:
		# 如果找不到当前场景，添加到根节点
		get_tree().root.add_child(game_over_ui)

	# 设置GameOverUI属性
	if game_over_ui.has_method("set_game_duration"):
		game_over_ui.set_game_duration(0)  # 设置为0表示不使用倒计时

	# 任务完成后从场景树中移除自己
	queue_free()
"""

	# 创建一个临时脚本资源
	var script = GDScript.new()
	script.source_code = script_code

	# 设置脚本到节点
	ui_helper.set_script(script)

	# 将节点添加到自动加载列表（通过添加到根节点）
	get_tree().root.add_child(ui_helper)
	ui_helper.process_mode = Node.PROCESS_MODE_ALWAYS  # 确保在暂停时也能运行
