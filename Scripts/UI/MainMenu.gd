extends Control

# 场景路径
@export var level_select_path: String = "res://Scenes/UI/LevelSelect.tscn"

# 节点引用
@onready var start_button = $MenuContainer/StartButton
@onready var options_button = $MenuContainer/OptionsButton
@onready var gallery_button = $MenuContainer/GalleryButton
@onready var quit_button = $MenuContainer/QuitButton
@onready var options_panel = $OptionsPanel
@onready var gallery_panel = $GalleryPanel
@onready var close_options_button = $OptionsPanel/CloseOptionsButton
@onready var close_gallery_button = $GalleryPanel/CloseGalleryButton
@onready var music_slider = $OptionsPanel/OptionsContainer/MusicVolumeContainer/MusicSlider
@onready var sfx_slider = $OptionsPanel/OptionsContainer/SFXVolumeContainer/SFXSlider
@onready var quality_option = $OptionsPanel/OptionsContainer/QualityContainer/QualityOption
@onready var language_option = $OptionsPanel/OptionsContainer/LanguageContainer/LanguageOption
@onready var background_particles = $BackgroundParticles
@onready var audio_player = $AudioStreamPlayer
@onready var button_sound = $ButtonSound
@onready var character_skin = $CharacterViewport/SubViewport/CharacterContainer/SophiaSkin

# 音效和音乐
var button_click_sound = preload("res://character/player/sounds/robot_jump.wav")
var menu_music_path = "res://character/player/sounds/robot_land.wav" # 临时使用，应替换为实际的菜单音乐

# 游戏设置
var music_volume: float = 0.8
var sfx_volume: float = 0.8
var quality_level: int = 1 # 0=低, 1=中, 2=高
var language: int = 0 # 0=中文, 1=英文

func _ready():
	# 连接按钮信号
	start_button.pressed.connect(_on_start_button_pressed)
	options_button.pressed.connect(_on_options_button_pressed)
	gallery_button.pressed.connect(_on_gallery_button_pressed)
	quit_button.pressed.connect(_on_quit_button_pressed)
	close_options_button.pressed.connect(_on_close_options_button_pressed)
	close_gallery_button.pressed.connect(_on_close_gallery_button_pressed)

	# 连接滑块信号
	music_slider.value_changed.connect(_on_music_volume_changed)
	sfx_slider.value_changed.connect(_on_sfx_volume_changed)
	quality_option.item_selected.connect(_on_quality_selected)
	language_option.item_selected.connect(_on_language_selected)

	# 设置按钮音效
	button_sound.stream = button_click_sound

	# 加载菜单音乐
	if ResourceLoader.exists(menu_music_path):
		var music = load(menu_music_path)
		if music:
			audio_player.stream = music
			audio_player.play()

	# 初始化角色动画
	if character_skin and character_skin.has_method("play_idle_animation"):
		character_skin.play_idle_animation()

	# 加载设置
	_load_settings()

	# 应用设置
	_apply_settings()

	# 初始化图鉴内容
	_initialize_gallery()

# 加载设置
func _load_settings():
	# 这里应该从配置文件加载设置
	# 暂时使用默认值
	pass

# 应用设置
func _apply_settings():
	# 设置音量
	# 检查音频总线是否存在，如果不存在则创建
	if AudioServer.get_bus_index("Music") == -1:
		# 创建Music总线
		AudioServer.add_bus()
		var music_bus_idx = AudioServer.get_bus_count() - 1
		AudioServer.set_bus_name(music_bus_idx, "Music")
		# 将Music总线发送到Master总线
		AudioServer.set_bus_send(music_bus_idx, "Master")
		print("创建了Music音频总线")

	if AudioServer.get_bus_index("SFX") == -1:
		# 创建SFX总线
		AudioServer.add_bus()
		var sfx_bus_idx = AudioServer.get_bus_count() - 1
		AudioServer.set_bus_name(sfx_bus_idx, "SFX")
		# 将SFX总线发送到Master总线
		AudioServer.set_bus_send(sfx_bus_idx, "Master")
		print("创建了SFX音频总线")

	# 设置音量
	AudioServer.set_bus_volume_db(AudioServer.get_bus_index("Music"), linear_to_db(music_volume))
	AudioServer.set_bus_volume_db(AudioServer.get_bus_index("SFX"), linear_to_db(sfx_volume))

	# 设置画质
	match quality_level:
		0: # 低
			background_particles.amount = 20
		1: # 中
			background_particles.amount = 50
		2: # 高
			background_particles.amount = 100

	# 设置语言
	# 这里应该设置游戏的语言
	# 暂时不实现

# 初始化图鉴内容
func _initialize_gallery():
	# 这里应该从游戏数据加载图鉴内容
	# 暂时使用示例数据
	pass

# 播放按钮音效
func _play_button_sound():
	if button_sound and button_sound.stream:
		button_sound.play()

# 按钮事件处理
func _on_start_button_pressed():
	_play_button_sound()
	# 切换到关卡选择页面
	if ResourceLoader.exists(level_select_path):
		get_tree().change_scene_to_file(level_select_path)
	else:
		push_error("找不到关卡选择页面场景")

func _on_options_button_pressed():
	_play_button_sound()
	# 显示选项面板
	options_panel.visible = true

	# 更新选项面板的值
	music_slider.value = music_volume
	sfx_slider.value = sfx_volume
	quality_option.selected = quality_level
	language_option.selected = language

func _on_gallery_button_pressed():
	_play_button_sound()
	# 显示图鉴面板
	gallery_panel.visible = true

func _on_quit_button_pressed():
	_play_button_sound()
	# 退出游戏
	get_tree().quit()

func _on_close_options_button_pressed():
	_play_button_sound()
	# 隐藏选项面板
	options_panel.visible = false

	# 保存设置
	_save_settings()

func _on_close_gallery_button_pressed():
	_play_button_sound()
	# 隐藏图鉴面板
	gallery_panel.visible = false

# 设置事件处理
func _on_music_volume_changed(value):
	music_volume = value
	AudioServer.set_bus_volume_db(AudioServer.get_bus_index("Music"), linear_to_db(music_volume))

func _on_sfx_volume_changed(value):
	sfx_volume = value
	AudioServer.set_bus_volume_db(AudioServer.get_bus_index("SFX"), linear_to_db(sfx_volume))

	# 播放音效示例
	_play_button_sound()

func _on_quality_selected(index):
	quality_level = index

	# 应用画质设置
	match quality_level:
		0: # 低
			background_particles.amount = 20
		1: # 中
			background_particles.amount = 50
		2: # 高
			background_particles.amount = 100

func _on_language_selected(index):
	language = index
	# 这里应该应用语言设置
	# 暂时不实现

# 保存设置
func _save_settings():
	# 这里应该将设置保存到配置文件
	# 暂时不实现
	pass
