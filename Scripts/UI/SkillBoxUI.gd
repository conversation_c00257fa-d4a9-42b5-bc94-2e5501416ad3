extends Control

# 配置
@export var player_path: NodePath  # 玩家节点路径
@export var skill_1_index: int = 0  # 技能1的索引
@export var skill_2_index: int = 1  # 技能2的索引

# 引用
var player: Node
var skill_1_box: Control
var skill_2_box: Control
var font: Font

# 状态
var is_initialized := false

func _ready() -> void:
	# 将自身添加到skill_ui组，方便重启游戏时处理
	add_to_group("skill_ui")

	# 确保控件可见
	visible = true
	show()

	# 重置内部状态标记
	is_initialized = false

	# 加载默认字体
	font = ThemeDB.fallback_font
	if not font:
		push_error("无法加载默认字体")
		font = FontFile.new()  # 尝试创建一个空字体作为后备

	# 设置锚点为左下角
	anchors_preset = Control.PRESET_BOTTOM_LEFT

	# 检查是否需要重新初始化（由游戏重启过程标记）
	if has_meta("needs_reinit"):
		# 移除重初始化标记
		remove_meta("needs_reinit")

		# 确保可见性
		visible = true
		modulate.a = 1.0

		# 立即初始化
		call_deferred("force_initialization")
		return

	# 检查是否为游戏重启情况
	var restart_data = get_node_or_null("/root/GameRestartData")
	if restart_data and restart_data.has_meta("is_restart") and restart_data.get_meta("is_restart"):
		print("技能UI：检测到游戏重启，延迟初始化")
		# 由游戏重启助手触发初始化
	else:
		# 第一次初始化UI
		call_deferred("delayed_initialization")

	# 连接窗口大小改变信号
	if not get_tree().root.size_changed.is_connected(_on_window_size_changed):
		get_tree().root.size_changed.connect(_on_window_size_changed)

# 延迟初始化，确保在场景变化后能正确初始化
func delayed_initialization() -> void:
	# 等待一段时间确保场景加载完成
	await get_tree().create_timer(0.2).timeout

	# 如果游戏处于暂停状态，记录并恢复
	var was_paused = get_tree().paused
	if was_paused:
		get_tree().paused = false

	# 查找或获取玩家引用
	find_player()

	# 如果没有找到玩家，再尝试几次
	var attempts = 0
	var max_attempts = 5

	while not player and attempts < max_attempts:
		attempts += 1
		# 打印调试信息
		await get_tree().create_timer(0.3).timeout
		find_player()


	# 创建技能框（仅在未创建时）
	if not skill_1_box or not is_instance_valid(skill_1_box) or not skill_2_box or not is_instance_valid(skill_2_box):
		create_skill_boxes()
	else:
		update_position_and_size()

	# 标记为已初始化
	is_initialized = true

	# 强制立即重绘
	queue_redraw()

	# 启动监视定时器，定期检查玩家引用是否有效
	start_player_monitor()

	# 如果必要，恢复暂停状态
	if was_paused:
		get_tree().paused = was_paused


# 启动玩家监视器，确保在场景变化后能重新连接到玩家
func start_player_monitor() -> void:
	var timer = Timer.new()
	timer.name = "PlayerMonitorTimer"
	timer.wait_time = 1.0  # 每秒检查一次
	timer.autostart = true
	timer.timeout.connect(check_player_reference)
	add_child(timer)

# 检查玩家引用是否有效，如果无效则尝试重新查找
func check_player_reference() -> void:
	if not is_instance_valid(player) or not player or player.is_queued_for_deletion():
		find_player()

		if player:
			# 如果找到了有效的玩家引用，确保技能框正确显示
			update_position_and_size()
			queue_redraw()


# 每帧更新
func _process(_delta: float) -> void:
	# 检查可见性
	if not visible:
		visible = true

	# 如果需要重初始化，则强制进行初始化
	if has_meta("needs_reinit"):
		remove_meta("needs_reinit")
		call_deferred("force_initialization")
		return

	if not is_initialized or not player:
		# 如果UI还未初始化但已经过去了一段时间，尝试延迟初始化
		if Engine.get_process_frames() > 60 and not is_initialized:  # 约1秒后
			call_deferred("force_initialization")
		return

	update_skill_cooldowns()
	queue_redraw()

func find_player() -> void:
	# 先尝试使用导出的路径
	if player_path and has_node(player_path):
		player = get_node(player_path)
		return

	# 尝试从组中找到玩家
	var players = get_tree().get_nodes_in_group("player")
	if players.size() > 0:
		player = players[0]
		return

	# 如果上述方法都失败，尝试在场景中搜索名为"Player"的节点
	var potential_player = get_tree().get_first_node_in_group("player")
	if potential_player:
		player = potential_player
		return

	# 如果所有方法都失败
	player = null

func create_skill_boxes() -> void:
	# 删除现有的技能框（如果存在）
	if skill_1_box and is_instance_valid(skill_1_box):
		skill_1_box.queue_free()
	if skill_2_box and is_instance_valid(skill_2_box):
		skill_2_box.queue_free()

	# 创建技能框1
	skill_1_box = Control.new()
	skill_1_box.name = "SkillBox1"
	skill_1_box.size = Vector2(100, 100)  # 设置初始默认尺寸为100x100
	skill_1_box.custom_minimum_size = Vector2(50, 50)  # 设置最小尺寸
	add_child(skill_1_box)

	# 创建技能框2
	skill_2_box = Control.new()
	skill_2_box.name = "SkillBox2"
	skill_2_box.size = Vector2(100, 100)  # 设置初始默认尺寸为100x100
	skill_2_box.custom_minimum_size = Vector2(50, 50)  # 设置最小尺寸
	add_child(skill_2_box)

	# 确保两个技能框与UI尺寸保持同步
	skill_1_box.size_flags_horizontal = Control.SIZE_EXPAND
	skill_1_box.size_flags_vertical = Control.SIZE_EXPAND
	skill_2_box.size_flags_horizontal = Control.SIZE_EXPAND
	skill_2_box.size_flags_vertical = Control.SIZE_EXPAND

	# 更新技能框位置和大小
	update_position_and_size()



func _on_window_size_changed() -> void:
	update_position_and_size()
	queue_redraw()

func update_position_and_size() -> void:
	if not is_initialized:
		return

	# 获取视口大小
	var viewport_size = get_viewport_rect().size
	if viewport_size.x <= 0 or viewport_size.y <= 0:
		push_warning("视口尺寸无效！")
		viewport_size = Vector2(1280, 720)  # 使用默认值

	# 计算缩放比例（基于屏幕高度）
	var scale_factor = max(viewport_size.y / 1080.0, 0.1)  # 确保缩放因子至少为0.1

	# 获取玩家头像大小作为参考
	var avatar_size = max(150.0 * scale_factor, 30.0)  # 默认玩家头像大小

	# 技能框大小为头像大小的一半
	var skill_box_size = avatar_size * 0.5

	# 设置技能框之间的间距
	var spacing = max(skill_box_size * 0.3, 10.0)  # 确保间距至少为10像素

	# 设置控件大小
	var total_width = skill_box_size * 2 + spacing
	custom_minimum_size = Vector2(total_width, skill_box_size)
	# 确保最小尺寸至少为 100x100
	custom_minimum_size.x = max(custom_minimum_size.x, 100)
	custom_minimum_size.y = max(custom_minimum_size.y, 100)

	size = custom_minimum_size

	# 检查并设置最小尺寸
	if size.x < 100 or size.y < 100:
		push_warning("UI尺寸过小，使用最小值")
		size = Vector2(max(size.x, 100), max(size.y, 100))

	# 设置技能框1的位置和大小
	skill_1_box.size = Vector2(skill_box_size, skill_box_size)
	skill_1_box.position = Vector2(0, 0)

	# 设置技能框2的位置和大小
	skill_2_box.size = Vector2(skill_box_size, skill_box_size)
	skill_2_box.position = Vector2(skill_box_size + spacing, 0)

	# 放置在左下角，与玩家头像对齐
	var margin = max(20.0 * scale_factor, 10.0)  # 确保边距至少为10像素
	position = Vector2(margin + avatar_size + spacing, viewport_size.y - size.y - margin)



	# 强制下一帧重绘
	queue_redraw()

func update_skill_cooldowns() -> void:
	# 无需操作，在_draw中直接获取和显示技能冷却信息
	pass

func _draw() -> void:
	if not is_initialized or not player:
		return

	# 在绘制前确保控件有合理大小
	if size.x <= 0 or size.y <= 0:

		update_position_and_size()
		return  # 等待下一帧重绘

	# 检查技能框控件是否有效尺寸
	if skill_1_box.size.x <= 0 or skill_1_box.size.y <= 0 or skill_2_box.size.x <= 0 or skill_2_box.size.y <= 0:

		update_position_and_size()
		return  # 等待下一帧重绘

	# 获取正确的按键显示文本
	var key_1_text = "B"
	var key_2_text = "C"

	# 绘制技能框1
	var skill_1 = get_skill(skill_1_index)
	draw_skill_box(skill_1_box.position, skill_1_box.size.x, key_1_text, skill_1)

	# 绘制技能框2
	var skill_2 = get_skill(skill_2_index)
	draw_skill_box(skill_2_box.position, skill_2_box.size.x, key_2_text, skill_2)

# 用于跟踪是否已经显示过警告
@onready var _has_shown_warning = false

func get_skill(index: int) -> Skill:
	if not player:
		return null

	# 检查player是否有equipped_skill_1和equipped_skill_2属性
	if index == 0 and "equipped_skill_1" in player:
		return player.equipped_skill_1
	elif index == 1 and "equipped_skill_2" in player:
		return player.equipped_skill_2

	# 兼容旧版本：检查player是否有active_skills属性
	if not "active_skills" in player:
		# 只显示一次警告
		if not _has_shown_warning:
			push_warning("player对象没有active_skills属性")
			_has_shown_warning = true
		return null

	# 检查active_skills是否为数组
	if not player.active_skills is Array:
		push_warning("player.active_skills不是数组")
		return null

	# 检查索引是否有效
	if index < 0 or index >= player.active_skills.size():
		# 不打印警告，因为这是正常情况（玩家可能没有装备所有技能槽）
		return null

	# 检查对应索引的技能是否为Skill类型
	var skill = player.active_skills[index]
	if not skill is Skill:
		push_warning("索引 " + str(index) + " 处的对象不是Skill类型")
		return null

	return skill

func draw_skill_box(pos: Vector2, box_size: float, key_text: String, skill: Skill) -> void:
	# 确保box_size为合理的正值
	if box_size <= 0:
		box_size = 100.0  # 使用较大的默认值，与最小尺寸一致

	# 检查字体是否可用
	if not font:
		font = ThemeDB.fallback_font
		if not font:
			# 如果仍然无法获取字体，则跳过绘制文本
			return

	var corner_radius = max(box_size * 0.08, 1.0)  # 圆角半径，至少为1像素
	var rect = Rect2(pos, Vector2(box_size, box_size))

	# 基础颜色 - 设置为完全透明
	var bg_color = Color(0.2, 0.2, 0.2, 0.0)  # 透明背景
	var _border_color = Color(0.5, 0.5, 0.5, 0.0)  # 透明边框（未使用，添加下划线前缀）
	var text_color = Color(1, 1, 1, 1)  # 技能文本颜色保持白色

	# 如果有技能，使用技能专用颜色
	if skill:
		bg_color = Color(0.2, 0.2, 0.3, 0.0)  # 透明背景
		_border_color = Color(0.4, 0.4, 0.9, 0.0)  # 透明边框（未使用，添加下划线前缀）

	# 绘制技能框背景（圆角矩形） - 现在是透明的
	draw_rounded_rectangle(rect, corner_radius, bg_color)

	# 绘制技能图标 - 修改为填满整个技能框
	if skill and skill.icon:
		# 使用整个框作为图标区域，不再使用边距
		var icon_margin = max(box_size * 0.05, 1.0)  # 非常小的边距，几乎不可见
		var icon_size = box_size - icon_margin * 2

		# 只有当图标大小合理时才绘制
		if icon_size > 0:
			var center_x = pos.x + box_size / 2
			var center_y = pos.y + box_size / 2
			var radius = icon_size / 2

			# 使用圆形蒙版绘制技能图标
			# 首先绘制圆形背景（可选）
			draw_circle(Vector2(center_x, center_y), radius, Color(0.1, 0.1, 0.1, 0.2))

			# 绘制图标本身
			var icon_rect = Rect2(
				pos.x + icon_margin,
				pos.y + icon_margin,
				icon_size,
				icon_size
			)

			# 检查是否是已使用的BurningSpiritSkill
			var is_used_burning_spirit = skill is BurningSpiritSkill and skill.has_method("is_used") and skill.is_used()

			# 使用圆形裁剪方式绘制图标
			if is_used_burning_spirit:
				# 如果是已使用的BurningSpiritSkill，使用灰色蒙版
				draw_texture_rect(skill.icon, icon_rect, false, Color(0.5, 0.5, 0.5, 0.5))

				# 绘制灰色蒙版
				draw_circle(Vector2(center_x, center_y), radius, Color(0.2, 0.2, 0.2, 0.7))

				# 绘制禁用标志（X）
				var line_width = max(box_size * 0.03, 2.0)
				var line_length = radius * 1.4
				draw_line(
					Vector2(center_x - line_length/2, center_y - line_length/2),
					Vector2(center_x + line_length/2, center_y + line_length/2),
					Color(0.8, 0.2, 0.2, 0.8),
					line_width
				)
				draw_line(
					Vector2(center_x + line_length/2, center_y - line_length/2),
					Vector2(center_x - line_length/2, center_y + line_length/2),
					Color(0.8, 0.2, 0.2, 0.8),
					line_width
				)
			else:
				# 正常绘制图标
				draw_texture_rect(skill.icon, icon_rect, false)

	# 绘制冷却遮罩 - 修改为圆形遮罩
	if skill and skill.is_on_cooldown:
		var cooldown_percent = skill.get_cooldown_percent()
		cooldown_percent = clamp(cooldown_percent, 0.0, 1.0)  # 确保百分比在有效范围内

		# 计算圆心和半径
		var center_x = pos.x + box_size / 2
		var center_y = pos.y + box_size / 2
		var radius = (box_size - 2) / 2  # 减小一点点以避免边缘锯齿

		# 绘制冷却遮罩为半透明圆形
		if cooldown_percent > 0:
			# 使用扇形表示冷却进度
			var start_angle = -PI/2  # 从顶部开始
			var end_angle = start_angle + 2 * PI * cooldown_percent  # 根据冷却百分比计算结束角度

			# 绘制扇形冷却遮罩
			draw_circle_arc_poly(Vector2(center_x, center_y), radius, start_angle, end_angle, Color(0.1, 0.1, 0.1, 0.7))

	# 绘制伪装技能持续时间遮罩（如果是DisguiseSkill且处于激活状态）- 修改为圆环
	if skill is DisguiseSkill and skill.is_disguised():
		var duration_percent = skill.get_active_percent()
		duration_percent = clamp(duration_percent, 0.0, 1.0)

		if duration_percent > 0:
			# 计算圆心和半径
			var center_x = pos.x + box_size / 2
			var center_y = pos.y + box_size / 2
			var radius = (box_size - 2) / 2  # 减小一点点以避免边缘锯齿

			# 使用圆环表示持续时间
			var arc_width = max(box_size * 0.05, 3.0)  # 圆环宽度
			var start_angle = -PI/2  # 从顶部开始
			var end_angle = start_angle + 2 * PI * duration_percent  # 根据持续时间百分比计算结束角度

			# 绘制圆环持续时间指示器
			draw_circle_arc(Vector2(center_x, center_y), radius, start_angle, end_angle, Color(0.2, 0.7, 0.3, 0.8), arc_width)

	# 不再绘制边框 (移除边框绘制代码)

	# 安全计算文本尺寸并绘制 - 保留此功能
	var key_font_size = max(box_size * 0.2, 8.0)  # 确保字体大小至少为8像素
	if key_font_size > 0 and key_text and key_text.length() > 0 and font:
		# 尝试绘制技能键位文本
		var key_text_pos = Vector2(
			pos.x + box_size * 0.9,
			pos.y + box_size * 0.2
		)
		# 使用安全调用
		draw_string(font, key_text_pos, key_text, HORIZONTAL_ALIGNMENT_CENTER, -1, key_font_size, text_color)

	# 如果技能在冷却中，显示剩余冷却时间 - 保留此功能
	if skill and skill.is_on_cooldown and skill.cooldown_remaining > 0 and font:
		var cooldown_text = "%.1f" % skill.cooldown_remaining
		var cd_font_size = max(box_size * 0.3, 10.0)  # 确保冷却字体大小合理

		if cd_font_size > 0:
			# 安全绘制冷却文本
			var cd_text_size
			# 安全获取文本尺寸
			cd_text_size = font.get_string_size(cooldown_text, HORIZONTAL_ALIGNMENT_CENTER, -1, cd_font_size)
			if cd_text_size:
				var cd_text_pos = pos + Vector2(box_size/2 - cd_text_size.x/2, box_size/2)
				draw_string(font, cd_text_pos, cooldown_text, HORIZONTAL_ALIGNMENT_CENTER, -1, cd_font_size, text_color)

	# 如果是伪装技能且处于激活状态，显示剩余持续时间 - 保留此功能
	if skill is DisguiseSkill and skill.is_disguised() and font:
		var duration_text = "%.1f" % skill.get_disguise_time_remaining()
		var duration_font_size = max(box_size * 0.3, 10.0)

		if duration_font_size > 0:
			# 安全绘制持续时间文本
			var duration_text_size = font.get_string_size(duration_text, HORIZONTAL_ALIGNMENT_CENTER, -1, duration_font_size)
			if duration_text_size:
				var duration_text_pos = pos + Vector2(box_size/2 - duration_text_size.x/2, box_size/2)
				# 使用绿色文本显示持续时间
				draw_string(font, duration_text_pos, duration_text, HORIZONTAL_ALIGNMENT_CENTER, -1, duration_font_size, Color(0.2, 1.0, 0.3, 1.0))

# 绘制圆角矩形
func draw_rounded_rectangle(rect: Rect2, radius: float, color: Color) -> void:
	# 绘制圆角矩形的中心部分
	var center_rect = Rect2(
		rect.position.x + radius,
		rect.position.y,
		rect.size.x - 2 * radius,
		rect.size.y
	)
	draw_rect(center_rect, color)

	# 绘制左右两侧部分
	var left_rect = Rect2(
		rect.position.x,
		rect.position.y + radius,
		radius,
		rect.size.y - 2 * radius
	)
	draw_rect(left_rect, color)

	var right_rect = Rect2(
		rect.position.x + rect.size.x - radius,
		rect.position.y + radius,
		radius,
		rect.size.y - 2 * radius
	)
	draw_rect(right_rect, color)

	# 绘制四个角落的圆弧
	# 左上角
	draw_circle(Vector2(rect.position.x + radius, rect.position.y + radius), radius, color)
	# 右上角
	draw_circle(Vector2(rect.position.x + rect.size.x - radius, rect.position.y + radius), radius, color)
	# 左下角
	draw_circle(Vector2(rect.position.x + radius, rect.position.y + rect.size.y - radius), radius, color)
	# 右下角
	draw_circle(Vector2(rect.position.x + rect.size.x - radius, rect.position.y + rect.size.y - radius), radius, color)

# 绘制圆角矩形边框
func draw_rounded_rectangle_outline(rect: Rect2, radius: float, color: Color, width: float) -> void:
	# 上边线
	draw_line(Vector2(rect.position.x + radius, rect.position.y),
		Vector2(rect.position.x + rect.size.x - radius, rect.position.y), color, width)

	# 下边线
	draw_line(Vector2(rect.position.x + radius, rect.position.y + rect.size.y),
		Vector2(rect.position.x + rect.size.x - radius, rect.position.y + rect.size.y), color, width)

	# 左边线
	draw_line(Vector2(rect.position.x, rect.position.y + radius),
		Vector2(rect.position.x, rect.position.y + rect.size.y - radius), color, width)

	# 右边线
	draw_line(Vector2(rect.position.x + rect.size.x, rect.position.y + radius),
		Vector2(rect.position.x + rect.size.x, rect.position.y + rect.size.y - radius), color, width)

	# 四个角落的圆弧
	# 左上角
	draw_arc(Vector2(rect.position.x + radius, rect.position.y + radius), radius, PI, 1.5 * PI, 16, color, width)
	# 右上角
	draw_arc(Vector2(rect.position.x + rect.size.x - radius, rect.position.y + radius), radius, 1.5 * PI, 2 * PI, 16, color, width)
	# 左下角
	draw_arc(Vector2(rect.position.x + radius, rect.position.y + rect.size.y - radius), radius, 0.5 * PI, PI, 16, color, width)
	# 右下角
	draw_arc(Vector2(rect.position.x + rect.size.x - radius, rect.position.y + rect.size.y - radius), radius, 0, 0.5 * PI, 16, color, width)

# 立即强制初始化
func force_initialization() -> void:

	# 确保游戏未暂停
	get_tree().paused = false

	# 查找玩家
	find_player()
	if not player:
		call_deferred("force_initialization")
		return

	# 创建技能框
	create_skill_boxes()

	# 更新位置和大小
	update_position_and_size()

	# 标记为已初始化
	is_initialized = true

	# 确保可见
	visible = true

	# 强制重绘
	queue_redraw()

	# 启动监视
	start_player_monitor()


# 设置玩家路径的辅助方法
func set_player_path(path: NodePath) -> void:
	player_path = path

	# 如果已经初始化，重新查找玩家
	if is_initialized:
		find_player()
		queue_redraw()

# 新增：绘制圆弧的辅助函数 (添加到类中的新函数)
func draw_circle_arc(center: Vector2, radius: float, start_angle: float, end_angle: float, color: Color, width: float = 1.0) -> void:
	var nb_points = 32  # 弧线的平滑度
	var points_arc = PackedVector2Array()

	for i in range(nb_points + 1):
		var angle_point = start_angle + i * (end_angle - start_angle) / nb_points
		points_arc.push_back(center + Vector2(cos(angle_point), sin(angle_point)) * radius)

	for i in range(nb_points):
		draw_line(points_arc[i], points_arc[i + 1], color, width)

# 新增：绘制填充扇形的辅助函数 (添加到类中的新函数)
func draw_circle_arc_poly(center: Vector2, radius: float, start_angle: float, end_angle: float, color: Color) -> void:
	var nb_points = 32  # 弧线的平滑度
	var points_arc = PackedVector2Array()

	points_arc.push_back(center)  # 添加圆心作为第一个点

	for i in range(nb_points + 1):
		var angle_point = start_angle + i * (end_angle - start_angle) / nb_points
		points_arc.push_back(center + Vector2(cos(angle_point), sin(angle_point)) * radius)

	draw_colored_polygon(points_arc, color)
