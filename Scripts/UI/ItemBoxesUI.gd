extends Control

# 配置选项
@export var item_box_size: float = 112.5  # 道具框大小（默认为头像大小的0.75倍）
@export var item_box_color: Color = Color(0.15, 0.15, 0.25, 0.8)  # 道具框背景颜色
@export var item_box_border_color: Color = Color(0.4, 0.4, 0.7, 0.8)  # 道具框边框颜色
@export var item_text_color: Color = Color(1, 1, 1, 1)  # 道具文本颜色
@export var item_box_corner_radius: int = 8  # 道具框圆角半径
@export var show_item_names: bool = false  # 默认不显示道具名称

var font: Font
var is_initialized := false
var items: Array = [null, null]  # 当前显示的道具列表

func _ready() -> void:
	# 确保控件可见
	visible = true
	show()

	# 加载默认字体
	font = ThemeDB.fallback_font

	# 设置锚点和边距，确保控件始终位于左上角
	anchors_preset = Control.PRESET_TOP_LEFT

	# 设置初始位置和大小
	update_position_and_size()

	# 连接信号，处理窗口大小改变
	get_tree().root.size_changed.connect(_on_window_size_changed)

	# 添加到分组，便于查找
	add_to_group("item_boxes_ui")
	add_to_group("item_slots")  # 添加到item_slots组，用于图标飞行动画

	is_initialized = true
	queue_redraw()

func _on_window_size_changed() -> void:
	update_position_and_size()
	queue_redraw()

func update_position_and_size() -> void:
	# 获取视口大小
	var viewport_size = get_viewport_rect().size

	# 计算缩放比例（基于屏幕高度）
	var scale_factor = viewport_size.y / 1080.0  # 1080p作为基准分辨率

	# 计算道具框大小
	var new_size = item_box_size * scale_factor

	# 确保道具框不会太大
	var max_size = min(viewport_size.x * 0.25, viewport_size.y * 0.25)
	new_size = min(new_size, max_size)

	# 计算两个道具框之间的间距
	var spacing = new_size * 0.3

	# 设置控件大小
	custom_minimum_size = Vector2(new_size * 2 + spacing, new_size)
	size = custom_minimum_size

	# 放置在左上角
	var margin = 20.0 * scale_factor
	position = Vector2(margin, margin)

func _draw() -> void:
	if not is_initialized:
		return

	# 计算道具框大小和间距
	var box_size = size.y
	var spacing = box_size * 0.3

	# 绘制第一个道具框（道具1）
	draw_item_box(Vector2(0, 0), box_size, "1", 0)

	# 绘制第二个道具框（道具2）
	draw_item_box(Vector2(box_size + spacing, 0), box_size, "2", 1)

# 绘制单个道具框
func draw_item_box(pos: Vector2, box_size: float, _text: String, item_index: int) -> void:
	var rect = Rect2(pos, Vector2(box_size, box_size))

	# 绘制道具框背景（圆角矩形）
	draw_rounded_rectangle(rect, item_box_corner_radius, item_box_color)

	# 绘制道具框边框
	draw_rounded_rectangle_outline(rect, item_box_corner_radius, item_box_border_color, 2.0)

	# 如果有道具，绘制道具图标
	if item_index < items.size() and items[item_index] != null:
		var item = items[item_index]

		# 绘制道具图标（如果有）
		if item.icon:
			var icon_size = box_size * 0.7
			var icon_rect = Rect2(
				pos.x + (box_size - icon_size) / 2,
				pos.y + (box_size - icon_size) / 2,
				icon_size,
				icon_size
			)
			draw_texture_rect(item.icon, icon_rect, false)

			# 如果道具在冷却中，绘制冷却遮罩
			if "is_on_cooldown" in item and item.is_on_cooldown:
				var cooldown_percent = item.get_cooldown_percent()

				# 绘制半透明灰色遮罩
				var cooldown_color = Color(0.3, 0.3, 0.3, 0.7)

				# 创建一个遮罩矩形，高度根据冷却百分比调整
				var mask_height = icon_rect.size.y * cooldown_percent
				var mask_rect = Rect2(
					icon_rect.position.x,
					icon_rect.position.y + icon_rect.size.y - mask_height,
					icon_rect.size.x,
					mask_height
				)

				# 绘制遮罩
				draw_rect(mask_rect, cooldown_color)

				# 显示冷却百分比文本
				if cooldown_percent > 0.1:  # 只有当冷却足够明显时才显示文本
					var cooldown_text = str(int(cooldown_percent * 100)) + "%"
					var font_size = box_size * 0.2
					var text_pos = Vector2(
						icon_rect.position.x + icon_rect.size.x / 2,
						icon_rect.position.y + icon_rect.size.y / 2
					)
					draw_string(font, text_pos, cooldown_text, HORIZONTAL_ALIGNMENT_CENTER, -1, font_size, Color(1, 1, 1, 0.9))
		else:
			# 如果没有图标，显示警告文本
			var font_size = box_size * 0.2
			var warning_text = "无图标"
			var warning_pos = Vector2(
				pos.x + box_size / 2,
				pos.y + box_size / 2
			)
			draw_string(font, warning_pos, warning_text, HORIZONTAL_ALIGNMENT_CENTER, -1, font_size, Color(1, 0, 0, 1))

		# 如果是一次性道具且已使用，显示"已用"标记
		if "is_one_time_use" in item and item.is_one_time_use and "was_used" in item and item.was_used:
			var used_text = "已用"
			var font_size = box_size * 0.2
			var used_pos = Vector2(
				pos.x + box_size / 2,
				pos.y + box_size * 0.8
			)
			draw_string(font, used_pos, used_text, HORIZONTAL_ALIGNMENT_CENTER, -1, font_size, Color(1, 0, 0, 0.9))

		# 如果是多次使用的道具，显示剩余使用次数角标
		elif not "is_one_time_use" in item or not item.is_one_time_use:
			if "uses_remaining" in item.get_info() and "max_uses" in item.get_info():
				var uses = item.get_info()["uses_remaining"]
				var max_uses = item.get_info()["max_uses"]

				# 只有当道具有多次使用且剩余次数小于最大次数时才显示角标
				if max_uses > 1:
					# 创建角标文本
					var text = str(uses)
					var font_size = box_size * 0.25

					# 计算角标位置（右下角）
					var badge_pos = Vector2(
						pos.x + box_size * 0.75,
						pos.y + box_size * 0.75
					)

					# 绘制角标背景
					var text_size = font.get_string_size(text, HORIZONTAL_ALIGNMENT_CENTER, -1, font_size)
					var badge_size = text_size + Vector2(10, 5)
					var badge_rect = Rect2(badge_pos, badge_size)
					draw_rect(badge_rect, Color(0, 0, 0, 0.7), true)

					# 绘制角标文本
					var text_pos = badge_pos + Vector2(badge_size.x / 2, badge_size.y * 0.7)
					draw_string(font, text_pos, text, HORIZONTAL_ALIGNMENT_CENTER, -1, font_size, Color(1, 1, 1, 1))

	else:
		# 如果没有道具，绘制空槽位提示（可选）
		var slot_text = str(item_index + 1)  # "1" 或 "2"
		var font_size = box_size * 0.15
		var slot_position = Vector2(
			pos.x + box_size * 0.85,
			pos.y + box_size * 0.2
		)
		draw_string(font, slot_position, slot_text, HORIZONTAL_ALIGNMENT_RIGHT, -1, font_size, Color(0.6, 0.6, 0.6, 0.5))

# 更新道具显示
func update_items(new_items: Array) -> void:
	# 清空当前道具列表
	items = [null, null]

	# 检查每个图标是否有效
	for i in range(new_items.size()):
		var item = new_items[i]
		if item and item.icon:
			if item.icon.get_width() <= 0 or item.icon.get_height() <= 0:
				# 尝试重新加载图标
				item.icon = load("res://icon/item_icon/gas.png")

	# 添加新道具
	for i in range(min(new_items.size(), 2)):
		if i < new_items.size() and new_items[i] != null:
			items[i] = new_items[i]

	# 重绘UI
	queue_redraw()

# 检查指定槽位是否有道具
func has_item(slot: int = 0) -> bool:
	if slot < 0 or slot >= items.size():
		return false
	return items[slot] != null

# 显示道具（用于图标飞行动画完成后）
func show_item(item: Object, slot: int = -1) -> void:
	if not item:
		return

	# 如果没有指定槽位，找到第一个空槽位
	if slot < 0:
		for i in range(items.size()):
			if items[i] == null:
				slot = i
				break

	# 如果仍然没有找到槽位，使用第一个槽位
	if slot < 0:
		slot = 0

	# 确保槽位有效
	if slot >= items.size():
		return

	# 设置道具
	items[slot] = item

	# 重绘UI
	queue_redraw()

	# 播放一个简单的动画效果
	var tween = create_tween()
	tween.tween_property(self, "modulate", Color(1.5, 1.5, 1.5, 1), 0.2)
	tween.tween_property(self, "modulate", Color(1, 1, 1, 1), 0.3)

# 绘制圆角矩形
func draw_rounded_rectangle(rect: Rect2, radius: float, color: Color) -> void:
	# 绘制圆角矩形的中心部分
	var center_rect = Rect2(
		rect.position.x + radius,
		rect.position.y,
		rect.size.x - 2 * radius,
		rect.size.y
	)
	draw_rect(center_rect, color)

	# 绘制左右两侧部分
	var left_rect = Rect2(
		rect.position.x,
		rect.position.y + radius,
		radius,
		rect.size.y - 2 * radius
	)
	draw_rect(left_rect, color)

	var right_rect = Rect2(
		rect.position.x + rect.size.x - radius,
		rect.position.y + radius,
		radius,
		rect.size.y - 2 * radius
	)
	draw_rect(right_rect, color)

	# 绘制四个角落的圆弧
	# 左上角
	draw_circle(Vector2(rect.position.x + radius, rect.position.y + radius), radius, color)
	# 右上角
	draw_circle(Vector2(rect.position.x + rect.size.x - radius, rect.position.y + radius), radius, color)
	# 左下角
	draw_circle(Vector2(rect.position.x + radius, rect.position.y + rect.size.y - radius), radius, color)
	# 右下角
	draw_circle(Vector2(rect.position.x + rect.size.x - radius, rect.position.y + rect.size.y - radius), radius, color)

# 绘制圆角矩形边框
func draw_rounded_rectangle_outline(rect: Rect2, radius: float, color: Color, width: float) -> void:
	# 暂时简化为直接绘制4条线和4个圆弧

	# 上边线
	draw_line(Vector2(rect.position.x + radius, rect.position.y),
		Vector2(rect.position.x + rect.size.x - radius, rect.position.y), color, width)

	# 下边线
	draw_line(Vector2(rect.position.x + radius, rect.position.y + rect.size.y),
		Vector2(rect.position.x + rect.size.x - radius, rect.position.y + rect.size.y), color, width)

	# 左边线
	draw_line(Vector2(rect.position.x, rect.position.y + radius),
		Vector2(rect.position.x, rect.position.y + rect.size.y - radius), color, width)

	# 右边线
	draw_line(Vector2(rect.position.x + rect.size.x, rect.position.y + radius),
		Vector2(rect.position.x + rect.size.x, rect.position.y + rect.size.y - radius), color, width)

	# 四个角落的圆弧
	# 左上角
	draw_arc(Vector2(rect.position.x + radius, rect.position.y + radius), radius, PI, 1.5 * PI, 16, color, width)
	# 右上角
	draw_arc(Vector2(rect.position.x + rect.size.x - radius, rect.position.y + radius), radius, 1.5 * PI, 2 * PI, 16, color, width)
	# 左下角
	draw_arc(Vector2(rect.position.x + radius, rect.position.y + rect.size.y - radius), radius, 0.5 * PI, PI, 16, color, width)
	# 右下角
	draw_arc(Vector2(rect.position.x + rect.size.x - radius, rect.position.y + rect.size.y - radius), radius, 0, 0.5 * PI, 16, color, width)
