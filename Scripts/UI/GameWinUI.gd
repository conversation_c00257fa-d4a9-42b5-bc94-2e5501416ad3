extends CanvasLayer

# 场景路径
var level_select_path := "res://Scenes/UI/LevelSelect.tscn"

# 节点引用
@onready var game_win_panel := $GameWinPanel
@onready var blur_rect := $BlurRect
@onready var game_win_title := $GameWinPanel/VBoxContainer/GameWinTitle
@onready var statistics_label := $GameWinPanel/VBoxContainer/StatisticsLabel
@onready var tips_label := $GameWinPanel/VBoxContainer/TipsLabel
@onready var restart_button := $GameWinPanel/VBoxContainer/ButtonsContainer/RestartButton
@onready var main_menu_button := $GameWinPanel/VBoxContainer/ButtonsContainer/MainMenuButton

# 动画控制
var title_tween: Tween

func _ready() -> void:
	# 初始隐藏界面
	game_win_panel.visible = false
	blur_rect.visible = false

	# 连接按钮信号
	restart_button.pressed.connect(_on_restart_button_pressed)
	main_menu_button.pressed.connect(_on_main_menu_button_pressed)

func show_game_win() -> void:
	# 暂停游戏
	get_tree().paused = true

	# 显示界面
	game_win_panel.visible = true
	blur_rect.visible = true

	# 创建标题晃动动画
	_create_title_animation()

	# 显示鼠标
	Input.mouse_mode = Input.MOUSE_MODE_VISIBLE

func _create_title_animation() -> void:
	if title_tween:
		title_tween.kill()

	title_tween = create_tween()
	title_tween.set_loops()
	title_tween.set_trans(Tween.TRANS_SINE)

	# 创建轻微晃动动画
	var original_position = game_win_title.position
	title_tween.tween_property(game_win_title, "position", original_position + Vector2(5, 2), 0.3)
	title_tween.tween_property(game_win_title, "position", original_position - Vector2(5, 2), 0.3)
	title_tween.tween_property(game_win_title, "position", original_position, 0.3)

func _on_restart_button_pressed() -> void:
	# 隐藏游戏胜利面板
	game_win_panel.visible = false
	blur_rect.visible = false

	# 清理场景中的小地图节点，防止重新加载场景时出现问题
	var minimaps = get_tree().get_nodes_in_group("minimap")
	for minimap in minimaps:
		if is_instance_valid(minimap):
			minimap.queue_free()

	# 等待一小段时间确保节点被正确清理
	await get_tree().create_timer(0.1).timeout

	# 恢复游戏时间
	get_tree().paused = false

	# 重新加载当前场景
	var current_scene = get_tree().current_scene.scene_file_path
	get_tree().change_scene_to_file(current_scene)

func _on_main_menu_button_pressed() -> void:
	# 隐藏游戏胜利面板
	game_win_panel.visible = false
	blur_rect.visible = false

	# 清理场景中的小地图节点，防止重新加载场景时出现问题
	var minimaps = get_tree().get_nodes_in_group("minimap")
	for minimap in minimaps:
		if is_instance_valid(minimap):
			minimap.queue_free()

	# 等待一小段时间确保节点被正确清理
	await get_tree().create_timer(0.1).timeout

	# 恢复游戏时间
	get_tree().paused = false

	# 切换到关卡选择页面
	if ResourceLoader.exists(level_select_path):
		get_tree().change_scene_to_file(level_select_path)
	else:
		push_error("找不到关卡选择页面场景")
		# 作为后备方案，尝试使用重启逻辑
		_on_restart_button_pressed()
