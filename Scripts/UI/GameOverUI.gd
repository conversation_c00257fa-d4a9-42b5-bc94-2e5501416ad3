extends CanvasLayer

# 时间控制
@export var game_duration := 30.0  # 游戏持续时间（秒）
var time_remaining := 0.0
var timer_active := false

# 场景路径
var level_select_path := "res://Scenes/UI/LevelSelect.tscn"

# 节点引用
@onready var game_over_panel := $GameOverPanel
@onready var blur_rect := $BlurRect
@onready var game_over_title := $GameOverPanel/VBoxContainer/GameOverTitle
@onready var statistics_label := $GameOverPanel/VBoxContainer/StatisticsLabel
@onready var tips_label := $GameOverPanel/VBoxContainer/TipsLabel
@onready var restart_button := $GameOverPanel/VBoxContainer/ButtonsContainer/RestartButton
@onready var main_menu_button := $GameOverPanel/VBoxContainer/ButtonsContainer/MainMenuButton

# 动画控制
var title_tween: Tween

func _ready() -> void:
	# 初始隐藏界面
	game_over_panel.visible = false
	blur_rect.visible = false

	# 设置初始倒计时
	time_remaining = game_duration
	timer_active = true

	# 连接按钮信号
	restart_button.pressed.connect(_on_restart_button_pressed)
	main_menu_button.pressed.connect(_on_main_menu_button_pressed)

func _process(delta: float) -> void:
	if timer_active and game_duration > 0:  # 只有当游戏时长大于0时才倒计时
		time_remaining -= delta
		if time_remaining <= 0:
			show_game_over()  # 使用默认参数，根据game_duration决定是否暂停

# 设置游戏时长（秒）
func set_game_duration(duration: float) -> void:
	game_duration = duration
	time_remaining = duration

	# 当时长设置为0时，计时器依然激活，但不会倒计时
	timer_active = true

# 启动或重启计时器
func start_timer() -> void:
	time_remaining = game_duration
	timer_active = true

# 停止计时器
func stop_timer() -> void:
	timer_active = false

func show_game_over(force_pause: bool = false) -> void:
	# 当游戏时长设置大于0时或强制暂停时，暂停游戏
	if game_duration > 0 or force_pause:
		# 停止游戏时间
		get_tree().paused = true

	# 停止计时器
	timer_active = false

	# 显示界面
	game_over_panel.visible = true
	blur_rect.visible = true

	# 创建标题晃动动画
	_create_title_animation()

	# 显示鼠标
	Input.mouse_mode = Input.MOUSE_MODE_VISIBLE

func _create_title_animation() -> void:
	if title_tween:
		title_tween.kill()

	title_tween = create_tween()
	title_tween.set_loops()
	title_tween.set_trans(Tween.TRANS_SINE)

	# 创建轻微晃动动画
	var original_position = game_over_title.position
	title_tween.tween_property(game_over_title, "position", original_position + Vector2(5, 2), 0.3)
	title_tween.tween_property(game_over_title, "position", original_position - Vector2(5, 2), 0.3)
	title_tween.tween_property(game_over_title, "position", original_position, 0.3)

func _on_restart_button_pressed() -> void:

	# 隐藏游戏结束面板
	game_over_panel.visible = false
	blur_rect.visible = false

	# 恢复游戏时间（取消暂停）
	get_tree().paused = false

	# 设置一个过渡动画
	var animation_player = get_node_or_null("AnimationPlayer")
	if animation_player and animation_player.has_animation("fade_out"):
		animation_player.play("fade_out")
		await animation_player.animation_finished

	# 直接切换到加载屏幕，让它处理初始化和重启
	if ResourceLoader.exists("res://Scenes/UI/LoadingScreen.tscn"):
		# 切换到加载场景，加载场景会负责正确初始化游戏
		get_tree().change_scene_to_file("res://Scenes/UI/LoadingScreen.tscn")
	else:
		push_error("找不到加载屏幕场景")

		# 作为后备方案，尝试使用旧的重启逻辑
		_legacy_restart()

# 旧的重启逻辑作为后备方案
func _legacy_restart() -> void:

	# 重置所有技能状态
	var players = get_tree().get_nodes_in_group("player")
	for player in players:
		if player.has_method("reset_skills"):
			player.reset_skills()

	# 清理场景中的小地图节点和其他可能导致问题的节点
	var minimaps = get_tree().get_nodes_in_group("minimap")
	for minimap in minimaps:
		if is_instance_valid(minimap):
			minimap.queue_free()

	# 只标记技能UI而不销毁它们，保证它们在新场景中仍然存在
	var skill_uis = get_tree().get_nodes_in_group("skill_ui")
	for ui in skill_uis:
		if is_instance_valid(ui):
			ui.set_meta("needs_reinit", true)

	# 通过回调方式处理场景加载
	# 创建一个一次性的自动加载节点传递给下一个场景
	var restart_data = Node.new()
	restart_data.name = "GameRestartData"
	restart_data.set_meta("is_restart", true)
	restart_data.process_mode = Node.PROCESS_MODE_ALWAYS  # 确保不受暂停影响

	# 确保将这个节点添加到场景树的根节点
	var root = get_tree().root
	if root:
		root.add_child(restart_data)

	# 设置脚本
	var helper_script = load("res://Scripts/GameRestartHelper.gd")
	if helper_script:
		restart_data.set_script(helper_script)

	# 重新加载当前场景
	var current_scene = get_tree().current_scene.scene_file_path
	if current_scene:
		get_tree().change_scene_to_file(current_scene)

func _on_main_menu_button_pressed() -> void:
	# 隐藏游戏结束面板
	game_over_panel.visible = false
	blur_rect.visible = false

	# 恢复游戏时间（取消暂停）
	get_tree().paused = false

	# 设置一个过渡动画
	var animation_player = get_node_or_null("AnimationPlayer")
	if animation_player and animation_player.has_animation("fade_out"):
		animation_player.play("fade_out")
		await animation_player.animation_finished

	# 切换到关卡选择页面
	if ResourceLoader.exists(level_select_path):
		get_tree().change_scene_to_file(level_select_path)
	else:
		push_error("找不到关卡选择页面场景")
		# 作为后备方案，尝试使用重启逻辑
		_on_restart_button_pressed()
