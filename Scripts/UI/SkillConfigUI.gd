extends Control

# 定义信号
signal ui_closed

# 下拉菜单引用
@onready var skill_1_dropdown = $VBoxContainer/Skill1Container/Skill1Dropdown
@onready var skill_2_dropdown = $VBoxContainer/Skill2Container/Skill2Dropdown

# 技能图标和描述引用
@onready var skill_1_icon = $VBoxContainer/Skill1Container/Skill1Icon
@onready var skill_1_description = $VBoxContainer/Skill1Container/Skill1Description
@onready var skill_2_icon = $VBoxContainer/Skill2Container/Skill2Icon
@onready var skill_2_description = $VBoxContainer/Skill2Container/Skill2Description

# 技能ID到索引的映射
var skill_id_to_index = {}
var index_to_skill_id = {}

func _ready():
	# 初始化下拉菜单
	initialize_dropdowns()

	# 连接信号
	skill_1_dropdown.item_selected.connect(_on_skill_1_selected)
	skill_2_dropdown.item_selected.connect(_on_skill_2_selected)
	$Panel/CloseButton.pressed.connect(_on_close_button_pressed)
	$BackgroundDim.pressed.connect(_on_close_button_pressed)

	# 加载当前配置
	load_current_configuration()

	# 打印日志，表示技能配置界面已打开
	print("\n\n[SkillConfigUI] 技能配置界面已打开\n\n")

# 初始化下拉菜单
func initialize_dropdowns():
	# 获取SkillManager
	var skill_manager = get_node_or_null("/root/SkillManager")
	if not skill_manager:
		push_error("SkillManager单例不存在")
		return

	# 清空下拉菜单
	skill_1_dropdown.clear()
	skill_2_dropdown.clear()

	# 重置映射
	skill_id_to_index = {}
	index_to_skill_id = {}

	# 获取所有已解锁的技能
	var unlocked_skills = skill_manager.get_unlocked_skill_ids()

	# 添加一个"无技能"选项
	skill_1_dropdown.add_item("无技能")
	skill_2_dropdown.add_item("无技能")
	skill_id_to_index["none"] = 0
	index_to_skill_id[0] = "none"

	# 为每个已解锁的技能添加选项
	var index = 1
	for skill_id in unlocked_skills:
		var skill_name = skill_manager.get_skill_name(skill_id)

		# 添加到下拉菜单
		skill_1_dropdown.add_item(skill_name)
		skill_2_dropdown.add_item(skill_name)

		# 更新映射
		skill_id_to_index[skill_id] = index
		index_to_skill_id[index] = skill_id

		index += 1

# 加载当前配置
func load_current_configuration():
	var skill_manager = get_node_or_null("/root/SkillManager")
	if not skill_manager:
		return

	# 获取当前装备的技能
	var skill_1_id = skill_manager.equipped_skill_1_id
	var skill_2_id = skill_manager.equipped_skill_2_id

	# 设置下拉菜单选中项
	if skill_id_to_index.has(skill_1_id):
		skill_1_dropdown.select(skill_id_to_index[skill_1_id])
		update_skill_1_info(skill_1_id)
	else:
		skill_1_dropdown.select(0)  # 选择"无技能"
		update_skill_1_info("none")

	if skill_id_to_index.has(skill_2_id):
		skill_2_dropdown.select(skill_id_to_index[skill_2_id])
		update_skill_2_info(skill_2_id)
	else:
		skill_2_dropdown.select(0)  # 选择"无技能"
		update_skill_2_info("none")

# 当技能1下拉菜单选择改变时
func _on_skill_1_selected(index):
	var skill_id = index_to_skill_id[index]

	# 更新SkillManager
	var skill_manager = get_node_or_null("/root/SkillManager")
	if skill_manager:
		if skill_id == "none":
			# 如果选择了"无技能"，则清空技能槽
			skill_manager.equipped_skill_1_id = ""
		else:
			skill_manager.equip_skill_1(skill_id)

	# 更新技能信息显示
	update_skill_1_info(skill_id)

# 当技能2下拉菜单选择改变时
func _on_skill_2_selected(index):
	var skill_id = index_to_skill_id[index]

	# 更新SkillManager
	var skill_manager = get_node_or_null("/root/SkillManager")
	if skill_manager:
		if skill_id == "none":
			# 如果选择了"无技能"，则清空技能槽
			skill_manager.equipped_skill_2_id = ""
		else:
			skill_manager.equip_skill_2(skill_id)

	# 更新技能信息显示
	update_skill_2_info(skill_id)

# 更新技能1信息显示
func update_skill_1_info(skill_id):
	var skill_manager = get_node_or_null("/root/SkillManager")
	if not skill_manager:
		return

	if skill_id == "none":
		# 清空显示
		skill_1_icon.texture = null
		skill_1_description.text = ""
	else:
		# 更新图标和描述
		skill_1_icon.texture = skill_manager.get_skill_icon(skill_id)
		skill_1_description.text = skill_manager.get_skill_description(skill_id)

# 更新技能2信息显示
func update_skill_2_info(skill_id):
	var skill_manager = get_node_or_null("/root/SkillManager")
	if not skill_manager:
		return

	if skill_id == "none":
		# 清空显示
		skill_2_icon.texture = null
		skill_2_description.text = ""
	else:
		# 更新图标和描述
		skill_2_icon.texture = skill_manager.get_skill_icon(skill_id)
		skill_2_description.text = skill_manager.get_skill_description(skill_id)

# 应用按钮点击
func _on_apply_button_pressed():
	# 技能配置已经在选择时保存到SkillManager中
	# 如果玩家已经存在，则立即初始化技能
	var player = get_tree().get_first_node_in_group("player")
	if player and player.has_method("initialize_skills"):
		player.initialize_skills()

	# 在控制台输出当前技能配置，便于调试
	var skill_manager = get_node_or_null("/root/SkillManager")
	if skill_manager:
		print("\n\n当前技能配置:")
		print("\u6280能1: " + skill_manager.equipped_skill_1_id)
		print("\u6280能2: " + skill_manager.equipped_skill_2_id + "\n\n")

	# 显示确认消息
	$ConfirmationLabel.text = "技能配置已应用！"
	$ConfirmationLabel.visible = true

	# 3秒后隐藏确认消息
	var timer = get_tree().create_timer(3.0)
	timer.timeout.connect(func(): $ConfirmationLabel.visible = false)

# 关闭按钮点击
func _on_close_button_pressed():
	# 打印日志，表示技能配置界面即将关闭
	print("\n\n[SkillConfigUI] 技能配置界面即将关闭\n\n")

	# 发送关闭信号
	emit_signal("ui_closed")

	# 隐藏配置界面
	visible = false

	# 确保不再处理输入
	set_process_input(false)

	# 将自身从场景树中移除
	queue_free()
