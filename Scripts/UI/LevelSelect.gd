extends Control

# 场景路径
@export var main_menu_path: String = "res://Scenes/UI/MainMenu.tscn"
@export var loading_screen_path: String = "res://Scenes/UI/LoadingScreen.tscn"

# 关卡配置
var level_configs = {
	"level_01": {
		"name": "第一关",
		"description": "基础关卡",
		"path": "res://Scenes/levels/Level01.tscn",
		"config_path": "res://Resources/LevelConfigs/level_01_config.tres"
	},
	"level_02": {
		"name": "第二关",
		"description": "进阶挑战",
		"path": "res://Scenes/levels/Level02.tscn",
		"config_path": "res://Resources/LevelConfigs/level_01_config.tres"  # 使用相同配置
	},
	"demo_level": {
		"name": "演示关卡",
		"description": "包含所有功能",
		"path": "res://Scenes/levels/Level01.tscn",  # 使用level_01场景
		"config_path": "res://Resources/LevelConfigs/demo_level.tres"
	},
	"all_modules": {
		"name": "全模块关卡",
		"description": "包含所有生成器模块",
		"path": "res://Scenes/levels/Level01.tscn",  # 使用level_01场景
		"config_path": "res://Resources/LevelConfigs/all_modules_config.tres"
	}
}

# 节点引用
@onready var level1_button = $LevelsContainer/Level1Button
@onready var level2_button = $LevelsContainer/Level2Button
@onready var demo_level_button = $LevelsContainer/DemoLevelButton
@onready var all_modules_button = $LevelsContainer/AllModulesButton
@onready var back_button = $BackButton
@onready var skill_config_button = $SkillConfigButton
@onready var audio_player = $AudioStreamPlayer
@onready var button_sound = $ButtonSound

# 技能配置UI场景路径
@export var skill_config_ui_path: String = "res://Scenes/UI/SkillConfigUI.tscn"

# 技能配置UI实例
var skill_config_ui_instance = null

# 音效和音乐
var button_click_sound = preload("res://character/player/sounds/robot_jump.wav")
var menu_music_path = "res://character/player/sounds/robot_land.wav" # 临时使用，应替换为实际的菜单音乐

# 全局游戏数据
var selected_level_id: String = ""
var selected_level_config_path: String = ""

func _ready():
	# 连接按钮信号
	level1_button.pressed.connect(_on_level1_button_pressed)
	level2_button.pressed.connect(_on_level2_button_pressed)
	demo_level_button.pressed.connect(_on_demo_level_button_pressed)
	all_modules_button.pressed.connect(_on_all_modules_button_pressed)
	back_button.pressed.connect(_on_back_button_pressed)
	skill_config_button.pressed.connect(_on_skill_config_button_pressed)

	# 设置按钮音效
	button_sound.stream = button_click_sound

	# 加载菜单音乐
	if ResourceLoader.exists(menu_music_path):
		var music = load(menu_music_path)
		if music:
			audio_player.stream = music
			audio_player.play()

# 播放按钮音效
func _play_button_sound():
	if button_sound and button_sound.stream:
		button_sound.play()

# 加载选定的关卡
func _load_selected_level():
	if selected_level_id.is_empty():
		push_error("未选择关卡")
		return

	# 保存选定的关卡信息到全局游戏数据
	var game_data = get_node_or_null("/root/GameData")
	if game_data:
		game_data.set_meta("selected_level_id", selected_level_id)
		game_data.set_meta("selected_level_config_path", selected_level_config_path)

	# 切换到加载屏幕
	if ResourceLoader.exists(loading_screen_path):
		get_tree().change_scene_to_file(loading_screen_path)
	else:
		push_error("找不到加载屏幕场景")

# 按钮事件处理
func _on_level1_button_pressed():
	_play_button_sound()
	selected_level_id = "level_01"
	selected_level_config_path = level_configs[selected_level_id]["config_path"]
	_load_selected_level()

func _on_level2_button_pressed():
	_play_button_sound()
	selected_level_id = "level_02"
	selected_level_config_path = level_configs[selected_level_id]["config_path"]
	_load_selected_level()

func _on_demo_level_button_pressed():
	_play_button_sound()
	selected_level_id = "demo_level"
	selected_level_config_path = level_configs[selected_level_id]["config_path"]
	_load_selected_level()

func _on_all_modules_button_pressed():
	_play_button_sound()
	selected_level_id = "all_modules"
	selected_level_config_path = level_configs[selected_level_id]["config_path"]
	_load_selected_level()

func _on_back_button_pressed():
	_play_button_sound()
	# 返回主菜单
	if ResourceLoader.exists(main_menu_path):
		get_tree().change_scene_to_file(main_menu_path)
	else:
		push_error("找不到主菜单场景")

# 技能配置按钮点击事件
func _on_skill_config_button_pressed():
	_play_button_sound()

	# 先清理所有现有的技能配置UI实例
	var existing_uis = get_tree().get_nodes_in_group("skill_config_ui")
	for ui in existing_uis:
		if is_instance_valid(ui):
			ui.queue_free()

	# 等待一帧，确保所有UI实例都被移除
	await get_tree().process_frame

	# 重置实例变量
	skill_config_ui_instance = null

	# 创建新的技能配置UI实例
	var skill_config_ui_scene = load(skill_config_ui_path)
	if skill_config_ui_scene:
		skill_config_ui_instance = skill_config_ui_scene.instantiate()
		# 添加到组，方便后续清理
		skill_config_ui_instance.add_to_group("skill_config_ui")
		# 连接关闭信号
		skill_config_ui_instance.ui_closed.connect(_on_skill_config_ui_closed)
		# 添加到场景树
		get_tree().root.add_child(skill_config_ui_instance)

		# 确保UI实例不会处理不相关的输入事件
		skill_config_ui_instance.set_process_input(true)
	else:
		push_error("无法加载技能配置UI场景：" + skill_config_ui_path)
		return

# 技能配置UI关闭信号处理
func _on_skill_config_ui_closed():
	# 重置skill_config_ui_instance变量
	skill_config_ui_instance = null

	# 确保所有技能配置UI实例都被移除
	var existing_uis = get_tree().get_nodes_in_group("skill_config_ui")
	for ui in existing_uis:
		if is_instance_valid(ui):
			ui.queue_free()

	print("所有技能配置UI实例已清理")
