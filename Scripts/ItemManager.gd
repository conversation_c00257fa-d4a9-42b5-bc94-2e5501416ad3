extends Node

# 所有可用道具的字典
var all_items = {
	"gas": preload("res://Resources/Items/GasItem.tres"),
	"energy_drink": preload("res://Resources/Items/EnergyDrink.tres"),
	"torch": preload("res://Resources/Items/Torch.tres"),
	"lightning_orb": preload("res://Resources/Items/LightningOrb.tres"),
	"trap": preload("res://Resources/Items/Trap.tres"),
	# 未来可以添加更多道具
}

# 当前已解锁的道具
var unlocked_items = {
	"gas": true,
	"energy_drink": true,
	"torch": true,
	"lightning_orb": true,
	"trap": true,
	# 未来可以添加更多道具
}

# 获取道具资源
func get_item_resource(item_id: String) -> Resource:
	if all_items.has(item_id):
		return all_items[item_id]
	return null

# 检查道具是否已解锁
func is_item_unlocked(item_id: String) -> bool:
	if unlocked_items.has(item_id):
		return unlocked_items[item_id]
	return false

# 解锁道具
func unlock_item(item_id: String) -> void:
	if all_items.has(item_id):
		unlocked_items[item_id] = true

# 获取道具名称
func get_item_name(item_id: String) -> String:
	var item = get_item_resource(item_id)
	if item:
		return item.item_name
	return "未知道具"

# 获取道具描述
func get_item_description(item_id: String) -> String:
	var item = get_item_resource(item_id)
	if item:
		return item.item_description
	return "无描述"

# 获取道具图标
func get_item_icon(item_id: String) -> Texture2D:
	var item = get_item_resource(item_id)
	if item:
		return item.icon
	return null

# 获取所有已解锁的道具ID列表
func get_unlocked_item_ids() -> Array:
	var result = []
	for item_id in unlocked_items.keys():
		if unlocked_items[item_id]:
			result.append(item_id)
	return result

# 连接到所有道具拾取物
func connect_to_all_pickups() -> void:
	var pickups = get_tree().get_nodes_in_group("item_pickup")
	for pickup in pickups:
		if pickup.has_signal("picked_up") and not pickup.is_connected("picked_up", _on_item_picked_up):
			pickup.connect("picked_up", _on_item_picked_up)

# 当道具被拾取时
func _on_item_picked_up(item_resource: Resource, player: Node) -> void:
	if player and player.has_method("add_item") and item_resource is Item:
		var item = item_resource.duplicate()
		item.initialize(player)
		player.add_item(item)

		# 更新UI
		update_item_ui(player)

# 更新道具UI
func update_item_ui(player: Node) -> void:
	# 等待一帧确保道具已添加到玩家
	await get_tree().process_frame

	# 获取玩家的道具列表
	var player_items = []
	if player.has_method("get_items"):
		player_items = player.get_items()

	# 查找道具栏UI
	var item_boxes = get_tree().get_nodes_in_group("item_boxes_ui")
	if item_boxes.size() > 0:
		var item_ui = item_boxes[0]
		if item_ui.has_method("update_items"):
			item_ui.update_items(player_items)

# 初始化
func _ready() -> void:
	# 连接到场景树的node_added信号
	if not get_tree().node_added.is_connected(_on_node_added):
		get_tree().node_added.connect(_on_node_added)

	# 延迟连接到所有道具拾取物
	call_deferred("connect_to_all_pickups")

# 当一个新节点被添加到场景树时的回调
func _on_node_added(node: Node) -> void:
	# 检查是否为道具拾取物
	if node.is_in_group("item_pickup"):
		call_deferred("connect_to_all_pickups")
