extends Camera3D
class_name CameraFollower

## Camera跟随脚本
## 让Camera跟随Player移动，保持固定的相对位置和角度

@export_group("跟随设置")
@export var target_path: NodePath  # 目标Player的路径
@export var follow_enabled: bool = true  # 是否启用跟随
@export var smooth_follow: bool = true  # 是否平滑跟随
@export var follow_speed: float = 5.0  # 跟随速度（仅在平滑跟随时使用）

@export_group("偏移设置")
@export var position_offset: Vector3 = Vector3(0, 30, 25)  # 相对Player的位置偏移
@export var look_at_target: bool = false  # 是否始终看向目标
@export var maintain_rotation: bool = true  # 是否保持固定旋转

# 目标Player引用
var target_player: Node3D
var initial_rotation: Vector3

func _ready() -> void:
	# 保存初始旋转
	initial_rotation = rotation_degrees
	
	# 查找目标Player
	_find_target_player()
	
	# 如果找到目标，立即设置初始位置
	if target_player and follow_enabled:
		_update_camera_position(false)

func _process(delta: float) -> void:
	if not follow_enabled or not target_player:
		return
	
	# 更新Camera位置
	_update_camera_position(smooth_follow, delta)

# 查找目标Player
func _find_target_player() -> void:
	if target_path:
		target_player = get_node_or_null(target_path)
	
	if not target_player:
		# 尝试在场景中查找Player
		var players = get_tree().get_nodes_in_group("player")
		if players.size() > 0:
			target_player = players[0]
	
	if target_player:
		print("[CameraFollower] 找到目标Player: ", target_player.name)
	else:
		push_warning("[CameraFollower] 未找到目标Player")

# 更新Camera位置
func _update_camera_position(smooth: bool, delta: float = 0.0) -> void:
	if not target_player:
		return
	
	# 计算目标位置
	var target_position = target_player.global_position + position_offset
	
	if smooth and delta > 0.0:
		# 平滑跟随
		global_position = global_position.lerp(target_position, follow_speed * delta)
	else:
		# 直接跟随
		global_position = target_position
	
	# 处理旋转
	if look_at_target:
		# 看向目标Player
		look_at(target_player.global_position, Vector3.UP)
	elif maintain_rotation:
		# 保持固定旋转
		rotation_degrees = initial_rotation

# 设置跟随目标
func set_target(new_target: Node3D) -> void:
	target_player = new_target
	if target_player:
		print("[CameraFollower] 设置新目标: ", target_player.name)
		if follow_enabled:
			_update_camera_position(false)

# 启用/禁用跟随
func set_follow_enabled(enabled: bool) -> void:
	follow_enabled = enabled
	print("[CameraFollower] 跟随状态: ", "启用" if enabled else "禁用")

# 设置位置偏移
func set_position_offset(offset: Vector3) -> void:
	position_offset = offset
	if target_player and follow_enabled:
		_update_camera_position(false)

# 设置跟随速度
func set_follow_speed(speed: float) -> void:
	follow_speed = speed

# 启用/禁用平滑跟随
func set_smooth_follow(smooth: bool) -> void:
	smooth_follow = smooth

# 启用/禁用看向目标
func set_look_at_target(look_at: bool) -> void:
	look_at_target = look_at
	maintain_rotation = not look_at

# 重置到初始旋转
func reset_rotation() -> void:
	rotation_degrees = initial_rotation

# 获取当前跟随状态信息
func get_follow_info() -> Dictionary:
	return {
		"target": target_player.name if target_player else "无",
		"follow_enabled": follow_enabled,
		"smooth_follow": smooth_follow,
		"follow_speed": follow_speed,
		"position_offset": position_offset,
		"look_at_target": look_at_target,
		"maintain_rotation": maintain_rotation
	}
