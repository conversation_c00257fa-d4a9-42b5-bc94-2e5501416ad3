extends Item
class_name EnergyDrink

# 道具固定属性
var speed_boost: float = 10.0  # 速度提升值
var duration: float = 5.0  # 效果持续时间（秒）
var original_move_speed: float = 0.0  # 保存玩家原始移动速度

func _init() -> void:
	# 设置道具基本属性
	item_name = "能量饮料"
	item_description = "使使用者移动速度 +10，持续 5 秒"
	is_one_time_use = true
	cooldown_time = 0.0  # 一次性道具，无冷却时间
	item_category = "utility"

# 覆盖初始化函数
func initialize(item_owner = null) -> void:
	# 调用父类的初始化方法
	super.initialize(item_owner)

# 使用道具的逻辑（重写父类的use方法）
func use() -> bool:
	# 检查是否有有效的所有者（玩家）
	if not owner:
		print("能量饮料使用失败：没有有效的所有者")
		return false

	# 检查玩家是否有move_speed属性
	if not "move_speed" in owner:
		print("能量饮料使用失败：玩家没有move_speed属性")
		return false

	# 保存原始移动速度
	original_move_speed = owner.move_speed

	# 增加玩家移动速度
	owner.move_speed += speed_boost

	# 显示使用效果
	show_use_effects()

	# 创建计时器，在持续时间结束后恢复速度
	var timer = Timer.new()
	owner.add_child(timer)
	timer.wait_time = duration
	timer.one_shot = true
	timer.timeout.connect(func():
		# 恢复原始速度
		if is_instance_valid(owner) and "move_speed" in owner:
			owner.move_speed = original_move_speed
			print("能量饮料效果结束，移动速度恢复为", original_move_speed)
		# 删除计时器
		timer.queue_free()
	)
	timer.start()

	print("能量饮料使用成功！移动速度从", original_move_speed, "提升到", owner.move_speed, "，持续", duration, "秒")

	# 返回使用成功
	return true

# 显示使用效果
func show_use_effects() -> void:
	if not owner:
		return

	# 创建粒子效果（如果需要）
	# var effect = GPUParticles3D.new()
	# owner.add_child(effect)
	# effect.emitting = true
	# effect.one_shot = true
	# effect.explosiveness = 0.8
	# effect.finished.connect(Callable(effect, "queue_free"))

	# 播放音效（如果需要）
	# var audio_player = AudioStreamPlayer3D.new()
	# owner.add_child(audio_player)
	# audio_player.stream = preload("res://sounds/energy_drink.ogg")  # 替换为实际音效路径
	# audio_player.play()
	# audio_player.finished.connect(Callable(audio_player, "queue_free"))

	# 显示UI提示
	if owner.has_method("show_notification"):
		owner.show_notification("速度提升！持续 " + str(duration) + " 秒")

	# 打印调试信息
	print("能量饮料使用成功！移动速度从 ", original_move_speed, " 提升到 ", owner.move_speed, "，持续 ", duration, " 秒")
