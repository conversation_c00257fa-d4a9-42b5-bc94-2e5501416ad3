extends "res://Scripts/CharacterBase.gd"

# 导入道具类
const EnergyDrinkScript = preload("res://Scripts/EnergyDrink.gd")
const TorchScript = preload("res://Scripts/Torch.gd")
const LightningOrbScript = preload("res://Scripts/LightningOrb.gd")
const TrapScript = preload("res://Scripts/Trap.gd")

@export_group("Movement")
## Ground movement acceleration in meters per second squared.
@export var acceleration := 20.0
## Minimum horizontal speed on the ground. This controls when the character skin's
## animation tree changes between the idle and running states.
@export var stopping_speed := 1.0

## Each frame, we find the height of the ground below the player and store it here.
var ground_height := 0.0

var _gravity := -10.0  # 降低重力，使跳跃更平滑
var _was_on_floor_last_frame := true

# We store the initial position of the player to reset to it when the player falls off the map.
@warning_ignore("unused_private_class_variable")
@onready var _start_position := global_position

@onready var _skin = $"Player01"

# 标志，用于防止自动动画系统打断手动播放的动画
var _is_playing_manual_animation = false
var _manual_animation_timer = 0.0

# 查找模型中的AnimationPlayer
func _find_model_animation_player():
	if not _skin:
		return null

	# 直接查找模型中的AnimationPlayer
	var animation_player = _skin.get_node_or_null("AnimationPlayer")
	if animation_player:
		return animation_player

	# 递归查找AnimationPlayer
	var result = _find_animation_player_recursive(_skin)
	if result:
		return result

	# 如果还是找不到，尝试直接使用Player01.glb中的AnimationPlayer
	var player01 = get_node_or_null("Player01")
	if player01:
		animation_player = player01.get_node_or_null("AnimationPlayer")
		if animation_player:
			return animation_player

	return null

func _find_animation_player_recursive(node):
	# 检查当前节点是否是AnimationPlayer
	if node is AnimationPlayer:
		return node

	# 递归检查所有子节点
	for child in node.get_children():
		var result = _find_animation_player_recursive(child)
		if result:
			return result

	return null

# 打印节点树结构
func _print_node_tree(_node, _indent_level = 0):
	pass
@onready var _jump_sound: AudioStreamPlayer3D = %JumpSound

# 落地音效相关变量
var _previous_velocity_y: float = 0.0
var _last_landing_time: int = 0
var landing_sound_cooldown_ms: int = 300  # 300毫秒冷却
var min_landing_velocity: float = -1.5    # 最小落地速度

# 调试控制（已移除Camera相关控制）
var debug_ui: Control

@export var ignite_duration := 1.0  # 点火持续时间，保留以便传递给树

# 技能系统
@export_group("Skills")
@export var skill_1_key: String = "skill_1"  # 技能1按键
@export var skill_2_key: String = "skill_2"  # 技能2按键

# 道具系统
@export_group("Items")
@export var item_1_key: String = "item_1"  # 道具1按键
@export var item_2_key: String = "item_2"  # 道具2按键

# 道具实例
var active_items = []  # 激活的道具实例



func _input(event: InputEvent) -> void:
	# 调用父类的_input方法，确保_skill_input_processed_this_frame在每帧重置
	super._input(event)

func _unhandled_input(_event: InputEvent) -> void:
	# 移除与点火相关的输入处理，因为我们在_process中已经处理了
	pass



# 全局标记，用于跟踪帧时间
var _last_frame_time := 0.0

func _physics_process(delta: float) -> void:
	# 调用父类的_physics_process方法，确保无敌期计时器能够正确更新
	super._physics_process(delta)

	# 检查是否是新的一帧
	var current_time = Time.get_ticks_msec()
	if current_time != _last_frame_time:
		_last_frame_time = current_time

	# 更新技能冷却
	update_skills(delta)

	# 更新道具状态
	update_items(delta)

	# 检查技能输入
	process_skill_inputs()

	# 检查道具输入
	check_item_inputs()

	# 处理树木交互
	handle_tree_interaction()

	# 检查是否处于伪装状态，如果是则跳过移动处理
	if is_disguised():
		# 在伪装状态下只应用重力
		velocity.y += _gravity * delta
		move_and_slide()
		return

	# 正常的移动处理逻辑

	# 计算移动方向（基于相机方向）
	var raw_input := Input.get_vector("move_left", "move_right", "move_up", "move_down", 0.4)
	var camera = get_viewport().get_camera_3d()
	var forward: Vector3 = camera.global_basis.z if camera else Vector3.FORWARD  # 修改：使用正向Z轴作为前向
	var right: Vector3 = camera.global_basis.x if camera else Vector3.RIGHT
	var move_direction: Vector3 = forward * raw_input.y + right * raw_input.x
	move_direction.y = 0.0
	move_direction = move_direction.normalized()

	# 更新角色朝向 - 只在有有效移动时更新朝向
	if move_direction.length() > 0.2:
		# 更新最后的移动方向
		_last_input_direction = Vector2(move_direction.x, move_direction.z).normalized()

		# 创建一个Vector3用于计算角度
		var direction_3d = Vector3(_last_input_direction.x, 0, _last_input_direction.y)

		# 由于模型已经顺时针旋转了90度，我们需要调整目标角度
		# 使用RIGHT而不是BACK作为基准方向
		var target_angle := Vector3.RIGHT.signed_angle_to(direction_3d, Vector3.UP)
		if _skin:
			# 使用固定的旋转速度，因为rotation_speed在CharacterBase.gd中不存在
			_skin.global_rotation.y = lerp_angle(_skin.rotation.y, target_angle, 12.0 * delta)
	# 停止移动时不更新朝向，保持最后的朝向

	# 移动处理
	var y_velocity := velocity.y
	velocity.y = 0.0
	velocity = velocity.move_toward(move_direction * move_speed, acceleration * delta)
	if is_equal_approx(move_direction.length_squared(), 0.0) and velocity.length_squared() < stopping_speed:
		velocity = Vector3.ZERO
	velocity.y = y_velocity + _gravity * delta

	# Character animations and visual effects.
	var ground_speed := Vector2(velocity.x, velocity.z).length()
	var is_just_jumping := Input.is_action_just_pressed("jump") and is_on_floor()

	# 如果正在播放手动动画，跳过自动动画系统
	if _is_playing_manual_animation:
		# 仍然处理跳跃物理，但不播放动画
		if is_just_jumping:
			velocity.y += jump_impulse
			_jump_sound.play()
		return

	if is_just_jumping:
		velocity.y += jump_impulse
		# 播放jump动画
		if _animation_player:
			var jump_animation_names = ["jump", "Jump", "jumping", "Jumping"]
			var _animation_applied = false

			for anim_name in jump_animation_names:
				if _animation_player.has_animation(anim_name):
					_animation_player.play(anim_name)
					_animation_applied = true
					break
		_jump_sound.play()
	elif not is_on_floor() and velocity.y < 0:
		# 播放fall动画
		if _animation_player and not _animation_player.is_playing():
			var fall_animation_names = ["fall", "Fall", "falling", "Falling"]
			var animation_applied = false

			for anim_name in fall_animation_names:
				if _animation_player.has_animation(anim_name):
					_animation_player.play(anim_name)
					animation_applied = true
					break

			# 如果没有fall动画，尝试使用jump动画
			if not animation_applied:
				var jump_animation_names = ["jump", "Jump", "jumping", "Jumping"]

				for anim_name in jump_animation_names:
					if _animation_player.has_animation(anim_name):
						_animation_player.play(anim_name)
						animation_applied = true
						break
	elif is_on_floor():
		if ground_speed > 0.0:
			# 播放跑步动画
			if _animation_player:
				# 检查当前是否已经在播放跑步动画
				var current_anim = ""
				if _animation_player.is_playing():
					current_anim = _animation_player.current_animation

				# 如果当前没有播放动画，或者正在播放的不是跑步动画
				var run_animation_names = ["run", "Run", "running", "Running", "walk", "Walk"]
				if not _animation_player.is_playing() or not (current_anim in run_animation_names):
					# 播放run动画
					var _animation_applied = false

					for anim_name in run_animation_names:
						if _animation_player.has_animation(anim_name):
							_animation_player.play(anim_name)
							_animation_applied = true
							break
		else:
			# 播放idle动画
			if _animation_player:
				var idle_animation_names = ["idle", "Idle", "IDLE", "stand", "Stand", "STAND"]
				var _animation_applied = false

				for anim_name in idle_animation_names:
					if _animation_player.has_animation(anim_name):
						_animation_player.play(anim_name)
						_animation_applied = true
						break

				# 如果没有idle动画，尝试使用第一个可用的动画
				if not _animation_applied:
					var _animations = _animation_player.get_animation_list()
					if _animations.size() > 0:
						_animation_player.play(_animations[0])

	if _dust_particles:
		_dust_particles.emitting = is_on_floor() && ground_speed > 0.0

	# 改进的落地音效检测
	check_landing_sound()

	_was_on_floor_last_frame = is_on_floor()
	move_and_slide()

# 改进的落地音效检测函数
func check_landing_sound() -> void:
	var current_time = Time.get_ticks_msec()

	# 检测真正的落地：从空中落到地面 + 有足够的下落速度
	if is_on_floor() and not _was_on_floor_last_frame:
		if _previous_velocity_y <= min_landing_velocity:  # 有足够的下落速度
			if current_time - _last_landing_time >= landing_sound_cooldown_ms:  # 冷却时间
				_landing_sound.play()
				_last_landing_time = current_time

	# 更新上一帧的Y轴速度
	_previous_velocity_y = velocity.y



# 初始化交互系统
func initialize_interaction() -> void:
	# 获取PlayerInteractor节点并连接信号
	var interactor = $PlayerInteractor
	if interactor:
		# 连接信号用于处理交互
		if not interactor.is_connected("interactor_area_entered", _on_interactor_area_entered):
			interactor.connect("interactor_area_entered", _on_interactor_area_entered)
		if not interactor.is_connected("interactor_area_exited", _on_interactor_area_exited):
			interactor.connect("interactor_area_exited", _on_interactor_area_exited)

func _ready():
	# 添加到玩家组
	add_to_group("player")

	# 确保equipped_items为空，防止编辑器中设置的默认道具被加载
	if equipped_items.size() > 0:
		equipped_items.clear()

	# 设置输入映射
	setup_input_actions()

	# Camera控制已移到CameraController

	# 确保鼠标始终可见
	Input.set_mouse_mode(Input.MOUSE_MODE_VISIBLE)

	# 设置碰撞掩码，确保能与敌人碰撞
	collision_mask = collision_mask | 4  # 添加第三层(4)，即敌人层

	# 强制设置碰撞层和掩码，确保覆盖场景中的设置
	set_collision_layer(2)  # 玩家属于第二层 (2 = 2^1)
	set_collision_mask(collision_mask)  # 保持当前的碰撞掩码


	# 初始化各个系统
	initialize_interaction()
	initialize_skills() # initialize_skills内部已经创建了新的技能实例，不需要再调用reset_skills
	initialize_items()
	initialize_health() # 初始化健康系统

	# 连接敌人信号
	connect_enemy_signals()



	# 注释掉这行，因为initialize_skills已经创建了新的技能实例
	# reset_skills() # 这会导致伪装技能被激活后立即解除

	# 确保玩家初始化时在地面上
	position.y = 0
	velocity.y = 0

	# 初始化_skin变量
	_skin = get_node_or_null("Player01")
	if not _skin and get_child_count() > 0:
		_skin = get_child(0)

	if _skin:
		# 初始化模型内部的AnimationPlayer
		_animation_player = _find_model_animation_player()
		if _animation_player:
			var _animations = _animation_player.get_animation_list()

			# 连接动画播放器信号
			_animation_player.animation_finished.connect(_on_animation_finished)

			# 尝试播放一个简单的动画来测试
			if _animation_player.has_animation("idle"):
				_animation_player.play("idle")

			# 播放idle动画
			var idle_animation_names = ["idle", "Idle", "IDLE", "stand", "Stand", "STAND"]
			var _animation_applied = false

			for anim_name in idle_animation_names:
				if _animation_player.has_animation(anim_name):
					_animation_player.play(anim_name)
					_animation_applied = true
					break





func create_debug_ui():
	# Camera调试UI已移除，因为Camera已经移到Level01
	pass

# Camera调试回调函数已移除，因为Camera已经移到Level01

# 处理树木交互
func handle_tree_interaction() -> void:
	# 如果没有当前树或处于伪装状态，直接返回
	if _current_tree == null:
		return

	# 如果玩家处于伪装状态，禁用交互
	if is_disguised():
		if _current_tree.has_method("stop_igniting"):
			_current_tree.stop_igniting()
		if _current_tree.has_method("remove_highlight"):
			_current_tree.remove_highlight()
		_current_tree = null
		return

	# 注意：原有的E键点火逻辑已移除，现在使用FireSkill技能处理点火功能

# 主要处理函数
func _process(delta: float) -> void:
	# 父类没有_process方法，所以不需要调用super._process(delta)

	# 更新手动动画计时器
	if _is_playing_manual_animation:
		_manual_animation_timer -= delta
		if _manual_animation_timer <= 0:
			_is_playing_manual_animation = false
			_manual_animation_timer = 0.0

	# 更新技能冷却
	update_skills(delta)

	# 不在_process中处理技能输入，只在_physics_process中处理
	# process_skill_inputs()

	# 处理道具输入
	check_item_inputs()



	# 处理树木交互
	handle_tree_interaction()

	# 检测"P"键的按下，播放点火动画
	if Input.is_action_just_pressed("test_fire_animation"):
		# 当按下"P"键时，开始播放点火动画
		play_fire_animation()

	# 检查动画播放状态
	if _animation_player and _animation_player.is_playing():
		var _current_anim = _animation_player.current_animation
		var _current_pos = _animation_player.current_animation_position

# 处理交互器进入区域
func _on_interactor_area_entered(area: Area3D) -> void:
	# 如果处于伪装状态，忽略交互
	if is_disguised():
		return

	# 检查是否是树木
	var parent = area.get_parent()
	if not parent or not parent.is_in_group("trees"):
		return

	# 设置当前树
	_current_tree = parent

	# 添加高亮
	if _current_tree.has_method("add_highlight"):
		_current_tree.add_highlight()

	# 处理GAS道具和Torch道具
	for item in active_items:
		if item is GasItem or item is Torch:
			item.set_current_tree(_current_tree)

# 处理交互器离开区域
func _on_interactor_area_exited(area: Area3D) -> void:
	# 检查是否是当前树
	var parent = area.get_parent()
	if not parent or not _current_tree or parent != _current_tree:
		return

	# 移除高亮
	if _current_tree.has_method("remove_highlight"):
		_current_tree.remove_highlight()

	# 停止点火
	if _current_tree.has_method("stop_igniting"):
		_current_tree.stop_igniting()

	# 清除GAS道具和Torch道具的当前树
	for item in active_items:
		if item is GasItem or item is Torch:
			item.clear_current_tree()

	# 清除当前树引用
	_current_tree = null

# 初始化技能系统
func initialize_skills() -> void:
	# 清除现有技能
	equipped_skill_1 = null
	equipped_skill_2 = null

	# 从技能管理器获取当前装备的技能
	var skill_manager = get_node_or_null("/root/SkillManager")
	if not skill_manager:
		push_error("SkillManager单例不存在")
		return

	# 装备技能1
	var skill1_resource = skill_manager.get_equipped_skill_1()
	if skill1_resource is Skill:
		equipped_skill_1 = skill1_resource.duplicate()
		equipped_skill_1.initialize(self)

	# 装备技能2
	var skill2_resource = skill_manager.get_equipped_skill_2()
	if skill2_resource is Skill:
		equipped_skill_2 = skill2_resource.duplicate()
		equipped_skill_2.initialize(self)


# 更新技能冷却
func update_skills(delta: float) -> void:
	# 更新技能1
	if equipped_skill_1:
		equipped_skill_1.update(delta)

	# 更新技能2
	if equipped_skill_2:
		equipped_skill_2.update(delta)

# 新的技能输入处理函数
func process_skill_inputs() -> void:
	# 处理技能1输入
	if Input.is_action_just_pressed(skill_1_key) and equipped_skill_1:
		# 检查是否是FLAME WALTZ被动技能
		if equipped_skill_1 is FlameWaltzSkill:
			# 被动技能不响应按键
			return

		# 如果是移动类技能（如闪烁），在伪装状态下不能使用
		if not is_disguised() or not equipped_skill_1.is_teleport_skill:
			equipped_skill_1.try_activate(self)
			return

	# 处理技能2输入
	if Input.is_action_just_pressed(skill_2_key) and equipped_skill_2:
		# 检查是否是FLAME WALTZ被动技能
		if equipped_skill_2 is FlameWaltzSkill:
			# 被动技能不响应按键
			return

		# 如果是移动类技能（如闪烁），在伪装状态下不能使用
		if not is_disguised() or not equipped_skill_2.is_teleport_skill:
			equipped_skill_2.try_activate(self)
			return

# 保留原来的函数作为兼容，但不再使用
func check_skill_inputs() -> void:
	# 不调用新的处理函数，因为这会导致重复处理
	# 仅作为兼容性保留这个空函数
	pass

	# 伪装技能处理 - 检查映射按键
	# 注释掉这部分，因为已经在上面处理了disguise_skill按键
	# 这里的重复检查导致伪装技能被激活两次
	# if Input.is_action_just_pressed(skill_2_key) and active_skills.size() > 1:
	# 	var disguise_skill = active_skills[1]
	# 	if disguise_skill is DisguiseSkill:
	# 		disguise_skill.try_activate()

# 使用指定技能槽位的技能
func use_skill(slot: int) -> void:
	# 已不再使用该函数，保留仅作兼容性
	push_warning("使用了已弃用的use_skill函数，请使用process_skill_inputs函数代替")

	# 兼容性处理
	if slot == 0 and equipped_skill_1:
		equipped_skill_1.try_activate()
	elif slot == 1 and equipped_skill_2:
		equipped_skill_2.try_activate()

# 平滑相机更新函数已移除，因为Camera已经移到Level01
func smooth_camera_transition(transition_time: float = 0.3) -> void:
	# 此函数已不再使用，Camera控制已移到CameraController
	pass

# 装备新技能
func equip_skill(skill_resource: Resource, slot: int = -1) -> bool:
	if not skill_resource is Skill:
		push_error("尝试装备非技能资源")
		return false

	# 创建技能实例
	var skill = skill_resource.duplicate()
	skill.initialize(self)

	# 根据槽位装备技能
	if slot == 0:
		equipped_skill_1 = skill
	elif slot == 1:
		equipped_skill_2 = skill
	else:
		# 如果没有指定槽位，选择第一个可用的槽位
		if equipped_skill_1 == null:
			equipped_skill_1 = skill
		elif equipped_skill_2 == null:
			equipped_skill_2 = skill
		else:
			push_warning("所有技能槽位已满，无法装备新技能")
			return false

	return true

# 卸下技能
func unequip_skill(slot: int) -> bool:
	if slot == 0 and equipped_skill_1:
		equipped_skill_1 = null
		return true
	elif slot == 1 and equipped_skill_2:
		equipped_skill_2 = null
		return true
	return false

# 设置输入动作
func setup_input_actions() -> void:
	# 注意：我们不再在这里设置技能按键，因为现在使用project.godot中的输入映射
	# 技能1使用E键，技能2使用F键

	# 设置箱子交互键（Q键）
	if InputMap.has_action("interact_chest"):
		InputMap.erase_action("interact_chest")

	InputMap.add_action("interact_chest")

	var chest_interact_event = InputEventKey.new()
	chest_interact_event.keycode = KEY_Q
	chest_interact_event.physical_keycode = KEY_Q
	chest_interact_event.pressed = true
	InputMap.action_add_event("interact_chest", chest_interact_event)

	# 只保留测试点火动画的P键设置
	if InputMap.has_action("test_fire_animation"):
		InputMap.erase_action("test_fire_animation")

	InputMap.add_action("test_fire_animation")

	var fire_test_event = InputEventKey.new()
	fire_test_event.keycode = KEY_P
	fire_test_event.physical_keycode = KEY_P
	fire_test_event.pressed = true
	InputMap.action_add_event("test_fire_animation", fire_test_event)

# 重置交互器位置并强制刷新交互检测
func reset_interactor_position() -> void:
	# 获取交互器
	var interactor = get_node_or_null("PlayerInteractor")
	if not interactor or not interactor is Area3D:
		return

	# 强制重新检测交互区域
	# 临时禁用再启用监控
	interactor.monitoring = false
	interactor.monitoring = true

	# 清除任何缓存状态
	if interactor.has_method("clear_interaction_state"):
		interactor.call("clear_interaction_state")

	# 手动重新扫描区域
	if interactor.has_method("scan_for_interactions"):
		interactor.call("scan_for_interactions")

	# 立即触发所有重叠区域的信号
	var overlapping_areas = interactor.get_overlapping_areas()
	for area in overlapping_areas:
		if interactor.has_signal("area_entered"):
			interactor.emit_signal("area_entered", area)

# 缓存变量用于伪装状态检查
var _cached_disguise_state := false
var _last_check_time := 0.0

# 检查玩家是否处于伪装状态 - 优化版本
func is_disguised() -> bool:
	# 每帧只检查一次伪装状态
	var current_time = Time.get_ticks_msec() / 1000.0
	if current_time - _last_check_time < 0.05: # 50ms缓存
		return _cached_disguise_state

	_last_check_time = current_time

	# 检查玩家是否有DisguiseModel子节点 - 最快的检查
	if has_node("DisguiseModel"):
		_cached_disguise_state = true
		return true

	# 检查玩家是否有disguised元数据标记
	if has_meta("disguised") and get_meta("disguised"):
		_cached_disguise_state = true
		return true

	# 检查技能中是否有激活的伪装技能
	# 检查技能槽1
	if equipped_skill_1 is DisguiseSkill and equipped_skill_1.is_disguised():
		_cached_disguise_state = true
		return true

	# 检查技能槽2
	if equipped_skill_2 is DisguiseSkill and equipped_skill_2.is_disguised():
		_cached_disguise_state = true
		return true

	_cached_disguise_state = false
	return false

# 重置所有技能状态 - 优化版本
func reset_skills() -> void:
	# 先解除伪装状态（如果有）
	# 检查技能槽1
	if equipped_skill_1 is DisguiseSkill and equipped_skill_1.is_disguised():
		equipped_skill_1.undisguise(self)

	# 检查技能槽2
	if equipped_skill_2 is DisguiseSkill and equipped_skill_2.is_disguised():
		equipped_skill_2.undisguise(self)

	# 重新初始化技能系统
	initialize_skills()

	# 重置每个技能的状态
	if equipped_skill_1:
		equipped_skill_1.reset()

	if equipped_skill_2:
		equipped_skill_2.reset()

# 初始化道具系统
func initialize_items() -> void:
	active_items.clear()

	# 为每个装备的道具创建实例
	for item_resource in equipped_items:
		if item_resource is Item:
			var item = item_resource.duplicate()
			item.initialize(self)
			active_items.append(item)

	# 确保道具数组不超过2个
	while active_items.size() > 2:
		active_items.pop_back()

	# 配置输入动作
	setup_item_input_actions()

	# 从ItemManager获取已解锁的道具（如果需要）
	var item_manager = get_node_or_null("/root/ItemManager")
	if item_manager:
		# 可以在这里添加初始道具
		pass

# 设置道具输入动作
func setup_item_input_actions() -> void:
	# 设置道具1按键（1键）
	if InputMap.has_action("item_1"):
		InputMap.erase_action("item_1")

	InputMap.add_action("item_1")

	var item1_event = InputEventKey.new()
	item1_event.keycode = KEY_1
	item1_event.physical_keycode = KEY_1
	item1_event.pressed = true
	InputMap.action_add_event("item_1", item1_event)

	# 设置道具2按键（2键）
	if InputMap.has_action("item_2"):
		InputMap.erase_action("item_2")

	InputMap.add_action("item_2")

	var item2_event = InputEventKey.new()
	item2_event.keycode = KEY_2
	item2_event.physical_keycode = KEY_2
	item2_event.pressed = true
	InputMap.action_add_event("item_2", item2_event)

	# 为对应的道具映射同样设置
	if InputMap.has_action(item_1_key) and item_1_key != "item_1":
		InputMap.erase_action(item_1_key)

	if item_1_key != "item_1":
		InputMap.add_action(item_1_key)
		InputMap.action_add_event(item_1_key, item1_event)

	if InputMap.has_action(item_2_key) and item_2_key != "item_2":
		InputMap.erase_action(item_2_key)

	if item_2_key != "item_2":
		InputMap.add_action(item_2_key)
		InputMap.action_add_event(item_2_key, item2_event)

# 检查道具输入 - 优化版本
func check_item_inputs() -> void:
	# 如果没有道具或处于伪装状态，直接返回
	if active_items.is_empty() or is_disguised():
		return

	# 检查道具输入
	if Input.is_action_just_pressed(item_1_key):
		use_item(0)

	if Input.is_action_just_pressed(item_2_key) and active_items.size() > 1:
		use_item(1)

# 更新道具状态
func update_items(delta: float) -> void:
	for i in range(active_items.size()):
		var item = active_items[i]
		if item and item is Item:
			item.update(delta)

	# 不再自动更新UI，UI将在飞行动画完成后手动更新
	# if Engine.get_process_frames() % 30 == 0:
	#     update_item_ui()

# 使用道具 - 优化版本
func use_item(slot: int) -> void:
	# 检查槽位是否有效
	if slot < 0 or slot >= active_items.size():
		return

	# 获取道具并检查有效性
	var item = active_items[slot]
	if not item or not item is Item:
		return

	# 如果处于伪装状态，不能使用道具
	if is_disguised():
		return

	# 尝试使用道具
	if item.try_use():
		# 检查是否需要移除道具（一次性道具已使用或多次使用道具用完）
		var should_remove = false

		# 如果是一次性道具且已使用
		if item.is_one_time_use and item.was_used:
			should_remove = true

		# 如果是多次使用道具且使用次数为0
		if "current_uses" in item and item.current_uses <= 0:
			should_remove = true

		# 如果需要移除，从背包中移除
		if should_remove:
			active_items[slot] = null
			# 更新UI
			call_deferred("update_item_ui") # 使用call_deferred避免在循环中修改数组

# 获取当前道具栏中的所有道具
func get_items() -> Array:
	return active_items

# 移除敌人选择相关的函数，因为CrimsonCageSkill技能现在自动选择敌人

# 添加道具到道具栏（只更新数据，不更新UI）
func add_item(item: Item, slot: int = -1) -> bool:
	if not item:
		return false

	# 检查道具栏是否已满
	if active_items.size() >= 2:  # 最多只能有两个道具
		return false

	# 初始化道具并设置所有者
	if item is GasItem or item is EnergyDrinkScript or item is TorchScript or item is LightningOrbScript or item is TrapScript:
		item.initialize(self)

	# 添加到特定栏位
	if slot >= 0 and slot < 2:
		# 检查栏位是否已有道具
		if slot < active_items.size():
			active_items[slot] = item
		else:
			# 如果栏位超出数组当前大小，扩展数组
			while active_items.size() <= slot:
				active_items.append(null)
			active_items[slot] = item
	else:
		# 添加到第一个可用栏位
		active_items.append(item)

	# 不自动更新UI，UI将在飞行动画完成后更新

	return true

# 手动更新道具UI
func update_item_ui_manually() -> void:
	update_item_ui()

# 移除道具
func remove_item(item_or_slot) -> bool:
	var removed = false

	# 支持通过道具对象移除
	if item_or_slot is Item:
		var item_to_remove = item_or_slot

		for i in range(active_items.size()):
			if active_items[i] == item_to_remove:
				active_items.remove_at(i)
				removed = true
				break
	else:
		# 支持通过索引移除
		var slot = int(item_or_slot)
		if slot >= 0 and slot < active_items.size():
			active_items.remove_at(slot)
			removed = true

	# 只有在成功移除后才更新UI
	if removed:
		call_deferred("update_item_ui")

	return removed

# 获取最近的树 - 优化版本
func get_nearest_tree(max_distance: float = 3.0) -> Node:
	# 如果处于伪装状态，不允许与树交互
	if is_disguised():
		return null

	# 如果已有当前树且距离足够近，直接返回
	if _current_tree and global_position.distance_to(_current_tree.global_position) <= max_distance:
		return _current_tree

	# 搜索"trees"组中的节点
	var trees = get_tree().get_nodes_in_group("trees")
	var nearest_tree = null
	var nearest_distance = max_distance

	for tree in trees:
		var distance = global_position.distance_to(tree.global_position)
		if distance < nearest_distance:
			nearest_distance = distance
			nearest_tree = tree

	return nearest_tree

# 更新道具UI - 优化版本
func update_item_ui() -> void:
	# 清理道具列表中的null项
	var cleaned_items = []
	for item in active_items:
		if item != null:
			cleaned_items.append(item)

	# 更新活动道具列表
	active_items = cleaned_items

	# 查找道具栏UI
	var item_boxes = get_tree().get_nodes_in_group("item_boxes_ui")
	if item_boxes.is_empty():
		# 如果没找到UI，在下一帧重试
		get_tree().process_frame.connect(_retry_update_ui, CONNECT_ONE_SHOT)
		return

	# 更新UI
	var item_ui = item_boxes[0]
	if item_ui.has_method("update_items"):
		# 直接传递清理后的道具列表
		item_ui.update_items(active_items)

# 重试更新UI（在下一帧）
func _retry_update_ui() -> void:
	# 等待一帧，给UI节点时间加载
	update_item_ui()

# 清空所有道具
func clear_items() -> void:
	active_items.clear()
	equipped_items.clear()
	update_item_ui()

# 动画播放完成回调
func _on_animation_finished(_anim_name: String) -> void:
	pass

# 播放点火动画
func play_fire_animation() -> void:
	# 检查_animation_player是否存在
	if not _animation_player:
		_animation_player = _find_model_animation_player()
		if not _animation_player:
			return

	# 如果已经在播放点火动画，则不重复播放
	if _animation_player.is_playing():
		var current_anim = _animation_player.current_animation.to_lower()
		var fire_animation_names_lower = ["fire", "ignite", "burn"]
		for anim_name_lower in fire_animation_names_lower:
			if current_anim.contains(anim_name_lower):
				return  # 已经在播放点火动画，不需要重新播放

	# 尝试播放点火动画
	var fire_animation_names = ["fire", "Fire", "ignite", "Ignite", "burn", "Burn"]
	var _animation_applied = false

	# 获取所有可用的动画
	var _animations = _animation_player.get_animation_list()

	for anim_name in fire_animation_names:
		if _animation_player.has_animation(anim_name):
			# 获取动画
			var anim = _animation_player.get_animation(anim_name)

			# 设置为循环模式，确保动画持续播放
			# 注意：有些动画可能不支持循环模式的修改
			if anim:
				# GDScript不支持try-except，所以我们直接设置
				anim.loop_mode = Animation.LOOP_LINEAR

			# 停止当前正在播放的动画（如果有）
			if _animation_player.is_playing():
				_animation_player.stop()

			# 设置手动播放动画标志，防止自动动画系统打断
			_is_playing_manual_animation = true
			_manual_animation_timer = 10.0  # 设置一个较长的时间，确保点火过程中不会被打断

			# 获取FireSkill的施法时间
			var fire_skill_cast_time = FireSkill.get_fire_cast_time()  # 使用FireSkill的静态方法

			# 计算动画速度
			var anim_length = anim.length
			var speed_scale = anim_length / fire_skill_cast_time

			# 设置动画速度
			_animation_player.speed_scale = speed_scale

			# 播放动画，使用自定义混合时间确保平滑过渡
			_animation_player.play(anim_name, 0.2)  # 0.2秒混合时间

			_animation_applied = true
			break

# 停止点火动画
func stop_fire_animation() -> void:
	if not _animation_player:
		return

	# 重置动画速度
	_animation_player.speed_scale = 1.0

	# 重置手动播放动画标志
	_is_playing_manual_animation = false
	_manual_animation_timer = 0.0

	if _animation_player.is_playing():
		# 检查当前播放的是否是点火动画
		var current_anim = _animation_player.current_animation
		var fire_animation_names = ["fire", "Fire", "ignite", "Ignite", "burn", "Burn"]

		# 检查当前动画是否是点火动画
		var is_fire_animation = false
		for anim_name in fire_animation_names:
			if current_anim.to_lower() == anim_name.to_lower():
				is_fire_animation = true
				break

		if is_fire_animation:
			# 播放idle动画，使用混合过渡而不是直接停止
			var idle_animation_names = ["idle", "Idle", "IDLE", "stand", "Stand", "STAND"]
			var animation_applied = false

			for anim_name in idle_animation_names:
				if _animation_player.has_animation(anim_name):
					# 使用混合时间确保平滑过渡
					_animation_player.play(anim_name, 0.2)  # 0.2秒混合时间
					animation_applied = true
					break

			# 如果没有找到idle动画，才直接停止
			if not animation_applied:
				_animation_player.stop()

# 播放指定的动画
func play_specific_animation(anim_name: String) -> void:
	# 检查_animation_player是否存在
	if not _animation_player:
		_animation_player = _find_model_animation_player()
		if not _animation_player:
			return

	# 检查动画是否存在
	if not _animation_player.has_animation(anim_name):
		return

	# 获取动画
	var anim = _animation_player.get_animation(anim_name)

	# 设置为非循环模式，确保动画只播放一次
	anim.loop_mode = Animation.LOOP_NONE

	# 停止当前正在播放的动画（如果有）
	if _animation_player.is_playing():
		_animation_player.stop()

	# 设置手动播放动画标志，防止自动动画系统打断
	_is_playing_manual_animation = true
	_manual_animation_timer = anim.length + 0.5  # 动画长度加一点缓冲时间

	# 播放动画，使用自定义混合时间确保平滑过渡
	_animation_player.play(anim_name, 0.2)  # 0.2秒混合时间

# 检查玩家是否在移动
func is_moving() -> bool:
	# 检查水平方向的速度
	var horizontal_velocity = Vector2(velocity.x, velocity.z).length()

	# 如果水平速度大于一定阈值，则认为玩家在移动
	return horizontal_velocity > 0.5  # 可以根据需要调整阈值

# 获取玩家的前进方向
func get_forward_direction() -> Vector3:
	# 如果有输入方向，使用输入方向
	if _last_input_direction != Vector2.ZERO:
		return Vector3(_last_input_direction.x, 0, _last_input_direction.y).normalized()

	# 否则使用相机的前方向
	var camera = get_viewport().get_camera_3d()
	if camera:
		var forward = -camera.global_transform.basis.z
		forward.y = 0
		return forward.normalized()

	# 如果都不可用，使用模型的前方向
	return -global_transform.basis.z.normalized()

# 重写_on_enemy_capture方法，检查是否有BurningSpiritSkill技能
func _on_enemy_capture() -> void:
	# 检查是否有BurningSpiritSkill技能
	var burning_spirit_skill = null

	# 检查技能槽1
	if equipped_skill_1 is BurningSpiritSkill and not equipped_skill_1.is_used():
		burning_spirit_skill = equipped_skill_1
	# 检查技能槽2
	elif equipped_skill_2 is BurningSpiritSkill and not equipped_skill_2.is_used():
		burning_spirit_skill = equipped_skill_2

	# 如果找到了未使用的BurningSpiritSkill技能，触发它
	if burning_spirit_skill:
		print("触发BURNING SPIRIT技能，抵御敌人抓捕")
		burning_spirit_skill.trigger()
		return

	# 如果没有BurningSpiritSkill技能或技能已被使用，减少血量
	super._on_enemy_capture()

# 连接敌人信号
func connect_enemy_signals() -> void:
	# 获取所有敌人
	var enemies = get_tree().get_nodes_in_group("enemies")

	for enemy in enemies:
		var player_interaction = enemy.get_node_or_null("PlayerInteractionComponent")
		if player_interaction:
			if player_interaction.has_signal("player_try_capture"):
				# 先断开之前的连接，避免重复连接
				if player_interaction.is_connected("player_try_capture", _on_enemy_try_capture):
					player_interaction.disconnect("player_try_capture", _on_enemy_try_capture)
				# 连接信号
				player_interaction.player_try_capture.connect(_on_enemy_try_capture)

# 重写健康系统初始化方法
func initialize_health() -> void:
	# 设置玩家默认血量为3
	health = 3
	max_health = 3

	# 检查是否装备了BURNING SPIRIT技能，如果有则增加1点血量
	if has_burning_spirit_skill():
		max_health += 1
		health += 1
		print("检测到BURNING SPIRIT技能，血量上限+1")

	# 发出血量变化信号
	emit_signal("health_changed", health, max_health)
	print("玩家初始血量: ", health, "/", max_health)

	# 创建血量UI
	create_health_ui()

# 检查是否装备了BURNING SPIRIT技能
func has_burning_spirit_skill() -> bool:
	if equipped_skill_1 is BurningSpiritSkill:
		return true
	if equipped_skill_2 is BurningSpiritSkill:
		return true
	return false

# 创建血量UI
func create_health_ui() -> void:
	# 检查是否已存在血量UI
	var existing_health_ui = get_tree().get_nodes_in_group("health_ui")
	if existing_health_ui.size() > 0:
		return

	# 创建血量UI容器
	var health_ui = HBoxContainer.new()
	health_ui.name = "HealthUI"
	health_ui.add_to_group("health_ui")

	# 设置位置在左下角玩家头像下方
	health_ui.set_anchors_preset(Control.PRESET_BOTTOM_LEFT)
	health_ui.position = Vector2(20, -30)  # 位置可能需要调整

	# 加载心形图标
	var heart_texture = load("res://Icon/MainIcon/RedHeart.png")

	# 创建心形图标
	for i in range(max_health):
		var heart = TextureRect.new()
		heart.texture = heart_texture
		heart.expand = true
		heart.custom_minimum_size = Vector2(30, 30)  # 设置心形图标大小
		health_ui.add_child(heart)

	# 将血量UI添加到场景中
	var player_avatar = get_tree().get_nodes_in_group("avatar")
	if player_avatar.size() > 0:
		player_avatar[0].add_child(health_ui)
	else:
		# 如果找不到玩家头像，添加到根节点
		get_tree().root.add_child(health_ui)

	# 连接血量变化信号
	health_changed.connect(_on_health_changed)

# 处理血量变化
func _on_health_changed(new_health: int, _max_health: int) -> void:
	# 更新血量UI
	var health_ui = get_tree().get_nodes_in_group("health_ui")

	# 详细日志：打印health_ui组中的所有节点信息
	print("=== [Player] _on_health_changed 调试信息 ===")
	print("health_ui组节点数量: ", health_ui.size())

	for i in range(health_ui.size()):
		var ui_node = health_ui[i]
		print("health_ui[", i, "]: ")
		print("  - 节点名称: ", ui_node.name)
		print("  - 节点类型: ", ui_node.get_class())
		print("  - 节点路径: ", ui_node.get_path())
		print("  - 是否有modulate属性: ", ui_node.has_method("set_modulate"))
		print("  - 子节点数量: ", ui_node.get_child_count())

		# 打印子节点信息
		var children = ui_node.get_children()
		for j in range(children.size()):
			var child = children[j]
			print("    子节点[", j, "]: ")
			print("      - 名称: ", child.name)
			print("      - 类型: ", child.get_class())
			print("      - 路径: ", child.get_path())
			print("      - 是否有modulate属性: ", child.has_method("set_modulate"))
			print("      - 是否是CanvasItem: ", child is CanvasItem)
			print("      - 是否是Node3D: ", child is Node3D)

	print("=== 调试信息结束 ===")

	if health_ui.size() > 0:
		var hearts = health_ui[0].get_children()

		# 更新每个心形图标的可见性
		for i in range(hearts.size()):
			print("正在处理heart[", i, "]: ", hearts[i].name, " (", hearts[i].get_class(), ")")
			if i < new_health:
				hearts[i].modulate = Color(1, 1, 1, 1)  # 显示
			else:
				hearts[i].modulate = Color(1, 1, 1, 0.3)  # 半透明

# 处理敌人尝试捕获的信号
func _on_enemy_try_capture(player: CharacterBody3D, _enemy: CharacterBody3D, _can_be_captured: bool) -> void:
	# 确保是当前玩家
	if player != self:
		return

	# 检查是否处于无敌期
	if is_invincible():
		return

	# 检查是否有技能提供的无敌状态
	var has_skill_invincibility = false

	# 检查技能槽1
	if equipped_skill_1 is BurningSpiritSkill:
		if equipped_skill_1.is_triggered() and equipped_skill_1.is_invincible():
			has_skill_invincibility = true

	# 检查技能槽2
	if not has_skill_invincibility and equipped_skill_2 is BurningSpiritSkill:
		if equipped_skill_2.is_triggered() and equipped_skill_2.is_invincible():
			has_skill_invincibility = true

	# 如果有技能提供的无敌状态，设置不能被捕获
	if has_skill_invincibility:
		return

	# 检查是否可以触发燃烧之魂技能
	var triggered_skill = false

	# 检查技能槽1
	if equipped_skill_1 is BurningSpiritSkill and not equipped_skill_1.is_triggered():
		equipped_skill_1.trigger()
		triggered_skill = true

	# 检查技能槽2
	if not triggered_skill and equipped_skill_2 is BurningSpiritSkill and not equipped_skill_2.is_triggered():
		equipped_skill_2.trigger()
		triggered_skill = true

	# 如果触发了技能，设置不能被捕获
	if triggered_skill:
		return

	# 如果没有无敌状态也没有触发技能，则被捕获
	_on_enemy_capture()
