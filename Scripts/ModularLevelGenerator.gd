@tool
extends Node3D

# 引入TreeGeneratorModule脚本
const TreeGeneratorModuleScript = preload("res://Scripts/GeneratorModules/TreeGeneratorModule.gd")
const FloorTileGeneratorModule = preload("res://Scripts/GeneratorModules/FloorTileGeneratorModule.gd")

# 关卡生成器配置
@export var level_config: LevelConfig
@export_subgroup("直接配置")
@export var level_size := Vector2(100, 100)
@export var cell_size := 2.5
@export var wall_height := 2.0

@export_group("生成控制")
@export var generate := false:
	set = _set_generate
@export var clear := false:
	set = _set_clear
@export var reset := false:
	set = _set_reset
@export var bake_navmesh := false:
	set = _set_bake_navmesh

@export_group("视觉优化")
@export var mesh_x_rotation_degrees: float = 20.0:
	set = _set_mesh_x_rotation
@export var apply_mesh_rotation := false:
	set = _set_apply_mesh_rotation

# 添加数据生成和清除控制
@export_group("数据控制")
@export var save_data := false:
	set = _set_save_data
@export var clear_data := false:
	set = _set_clear_data

# 编辑器状态变量
var _is_baking := false
var edited_scene_root: Node = null

# 可用的生成器模块
var _available_modules = {}
var _loaded_modules = {}

func _ready() -> void:
	# 设置初始位置
	position = Vector3(0, 0, 0)

	# 在编辑器模式下获取当前编辑的场景根节点
	if Engine.is_editor_hint():
		edited_scene_root = get_tree().edited_scene_root

	# 加载所有可用的生成器模块
	_load_available_modules()

	# 设置导航地图的参数
	if get_world_3d():
		var nav_map = get_world_3d().navigation_map
		# 设置导航地图参数
		NavigationServer3D.map_set_cell_size(nav_map, 1.0)
		NavigationServer3D.map_set_cell_height(nav_map, 0.5)
		NavigationServer3D.map_set_edge_connection_margin(nav_map, 2.0)
		# 设置更小的合并栅格比例
		ProjectSettings.set_setting("navigation/3d/merge_rasterizer_cell_scale", 0.001)

# 加载所有可用的生成器模块
func _load_available_modules() -> void:
	# 创建内置的生成器模块
	_register_builtin_modules()

	# 从特定目录加载自定义模块
	var module_dir = "res://Scripts/GeneratorModules/"
	var dir = DirAccess.open(module_dir)
	if dir:
		dir.list_dir_begin()
		var file = dir.get_next()
		while file != "":
			if file.ends_with(".gd") and not file.begins_with("GeneratorModule"):
				var module_path = module_dir + file
				if ResourceLoader.exists(module_path):
					var module_script = load(module_path)
					if module_script:
						var module = module_script.new()
						if module is GeneratorModule:
							_available_modules[module.module_id] = module_script
			file = dir.get_next()
		dir.list_dir_end()

# 注册内置模块
func _register_builtin_modules() -> void:
	var module_classes = [
		FloorGeneratorModule,
		WallGeneratorModule,
		TreeGeneratorModule,
		ChestGeneratorModule,
		RockGeneratorModule,
		BarrelGeneratorModule,
		DecorationGeneratorModule,
		FloorTileGeneratorModule,
		FogOfWarGeneratorModule
	]

	for module_class in module_classes:
		var module = module_class.new()
		_available_modules[module.module_id] = module_class

# 设置按钮回调
func _set_generate(value: bool) -> void:
	generate = false
	if value:
		generate_level()

func _set_clear(value: bool) -> void:
	clear = false
	if value:
		clear_all()

func _set_reset(value: bool) -> void:
	reset = false
	if value:
		reset_properties()

func _set_bake_navmesh(value: bool) -> void:
	bake_navmesh = false
	if value:
		_bake_navigation_mesh()

# Mesh旋转控制回调
func _set_mesh_x_rotation(value: float) -> void:
	mesh_x_rotation_degrees = value
	# 实时更新已存在的mesh旋转
	if Engine.is_editor_hint():
		_apply_mesh_rotation_to_existing()

func _set_apply_mesh_rotation(value: bool) -> void:
	apply_mesh_rotation = false
	if value:
		_apply_mesh_rotation_to_existing()

# 数据生成和清除的回调函数
func _set_save_data(value: bool) -> void:
	save_data = false
	if value:
		_save_game_data()

func _set_clear_data(value: bool) -> void:
	clear_data = false
	if value:
		_clear_game_data()

# 主要的生成方法
func generate_level() -> void:
	if not Engine.is_editor_hint():
		push_error("[ModularLevelGenerator] 错误：只能在编辑器中生成关卡！")
		return

	# 先清除当前关卡
	clear_all()

	# 清除所有游戏数据，确保巡逻点数据也被清除
	_clear_game_data()

	# 创建新的NavigationRegion3D节点
	var nav_region = NavigationRegion3D.new()
	nav_region.name = "NavigationRegion3D"
	get_parent().add_child(nav_region)
	if Engine.is_editor_hint():
		nav_region.owner = get_tree().edited_scene_root

	# 创建GameData节点（如果不存在）
	var game_data = get_tree().root.get_node_or_null("GameData")
	if not game_data:
		game_data = Node.new()
		game_data.name = "GameData"
		game_data.set_script(load("res://Scripts/GameData.gd"))
		get_tree().root.add_child(game_data)
		if Engine.is_editor_hint():
			game_data.owner = get_tree().edited_scene_root

	# 创建PatrolPointManager节点
	var patrol_manager = Node.new()
	patrol_manager.name = "PatrolPointManager"
	patrol_manager.set_script(load("res://Scripts/PatrolPointManager.gd"))
	get_parent().add_child(patrol_manager)
	if Engine.is_editor_hint():
		patrol_manager.owner = get_tree().edited_scene_root

	# 使用配置或直接设置的值
	var config_size = level_size
	var config_cell_size = cell_size
	var config_wall_height = wall_height

	if level_config:
		config_size = level_config.level_size
		config_cell_size = level_config.cell_size
		config_wall_height = level_config.wall_height

	# 更新本地变量以便在模块中使用
	level_size = config_size
	cell_size = config_cell_size
	wall_height = config_wall_height

	# 根据配置决定要使用的模块
	var modules_to_use = []

	if level_config:
		# 使用配置中定义的模块
		modules_to_use = level_config.enabled_modules
	else:
		# 如果没有配置，使用所有可用的模块
		modules_to_use = _available_modules.keys()

	# 清除之前加载的模块
	for module in _loaded_modules.values():
		module.clear()
	_loaded_modules.clear()

	# 确保玩家节点存在
	var player = get_parent().get_node_or_null("Player")
	if not player:
		player = get_parent().get_node_or_null("player")
		if not player:
			player = get_parent().get_node_or_null("PlayerBody")
			if not player:
				# 尝试在场景中查找任何包含"player"的节点
				for child in get_parent().get_children():
					if "player" in child.name.to_lower():
						player = child
						break



	# 逐个生成模块
	for module_id in modules_to_use:
		if _available_modules.has(module_id):
			var module_class = _available_modules[module_id]
			var module = module_class.new()

			# 应用模块配置
			if level_config and level_config.module_configs.has(module_id):
				var module_config = level_config.module_configs[module_id].duplicate()



				if module_id == "decoration_generator":
					module_config["player"] = player

				module.apply_config(module_config)
			elif module_id == "decoration_generator":
				var default_config = module.get_default_config()
				default_config["player"] = player
				module.apply_config(default_config)

			# 验证配置
			if not module.validate_config():
				push_error("[ModularLevelGenerator] 错误：模块 " + module_id + " 的配置验证失败！")
				continue

			# 保存模块实例
			_loaded_modules[module_id] = module

			# 调用生成方法
			module.generate(self, nav_region)

	# 烘焙导航网格
	_bake_navigation_mesh()

	# 应用Mesh旋转优化
	_apply_mesh_rotation_to_existing()


# 烘焙导航网格
func _bake_navigation_mesh() -> void:
	if _is_baking:
		return

	_is_baking = true

	# 获取NavigationRegion3D节点
	var nav_region = get_parent().get_node_or_null("NavigationRegion3D")
	if not nav_region:
		push_error("[ModularLevelGenerator] 错误：找不到NavigationRegion3D节点！")
		_is_baking = false
		return

	# 设置导航地图参数
	if get_world_3d():
		var nav_map = get_world_3d().navigation_map
		NavigationServer3D.map_set_cell_size(nav_map, 1.0)
		NavigationServer3D.map_set_cell_height(nav_map, 0.5)
		NavigationServer3D.map_set_edge_connection_margin(nav_map, 2.0)
		# 修复导航网格合并错误
		ProjectSettings.set_setting("navigation/3d/merge_rasterizer_cell_scale", 0.001)

	# 获取或创建导航网格资源
	var nav_mesh = NavigationMesh.new()

	# 配置导航网格参数
	nav_mesh.cell_size = 0.25
	nav_mesh.cell_height = 0.25
	nav_mesh.agent_height = 1.75
	nav_mesh.agent_radius = 0.375
	nav_mesh.agent_max_climb = 0.5
	nav_mesh.agent_max_slope = 45.0
	nav_mesh.region_min_size = 2.0
	nav_mesh.region_merge_size = 20.0
	nav_mesh.edge_max_length = 12.0
	nav_mesh.filter_low_hanging_obstacles = true
	nav_mesh.filter_ledge_spans = true
	nav_mesh.filter_walkable_low_height_spans = true

	# 设置导航区域
	nav_region.navigation_mesh = nav_mesh

	# 等待一帧确保导航网格已更新
	await get_tree().process_frame

	# 重新烘焙导航网格
	nav_region.bake_navigation_mesh()

	# 等待烘焙完成
	await get_tree().create_timer(0.1).timeout

	_is_baking = false

# 清除所有生成的内容
func clear_all() -> void:
	# 清除所有加载的模块
	for module in _loaded_modules.values():
		module.clear()
	_loaded_modules.clear()

	# 获取并删除NavigationRegion3D节点
	var nav_region = get_parent().get_node_or_null("NavigationRegion3D")
	if nav_region:
		nav_region.queue_free()

	# 获取并删除PatrolPointManager节点
	var patrol_manager = get_parent().get_node_or_null("PatrolPointManager")
	if patrol_manager:
		patrol_manager.queue_free()


# 重置属性到默认值
func reset_properties() -> void:
	# 设置新值
	level_size = Vector2(100, 100)
	cell_size = 2.5
	wall_height = 2.0

	# 通知属性列表已更改
	notify_property_list_changed()


# 保存游戏数据
func _save_game_data() -> void:
	if not Engine.is_editor_hint():
		push_error("[ModularLevelGenerator] 错误：只能在编辑器中保存数据！")
		return


	# 获取当前编辑的场景根节点
	var scene_root = get_tree().edited_scene_root
	if not scene_root:
		push_error("[ModularLevelGenerator] 错误：无法获取当前编辑的场景根节点！")
		return


	# 保存树木数据
	TreeGeneratorModuleScript.save_trees_data(scene_root)




# 清除游戏数据
func _clear_game_data() -> void:
	if not Engine.is_editor_hint():
		push_error("[ModularLevelGenerator] 错误：只能在编辑器中清除数据！")
		return


	# 清除所有数据
	TreeGeneratorModuleScript.clear_all_data()



# 创建并保存关卡配置
func create_level_config(config_name: String = "new_level_config") -> LevelConfig:
	var config = LevelConfig.new()

	# 设置基本属性
	config.level_id = config_name
	config.level_name = "新关卡配置"
	config.level_size = level_size
	config.cell_size = cell_size
	config.wall_height = wall_height

	# 初始化模块配置
	var module_configs = {}

	# 启用所有默认模块
	config.enabled_modules = [
		"floor_generator",
		"wall_generator",
		"tree_generator",
		"chest_generator",
		"rock_generator",
		"barrel_generator",
		"decoration_generator",
		"floor_tile_generator",
		"fog_of_war_generator"
	]

	# 设置默认配置
	for module_id in config.enabled_modules:
		if _available_modules.has(module_id):
			var module_script = _available_modules[module_id]
			var module = module_script.new()
			var module_config = module.get_default_config()
			config.module_configs[module_id] = module_config

	# 保存配置
	var save_path = "res://Resources/LevelConfigs/" + config_name + ".tres"
	var dir = DirAccess.open("res://Resources")
	if not dir.dir_exists("LevelConfigs"):
		dir.make_dir("LevelConfigs")

	var result = ResourceSaver.save(config, save_path)
	if result == OK:
		return config
	else:
		push_error("[ModularLevelGenerator] 保存关卡配置失败: " + str(result))
		return null

# ==================== Mesh旋转优化功能 ====================

# 应用Mesh旋转到现有的所有游戏元素
func _apply_mesh_rotation_to_existing() -> void:
	if not Engine.is_editor_hint():
		return

	print("[ModularLevelGenerator] 开始应用Mesh旋转优化，X轴旋转角度: ", mesh_x_rotation_degrees, "度")

	var scene_root = get_parent()
	if not scene_root:
		push_error("[ModularLevelGenerator] 无法获取场景根节点")
		return

	var rotation_radians = deg_to_rad(mesh_x_rotation_degrees)
	var processed_count = 0

	# 递归处理所有节点
	processed_count += _apply_mesh_rotation_recursive(scene_root, rotation_radians)

	print("[ModularLevelGenerator] Mesh旋转优化完成，共处理 ", processed_count, " 个MeshInstance3D节点")

# 递归应用Mesh旋转
func _apply_mesh_rotation_recursive(node: Node, rotation_radians: float) -> int:
	var processed_count = 0

	# 检查当前节点是否是MeshInstance3D
	if node is MeshInstance3D:
		# 检查是否应该跳过此节点
		if _should_skip_mesh_rotation(node):
			return processed_count

		# 应用X轴旋转
		var current_rotation = node.rotation
		current_rotation.x = rotation_radians
		node.rotation = current_rotation
		processed_count += 1

		# 调试输出
		if processed_count <= 5:  # 只输出前5个作为示例
			print("  - 已旋转MeshInstance3D: ", node.get_path(), " (X轴: ", mesh_x_rotation_degrees, "度)")

	# 递归处理所有子节点
	for child in node.get_children():
		processed_count += _apply_mesh_rotation_recursive(child, rotation_radians)

	return processed_count

# 判断是否应该跳过某个MeshInstance3D的旋转
func _should_skip_mesh_rotation(mesh_node: MeshInstance3D) -> bool:
	var node_path = str(mesh_node.get_path()).to_lower()
	var node_name = mesh_node.name.to_lower()
	var parent_name = ""

	if mesh_node.get_parent():
		parent_name = mesh_node.get_parent().name.to_lower()

	# 跳过地面相关的Mesh
	var skip_keywords = [
		"floor",      # 地面
		"ground",     # 地面
		"terrain",    # 地形
		"navigation", # 导航网格
		"navmesh",    # 导航网格
		"csgbox3d"    # CSG地面盒子
	]

	for keyword in skip_keywords:
		if keyword in node_name or keyword in parent_name or keyword in node_path:
			return true

	# 跳过Camera相关的Mesh
	if "camera" in node_path:
		return true

	return false
