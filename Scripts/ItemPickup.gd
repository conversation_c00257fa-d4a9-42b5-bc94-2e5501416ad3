extends Area3D
class_name ItemPickup

# 信号
signal picked_up(item_resource, player)

# 配置参数
@export var item_resource: Resource  # 此拾取物提供的道具资源
@export var auto_rotate: bool = true  # 是否自动旋转
@export var rotation_speed: float = 1.0  # 旋转速度
@export var hover_amplitude: float = 0.1  # 悬浮振幅
@export var hover_speed: float = 2.0  # 悬浮速度
@export var highlight_on_near: bool = true  # 靠近时是否高亮
@export var pickup_sound: AudioStream  # 拾取音效

# 内部变量
var _initial_position: Vector3
var _time_passed: float = 0.0
var _player_nearby: bool = false
var _original_materials = []
var _highlight_materials = []

# 初始化
func _ready() -> void:
	# 记录初始位置
	_initial_position = global_position

	# 添加到道具拾取物组
	add_to_group("item_pickup")

	# 设置碰撞层和掩码
	collision_layer = 4  # 道具层
	collision_mask = 2   # 玩家层

	# 连接信号
	body_entered.connect(_on_body_entered)

	# 准备高亮材质
	_prepare_highlight_materials()

	# 初始化组件
	_initialize_components()

# 准备高亮材质
func _prepare_highlight_materials() -> void:
	if not highlight_on_near:
		return

	# 查找所有MeshInstance3D子节点
	var meshes = []
	for child in get_children():
		if child is MeshInstance3D:
			meshes.append(child)

	# 为每个网格创建高亮材质
	for mesh in meshes:
		if mesh.get_surface_override_material_count() > 0:
			var original_material = mesh.get_surface_override_material(0)
			_original_materials.append(original_material)

			# 创建高亮材质
			var highlight_material = StandardMaterial3D.new()
			if original_material and original_material is BaseMaterial3D:
				# 继承原材质的属性
				highlight_material.albedo_color = original_material.albedo_color
				highlight_material.albedo_texture = original_material.albedo_texture
			else:
				# 默认高亮颜色
				highlight_material.albedo_color = Color(1, 1, 0.5)  # 淡黄色

			# 添加发光效果
			highlight_material.emission_enabled = true
			highlight_material.emission = Color(1, 1, 0.5, 0.7)
			highlight_material.emission_energy = 0.5

			_highlight_materials.append(highlight_material)

# 用于跟踪是否已经显示过错误
var _has_shown_error = false

# 初始化组件
func _initialize_components() -> void:
	# 检查是否有有效的道具资源
	if not item_resource or not item_resource is Item:
		# 只显示一次错误
		if not _has_shown_error:
			push_error("道具拾取物没有设置有效的道具资源!")
			_has_shown_error = true

		# 尝试加载默认道具
		item_resource = load("res://Resources/GasItem.tres")

	# 创建视觉指示器（如果没有子节点）
	if get_child_count() == 0:
		var mesh_instance = MeshInstance3D.new()
		add_child(mesh_instance)

		# 创建默认网格
		var mesh = BoxMesh.new()
		mesh.size = Vector3(0.3, 0.3, 0.3)
		mesh_instance.mesh = mesh

		# 设置默认材质
		var material = StandardMaterial3D.new()
		material.albedo_color = Color(0.2, 0.8, 0.2)
		mesh_instance.set_surface_override_material(0, material)

	# 创建碰撞形状（如果没有）
	if not has_node("CollisionShape3D"):
		var collision_shape = CollisionShape3D.new()
		add_child(collision_shape)

		# 创建默认的球形碰撞
		var sphere_shape = SphereShape3D.new()
		sphere_shape.radius = 0.5
		collision_shape.shape = sphere_shape

# 处理帧更新
func _process(delta: float) -> void:
	_time_passed += delta

	# 自动旋转
	if auto_rotate:
		rotate_y(rotation_speed * delta)

	# 悬浮效果
	if hover_amplitude > 0:
		var hover_offset = sin(_time_passed * hover_speed) * hover_amplitude
		global_position.y = _initial_position.y + hover_offset

	# 处理玩家接近的高亮效果
	if _player_nearby and highlight_on_near:
		_apply_highlight_materials()

# 当玩家进入区域时
func _on_body_entered(body: Node3D) -> void:
	# 检查是否是玩家
	if body.is_in_group("player"):
		_player_nearby = true

		# 执行拾取逻辑
		_pickup(body)

# 执行拾取动作
func _pickup(player: Node3D) -> void:
	# 检查道具资源是否有效
	if not item_resource or not item_resource is Item:
		push_error("尝试拾取无效道具资源")
		return

	# 检查玩家背包是否已满
	var inventory_full = false
	if player.has_method("get_items"):
		var items = player.get_items()
		inventory_full = items.size() >= 2  # 假设道具栏上限为2

	if inventory_full:
		return

	# 设置碰撞为禁用，防止多次拾取
	collision_layer = 0
	collision_mask = 0

	# 发出拾取信号，让ItemSystem处理道具添加逻辑
	emit_signal("picked_up", item_resource, player)

	# 等待一下确保道具已添加到玩家背包，然后销毁自己
	await get_tree().create_timer(0.1).timeout
	queue_free()

# 应用高亮材质
func _apply_highlight_materials() -> void:
	var meshes = []
	for child in get_children():
		if child is MeshInstance3D:
			meshes.append(child)

	for i in range(min(meshes.size(), _highlight_materials.size())):
		meshes[i].set_surface_override_material(0, _highlight_materials[i])
