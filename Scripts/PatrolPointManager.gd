extends Node

# 巡逻点管理器
# 负责将巡逻点分配给敌人

# 信号
signal patrol_points_assigned

# 巡逻点分配策略
enum AssignStrategy { RANDOM, NEAREST, ROUND_ROBIN }
@export var assign_strategy: AssignStrategy = AssignStrategy.NEAREST

# 在游戏开始时自动分配巡逻点
@export var auto_assign_patrol_points := true

# 当前分配的巡逻点索引（用于轮询策略）
var _current_patrol_point_index := 0

# 场景中的巡逻点节点
var _patrol_point_nodes := []

# 初始化完成标志
var _initialization_complete := false

func _ready() -> void:
	# 添加到巡逻管理器组
	add_to_group("patrol_managers")

	# 在下一帧执行初始化，确保场景已完全加载
	call_deferred("_initialize")

# 延迟初始化，确保在游戏开始前完成
func _initialize() -> void:
	# 等待场景树准备好
	await get_tree().process_frame

	# 查找场景中的所有巡逻点
	find_patrol_points()

	# 自动分配巡逻点
	if auto_assign_patrol_points:
		assign_patrol_points_to_enemies()

	# 标记初始化完成
	_initialization_complete = true

	# 发出信号
	patrol_points_assigned.emit()

# 查找场景中的所有巡逻点
func find_patrol_points() -> void:
	# 清除现有巡逻点
	_patrol_point_nodes.clear()

	# 获取NavigationRegion3D节点
	var nav_region = get_parent().get_node_or_null("NavigationRegion3D")
	if not nav_region:
		push_error("PatrolPointManager: NavigationRegion3D节点不存在")
		return

	# 查找所有巡逻点节点
	for child in nav_region.get_children():
		if child.is_in_group("patrol_points") or "PatrolPoint" in child.name:
			_patrol_point_nodes.append(child)

	# 如果没有找到巡逻点，打印警告
	if _patrol_point_nodes.is_empty():
		push_warning("PatrolPointManager: 没有找到巡逻点")


# 将巡逻点分配给敌人
func assign_patrol_points_to_enemies() -> void:
	# 获取所有敌人
	var enemies = get_tree().get_nodes_in_group("enemies")
	if enemies.is_empty():
		return

	# 如果没有巡逻点，为每个敌人分配当前位置作为巡逻点
	if _patrol_point_nodes.is_empty():
		for enemy in enemies:
			# 初始分配时不排除当前巡逻点
			assign_patrol_point_to_enemy(enemy, -1, false)

		# 发出信号
		patrol_points_assigned.emit()
		return

	# 根据策略分配巡逻点
	match assign_strategy:
		AssignStrategy.RANDOM:
			_assign_random_patrol_points(enemies)
		AssignStrategy.NEAREST:
			_assign_nearest_patrol_points(enemies)
		AssignStrategy.ROUND_ROBIN:
			_assign_round_robin_patrol_points(enemies)

	# 发出信号
	patrol_points_assigned.emit()

# 随机分配巡逻点
func _assign_random_patrol_points(enemies: Array) -> void:
	for enemy in enemies:
		# 随机选择一个巡逻点索引
		var point_index = randi() % _patrol_point_nodes.size()
		# 使用通用方法分配巡逻点，初始分配时不排除当前巡逻点
		assign_patrol_point_to_enemy(enemy, point_index, false)

# 分配最近的巡逻点
func _assign_nearest_patrol_points(enemies: Array) -> void:
	for enemy in enemies:
		# 找到最近的巡逻点
		var nearest_index = 0
		var nearest_distance = INF

		for i in range(_patrol_point_nodes.size()):
			var point = _patrol_point_nodes[i]
			var distance = enemy.global_position.distance_to(point.global_position)

			if distance < nearest_distance:
				nearest_distance = distance
				nearest_index = i

		# 使用通用方法分配巡逻点，初始分配时不排除当前巡逻点
		assign_patrol_point_to_enemy(enemy, nearest_index, false)

# 轮询分配巡逻点
func _assign_round_robin_patrol_points(enemies: Array) -> void:
	for enemy in enemies:
		# 轮询选择一个巡逻点
		var point_index = _current_patrol_point_index
		_current_patrol_point_index = (_current_patrol_point_index + 1) % _patrol_point_nodes.size()

		# 使用通用方法分配巡逻点，初始分配时不排除当前巡逻点
		assign_patrol_point_to_enemy(enemy, point_index, false)

# 不再需要存储上一次分配的巡逻点索引

# 存储每个敌人当前的巡逻点索引
var _current_assigned_points = {}

# 为单个敌人分配巡逻点
func assign_patrol_point_to_enemy(enemy: Node, point_index: int = -1, exclude_current: bool = true) -> bool:
	# 如果没有巡逻点，使用敌人当前位置作为巡逻点
	if _patrol_point_nodes.is_empty():

		# 设置敌人的导航目标为自身位置
		var nav_component = enemy.get_node_or_null("NavigationComponent")
		if nav_component and nav_component.has_method("set_target_position"):
			nav_component.set_target_position(enemy.global_position)

			# 保存位置到敌人
			if enemy.has_method("set_current_patrol_point"):
				enemy.set_current_patrol_point(enemy.global_position)

			return true

		return false

	# 获取敌人当前的巡逻点索引
	var current_point_index = _current_assigned_points.get(enemy, -1)

	# 如果只有一个巡逻点，直接使用它
	if _patrol_point_nodes.size() == 1:
		point_index = 0
	# 如果没有指定索引或索引无效，需要选择一个
	elif point_index < 0 or point_index >= _patrol_point_nodes.size():
		# 如果需要排除当前巡逻点且有多个巡逻点
		if exclude_current and _patrol_point_nodes.size() > 1 and current_point_index >= 0:
			var available_indices = []
			for i in range(_patrol_point_nodes.size()):
				if i != current_point_index:  # 排除当前巡逻点
					available_indices.append(i)

			if not available_indices.is_empty():
				point_index = available_indices[randi() % available_indices.size()]
			else:
				# 如果没有可用的巡逻点（理论上不应该发生），随机选择
				point_index = randi() % _patrol_point_nodes.size()
		else:
			# 不排除当前巡逻点，或者是第一次分配，随机选择
			point_index = randi() % _patrol_point_nodes.size()

	# 保存当前分配的巡逻点索引
	_current_assigned_points[enemy] = point_index

	# 获取巡逻点和位置
	var point = _patrol_point_nodes[point_index]
	var patrol_point_position = point.global_position

	# 详细日志：巡逻点分配信息
	print("[PATROL_DEBUG] 为敌人分配巡逻点:")
	print("  - 敌人名称: ", enemy.name)
	print("  - 巡逻点索引: ", point_index)
	print("  - 巡逻点名称: ", point.name)
	print("  - 巡逻点位置: ", patrol_point_position)
	print("  - 排除当前巡逻点: ", exclude_current)
	if exclude_current:
		var current_index = _current_assigned_points.get(enemy, -1)
		print("  - 之前的巡逻点索引: ", current_index)

	# 设置敌人的导航目标
	var nav_comp = enemy.get_node_or_null("NavigationComponent")
	if nav_comp and nav_comp.has_method("set_target_position"):
		nav_comp.set_target_position(patrol_point_position)

		# 保存位置到敌人
		if enemy.has_method("set_current_patrol_point"):
			enemy.set_current_patrol_point(patrol_point_position)

		print("[PATROL_DEBUG] 巡逻点分配成功")
		return true

	return false
