extends Node3D
class_name BlinkEffect

@export var life_time: float = 1.0  # 特效持续时间
@export var color: Color = Color(0.5, 0.5, 1.0, 1.0)  # 特效颜色

func _ready() -> void:
	# 创建一个简单的粒子效果
	var particles = GPUParticles3D.new()
	add_child(particles)
	
	# 创建粒子材质
	var material = ParticleProcessMaterial.new()
	material.emission_shape = ParticleProcessMaterial.EMISSION_SHAPE_SPHERE
	material.emission_sphere_radius = 0.3
	material.direction = Vector3(0, 1, 0)
	material.spread = 180.0
	material.gravity = Vector3(0, -1.0, 0)
	material.initial_velocity_min = 2.0
	material.initial_velocity_max = 5.0
	material.scale_min = 0.1
	material.scale_max = 0.3
	material.color = color
	particles.process_material = material
	
	# 创建粒子网格
	var mesh = SphereMesh.new()
	mesh.radius = 0.1
	mesh.height = 0.2
	particles.draw_pass_1 = mesh
	
	# 设置粒子参数
	particles.amount = 30
	particles.lifetime = 0.5
	particles.one_shot = true
	particles.explosiveness = 0.8
	particles.emitting = true
	
	# 添加自销毁定时器
	var timer = Timer.new()
	add_child(timer)
	timer.wait_time = life_time
	timer.one_shot = true
	timer.timeout.connect(_on_timer_timeout)
	timer.start()

func _on_timer_timeout() -> void:
	queue_free()  # 特效生命周期结束后自动销毁 