extends Item
class_name Torch

# 道具固定属性
var max_uses: int = 5  # 最大使用次数
var current_uses: int = 5  # 当前剩余使用次数
var _current_tree = null  # 当前目标树

func _init() -> void:
	# 设置道具基本属性
	item_name = "火把"
	item_description = "立即点燃一棵树木，最多可以使用5次。"
	is_one_time_use = false  # 不是一次性道具，可以使用多次
	cooldown_time = 0.0  # 无冷却时间
	item_category = "utility"

# 覆盖初始化函数
func initialize(item_owner = null) -> void:
	# 调用父类的初始化方法
	super.initialize(item_owner)
	current_uses = max_uses

# 设置当前目标树
func set_current_tree(tree) -> void:
	_current_tree = tree

# 清除当前目标树
func clear_current_tree() -> void:
	_current_tree = null

# 使用道具的逻辑
func use() -> bool:
	# 检查是否有有效的所有者（玩家）
	if not owner:
		return false

	# 检查是否还有剩余使用次数
	if current_uses <= 0:
		return false

	# 检查是否有目标树
	if not _current_tree:
		# 尝试获取最近的树
		if owner.has_method("get_nearest_tree"):
			_current_tree = owner.get_nearest_tree()

		# 如果仍然没有目标树，返回失败
		if not _current_tree:
			return false

	# 检查树是否可以被点燃（未燃烧或已熄灭）
	if not _current_tree.has_method("start_burning") or (_current_tree.has_method("is_burning") and _current_tree.is_burning()):
		return false

	# 点燃树
	_current_tree.start_burning()

	# 减少使用次数
	current_uses -= 1

	# 如果使用完毕，标记为已使用并设置为一次性道具
	if current_uses <= 0:
		was_used = true
		is_one_time_use = true  # 将多次使用道具转换为一次性道具，以便系统自动移除

	# 显示使用效果
	show_use_effects()

	# 更新道具UI以显示新的使用次数
	if owner.has_method("update_item_ui"):
		owner.update_item_ui()

	return true

# 显示使用效果
func show_use_effects() -> void:
	if not owner:
		return

	# 显示UI提示
	if owner.has_method("show_notification"):
		if current_uses > 0:
			owner.show_notification("树木已点燃！剩余使用次数：" + str(current_uses))
		else:
			owner.show_notification("树木已点燃！火把已用尽")

# 获取道具信息（重写父类方法，添加使用次数信息）
func get_info() -> Dictionary:
	var info = super.get_info()
	info["uses_remaining"] = current_uses
	info["max_uses"] = max_uses
	return info
