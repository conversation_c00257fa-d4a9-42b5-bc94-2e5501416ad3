extends Node3D

@export var player_vision_radius: float = 50.0  # 玩家视野半径
@export var enemy_vision_radius: float = 8.0   # 敌人视野半径
@export var fog_height: float = 0.1  # 迷雾高度
@export var fog_color_unexplored := Color(0.0, 0.0, 0.0, 1.0)  # 未探索区域迷雾颜色
@export var fog_color_explored := Color(0.0, 0.0, 0.0, 0.85)   # 已探索区域迷雾颜色(黑色，保持透明度)

# 存储导出的初始视野半径，用于在启用/禁用迷雾时恢复设置
# 初始值设为-1，表示尚未初始化
var _initial_player_vision_radius: float = -1

# 设置启用状态，但确保在_initial_player_vision_radius初始化后调用setter
@export var enabled := true:
	set = set_fog_enabled

# 迷雾状态枚举
enum FogState {
	UNEXPLORED,    # 未探索 - 黑色迷雾
	EXPLORED,      # 已探索但不可见 - 灰色迷雾
	VISIBLE        # 可见 - 无迷雾
}

# 存储每个物体的迷雾状态
var fog_states: Dictionary = {}
var explored_areas: Array[Vector2] = []  # 存储已探索的区域

# 节点引用
var player: Node3D
var enemies: Array[Node3D] = []
var fog_objects: Array[Node3D] = []

# 迷雾相关节点
var fog_plane: MeshInstance3D
var fog_material: ShaderMaterial

# 视野范围指示器
var vision_indicator: MeshInstance3D

# 确保在节点初始化时就保存初始视野半径，避免被其他地方修改
func _init():
	pass  # 删除print调用但保留函数

func _ready() -> void:
	# 在_ready中保存初始值，这时场景属性已完全加载
	if _initial_player_vision_radius < 0:
		_initial_player_vision_radius = player_vision_radius

	# 如果在编辑器中，不执行初始化逻辑
	if Engine.is_editor_hint():
		return

	# 等待一帧确保所有节点都已准备就绪
	if get_tree():
		await get_tree().process_frame

	# 获取相机设置
	var camera = get_viewport().get_camera_3d()
	if camera:
		# 确保相机可以看到所有层
		camera.cull_mask = 0xFFFFFFFF  # 启用所有层
	else:
		return

	# 创建迷雾平面
	_create_fog_plane()

	# 获取玩家节点
	if get_tree():
		player = get_tree().get_first_node_in_group("player")

	# 等待一小段时间
	if get_tree():
		await get_tree().create_timer(0.1).timeout

	# 初始化其他组件
	_create_vision_indicator()
	_init_fog_objects()

	# 如果迷雾系统被禁用，设置超大视野半径
	if not enabled:
		player_vision_radius = 150.0
		if fog_plane:
			fog_plane.visible = false

	# 在游戏开始时记录迷雾系统状态
	var _status_text = "启用" if enabled else "禁用"

func _create_fog_plane() -> void:
	# 创建迷雾平面
	var plane_mesh = PlaneMesh.new()
	plane_mesh.size = Vector2(2000, 2000)  # 增大尺寸以确保完全覆盖
	plane_mesh.subdivide_width = 200
	plane_mesh.subdivide_depth = 200

	# 创建shader材质
	fog_material = ShaderMaterial.new()
	fog_material.shader = preload("res://Shaders/FogOfWar.gdshader")
	fog_material.render_priority = 100  # 确保渲染在最上层

	# 确保颜色设置正确
	fog_color_unexplored = Color(0.0, 0.0, 0.0, 1.0)  # 黑色不透明
	fog_color_explored = Color(0.0, 0.0, 0.0, 0.85)    # 黑色，保持透明度

	# 设置初始shader参数
	fog_material.set_shader_parameter("player_position", Vector2(0, 0))
	fog_material.set_shader_parameter("vision_radius", player_vision_radius)
	fog_material.set_shader_parameter("fog_color_unexplored", fog_color_unexplored)
	fog_material.set_shader_parameter("fog_color_explored", fog_color_explored)
	fog_material.set_shader_parameter("num_explored_areas", 0)  # 初始没有已探索区域

	# 初始化相机矩阵参数，防止shader初始化时出错
	var identity_matrix = Transform3D()
	fog_material.set_shader_parameter("camera_matrix", identity_matrix)

	# 确保创建空的已探索位置数组
	var empty_explored_positions = PackedVector2Array()
	for i in range(100):
		empty_explored_positions.append(Vector2(0, 0))
	fog_material.set_shader_parameter("explored_positions", empty_explored_positions)

	# 创建迷雾平面节点
	fog_plane = MeshInstance3D.new()
	fog_plane.mesh = plane_mesh
	fog_plane.material_override = fog_material
	fog_plane.position = Vector3.ZERO  # 将平面置于原点
	fog_plane.position.y = fog_height  # 设置高度
	fog_plane.rotation = Vector3.ZERO
	fog_plane.cast_shadow = GeometryInstance3D.SHADOW_CASTING_SETTING_OFF

	# 设置渲染层级
	fog_plane.layers = 1  # 确保在主要渲染层上可见

	# 确保透明度设置正确
	fog_material.set_shader_parameter("depth_test_disabled", true)

	add_child(fog_plane)

func _create_vision_indicator() -> void:
	# 跳过创建视野指示器，直接返回
	return

	# 以下代码是旧版本的实现，当前被禁用
	# 创建视野指示器网格（圆形）
	# var cylinder_mesh = CylinderMesh.new()
	# cylinder_mesh.top_radius = player_vision_radius  # 设置圆形半径
	# cylinder_mesh.bottom_radius = player_vision_radius
	# cylinder_mesh.height = 0.01  # 非常扁平的圆柱体
	# cylinder_mesh.radial_segments = 32  # 设置边缘的平滑度
	# cylinder_mesh.rings = 1

	# 创建视野范围材质
	# var vision_material = StandardMaterial3D.new()
	# vision_material.albedo_color = Color(0.0, 0.7, 1.0, 0.3)  # 蓝色半透明
	# vision_material.emission_enabled = true
	# vision_material.emission = Color(0.0, 0.7, 1.0, 1.0)
	# vision_material.emission_energy = 0.5
	# vision_material.transparency = BaseMaterial3D.TRANSPARENCY_ALPHA
	# vision_material.cull_mode = BaseMaterial3D.CULL_DISABLED  # 确保两面都可见

	# 创建视野范围指示器节点
	# vision_indicator = MeshInstance3D.new()
	# vision_indicator.mesh = cylinder_mesh
	# vision_indicator.material_override = vision_material
	# vision_indicator.position.y = 0.1  # 稍微抬高一点，避免与地面重叠

	# 修正视野指示器的形状，使其在45度相机视角下呈现为圆形
	# 沿z轴（深度方向）拉伸网格，补偿相机视角的压缩效果
	# var camera_angle_correction = 1.4142  # sqrt(2)，45度角的补偿因子
	# vision_indicator.scale.z = camera_angle_correction  # 拉伸z轴方向

	# 禁用视野指示器显示
	# vision_indicator.visible = false

	# add_child(vision_indicator)

func _init_fog_objects() -> void:
	# 清空当前对象列表
	fog_objects.clear()
	fog_states.clear()

	# 从配置文件加载迷雾对象数据
	_load_fog_objects_from_config()

	# 根据迷雾状态设置可见性
	for obj in fog_objects:
		if is_instance_valid(obj):
			fog_states[obj] = FogState.UNEXPLORED
			_update_object_visibility(obj, FogState.UNEXPLORED)

# 从配置文件加载迷雾对象数据
func _load_fog_objects_from_config() -> void:
	# 清空当前对象列表
	fog_objects.clear()

	# 确保场景树已经准备好
	if not is_inside_tree():
		return

	# 获取玩家节点
	if get_tree():
		player = get_tree().get_first_node_in_group("player")

	# 尝试从场景树中获取GameData节点
	var game_data = null
	if Engine.has_singleton("GameData"):
		game_data = Engine.get_singleton("GameData")
	else:
		# 如果没有全局单例，尝试从场景树中获取
		if get_tree() and get_tree().root:
			game_data = get_tree().root.get_node_or_null("GameData")

	if game_data:
		# 从游戏数据中获取敌人节点
		if game_data.has_method("get_enemies_data"):
			var enemies_data = game_data.get_enemies_data()
			if enemies_data:
				for enemy in enemies_data:
					if is_instance_valid(enemy):
						fog_objects.append(enemy)

		# 从游戏数据中获取树木节点
		if game_data.has_method("get_trees_data"):
			var trees_data = game_data.get_trees_data()

			if trees_data:
				var _added_count = 0

				# 从场景树中获取所有树木节点
				var scene_trees = get_tree().get_nodes_in_group("trees")

				# 遍历游戏数据中的树木数据
				for tree_data in trees_data:
					# 如果是字典，根据名称在场景树中查找实际的树木节点
					if typeof(tree_data) == TYPE_DICTIONARY and tree_data.has("name"):
						var tree_name = tree_data["name"]

						# 在场景树中查找对应名称的树木节点
						for scene_tree in scene_trees:
							if scene_tree.name == tree_name:
								# 找到了实际的树木节点
								fog_objects.append(scene_tree)
								break

					# 如果是实际的节点对象，直接添加
					elif is_instance_valid(tree_data):
						fog_objects.append(tree_data)
	else:
		# 如果没有GameData，尝试直接从场景树中获取敌人和树木
		if get_tree():
			# 获取敌人 - 只从enemies组获取
			var enemy_nodes = get_tree().get_nodes_in_group("enemies")

			for enemy in enemy_nodes:
				if is_instance_valid(enemy):
					fog_objects.append(enemy)

			# 获取树木
			var trees = get_tree().get_nodes_in_group("trees")
			for tree in trees:
				if is_instance_valid(tree):
					fog_objects.append(tree)


func _process(_delta: float) -> void:
	if not player or not fog_material:
		return

	if not enabled:
		# 如果迷雾系统被禁用，设置超大视野半径但保持正常逻辑执行
		if player_vision_radius < 150.0:
			player_vision_radius = 150.0
			_update_fog_material()  # 保留这个重要的函数调用

		# 确保迷雾平面不可见
		if fog_plane:
			fog_plane.visible = false
	else:
		# 确保迷雾平面可见
		if fog_plane and not fog_plane.visible:
			fog_plane.visible = true

	# 无论迷雾是否启用，都更新已探索区域和物体状态
	# 这样在禁用迷雾时，超大视野半径会使所有物体状态变为VISIBLE
	_update_explored_areas()
	_update_fog_states()

	# 更新所有物体的可见性
	var objects_to_remove = []
	for obj in fog_objects:
		if is_instance_valid(obj) and obj in fog_states:
			_update_object_visibility(obj, fog_states[obj])
		else:
			# 标记无效对象以便移除
			objects_to_remove.append(obj)

	# 移除无效对象
	for obj in objects_to_remove:
		fog_objects.erase(obj)
		if obj in fog_states:
			fog_states.erase(obj)

	# 更新迷雾材质参数
	_update_fog_material()

func _update_explored_areas() -> void:
	# 获取玩家位置
	var player_pos = Vector2(player.position.x, player.position.z)

	# 检查是否在已探索区域内
	var is_in_explored = false
	for area in explored_areas:
		if area.distance_to(player_pos) < player_vision_radius:
			is_in_explored = true
			break

	# 如果不在已探索区域内，添加新的已探索区域
	if not is_in_explored:
		explored_areas.append(player_pos)

		# 限制已探索区域的数量，保留较新的区域
		if explored_areas.size() > 100:
			explored_areas.remove_at(0)

func _update_fog_states() -> void:
	# 确保玩家存在
	if not player:
		return

	var player_pos = Vector2(player.position.x, player.position.z)

	for obj in fog_objects:
		if not is_instance_valid(obj):
			continue

		# 只跟踪树木对象
		var _is_tree = obj.is_in_group("trees")

		var obj_pos = Vector2(obj.position.x, obj.position.z)
		var distance_to_player = player_pos.distance_to(obj_pos)

		# 记录当前状态
		var old_state = fog_states.get(obj, FogState.UNEXPLORED)
		var new_state = old_state

		# 检查是否在玩家视野范围内
		if distance_to_player <= player_vision_radius:
			new_state = FogState.VISIBLE
		# 检查是否在已探索区域内
		else:
			var is_explored = false
			for area in explored_areas:
				if area.distance_to(obj_pos) < player_vision_radius:
					is_explored = true
					break

			if is_explored:
				# 如果是敌人，在已探索区域也保持不可见
				if obj.is_in_group("enemies"):
					new_state = FogState.UNEXPLORED
				else:
					new_state = FogState.EXPLORED
			else:
				new_state = FogState.UNEXPLORED
		# 更新状态
		fog_states[obj] = new_state

func _update_object_visibility(obj: Node3D, state: FogState) -> void:
	# 检查对象是否有效
	if not is_instance_valid(obj):
		return

	# 检查对象是否仍在场景树中
	if not obj.is_inside_tree():
		fog_objects.erase(obj)  # 从列表中移除无效对象
		return

	# 特殊处理地板对象 - 地板始终可见，但在未探索区域用迷雾覆盖
	if obj.is_in_group("floor"):
		obj.visible = true
		return

	# 根据状态设置物体的可见性
	match state:
		FogState.UNEXPLORED:
			if obj.has_method("set_fog_state"):
				obj.set_fog_state(FogState.UNEXPLORED)
			else:
				obj.visible = false
		FogState.EXPLORED:
			if obj.has_method("set_fog_state"):
				obj.set_fog_state(FogState.EXPLORED)
			else:
				# 如果是敌人，在已探索状态下也保持不可见
				if obj.is_in_group("enemies"):
					obj.visible = false
				else:
					obj.visible = true
		FogState.VISIBLE:
			if obj.has_method("set_fog_state"):
				obj.set_fog_state(FogState.VISIBLE)
			else:
				obj.visible = true

func _update_fog_material() -> void:
	if not player or not fog_material:
		return

	# 更新shader参数
	var player_pos_2d = Vector2(player.global_position.x, player.global_position.z)
	fog_material.set_shader_parameter("player_position", player_pos_2d)

	# 获取并传递相机矩阵
	var camera = get_viewport().get_camera_3d()
	if camera:
		fog_material.set_shader_parameter("camera_matrix", camera.global_transform)

	# 确保颜色参数正确设置
	fog_material.set_shader_parameter("fog_color_unexplored", fog_color_unexplored)  # 黑色
	fog_material.set_shader_parameter("fog_color_explored", fog_color_explored)  # 黑色，保持透明度

	# 更新已探索区域
	var explored_positions = PackedVector2Array()
	for pos in explored_areas:
		explored_positions.append(pos)

	# 确保数组大小为100
	while explored_positions.size() < 100:
		explored_positions.append(Vector2(0, 0))

	fog_material.set_shader_parameter("explored_positions", explored_positions)
	fog_material.set_shader_parameter("num_explored_areas", explored_areas.size())
	fog_material.set_shader_parameter("vision_radius", player_vision_radius)


# 检查两个单位是否在彼此的视野范围内
func can_see_each_other(unit1: Node3D, unit2: Node3D) -> bool:
	var pos1 = Vector2(unit1.position.x, unit1.position.z)
	var pos2 = Vector2(unit2.position.x, unit2.position.z)

	# 获取两个单位的视野半径
	var radius1 = player_vision_radius if unit1.is_in_group("player") else enemy_vision_radius
	var radius2 = player_vision_radius if unit2.is_in_group("player") else enemy_vision_radius

	# 计算距离
	var distance = pos1.distance_to(pos2)

	# 如果距离小于两个单位的视野半径之和，则可以看到对方
	return distance <= (radius1 + radius2)

# 注册一个需要战争迷雾效果的对象
func register_fog_object(obj: Node3D) -> void:
	if not fog_objects.has(obj):
		fog_objects.append(obj)
		fog_states[obj] = FogState.UNEXPLORED
		_update_object_visibility(obj, FogState.UNEXPLORED)

# 注销一个战争迷雾对象
func unregister_fog_object(obj: Node3D) -> void:
	fog_objects.erase(obj)

# 设置迷雾系统启用状态
func set_fog_enabled(value: bool) -> void:
	# 确保_initial_player_vision_radius已初始化
	if _initial_player_vision_radius < 0:
		_initial_player_vision_radius = player_vision_radius

	# 如果状态没有变化，不做任何操作
	if enabled == value:
		return


	enabled = value

	# 确保迷雾平面的可见性与启用状态一致
	if fog_plane:
		fog_plane.visible = enabled

	# 如果禁用迷雾，只需设置超大视野半径
	if not enabled:
		# 设置超大视野半径，使所有区域可见
		player_vision_radius = 150.0

		# 只有在节点已添加到场景树时才初始化迷雾对象
		if is_inside_tree():
			_init_fog_objects()


		# 当迷雾禁用时，将所有敌人的迷雾状态设置为VISIBLE
		for obj in fog_objects:
			if is_instance_valid(obj) and obj.is_in_group("enemies"):
				fog_states[obj] = FogState.VISIBLE
				_update_object_visibility(obj, FogState.VISIBLE)
	else:
		# 恢复为编辑器中设置的初始视野半径
		player_vision_radius = _initial_player_vision_radius

		# 只有在节点已添加到场景树时才初始化迷雾对象
		if is_inside_tree():
			_init_fog_objects()

# 检查迷雾系统是否启用
func is_enabled() -> bool:
	return enabled

# 设置整个地图为可见（用于禁用迷雾时）
func set_all_visible() -> void:
	if not fog_material:
		return

	# 创建覆盖整个地图的已探索位置
	var full_map_positions = PackedVector2Array()
	var map_size = 500.0
	var step = 20.0

	for x in range(-map_size, map_size + 1, step):
		for z in range(-map_size, map_size + 1, step):
			full_map_positions.append(Vector2(x, z))

	# 限制数组大小为100
	while full_map_positions.size() > 100:
		full_map_positions.remove_at(full_map_positions.size() - 1)

	# 更新shader参数
	fog_material.set_shader_parameter("explored_positions", full_map_positions)
	fog_material.set_shader_parameter("num_explored_areas", full_map_positions.size())
	fog_material.set_shader_parameter("vision_radius", 25.0)

	# 设置所有物体为可见
	for obj in fog_objects:
		if is_instance_valid(obj):
			obj.visible = true
