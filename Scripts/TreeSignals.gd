extends Node

# 树木状态变化信号
signal tree_caught_fire(tree: Node3D)  # 树木着火时发出
signal tree_extinguished(tree: Node3D)  # 树木被灭火时发出
signal tree_burned_out(tree: Node3D)    # 树木完全燃烧殆尽时发出
# 新增：GAS道具加速燃烧信号
signal tree_gas_accelerated(tree: Node3D)  # 树木被GAS道具加速燃烧时发出

# 树木交互信号
signal tree_ignited(tree: Node3D, igniter: Node3D)  # 树木被点燃时发出（包含点火者信息）
signal tree_extinguishing(tree: Node3D, extinguisher: Node3D)  # 正在被灭火时发出
signal tree_extinguish_completed(tree: Node3D, extinguisher: Node3D)  # 灭火完成时发出

# 火势蔓延信号
signal fire_spread_started(from_tree: Node3D, to_tree: Node3D)  # 火势开始蔓延到其他树时发出
signal fire_spread_completed(from_tree: Node3D, to_tree: Node3D)  # 火势蔓延完成时发出

# 树木生命周期信号
signal tree_health_changed(tree: Node3D, old_health: float, new_health: float)  # 树木生命值改变时发出
signal tree_destroyed(tree: Node3D)  # 树木被破坏时发出

func _ready() -> void:
	# 设置为自动加载单例
	process_mode = Node.PROCESS_MODE_ALWAYS

# 便捷方法，用于发送信号
func emit_tree_caught_fire(tree: Node3D) -> void:
	emit_signal("tree_caught_fire", tree)

func emit_tree_extinguished(tree: Node3D) -> void:
	emit_signal("tree_extinguished", tree)

func emit_tree_burned_out(tree: Node3D) -> void:
	emit_signal("tree_burned_out", tree)

# 新增：GAS道具加速燃烧信号的便捷方法
func emit_tree_gas_accelerated(tree: Node3D) -> void:
	emit_signal("tree_gas_accelerated", tree)

func emit_tree_ignited(tree: Node3D, igniter: Node3D) -> void:
	emit_signal("tree_ignited", tree, igniter)

func emit_tree_extinguishing(tree: Node3D, extinguisher: Node3D) -> void:
	emit_signal("tree_extinguishing", tree, extinguisher)

func emit_tree_extinguish_completed(tree: Node3D, extinguisher: Node3D) -> void:
	emit_signal("tree_extinguish_completed", tree, extinguisher)

func emit_fire_spread_started(from_tree: Node3D, to_tree: Node3D) -> void:
	emit_signal("fire_spread_started", from_tree, to_tree)

func emit_fire_spread_completed(from_tree: Node3D, to_tree: Node3D) -> void:
	emit_signal("fire_spread_completed", from_tree, to_tree)

func emit_tree_health_changed(tree: Node3D, old_health: float, new_health: float) -> void:
	emit_signal("tree_health_changed", tree, old_health, new_health)

func emit_tree_destroyed(tree: Node3D) -> void:
	emit_signal("tree_destroyed", tree)
