extends Node3D

# Camera控制器 - 负责管理游戏中的主相机
# 从Player中独立出来，提供更好的架构设计

@export_group("Camera Settings")
@export var camera_distance := 15.0  # 相机距离
@export var camera_height := 15.0  # 相机高度
@export var camera_smooth_speed := 5.0  # 相机平滑移动速度
@export var camera_right_offset := 0.0  # 相机右侧偏移

# 相机旋转
@export var camera_rotation_x: float = -0.5  # 初始俯视角度
@export var camera_rotation_y: float = 0.0
@export var camera_sensitivity: float = 0.003
@export var camera_smoothing: float = 10.0
@export var camera_min_angle: float = -1.2  # 约-70度
@export var camera_max_angle: float = 0.5   # 约30度
@export var camera_angle: float = -0.5      # 固定模式下的相机角度
@export var is_camera_free: bool = true     # 是否自由旋转相机
@export var mouse_sensitivity := 0.005  # 鼠标灵敏度

# 内部变量
var _target_camera_pos: Vector3 = Vector3.ZERO
var _player: CharacterBody3D
var _spring_arm: SpringArm3D
var _camera: Camera3D

func _ready() -> void:
	# 获取相机组件
	_spring_arm = get_node_or_null("%SpringArm3D")
	_camera = get_node_or_null("%Camera3D")
	
	# 查找Player
	_player = get_tree().get_first_node_in_group("player")
	
	if not _spring_arm or not _camera:
		push_error("CameraController: 无法找到SpringArm3D或Camera3D节点")
		return
	
	# 初始化相机
	initialize_camera()

func _input(event: InputEvent) -> void:
	if not _player:
		return
		
	# 处理鼠标移动事件，控制相机旋转
	if event is InputEventMouseMotion and is_camera_free and Input.get_mouse_mode() == Input.MOUSE_MODE_CAPTURED:
		# 更新相机旋转角度
		camera_rotation_y -= event.relative.x * mouse_sensitivity
		camera_rotation_x -= event.relative.y * mouse_sensitivity

		# 限制上下旋转角度，防止过度旋转
		camera_rotation_x = clamp(camera_rotation_x, -PI/2 + 0.1, PI/2 - 0.1)

		# 应用旋转
		rotation.x = camera_rotation_x
		rotation.y = camera_rotation_y

	# 切换相机模式 - 保持鼠标可见
	if event.is_action_pressed("ui_cancel"):
		Input.set_mouse_mode(Input.MOUSE_MODE_VISIBLE)

	# 切换相机自由模式
	if event.is_action_pressed("ui_focus_next"):  # Tab键
		is_camera_free = !is_camera_free
		if is_camera_free:
			print("相机已切换为自由模式")
		else:
			print("相机已切换为固定模式")
			# 重置相机位置
			initialize_camera()

func _process(delta: float) -> void:
	if not _player:
		return
		
	# 更新相机位置跟随玩家
	update_camera_position(delta)

# 初始化相机系统
func initialize_camera() -> void:
	if not _spring_arm or not _camera:
		return
		
	if not is_camera_free:
		# 固定模式：设置相机为半俯视状态
		rotation.x = camera_angle  # 设置为-45度半俯视角度
		rotation.y = 0  # 固定水平旋转为0

		# 重置旋转变量
		camera_rotation_x = camera_angle
		camera_rotation_y = 0
	else:
		# 自由模式：保持当前旋转
		rotation.x = camera_rotation_x
		rotation.y = camera_rotation_y

	# 调整相机视野
	var base_fov = 75.0  # 增大基础视野角度，提供更广阔的视角
	_camera.fov = base_fov  # 使用基础视野角度，不再进行补偿

	# 初始化相机目标位置
	update_camera_target()

	# 捕获鼠标
	Input.set_mouse_mode(Input.MOUSE_MODE_CAPTURED)

# 更新相机目标位置
func update_camera_target() -> void:
	_target_camera_pos = Vector3(
		camera_right_offset,
		camera_height,
		camera_distance
	)

# 更新相机位置
func update_camera_position(delta: float) -> void:
	if not _player or not _spring_arm:
		return
		
	# 相机枢轴跟随玩家
	global_position = _player.global_position
	
	# 平滑移动SpringArm到目标位置
	_spring_arm.position = _spring_arm.position.lerp(_target_camera_pos, camera_smooth_speed * delta)
	_spring_arm.spring_length = camera_distance

# 获取相机对象（供其他系统使用）
func get_camera() -> Camera3D:
	return _camera

# 获取相机前方向（供其他系统使用）
func get_camera_forward() -> Vector3:
	if _camera:
		var forward = _camera.global_basis.z
		forward.y = 0
		return forward.normalized()
	return Vector3.FORWARD

# 获取相机右方向（供其他系统使用）
func get_camera_right() -> Vector3:
	if _camera:
		var right = _camera.global_basis.x
		right.y = 0
		return right.normalized()
	return Vector3.RIGHT
