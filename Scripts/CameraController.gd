extends Node3D

# 固定Camera控制器 - 管理固定半俯视角相机
# 只负责跟随Player移动，不支持旋转

@export_group("Fixed Camera Settings")
@export var fixed_rotation_x: float = -0.5  # 固定俯视角度
@export var fixed_rotation_y: float = 0.0   # 固定水平角度
@export var camera_fov: float = 75.0  # 相机视野角度

# 内部变量
var _player: CharacterBody3D
var _spring_arm: SpringArm3D
var _camera: Camera3D

func _ready() -> void:
	# 获取相机组件
	_spring_arm = get_node_or_null("%SpringArm3D")
	_camera = get_node_or_null("%Camera3D")

	# 查找Player
	_player = get_tree().get_first_node_in_group("player")

	if not _spring_arm or not _camera:
		push_error("FixedCameraController: 无法找到SpringArm3D或Camera3D节点")
		return

	# 初始化固定相机
	initialize_fixed_camera()

func _input(event: InputEvent) -> void:
	# 固定Camera不处理任何输入事件
	# 保持鼠标可见用于UI交互
	if event.is_action_pressed("ui_cancel"):
		Input.set_mouse_mode(Input.MOUSE_MODE_VISIBLE)

func _process(delta: float) -> void:
	if not _player:
		return

	# 更新相机位置跟随玩家
	update_camera_position(delta)

# 初始化固定相机系统
func initialize_fixed_camera() -> void:
	if not _spring_arm or not _camera:
		return

	# 设置固定的半俯视角度
	rotation.x = fixed_rotation_x  # 固定俯视角度
	rotation.y = fixed_rotation_y  # 固定水平角度

	# 设置相机视野
	_camera.fov = camera_fov

	# 保持鼠标可见，不捕获鼠标
	Input.set_mouse_mode(Input.MOUSE_MODE_VISIBLE)

	# 移除update_camera_target()调用，保持编辑器中设置的Camera位置

# 移除update_camera_target()函数，不再需要动态计算Camera位置

# 更新相机位置（只跟随，不旋转）
func update_camera_position(delta: float) -> void:
	if not _player:
		return

	# 相机枢轴跟随玩家
	global_position = _player.global_position

	# 确保旋转始终保持固定
	rotation.x = fixed_rotation_x
	rotation.y = fixed_rotation_y

	# 移除SpringArm位置控制，保持编辑器中设置的Camera位置

# 获取相机对象（供其他系统使用）
func get_camera() -> Camera3D:
	return _camera

# 获取固定的相机前方向（供其他系统使用）
func get_camera_forward() -> Vector3:
	# 固定Camera的前方向始终是固定的
	var forward = Vector3(0, 0, 1)  # 固定朝向Z轴正方向
	return forward.normalized()

# 获取固定的相机右方向（供其他系统使用）
func get_camera_right() -> Vector3:
	# 固定Camera的右方向始终是固定的
	var right = Vector3(1, 0, 0)  # 固定朝向X轴正方向
	return right.normalized()
