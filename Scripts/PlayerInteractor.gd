# 清除交互状态，用于重置交互器状态
func clear_interaction_state() -> void:
    # 如果有保存当前交互物体的变量，将其重置
    if has_method("_on_interactor_area_exited"):
        # 模拟所有物体离开交互区域
        var areas = get_overlapping_areas()
        for area in areas:
            _on_interactor_area_exited(area)

# 扫描交互区域，强制重新检测
func scan_for_interactions() -> void:
    # 获取当前重叠的区域
    var areas = get_overlapping_areas()
    # 模拟所有物体进入交互区域
    for area in areas:
        if has_method("_on_interactor_area_entered"):
            _on_interactor_area_entered(area) 