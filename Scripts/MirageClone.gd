extends "res://Scripts/CharacterBase.gd"
class_name MirageClone

# 玩家引用
var player: Node3D = null
# 移动速度增量
var speed_bonus: float = 1.0
# 手动动画控制
var _is_playing_manual_animation: bool = false
var _manual_animation_timer: float = 0.0

# 添加必要的变量声明
var _skin: Node3D = null  # 这个变量在父类中没有，所以保留
# 注意：_current_tree 和 _animation_player 已在父类中声明，不需要在这里重复声明

# 重写_ready方法，添加错误处理
func _ready():
	# 不调用super._ready()，因为它依赖于@onready变量
	# 而是手动初始化必要的组件

	# 添加到玩家组
	add_to_group("player")

	# 设置默认移动速度（如果没有从玩家复制）
	move_speed = 9.0  # 默认值 = 玩家速度(8.0) + speed_bonus(1.0)

	# 注意：碰撞层和掩码将在initialize方法中从玩家复制
	# 这里不再硬编码设置

	# MirageClone的交互器直接作为子节点

	# 初始化交互系统
	initialize_interaction()

# 覆盖敌人捕获钩子方法
func _on_enemy_capture() -> void:
	# 分身被捕获时只是消失，不会导致游戏结束
	queue_free()

# 覆盖相机初始化钩子方法
func _on_initialize_camera() -> void:
	# 分身不需要初始化相机
	pass

func initialize(player_ref: Node3D):
	player = player_ref

	# 复制玩家的基础属性
	if player:
		# 直接从玩家复制碰撞设置
		collision_layer = player.collision_layer
		collision_mask = player.collision_mask

		# 如果玩家有move_speed属性，复制它
		if player.get("move_speed") != null:
			move_speed = player.move_speed + speed_bonus

		# 如果玩家有jump_impulse属性，复制它
		if player.get("jump_impulse") != null:
			jump_impulse = player.jump_impulse

		# 如果玩家有gravity属性，复制它
		if player.get("_gravity") != null:
			gravity = player._gravity

		# 查找皮肤模型
		_skin = get_node_or_null("Player01")
		if not _skin and get_child_count() > 0:
			for child in get_children():
				if child is Node3D and not child is CollisionShape3D:
					_skin = child
					break

	# 重新初始化动画播放器
	_animation_player = _find_animation_player()

	# 设置初始朝向与玩家一致
	if player and player.get("_last_input_direction") != null:
		var direction = Vector2.ZERO

		# 检查_last_input_direction的类型
		if player._last_input_direction is Vector2:
			direction = player._last_input_direction
		elif player._last_input_direction is Vector3:
			direction = Vector2(player._last_input_direction.x, player._last_input_direction.z)

		if direction != Vector2.ZERO:
			# 计算朝向角度
			var angle = atan2(direction.x, direction.y)
			# 设置Y轴旋转
			if _skin:
				_skin.rotation.y = angle

# 重写_physics_process方法，添加错误处理
func _physics_process(delta):
	# 检查玩家引用
	if not player or not is_instance_valid(player):
		queue_free()
		return

	# 更新手动动画计时器
	if _is_playing_manual_animation:
		_manual_animation_timer -= delta
		if _manual_animation_timer <= 0:
			_is_playing_manual_animation = false
			_manual_animation_timer = 0.0

	# 处理交互器更新
	_physics_process_interactor(delta)

	# 处理树木交互
	handle_tree_interaction()

	# 检查是否被敌人捕获
	_check_enemy_capture()

	# 处理移动 - 使用与Player.gd相同的相机相对移动逻辑
	var input_dir = Input.get_vector("move_left", "move_right", "move_up", "move_down", 0.4)
	# 只在有输入时更新_last_input_direction，保持最后的有效方向
	if input_dir.length() > 0.1:
		_last_input_direction = input_dir

	# 获取玩家相机方向
	var camera_forward = Vector3.FORWARD
	var camera_right = Vector3.RIGHT

	# 尝试从场景获取相机方向
	var camera = get_viewport().get_camera_3d()
	if camera:
		camera_forward = camera.global_basis.z
		camera_right = camera.global_basis.x

		# 确保方向在水平面上
		camera_forward.y = 0
		camera_forward = camera_forward.normalized()
		camera_right.y = 0
		camera_right = camera_right.normalized()

	# 计算移动方向（基于相机方向）
	var direction = camera_forward * input_dir.y + camera_right * input_dir.x
	direction.y = 0
	direction = direction.normalized()

	if direction:
		# 设置水平速度 - 比玩家快1个单位
		velocity.x = direction.x * move_speed
		velocity.z = direction.z * move_speed

		# 更新角色朝向 - 只在有有效移动时更新朝向
		if _skin and direction.length() > 0.2:
			# 创建一个Vector3用于计算角度
			var direction_3d = Vector3(direction.x, 0, direction.z)

			# 由于模型已经顺时针旋转了90度，我们需要调整目标角度
			# 使用RIGHT而不是BACK作为基准方向
			var target_angle = Vector3.RIGHT.signed_angle_to(direction_3d, Vector3.UP)

			# 使用固定的旋转速度
			_skin.global_rotation.y = lerp_angle(_skin.rotation.y, target_angle, 12.0 * delta)

		# 播放跑步动画 - 但只有在没有手动播放动画时才播放
		if not _is_playing_manual_animation:
			if is_on_floor() and _animation_player and _animation_player.has_animation("run"):
				if not _animation_player.is_playing() or _animation_player.current_animation != "run":
					_animation_player.play("run")
	else:
		# 停止移动
		velocity.x = 0
		velocity.z = 0

		# 播放待机动画 - 但只有在没有手动播放动画时才播放
		if not _is_playing_manual_animation:
			if is_on_floor() and _animation_player and _animation_player.has_animation("idle"):
				if not _animation_player.is_playing() or _animation_player.current_animation != "idle":
					_animation_player.play("idle")

	# 应用重力
	velocity.y += gravity * delta

	# 处理跳跃
	if is_on_floor() and Input.is_action_just_pressed("jump"):
		velocity.y = jump_impulse

		# 播放跳跃动画 - 但只有在没有手动播放动画时才播放
		if not _is_playing_manual_animation:
			if _animation_player and _animation_player.has_animation("jump"):
				_animation_player.play("jump")

	# 更新技能冷却时间
	update_skills(delta)

	# 应用移动
	move_and_slide()

func _check_enemy_capture():
	# 获取所有敌人
	var enemies = get_tree().get_nodes_in_group("enemies")

	for enemy in enemies:
		# 使用固定的捕获距离，不再依赖敌人的get_catch_distance方法
		var catch_distance = 2.0  # 2米的捕获距离
		var distance = global_position.distance_to(enemy.global_position)

		# 如果距离小于捕获距离，则被捕获
		if distance <= catch_distance:
			# 调用钩子方法处理被捕获事件
			_on_enemy_capture()
			return

func _find_animation_player():
	# 首先检查_skin是否存在
	if not _skin:
		# 尝试初始化_skin
		_skin = get_node_or_null("Player01")
		if not _skin and get_child_count() > 0:
			# 尝试查找第一个非CollisionShape3D的子节点作为_skin
			for child in get_children():
				if child is Node3D and not child is CollisionShape3D:
					_skin = child
					break

	# 如果_skin存在，直接查找其中的AnimationPlayer
	if _skin:
		var animation_player = _skin.get_node_or_null("AnimationPlayer")
		if animation_player:
			return animation_player

	# 查找模型中的AnimationPlayer
	for child in get_children():
		if child is AnimationPlayer:
			return child

	# 查找Player01中的AnimationPlayer
	var player01 = get_node_or_null("Player01")
	if player01:
		var animation_player = player01.get_node_or_null("AnimationPlayer")
		if animation_player:
			return animation_player

		for child in player01.get_children():
			if child is AnimationPlayer:
				return child

	# 递归查找
	var result = _find_animation_player_recursive(self)
	return result

func _find_animation_player_recursive(node):
	for child in node.get_children():
		if child is AnimationPlayer:
			return child

		var result = _find_animation_player_recursive(child)
		if result:
			return result

	return null

# 跳跃动画结束回调
func _on_jump_animation_finished(anim_name):
	# 如果正在播放手动动画，不要干扰它
	if _is_playing_manual_animation:
		return

	# 如果是跳跃动画结束，播放下落或待机动画
	if anim_name == "jump" or anim_name == "Jump":
		if _skin and _skin.has_node("AnimationPlayer"):
			var skin_anim_player = _skin.get_node("AnimationPlayer")

			if not is_on_floor():
				# 如果不在地面上，播放下落动画
				if skin_anim_player.has_animation("fall"):
					skin_anim_player.play("fall")
				else:
					skin_anim_player.play("jump")
			else:
				# 如果在地面上，播放待机或跑步动画
				var input_dir = Input.get_vector("move_left", "move_right", "move_up", "move_down", 0.4)
				if input_dir != Vector2.ZERO:
					if skin_anim_player.has_animation("run"):
						skin_anim_player.play("run")
				else:
					if skin_anim_player.has_animation("idle"):
						skin_anim_player.play("idle")

# 处理树木交互
func handle_tree_interaction() -> void:
	# 如果没有当前树，直接返回
	if _current_tree == null:
		return

	# 检查是否按下了点火键（F键）
	if Input.is_action_just_pressed("skill_2"):
		# 如果已经在播放点火动画，不要重复播放
		var already_playing_fire = false
		if _animation_player and _animation_player.is_playing():
			var current_anim = _animation_player.current_animation.to_lower()
			var fire_animation_names_lower = ["fire", "ignite", "burn"]
			for anim_name_lower in fire_animation_names_lower:
				if current_anim.contains(anim_name_lower):
					already_playing_fire = true
					break

		# 开始点火 - 使用与FireSkill相同的逻辑
		if _current_tree.has_method("start_igniting_with_duration"):
			# 创建一个模拟的FireSkill对象
			var mock_fire_skill = {
				"skill_category": "fire",
				"cast_time": 2.0  # 使用较短的点火时间
			}

			# 使用带持续时间的点火方法
			_current_tree.start_igniting_with_duration(mock_fire_skill.cast_time, mock_fire_skill)

			# 播放点火动画（如果还没有播放）
			if not already_playing_fire:
				play_fire_animation()

			# 启动点火计时器
			_start_ignite_timer()
		elif _current_tree.has_method("start_igniting"):
			# 如果没有带持续时间的方法，使用普通方法
			_current_tree.start_igniting()

			# 播放点火动画（如果还没有播放）
			if not already_playing_fire:
				play_fire_animation()

	# 检查是否释放了点火键
	if Input.is_action_just_released("skill_2"):
		# 停止点火
		if _current_tree.has_method("stop_igniting"):
			_current_tree.stop_igniting()

			# 停止点火动画
			stop_fire_animation()

			# 停止点火计时器
			_stop_ignite_timer()

# 点火计时器变量
var _ignite_timer: Timer = null
var _current_ignite_time: float = 0.0
var _ignite_duration: float = 2.0  # 默认点火时间

# 启动点火计时器
func _start_ignite_timer() -> void:
	# 如果已有计时器，先停止
	_stop_ignite_timer()

	# 创建新计时器
	_ignite_timer = Timer.new()
	_ignite_timer.wait_time = 0.1  # 每0.1秒更新一次
	_ignite_timer.one_shot = false
	_ignite_timer.timeout.connect(_on_ignite_timer_timeout)
	add_child(_ignite_timer)

	# 重置点火时间
	_current_ignite_time = 0.0

	# 启动计时器
	_ignite_timer.start()

# 停止点火计时器
func _stop_ignite_timer() -> void:
	if _ignite_timer:
		_ignite_timer.stop()
		_ignite_timer.queue_free()
		_ignite_timer = null

	# 重置点火时间
	_current_ignite_time = 0.0

# 点火计时器超时回调
func _on_ignite_timer_timeout() -> void:
	# 如果没有当前树，停止计时器
	if not _current_tree or not _current_tree.has_method("update_ignite_time"):
		_stop_ignite_timer()
		return

	# 如果不再按住点火键，停止计时器
	if not Input.is_action_pressed("skill_2"):
		_stop_ignite_timer()
		return

	# 更新点火时间
	_current_ignite_time += 0.1

	# 更新树的点火进度
	_current_tree.update_ignite_time(_current_ignite_time)

	# 如果点火完成，点燃树木
	if _current_ignite_time >= _ignite_duration:
		if _current_tree.has_method("start_burning"):
			_current_tree.start_burning()

		# 停止点火计时器
		_stop_ignite_timer()

		# 继续播放点火动画一段时间，让玩家看到点火效果
		# 不立即停止点火动画，让它继续播放一段时间
		# 当玩家移动或执行其他动作时，动画系统会自动切换到其他动画

# 播放点火动画
func play_fire_animation() -> void:
	# 检查_animation_player是否存在
	if not _animation_player:
		_animation_player = _find_animation_player()
		if not _animation_player:
			return

	# 获取所有可用的动画
	var animations = _animation_player.get_animation_list()

	# 如果已经在播放点火动画，则不重复播放
	if _animation_player.is_playing():
		var current_anim = _animation_player.current_animation.to_lower()

		var fire_animation_names_lower = ["fire", "ignite", "burn"]
		for anim_name_lower in fire_animation_names_lower:
			if current_anim.contains(anim_name_lower):
				# 确保手动播放标志已设置，防止被其他动画打断
				_is_playing_manual_animation = true
				_manual_animation_timer = 10.0
				return  # 已经在播放点火动画，不需要重新播放

	# 尝试播放点火动画
	var fire_animation_names = ["fire", "Fire", "ignite", "Ignite", "burn", "Burn"]
	var animation_applied = false

	# 首先尝试播放"fire"动画，这是最常见的点火动画名称
	if _animation_player.has_animation("fire"):
		_play_specific_fire_animation("fire")
		return

	# 如果没有"fire"动画，尝试其他名称
	for anim_name in fire_animation_names:
		if _animation_player.has_animation(anim_name):
			_play_specific_fire_animation(anim_name)
			animation_applied = true
			break

	if not animation_applied:
		# 如果没有找到点火动画，但有idle动画，可以尝试播放idle动画
		if _animation_player.has_animation("idle"):
			_animation_player.play("idle")

# 播放特定的点火动画
func _play_specific_fire_animation(anim_name: String) -> void:
	# 获取动画
	var anim = _animation_player.get_animation(anim_name)

	# 设置为循环模式，确保动画持续播放
	if anim:
		anim.loop_mode = Animation.LOOP_LINEAR

	# 停止当前正在播放的动画（如果有）
	if _animation_player.is_playing():
		_animation_player.stop()

	# 设置手动播放动画标志，防止自动动画系统打断
	_is_playing_manual_animation = true
	_manual_animation_timer = 10.0  # 设置一个较长的时间，确保点火过程中不会被打断

	# 播放动画，使用自定义混合时间确保平滑过渡
	_animation_player.play(anim_name, 0.2)  # 0.2秒混合时间

# 停止点火动画
func stop_fire_animation() -> void:
	if not _animation_player:
		return

	# 重置动画速度
	_animation_player.speed_scale = 1.0

	# 重置手动播放动画标志
	_is_playing_manual_animation = false
	_manual_animation_timer = 0.0

	if _animation_player.is_playing():
		# 检查当前播放的是否是点火动画
		var current_anim = _animation_player.current_animation
		var fire_animation_names = ["fire", "Fire", "ignite", "Ignite", "burn", "Burn"]

		# 检查当前动画是否是点火动画
		var is_fire_animation = false
		for anim_name in fire_animation_names:
			if current_anim.to_lower() == anim_name.to_lower():
				is_fire_animation = true
				break

		if is_fire_animation:
			# 播放idle动画，使用混合过渡而不是直接停止
			var idle_animation_names = ["idle", "Idle", "IDLE", "stand", "Stand", "STAND"]
			var animation_applied = false

			for anim_name in idle_animation_names:
				if _animation_player.has_animation(anim_name):
					# 使用混合时间确保平滑过渡
					_animation_player.play(anim_name, 0.2)  # 0.2秒混合时间
					animation_applied = true
					break

			# 如果没有找到idle动画，才直接停止
			if not animation_applied:
				_animation_player.stop()

# 覆盖父类的初始化交互系统方法
func initialize_interaction() -> void:
	# 检查是否已经有交互器（现在直接在MirageClone下）
	var existing_interactor = get_node_or_null("PlayerInteractor")
	if existing_interactor:
		# 如果不是Interactor类型，删除并重新创建
		if not existing_interactor is Interactor:
			existing_interactor.queue_free()
			# 等待下一帧再创建新的交互器
			await get_tree().process_frame
			# 递归调用自身，重新创建交互器
			initialize_interaction()
			return

		# 断开所有现有连接，避免重复连接
		if existing_interactor.is_connected("area_entered", _on_interactor_area_entered):
			existing_interactor.disconnect("area_entered", _on_interactor_area_entered)
		if existing_interactor.is_connected("area_exited", _on_interactor_area_exited):
			existing_interactor.disconnect("area_exited", _on_interactor_area_exited)

		# 重新连接信号
		existing_interactor.connect("area_entered", _on_interactor_area_entered)
		existing_interactor.connect("area_exited", _on_interactor_area_exited)

		# 设置交互器的碰撞掩码为2，与Interactable的碰撞层匹配
		existing_interactor.collision_mask = 2  # 修改为2，与Interactable的碰撞层匹配

		# 确保控制器设置正确
		existing_interactor.controller = self
		return

	# 创建交互器 - 使用Interactor类
	var player_interactor = Interactor.new()
	player_interactor.name = "PlayerInteractor"  # 使用包含"player"的名称，以便与Tree.gd中的检查兼容
	player_interactor.collision_layer = 0  # 不需要碰撞层
	player_interactor.collision_mask = 2  # 修改为2，与Interactable的碰撞层匹配
	player_interactor.controller = self  # 设置控制器为分身自身

	# 添加碰撞形状 - 增大半径以确保能检测到树木
	var collision_shape = CollisionShape3D.new()
	var sphere_shape = SphereShape3D.new()
	sphere_shape.radius = 4.0  # 增大交互范围，确保能检测到树木
	collision_shape.shape = sphere_shape
	player_interactor.add_child(collision_shape)

	# 直接添加到MirageClone
	add_child(player_interactor)

	# 连接信号
	player_interactor.connect("area_entered", _on_interactor_area_entered)
	player_interactor.connect("area_exited", _on_interactor_area_exited)

# 添加_physics_process方法，处理交互器的更新
func _physics_process_interactor(delta: float) -> void:
	# 获取交互器
	var interactor = get_node_or_null("PlayerInteractor")
	if not interactor:
		return

	if not interactor is Interactor:
		return

	# 确保交互器跟随分身移动
	interactor.global_position = global_position

	# 获取最近的可交互物体
	var closest_interactable = interactor.get_closest_interactable()

	if closest_interactable:
		# 获取父节点
		var parent = closest_interactable.get_parent()
		if parent:
			# 检查是否是树木
			if parent.is_in_group("trees"):
				# 如果不是当前的树
				if parent != _current_tree:
					# 如果有当前树，先取消高亮
					if _current_tree and _current_tree.has_method("remove_highlight"):
						_current_tree.remove_highlight()

					# 设置新的当前树
					_current_tree = parent

					# 添加高亮
					if _current_tree.has_method("add_highlight"):
						_current_tree.add_highlight()
			else:
				# 不是树木，忽略
				pass
	else:
		# 如果没有最近的可交互物体，但有当前树
		if _current_tree:
			# 取消高亮
			if _current_tree.has_method("remove_highlight"):
				_current_tree.remove_highlight()

			# 清除当前树
			_current_tree = null

# 处理交互器进入区域 - 保留但不再使用
func _on_interactor_area_entered(area: Area3D) -> void:
	# 检查是否是树木
	var parent = area.get_parent()
	if not parent:
		return

	if not parent.is_in_group("trees"):
		return

	# 设置当前树
	_current_tree = parent

	# 添加高亮
	if _current_tree.has_method("add_highlight"):
		_current_tree.add_highlight()

# 处理交互器离开区域
func _on_interactor_area_exited(area: Area3D) -> void:
	# 检查是否是当前树
	var parent = area.get_parent()
	if not parent:
		return

	if not _current_tree:
		return

	if parent != _current_tree:
		return

	# 检查是否正在点火
	var is_igniting = Input.is_action_pressed("skill_2")

	# 如果正在点火，给一个短暂的宽限期，避免因为轻微移动导致点火中断
	if is_igniting:
		# 当玩家释放点火键时，会自动停止点火
		return

	# 移除高亮
	if _current_tree.has_method("remove_highlight"):
		_current_tree.remove_highlight()

	# 停止点火
	if _current_tree.has_method("stop_igniting"):
		_current_tree.stop_igniting()

	# 停止点火动画
	stop_fire_animation()

	# 清除当前树引用
	_current_tree = null
