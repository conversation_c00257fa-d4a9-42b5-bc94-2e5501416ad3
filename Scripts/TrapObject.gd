extends Area3D
class_name TrapObject

# 陷阱属性
var trap_duration: float = 20.0  # 陷阱效果持续时间（秒）
var is_triggered: bool = false  # 陷阱是否已触发
var affected_nodes = []  # 受陷阱影响的节点

# 初始化
func _ready() -> void:
	# 设置碰撞层和掩码
	collision_layer = 16  # 陷阱层
	collision_mask = 2 | 4  # 玩家层 | 敌人层

	# 连接信号
	if not body_entered.is_connected(_on_body_entered):
		body_entered.connect(_on_body_entered)

	# 添加到陷阱组
	if not is_in_group("traps"):
		add_to_group("traps")

# 初始化陷阱
func initialize(size: Vector3) -> void:
	# 创建碰撞形状
	var collision_shape = CollisionShape3D.new()
	var box_shape = BoxShape3D.new()
	box_shape.size = size
	collision_shape.shape = box_shape
	add_child(collision_shape)

	# 创建视觉效果
	create_visual_effect(size)

	# 修改地面颜色
	change_floor_color()

# 创建视觉效果
func create_visual_effect(size: Vector3) -> void:
	# 创建网格实例
	var mesh_instance = MeshInstance3D.new()
	add_child(mesh_instance)

	# 创建网格
	var mesh = BoxMesh.new()
	mesh.size = Vector3(size.x, 0.05, size.z)  # 薄一点的盒子
	mesh_instance.mesh = mesh

	# 创建材质
	var material = StandardMaterial3D.new()
	material.albedo_color = Color(0.2, 0.2, 0.2, 0.7)  # 黑灰色，半透明
	material.transparency = BaseMaterial3D.TRANSPARENCY_ALPHA
	mesh_instance.material_override = material

# 修改地面颜色
func change_floor_color() -> void:
	# 获取陷阱下方的地面
	var space_state = get_world_3d().direct_space_state
	var ray_params = PhysicsRayQueryParameters3D.new()
	ray_params.from = global_position + Vector3(0, 0.1, 0)
	ray_params.to = global_position + Vector3(0, -0.1, 0)
	ray_params.collision_mask = 1  # 地面层

	var result = space_state.intersect_ray(ray_params)

	if result:
		var floor_node = result.collider

		# 如果地面有MeshInstance3D子节点，修改其材质
		for child in floor_node.get_children():
			if child is MeshInstance3D:
				var original_material = null
				if child.has_method("get_surface_override_material"):
					original_material = child.get_surface_override_material(0)

				if original_material:
					var new_material = original_material.duplicate()
					new_material.albedo_color = Color(0.3, 0.3, 0.3)  # 黑灰色
					child.set_surface_override_material(0, new_material)

# 当物体进入陷阱区域时
func _on_body_entered(body: Node3D) -> void:
	# 如果陷阱已触发，不再处理
	if is_triggered:
		return

	# 检查是否是玩家或敌人
	if body.is_in_group("player") or body.is_in_group("enemies"):
		# 触发陷阱
		trigger_trap(body)

# 触发陷阱
func trigger_trap(body: Node3D) -> void:
	is_triggered = true

	# 添加到受影响节点列表
	affected_nodes.append(body)

	# 停止节点移动
	stop_movement(body)

	# 播放触发动画
	play_trigger_animation()

	# 创建计时器，在持续时间结束后恢复移动
	var timer = Timer.new()
	add_child(timer)
	timer.wait_time = trap_duration
	timer.one_shot = true
	timer.timeout.connect(func():
		# 恢复所有受影响节点的移动
		for node in affected_nodes:
			if is_instance_valid(node):
				restore_movement(node)

		# 删除陷阱
		queue_free()
	)
	timer.start()

# 停止节点移动
func stop_movement(node: Node3D) -> void:
	# 保存原始速度
	if "move_speed" in node:
		node.set_meta("original_move_speed", node.move_speed)
		node.move_speed = 0

	# 如果是玩家，显示通知
	if node.is_in_group("player") and node.has_method("show_notification"):
		node.show_notification("你被困住了！持续 " + str(trap_duration) + " 秒")

	# 如果是敌人，可能需要特殊处理
	if node.is_in_group("enemies"):
		# 可能需要停止敌人的AI或动画
		if node.has_method("set_trapped"):
			node.set_trapped(true)

# 恢复节点移动
func restore_movement(node: Node3D) -> void:
	# 恢复原始速度
	if "move_speed" in node and node.has_meta("original_move_speed"):
		node.move_speed = node.get_meta("original_move_speed")
		node.remove_meta("original_move_speed")

	# 如果是玩家，显示通知
	if node.is_in_group("player") and node.has_method("show_notification"):
		node.show_notification("你可以移动了！")

	# 如果是敌人，恢复AI
	if node.is_in_group("enemies"):
		if node.has_method("set_trapped"):
			node.set_trapped(false)

# 播放触发动画
func play_trigger_animation() -> void:
	# 创建动画
	var tween = create_tween()

	# 获取网格实例
	var mesh_instance = null
	for child in get_children():
		if child is MeshInstance3D:
			mesh_instance = child
			break

	if mesh_instance:
		# 修改材质颜色为红色
		var material = mesh_instance.material_override.duplicate()
		material.albedo_color = Color(0.8, 0.2, 0.2, 0.9)  # 红色，更不透明
		mesh_instance.material_override = material

		# 闪烁动画
		tween.tween_property(mesh_instance, "visible", false, 0.2)
		tween.tween_property(mesh_instance, "visible", true, 0.2)
		tween.tween_property(mesh_instance, "visible", false, 0.2)
		tween.tween_property(mesh_instance, "visible", true, 0.2)
