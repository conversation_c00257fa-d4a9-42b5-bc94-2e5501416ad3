extends Resource
class_name Item

# 基本道具属性
@export var item_name: String = "未命名道具"
@export var item_description: String = "未知道具"
@export var icon: Texture2D  # 道具图标
@export var is_one_time_use: bool = true  # 是否为一次性使用道具
@export var cooldown_time: float = 0.0  # 冷却时间（秒），对于可重复使用的道具
@export var item_category: String = ""  # 道具类别，如"utility"、"combat"等

# 道具内部状态
var owner = null  # 道具的拥有者，通常是Player
var is_active: bool = false  # 道具是否可以使用
var was_used: bool = false  # 道具是否已被使用过
var is_on_cooldown: bool = false  # 是否在冷却中
var cooldown_remaining: float = 0.0  # 剩余冷却时间

# 初始化函数
func initialize(item_owner) -> void:
	owner = item_owner
	was_used = false
	is_active = true
	is_on_cooldown = false
	cooldown_remaining = 0.0

# 尝试使用道具
func try_use() -> bool:
	if not is_active or is_on_cooldown:
		return false

	if is_one_time_use and was_used:
		return false

	# 执行道具使用
	var success = use()

	# 执行使用后的效果
	if success:
		if is_one_time_use:
			was_used = true
		else:
			start_cooldown()

		after_use()

		# 如果是一次性道具，通知所有者移除此道具
		if is_one_time_use and was_used and owner and owner.has_method("remove_item"):
			call_deferred("notify_owner_to_remove")

	return success

# 开始冷却
func start_cooldown() -> void:
	if cooldown_time > 0:
		is_on_cooldown = true
		cooldown_remaining = cooldown_time

# 更新冷却状态
func update(delta: float) -> void:
	if is_on_cooldown:
		cooldown_remaining -= delta
		if cooldown_remaining <= 0:
			is_on_cooldown = false
			cooldown_remaining = 0

# 获取冷却百分比
func get_cooldown_percent() -> float:
	if not is_on_cooldown or cooldown_time <= 0:
		return 0.0
	return cooldown_remaining / cooldown_time

# 通知所有者移除此道具
func notify_owner_to_remove() -> void:
	if owner and owner.has_method("remove_item"):
		owner.remove_item(self)

# 由子类重写的使用方法
func use() -> bool:
	push_error("Item.use() 方法必须由子类重写！")
	return false

# 道具使用后的处理，可以由子类重写
func after_use() -> void:
	# 默认实现为空
	pass

# 获取道具信息
func get_info() -> Dictionary:
	return {
		"name": item_name,
		"description": item_description,
		"icon": icon,
		"is_one_time_use": is_one_time_use,
		"was_used": was_used,
		"is_on_cooldown": is_on_cooldown,
		"cooldown_percent": get_cooldown_percent(),
		"category": item_category
	}

# 设置道具状态
func set_active(active: bool) -> void:
	is_active = active

# 重置道具状态
func reset() -> void:
	was_used = false
	is_on_cooldown = false
	cooldown_remaining = 0.0