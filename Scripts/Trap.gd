extends Item
class_name Trap

# 道具固定属性
var trap_scene = null  # 陷阱场景，将在 _init 中加载
var trap_size = Vector3(4, 0.1, 4)  # 陷阱大小（4块地板大小）

# 硬编码的陷阱场景
var trap_scene_hardcoded = null

func _init() -> void:
	# 设置道具基本属性
	item_name = "陷阱"
	item_description = "放下一个陷阱，player/enemy碰到后触发，触发后player/enemy停止移动20秒。"
	is_one_time_use = true  # 一次性道具
	cooldown_time = 0.0  # 无冷却时间
	item_category = "utility"

	# 尝试加载陷阱场景
	trap_scene = load("res://Scenes/Prefabs/TrapObject.tscn")
	print("Trap._init() 陷阱场景: ", trap_scene)

	# 如果加载失败，尝试其他路径
	if trap_scene == null:
		trap_scene = load("res://Scenes/TrapObject.tscn")
		print("Trap._init() 尝试其他路径加载陷阱场景: ", trap_scene)

# 覆盖初始化函数
func initialize(item_owner = null) -> void:
	# 调用父类的初始化方法
	super.initialize(item_owner)

	# 确保陷阱场景已加载
	if trap_scene == null:
		# 尝试加载陷阱场景
		trap_scene = load("res://Scenes/Prefabs/TrapObject.tscn")
		print("Trap.initialize() 陷阱场景: ", trap_scene)

		# 如果加载失败，尝试其他路径
		if trap_scene == null:
			trap_scene = load("res://Scenes/TrapObject.tscn")
			print("Trap.initialize() 尝试其他路径加载陷阱场景: ", trap_scene)

# 使用道具的逻辑
func use() -> bool:
	print("Trap.use() 被调用")

	# 检查是否有有效的所有者（玩家）
	if not owner:
		print("Trap.use() 失败: 没有有效的所有者")
		return false

	# 获取玩家前方的位置
	var forward_position = get_placement_position()
	print("Trap.use() 获取放置位置: ", forward_position)

	if forward_position == null:
		# 如果无法获取有效的放置位置，返回失败
		print("Trap.use() 失败: 无法获取有效的放置位置")
		return false

	# 检查陷阱场景是否已加载
	if trap_scene == null:
		print("Trap.use() 失败: 陷阱场景未加载")

		# 最后尝试直接加载
		trap_scene = load("res://Scenes/Prefabs/TrapObject.tscn")
		print("Trap.use() 最后尝试加载陷阱场景: ", trap_scene)

		if trap_scene == null:
			trap_scene = load("res://Scenes/TrapObject.tscn")
			print("Trap.use() 最后尝试加载备用路径陷阱场景: ", trap_scene)

			if trap_scene == null:
				# 如果仍然无法加载，尝试创建一个硬编码的场景
				print("Trap.use() 尝试创建硬编码的陷阱场景")

				# 创建一个新的场景
				var scene = PackedScene.new()

				# 创建根节点
				var root = Area3D.new()
				root.set_script(load("res://Scripts/TrapObject.gd"))
				root.collision_layer = 16
				root.collision_mask = 6

				# 将根节点打包到场景中
				var error = scene.pack(root)
				if error == OK:
					trap_scene = scene
					print("Trap.use() 成功创建硬编码的陷阱场景")
				else:
					print("Trap.use() 创建硬编码的陷阱场景失败: ", error)
					return false

	# 创建陷阱实例
	print("Trap.use() 创建陷阱实例")
	var trap_instance = trap_scene.instantiate()
	if trap_instance == null:
		print("Trap.use() 失败: 无法实例化陷阱场景")
		return false

	owner.get_parent().add_child(trap_instance)

	# 设置陷阱位置
	trap_instance.global_position = forward_position
	print("Trap.use() 设置陷阱位置: ", forward_position)

	# 设置陷阱属性
	trap_instance.initialize(trap_size)
	print("Trap.use() 初始化陷阱属性")

	# 显示使用效果
	show_use_effects()

	print("Trap.use() 成功")
	# 返回使用成功
	return true

# 获取放置位置
func get_placement_position():  # 移除返回类型，允许返回null
	# 获取玩家前方的位置
	var player_position = owner.global_position
	var forward_direction = owner.get_global_transform().basis.z.normalized()

	print("get_placement_position() 玩家位置: ", player_position)
	print("get_placement_position() 前方方向: ", forward_direction)

	# 计算前方位置（距离玩家5米）
	var forward_position = player_position - forward_direction * 5.0
	print("get_placement_position() 前方位置: ", forward_position)

	# 向下投射射线，确保陷阱放在地面上
	var space_state = owner.get_world_3d().direct_space_state
	var ray_params = PhysicsRayQueryParameters3D.new()
	ray_params.from = forward_position + Vector3(0, 1, 0)
	ray_params.to = forward_position + Vector3(0, -1, 0)
	ray_params.collision_mask = 1  # 地面层

	print("get_placement_position() 射线起点: ", ray_params.from)
	print("get_placement_position() 射线终点: ", ray_params.to)

	var result = space_state.intersect_ray(ray_params)
	print("get_placement_position() 射线检测结果: ", result)

	if result:
		print("get_placement_position() 碰撞对象: ", result.collider)

		# 检查碰撞对象的组
		var groups = []
		if result.collider.has_method("get_groups"):
			groups = result.collider.get_groups()
		print("get_placement_position() 碰撞对象组: ", groups)

		# 检查碰撞对象是否是地面
		# 放宽条件，任何碰撞对象都可以放置陷阱
		print("get_placement_position() 找到地面，位置: ", result.position)
		# 返回地面上的位置
		return result.position
	else:
		print("get_placement_position() 没有检测到碰撞")

	return null

# 显示使用效果
func show_use_effects() -> void:
	if not owner:
		return

	# 显示UI提示
	if owner.has_method("show_notification"):
		owner.show_notification("陷阱已放置！")
