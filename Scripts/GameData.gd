extends Node

# 数据文件路径
const TREES_DATA_PATH = "user://trees_data.json"
const GAME_SETTINGS_PATH = "user://game_settings.json"

# 树木数据
var trees_data = []

# 敌人数据
var enemies_data = []

# 游戏设置
var game_settings = {
	"fog_enabled": true,
	"difficulty": "normal",
	"music_volume": 0.8,
	"sfx_volume": 0.8,
	"quality_level": 1, # 0=低, 1=中, 2=高
	"language": 0 # 0=中文, 1=英文
}

# 关卡选择相关
var current_level_id: String = "level_01"
var current_level_config_path: String = ""

func _ready():
	# 加载所有数据
	load_all_data()

# 加载所有数据
func load_all_data() -> void:
	load_trees_data()
	# 不再需要加载巡逻点数据，因为巡逻点直接由TreeGeneratorModule创建
	load_enemies_data()
	load_game_settings()

# 树木数据相关方法
func load_trees_data() -> void:
	if FileAccess.file_exists(TREES_DATA_PATH):
		var file = FileAccess.open(TREES_DATA_PATH, FileAccess.READ)
		var json_string = file.get_as_text()
		file.close()

		var json_result = JSON.parse_string(json_string)
		if json_result != null:
			trees_data = json_result

# 保存树木数据
func save_trees_data() -> void:
	var file = FileAccess.open(TREES_DATA_PATH, FileAccess.WRITE)
	var json_string = JSON.stringify(trees_data)
	file.store_string(json_string)
	file.close()

# 设置树木数据
func set_trees_data(data) -> void:
	trees_data = data
	save_trees_data()

# 获取树木数据
func get_trees_data():
	return trees_data

# 巡逻点相关方法已移除，因为巡逻点直接由TreeGeneratorModule创建

# 敌人数据相关方法
func load_enemies_data() -> void:
	# 在运行时加载敌人节点
	if get_tree():
		enemies_data = get_tree().get_nodes_in_group("enemies")
		if enemies_data.is_empty():
			enemies_data = get_tree().get_nodes_in_group("enemy")

# 获取敌人数据
func get_enemies_data():
	# 如果敌人数据为空，尝试重新加载
	if enemies_data.is_empty() and get_tree():
		load_enemies_data()
	return enemies_data

# 游戏设置相关方法
func load_game_settings() -> void:
	if FileAccess.file_exists(GAME_SETTINGS_PATH):
		var file = FileAccess.open(GAME_SETTINGS_PATH, FileAccess.READ)
		var json_string = file.get_as_text()
		file.close()

		var json_result = JSON.parse_string(json_string)
		if json_result != null:
			game_settings = json_result

# 保存游戏设置
func save_game_settings() -> void:
	var file = FileAccess.open(GAME_SETTINGS_PATH, FileAccess.WRITE)
	var json_string = JSON.stringify(game_settings)
	file.store_string(json_string)
	file.close()

# 设置战争迷雾
func set_fog_enabled(enabled) -> void:
	game_settings.fog_enabled = enabled
	save_game_settings()

# 获取战争迷雾状态
func is_fog_enabled() -> bool:
	return game_settings.fog_enabled

# 设置难度
func set_difficulty(difficulty) -> void:
	game_settings.difficulty = difficulty
	save_game_settings()

# 获取难度
func get_difficulty() -> String:
	return game_settings.difficulty

# 设置当前关卡
func set_current_level(level_id: String, config_path: String = "") -> void:
	current_level_id = level_id
	current_level_config_path = config_path

# 获取当前关卡ID
func get_current_level_id() -> String:
	return current_level_id

# 获取当前关卡配置路径
func get_current_level_config_path() -> String:
	return current_level_config_path

# 设置音量
func set_music_volume(volume: float) -> void:
	game_settings.music_volume = volume
	save_game_settings()
	print("[GAME_DATA] 设置音乐音量: ", volume)

# 获取音乐音量
func get_music_volume() -> float:
	return game_settings.music_volume

# 设置音效音量
func set_sfx_volume(volume: float) -> void:
	game_settings.sfx_volume = volume
	save_game_settings()
	print("[GAME_DATA] 设置音效音量: ", volume)

# 获取音效音量
func get_sfx_volume() -> float:
	return game_settings.sfx_volume

# 设置画质等级
func set_quality_level(level: int) -> void:
	game_settings.quality_level = level
	save_game_settings()
	print("[GAME_DATA] 设置画质等级: ", level)

# 获取画质等级
func get_quality_level() -> int:
	return game_settings.quality_level

# 设置语言
func set_language(lang: int) -> void:
	game_settings.language = lang
	save_game_settings()
	print("[GAME_DATA] 设置语言: ", lang)

# 获取语言
func get_language() -> int:
	return game_settings.language

# 将Vector3转换为字典
func vector3_to_dict(vec: Vector3) -> Dictionary:
	return {"x": vec.x, "y": vec.y, "z": vec.z}

# 将字典转换为Vector3
func dict_to_vector3(dict: Dictionary) -> Vector3:
	return Vector3(dict.x, dict.y, dict.z)
