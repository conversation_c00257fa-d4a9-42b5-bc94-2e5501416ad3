extends Node

# 被动技能初始化器
# 用于初始化和管理被动技能

# 被动技能实例
var passive_skills = []

func _ready() -> void:
	# 等待一帧确保所有节点都已准备就绪
	await get_tree().process_frame

	# 初始化被动技能
	initialize_passive_skills()

# 初始化被动技能
func initialize_passive_skills() -> void:
	# 清空现有被动技能
	passive_skills.clear()

	# 从技能管理器获取被动技能
	var skill_manager = get_node_or_null("/root/SkillManager")
	if not skill_manager:
		push_error("SkillManager单例不存在")
		return

	# 检查FLAME WALTZ技能是否被配置
	var is_flame_waltz_equipped = false

	# 获取玩家当前装备的技能
	var equipped_skills = skill_manager.get_equipped_skills()
	for skill_key in equipped_skills:
		if skill_key == "flame_waltz":
			is_flame_waltz_equipped = true
			break

	# 只有当FLAME WALTZ被配置时才初始化
	if is_flame_waltz_equipped:
		# 获取FLAME WALTZ技能
		var flame_waltz_skill = skill_manager.get_skill_resource("flame_waltz")
		if flame_waltz_skill:
			# 创建技能实例
			var skill_instance = flame_waltz_skill.duplicate()

			# 初始化技能
			if skill_instance.has_method("initialize"):
				skill_instance.initialize(self)

			# 添加到被动技能列表
			passive_skills.append(skill_instance)

			print("被动技能 FLAME WALTZ 已初始化")
		else:
			push_error("无法加载 FLAME WALTZ 技能")
	else:
		print("被动技能 FLAME WALTZ 未被配置，不初始化")
