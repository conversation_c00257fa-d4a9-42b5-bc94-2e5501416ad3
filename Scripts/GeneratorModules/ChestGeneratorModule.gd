class_name ChestGeneratorModule
extends GeneratorModule

func _init() -> void:
	module_id = "chest_generator"
	module_name = "宝箱生成器"
	description = "在关卡中生成宝箱"

func get_default_config() -> Dictionary:
	return {
		"chest_scene": "res://Scenes/Prefabs/Chest.tscn",
		"max_chests": 5,
		"min_distance": 5.0,  # 宝箱间的最小距离
		"border_margin": 10.0,  # 距边界的最小距离
		"min_distance_to_trees": 5.0, # 与树木的最小距离
		"min_distance_to_rocks": 3.0, # 与岩石的最小距离
		"min_distance_to_barrels": 3.0, # 与木桶的最小距离
		"random_rotation": true  # 是否随机旋转宝箱
	}

func generate(level_generator: Node3D, nav_region: NavigationRegion3D) -> void:
	# 清除现有宝箱
	clear()

	# 加载宝箱场景
	var chest_scene_path = config.get("chest_scene", "res://Scenes/Prefabs/Chest.tscn")
	var chest_scene = load(chest_scene_path)
	if not chest_scene:
		push_error("ChestGeneratorModule: 无法加载宝箱场景: " + chest_scene_path)
		return

	# 获取关卡参数
	var level_size = level_generator.level_size
	# 宝箱生成范围：围墙范围内 (level_size - Vector2(100, 100))
	var chest_area = level_size - Vector2(100, 100)
	var max_chests = config.get("max_chests", 5)
	var min_distance = config.get("min_distance", 5.0)
	var border_margin = config.get("border_margin", 10.0)
	var min_distance_to_trees = config.get("min_distance_to_trees", 5.0)
	var min_distance_to_rocks = config.get("min_distance_to_rocks", 3.0)
	var min_distance_to_barrels = config.get("min_distance_to_barrels", 3.0)
	var random_rotation = config.get("random_rotation", true)

	# 获取其他物体的位置
	var tree_positions = _get_object_positions(nav_region, "tree")
	var rock_positions = _get_object_positions(nav_region, "rock")
	var barrel_positions = _get_object_positions(nav_region, "barrel")

	# 生成宝箱位置
	var chest_positions = _generate_chest_positions(chest_area, max_chests, min_distance, border_margin,
											tree_positions, rock_positions, barrel_positions,
											min_distance_to_trees, min_distance_to_rocks, min_distance_to_barrels)

	# 生成宝箱
	for i in range(chest_positions.size()):
		var pos = chest_positions[i]
		var chest = chest_scene.instantiate()
		chest.position = Vector3(pos.x, 0, pos.y)  # 放在地面上

		# 设置宝箱名称为"chest@编号"
		chest.name = "chest@" + str(i)

		# 随机旋转
		if random_rotation:
			chest.rotation.y = randf_range(0, TAU)

		# 添加到组
		chest.add_to_group("chest")
		chest.add_to_group("chests")
		chest.add_to_group("fog_objects")

		# 添加到navigation区域
		nav_region.add_child(chest)
		if level_generator.edited_scene_root:
			chest.owner = level_generator.edited_scene_root

		# 保存生成的对象
		generated_objects.append(chest)

# 获取特定类型的对象位置
func _get_object_positions(nav_region, type: String) -> Array:
	var positions = []

	# 安全检查，如果nav_region为空，返回空数组
	if nav_region == null:
		return positions

	# 查找所有指定类型的对象
	var objects = []
	for child in nav_region.get_children():
		if type == "tree" and (child.is_in_group("tree") or child.is_in_group("fog_objects") or child.name.begins_with("Tree")):
			objects.append(child)
		elif type == "rock" and (child.is_in_group("rock") or child.is_in_group("rocks")):
			objects.append(child)
		elif type == "barrel" and (child.is_in_group("barrel") or child.is_in_group("barrels")):
			objects.append(child)

	# 提取位置
	for obj in objects:
		positions.append(Vector2(obj.position.x, obj.position.z))

	return positions

# 生成宝箱位置
func _generate_chest_positions(level_size: Vector2, max_chests: int, min_distance: float,
							  border_margin: float, tree_positions: Array, rock_positions: Array,
							  barrel_positions: Array, min_distance_to_trees: float,
							  min_distance_to_rocks: float, min_distance_to_barrels: float) -> Array:
	var positions = []
	var half_size = level_size / 2.0
	var available_area = Rect2(
		-half_size.x + border_margin,
		-half_size.y + border_margin,
		level_size.x - 2 * border_margin,
		level_size.y - 2 * border_margin
	)

	# 尝试生成宝箱位置，直到达到最大数量或尝试了足够多的次数
	var attempts = 0
	var max_attempts = max_chests * 20  # 宝箱比较重要，多尝试几次

	while positions.size() < max_chests and attempts < max_attempts:
		attempts += 1

		# 随机生成位置
		var pos = Vector2(
			randf_range(available_area.position.x, available_area.end.x),
			randf_range(available_area.position.y, available_area.end.y)
		)

		# 检查与现有宝箱的距离
		var too_close_to_chest = false
		for existing_pos in positions:
			if existing_pos.distance_to(pos) < min_distance:
				too_close_to_chest = true
				break

		if too_close_to_chest:
			continue

		# 检查与树木的距离
		var too_close_to_tree = false
		for tree_pos in tree_positions:
			if tree_pos.distance_to(pos) < min_distance_to_trees:
				too_close_to_tree = true
				break

		if too_close_to_tree:
			continue

		# 检查与岩石的距离
		var too_close_to_rock = false
		for rock_pos in rock_positions:
			if rock_pos.distance_to(pos) < min_distance_to_rocks:
				too_close_to_rock = true
				break

		if too_close_to_rock:
			continue

		# 检查与木桶的距离
		var too_close_to_barrel = false
		for barrel_pos in barrel_positions:
			if barrel_pos.distance_to(pos) < min_distance_to_barrels:
				too_close_to_barrel = true
				break

		if not too_close_to_barrel:
			positions.append(pos)

	return positions

func validate_config() -> bool:
	# 如果存在覆盖参数，使用它
	var _override_validation = config.get("_override_validation", false)
	if _override_validation:
		return true

	# 检查场景路径
	if not config.has("chest_scene") or not config.get("chest_scene") is String:
		push_error("ChestGeneratorModule: 缺少 chest_scene 或类型不是 String")
		return false

	var chest_path = config.get("chest_scene", "")
	if not ResourceLoader.exists(chest_path):
		push_error("ChestGeneratorModule: chest_scene 路径不存在: " + chest_path)
		return false

	# 检查数量设置
	if not config.has("max_chests") or not config.get("max_chests") is int:
		push_error("ChestGeneratorModule: 缺少 max_chests 或类型不是 int")
		return false

	# 检查距离设置
	if not config.has("min_distance") or not config.get("min_distance") is float:
		push_error("ChestGeneratorModule: 缺少 min_distance 或类型不是 float")
		return false
	if not config.has("border_margin") or not config.get("border_margin") is float:
		push_error("ChestGeneratorModule: 缺少 border_margin 或类型不是 float")
		return false
	if not config.has("min_distance_to_trees") or not config.get("min_distance_to_trees") is float:
		push_error("ChestGeneratorModule: 缺少 min_distance_to_trees 或类型不是 float")
		return false
	if not config.has("min_distance_to_rocks") or not config.get("min_distance_to_rocks") is float:
		push_error("ChestGeneratorModule: 缺少 min_distance_to_rocks 或类型不是 float")
		return false
	if not config.has("min_distance_to_barrels") or not config.get("min_distance_to_barrels") is float:
		push_error("ChestGeneratorModule: 缺少 min_distance_to_barrels 或类型不是 float")
		return false

	# 检查旋转设置
	if not config.has("random_rotation") or not config.get("random_rotation") is bool:
		push_error("ChestGeneratorModule: 缺少 random_rotation 或类型不是 bool")
		return false

	return true