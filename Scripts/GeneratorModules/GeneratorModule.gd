class_name GeneratorModule
extends Resource

# 模块标识符
@export var module_id: String = "base_module"
@export var module_name: String = "基础模块"
@export_multiline var description: String = "基础生成器模块，所有具体模块都应该继承此类"

# 模块配置项
@export var config: Dictionary = {}

# 为每个模块保存的内容
var generated_objects: Array = []

# 生成方法(由子类实现)
func generate(_level_generator, _navigation_region) -> void:
	push_error("GeneratorModule: 生成方法必须由子类实现")
	pass

# 清理生成的对象
func clear() -> void:
	for obj in generated_objects:
		if is_instance_valid(obj):
			obj.queue_free()
	generated_objects.clear()

# 校验配置(由子类实现)
func validate_config() -> bool:
	return true

# 获取配置模板(由子类实现)
func get_config_template() -> Dictionary:
	return {}

# 应用配置
func apply_config(new_config: Dictionary) -> void:
	config = new_config.duplicate()
	
# 提供默认配置(由子类实现)
func get_default_config() -> Dictionary:
	return {} 