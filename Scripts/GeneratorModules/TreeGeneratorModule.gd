class_name TreeGeneratorModule
extends GeneratorModule


func _init() -> void:
	module_id = "tree_generator"
	module_name = "树木生成器"
	description = "在关卡中随机生成树木"

# 重写apply_config方法，确保tree_t_number在有效范围内
func apply_config(new_config: Dictionary) -> void:
	# 调用父类方法
	super.apply_config(new_config)

	# 确保tree_t_number在有效范围内（0到max_trees）
	if config.has("max_trees"):
		var max_trees = config.get("max_trees", 0)

		# 如果没有tree_t_number，添加它
		if not config.has("tree_t_number"):
			config["tree_t_number"] = 0

		# 确保tree_t_number在有效范围内
		var tree_t_number = config.get("tree_t_number", 0)
		if tree_t_number < 0:
			config["tree_t_number"] = 0
		elif tree_t_number > max_trees:
			config["tree_t_number"] = max_trees

func get_default_config() -> Dictionary:
	return {
		"tree_scene": "res://Scenes/tree.tscn",
		"max_trees": 10,
		"min_distance": 12.5,  # 树木间的最小距离（原来的5.0 * 2.5）
		"border_margin": 12.5,  # 距边界的最小距离（原来的5.0 * 2.5）
		"distribution": "random", # random, grid, cluster
		"grid_spacing": 25.0,    # 网格分布的间距（原来的10.0 * 2.5）
		"cluster_centers": 3,    # 聚集分布的聚集点数量
		"cluster_radius": 37.5,  # 聚集分布的半径（原来的15.0 * 2.5）
		"tree_t_number": 0       # 燃尽后为TYPE_T类型的树木数量 (0-max_trees)
	}

func generate(level_generator, nav_region) -> void:
	# 清除现有树木
	clear()

	# 加载树木场景
	var tree_scene_path = config.get("tree_scene", "res://Scenes/tree.tscn")
	var tree_scene = load(tree_scene_path)
	if not tree_scene:
		push_error("TreeGeneratorModule: 无法加载树木场景: " + tree_scene_path)
		return

	# 获取关卡参数
	var level_size = level_generator.level_size
	# 树木生成范围：围墙范围内 (level_size - Vector2(100, 100))
	var tree_area = level_size - Vector2(100, 100)
	var max_trees = config.get("max_trees", 10)
	var min_distance = config.get("min_distance", 5.0)
	var border_margin = config.get("border_margin", 5.0)
	var distribution = config.get("distribution", "random")

	# 根据分布类型生成树木位置
	var tree_positions = []

	# 获取正确的分布类型
	var distribution_type = config.get("distribution_type", distribution)

	match distribution_type:
		"random":
			tree_positions = _generate_random_positions(tree_area, max_trees, min_distance, border_margin)
		"grid":
			var grid_spacing = config.get("grid_spacing", 10.0)
			tree_positions = _generate_grid_positions(tree_area, grid_spacing, border_margin)
			# 限制数量
			if tree_positions.size() > max_trees:
				tree_positions = tree_positions.slice(0, max_trees)
		"cluster":
			var cluster_centers = config.get("cluster_centers", 3)
			var cluster_radius = config.get("cluster_radius", 15.0)
			tree_positions = _generate_cluster_positions(tree_area, max_trees, min_distance, border_margin, cluster_centers, cluster_radius)
		_:
			push_error("TreeGeneratorModule: 未知的分布类型: ", distribution_type)
			return

	# 生成树木
	var trees = []  # 存储所有生成的树木实例
	var patrol_points_nodes = []  # 存储所有生成的巡逻点节点

	for i in range(tree_positions.size()):
		var pos = tree_positions[i]
		var tree = tree_scene.instantiate()
		tree.position = Vector3(pos.x, 0, pos.y)  # 放在地面上

		# 将树木缩放为原来的2.5倍
		tree.scale = Vector3(2.5, 2.5, 2.5)

		# 设置树木名称为"tree@编号"
		tree.name = "tree@" + str(i)

		# 添加到组
		tree.add_to_group("fog_objects")
		tree.add_to_group("trees")

		# 添加到navigation区域
		nav_region.add_child(tree)
		if level_generator.edited_scene_root:
			tree.owner = level_generator.edited_scene_root

		# 保存生成的对象
		generated_objects.append(tree)
		trees.append(tree)  # 添加到树木列表

		# 为每棵树生成一个巡逻点
		# 在树周围生成巡逻点，确保在适当范围内（考虑到树木已经放大2.5倍）
		var angle = randf() * 2 * PI
		var distance = randf_range(2.5, 7.5)  # 设置2.5-7.5米的距离（原来的1-3米 * 2.5）
		var offset = Vector3(cos(angle) * distance, 0, sin(angle) * distance)
		var patrol_point_pos = tree.global_position + offset

		# 确保巡逻点的y坐标合适
		patrol_point_pos.y = 1.0  # 假设地面高度为0，敌人高度为1

		# 创建巡逻点节点
		var patrol_point = Node3D.new()
		patrol_point.name = "PatrolPoint_" + str(i)
		patrol_point.position = patrol_point_pos

		# 如果有PatrolPoint脚本，设置脚本
		var patrol_point_script = load("res://Scripts/PatrolPoint.gd")
		if patrol_point_script:
			patrol_point.set_script(patrol_point_script)
			# 设置巡逻点ID
			if patrol_point.has_method("set_data"):
				patrol_point.set_data({
					"id": i,  # 使用与树木相同的ID
					"position": {
						"x": patrol_point_pos.x,
						"y": patrol_point_pos.y,
						"z": patrol_point_pos.z
					}
				})

		# 添加到导航区域
		nav_region.add_child(patrol_point)
		if level_generator.edited_scene_root:
			patrol_point.owner = level_generator.edited_scene_root

		# 添加到巡逻点组
		patrol_point.add_to_group("patrol_points")

		# 保存生成的巡逻点节点
		patrol_points_nodes.append(patrol_point)
		generated_objects.append(patrol_point)

	# 分配燃尽类型
	# 确保tree_t_number不超过树的总数
	var max_trees_count = trees.size()
	var tree_t_number = min(config.get("tree_t_number", 0), max_trees_count)

	# 更新配置中的tree_t_number，确保它不超过实际树的数量
	config["tree_t_number"] = tree_t_number

	if tree_t_number > 0:
		# 随机打乱树木列表
		trees.shuffle()

		# 设置前tree_t_number个树为TYPE_T
		var _t_count = 0
		for i in range(tree_t_number):
			if i < trees.size():
				# 检查树木是否有preset_burnt_out_type属性
				if "preset_burnt_out_type" in trees[i]:
					# 获取BurntOutType枚举
					var BurntOutType = trees[i].get("BurntOutType")
					if BurntOutType != null:
						# 直接设置属性，而不是调用方法
						trees[i].preset_burnt_out_type = BurntOutType.TYPE_T
					_t_count += 1

		# 设置剩余树为TYPE_F
		var _f_count = 0
		for i in range(tree_t_number, trees.size()):
			# 检查树木是否有preset_burnt_out_type属性
			if "preset_burnt_out_type" in trees[i]:
				# 获取BurntOutType枚举
				var BurntOutType = trees[i].get("BurntOutType")
				if BurntOutType != null:
					# 直接设置属性，而不是调用方法
					trees[i].preset_burnt_out_type = BurntOutType.TYPE_F
				_f_count += 1

func _generate_random_positions(level_size: Vector2, max_count: int, min_distance: float, border_margin: float) -> Array:
	var positions = []
	var half_size = level_size / 2.0
	var available_area = Rect2(
		-half_size.x + border_margin,
		-half_size.y + border_margin,
		level_size.x - 2 * border_margin,
		level_size.y - 2 * border_margin
	)

	# 尝试生成树木位置，直到达到最大数量或尝试了足够多的次数
	var attempts = 0
	var max_attempts = max_count * 10  # 设置最大尝试次数，避免无限循环

	while positions.size() < max_count and attempts < max_attempts:
		attempts += 1

		# 随机生成位置
		var pos = Vector2(
			randf_range(available_area.position.x, available_area.end.x),
			randf_range(available_area.position.y, available_area.end.y)
		)

		# 检查与现有树木的距离
		var too_close = false
		for existing_pos in positions:
			if existing_pos.distance_to(pos) < min_distance:
				too_close = true
				break

		if not too_close:
			positions.append(pos)

	# 如果生成的位置数量小于max_count，打印警告
	if positions.size() < max_count:
		push_warning("TreeGeneratorModule: 警告：只生成了 ", positions.size(), " 个位置，少于请求的 ", max_count, " 个")

	# 如果只生成了一个位置，打印错误
	if positions.size() == 1:
		push_error("TreeGeneratorModule: 错误：只生成了1个位置！可能是min_distance太大或available_area太小")

	return positions

func _generate_grid_positions(level_size: Vector2, grid_spacing: float, border_margin: float) -> Array:
	var positions = []
	var half_size = level_size / 2.0

	# 计算网格起始和结束坐标
	var start_x = -half_size.x + border_margin
	var end_x = half_size.x - border_margin
	var start_y = -half_size.y + border_margin
	var end_y = half_size.y - border_margin

	# 生成网格位置
	for x in range(start_x, end_x + 1, grid_spacing):
		for y in range(start_y, end_y + 1, grid_spacing):
			positions.append(Vector2(x, y))

	# 随机打乱，以便在限制数量时能随机选择
	positions.shuffle()

	return positions

func _generate_cluster_positions(level_size: Vector2, max_count: int, min_distance: float,
								border_margin: float, cluster_count: int, cluster_radius: float) -> Array:
	var positions = []
	var half_size = level_size / 2.0
	var available_area = Rect2(
		-half_size.x + border_margin,
		-half_size.y + border_margin,
		level_size.x - 2 * border_margin,
		level_size.y - 2 * border_margin
	)

	# 生成聚集中心点
	var cluster_centers = []
	for i in range(cluster_count):
		var center = Vector2(
			randf_range(available_area.position.x, available_area.end.x),
			randf_range(available_area.position.y, available_area.end.y)
		)
		cluster_centers.append(center)

	# 在每个聚集点周围生成树木
	var trees_per_cluster = max_count / float(cluster_count)
	for center in cluster_centers:
		var cluster_positions = []
		var attempts = 0
		var max_attempts = trees_per_cluster * 10

		while cluster_positions.size() < trees_per_cluster and attempts < max_attempts:
			attempts += 1

			# 在聚集点周围随机生成位置
			var angle = randf_range(0, TAU)
			var distance = randf_range(0, cluster_radius)
			var pos = center + Vector2(cos(angle), sin(angle)) * distance

			# 确保位置在可用区域内
			if not available_area.has_point(pos):
				continue

			# 检查与现有树木的距离
			var too_close = false
			for existing_pos in positions + cluster_positions:
				if existing_pos.distance_to(pos) < min_distance:
					too_close = true
					break

			if not too_close:
				cluster_positions.append(pos)

		# 将该聚集点的树木添加到总列表
		positions.append_array(cluster_positions)

	return positions

func validate_config() -> bool:
	# 如果存在覆盖参数，使用它
	var _override_validation = config.get("_override_validation", false)
	if _override_validation:
		return true

	# 检查场景路径
	if not config.has("tree_scene") or not config.get("tree_scene") is String:
		push_error("TreeGeneratorModule: 缺少 tree_scene 或类型不是 String")
		return false

	var tree_path = config.get("tree_scene", "")
	if not ResourceLoader.exists(tree_path):
		push_error("TreeGeneratorModule: tree_scene 路径不存在: " + tree_path)
		return false

	# 检查数量设置
	if not config.has("max_trees") or not config.get("max_trees") is int:
		push_error("TreeGeneratorModule: 缺少 max_trees 或类型不是 int")
		return false

	# 检查tree_t_number设置
	var max_trees = config.get("max_trees", 0)

	if config.has("tree_t_number"):
		var tree_t_number = config.get("tree_t_number")
		if not tree_t_number is int or tree_t_number < 0:
			push_error("TreeGeneratorModule: tree_t_number 必须是非负整数")
			return false

		# 确保tree_t_number在有效范围内（0到max_trees）
		if tree_t_number < 0:
			config["tree_t_number"] = 0
		elif tree_t_number > max_trees:
			config["tree_t_number"] = max_trees
	else:
		# 如果配置中没有tree_t_number参数，添加它
		config["tree_t_number"] = 0

	# 检查距离设置
	if not config.has("min_distance") or not config.get("min_distance") is float:
		push_error("TreeGeneratorModule: 缺少 min_distance 或类型不是 float")
		return false
	if not config.has("border_margin") or not config.get("border_margin") is float:
		push_error("TreeGeneratorModule: 缺少 border_margin 或类型不是 float")
		return false
	if not config.has("min_distance_to_chests") or not config.get("min_distance_to_chests") is float:
		push_error("TreeGeneratorModule: 缺少 min_distance_to_chests 或类型不是 float")
		return false

	# 确保min_distance_to_chests与树木大小匹配
	var min_distance_to_chests = config.get("min_distance_to_chests", 3.0)
	config["min_distance_to_chests"] = min_distance_to_chests * 2.5 # 原来的值 * 2.5

	# 检查分布类型
	if not config.has("distribution_type") or not config.get("distribution_type") is String:
		push_error("TreeGeneratorModule: 缺少 distribution_type 或类型不是 String")
		return false
	var distribution = config.get("distribution_type", "")
	if not distribution in ["random", "grid", "cluster"]:
		push_error("TreeGeneratorModule: distribution_type 值无效: " + distribution)
		return false

	return true

# 保存树木数据
static func save_trees_data(scene_root: Node) -> void:
	if not scene_root:
		return

	var trees_data = []
	var trees = []

	# 递归查找所有节点
	var all_nodes = _get_all_children(scene_root)

	# 查找所有NavigationRegion3D节点
	var nav_regions = []
	for node in all_nodes:
		if node is NavigationRegion3D:
			nav_regions.append(node)

	# 如果找到了NavigationRegion3D节点
	if nav_regions.size() > 0:
		# 使用第一个NavigationRegion3D节点
		var nav_region = nav_regions[0]

		# 查找所有树木节点
		for child in nav_region.get_children():
			# 使用更宽松的条件查找树木节点
			if child is Node3D and (child.name.begins_with("tree") or "tree" in child.name.to_lower()):
				if not trees.has(child):
					trees.append(child)

	# 处理树木数据
	for tree in trees:
		if tree is Node3D:
			var tree_data = {
				"position": {
					"x": tree.global_position.x,
					"y": tree.global_position.y,
					"z": tree.global_position.z
				},
				"name": tree.name,
				"type": tree.get_class()
			}

			# 如果树有额外属性，也保存它们
			if "health" in tree:
				tree_data["health"] = tree.health

			if "state" in tree:
				tree_data["state"] = tree.state

			trees_data.append(tree_data)

	# 保存到临时文件，以便在游戏运行时加载
	var file = FileAccess.open("user://trees_data.json", FileAccess.WRITE)
	var json_string = JSON.stringify(trees_data)
	file.store_string(json_string)
	file.close()

# 清除所有数据
static func clear_all_data() -> void:
	# 清除树木数据
	if FileAccess.file_exists("user://trees_data.json"):
		var dir = DirAccess.open("user://")
		dir.remove("trees_data.json")

# 递归获取所有子节点
static func _get_all_children(node: Node) -> Array:
	var children = []
	for child in node.get_children():
		children.append(child)
		children.append_array(_get_all_children(child))
	return children
