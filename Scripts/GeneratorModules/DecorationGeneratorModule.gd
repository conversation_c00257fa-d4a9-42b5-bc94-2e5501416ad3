class_name DecorationGeneratorModule
extends GeneratorModule

func _init() -> void:
	module_id = "decoration_generator"
	module_name = "装饰物生成器"
	description = "在关卡中生成装饰物，如花草等"

func get_default_config() -> Dictionary:
	return {
		"decoration_scene": "res://Scenes/Prefabs/Decoration.tscn",
		"max_decorations": 20,
		"min_distance": 2.0,  # 装饰物间的最小距离
		"border_margin": 5.0,  # 距边界的最小距离
		"distribution_type": "random", # random, grouped, near_objects
		"min_distance_to_trees": 1.0, # 与树木的最小距离
		"min_distance_to_chests": 2.0, # 与宝箱的最小距离
		"min_distance_to_rocks": 1.0, # 与岩石的最小距离
		"min_distance_to_barrels": 1.0, # 与木桶的最小距离
		"random_rotation": true,  # 是否随机旋转装饰物
		"random_scale": true, # 是否随机缩放
		"min_scale": 0.6, # 最小缩放
		"max_scale": 1.4 # 最大缩放
	}

func generate(level_generator: Node3D, nav_region: NavigationRegion3D) -> void:
	# 清除现有装饰物
	clear()

	# 加载装饰物场景
	var decoration_scene_path = config.get("decoration_scene", "res://Scenes/Prefabs/Decoration.tscn")
	var decoration_scene = load(decoration_scene_path)
	if not decoration_scene:
		push_error("DecorationGeneratorModule: 无法加载装饰物场景: " + decoration_scene_path)
		return

	# 获取关卡参数
	var level_size = level_generator.level_size
	var max_decorations = config.get("max_decorations", 20)
	var min_distance = config.get("min_distance", 2.0)
	var border_margin = config.get("border_margin", 5.0)
	var distribution_type = config.get("distribution_type", "random")
	var min_distance_to_trees = config.get("min_distance_to_trees", 1.0)
	var min_distance_to_chests = config.get("min_distance_to_chests", 2.0)
	var min_distance_to_rocks = config.get("min_distance_to_rocks", 1.0)
	var min_distance_to_barrels = config.get("min_distance_to_barrels", 1.0)
	var random_rotation = config.get("random_rotation", true)
	var random_scale = config.get("random_scale", true)
	var min_scale = config.get("min_scale", 0.6)
	var max_scale = config.get("max_scale", 1.4)

	# 获取其他物体的位置
	var tree_positions = _get_object_positions(nav_region, "tree")
	var chest_positions = _get_object_positions(nav_region, "chest")
	var rock_positions = _get_object_positions(nav_region, "rock")
	var barrel_positions = _get_object_positions(nav_region, "barrel")

	# 根据分布类型生成装饰物位置
	var decoration_positions = []

	match distribution_type:
		"random":
			decoration_positions = _generate_random_positions(
				level_size, max_decorations, min_distance, border_margin,
				tree_positions, chest_positions, rock_positions, barrel_positions,
				min_distance_to_trees, min_distance_to_chests, min_distance_to_rocks, min_distance_to_barrels
			)
		"grouped":
			decoration_positions = _generate_grouped_positions(
				level_size, max_decorations, min_distance, border_margin,
				tree_positions, chest_positions, rock_positions, barrel_positions,
				min_distance_to_trees, min_distance_to_chests, min_distance_to_rocks, min_distance_to_barrels
			)
		"near_objects":
			decoration_positions = _generate_near_objects_positions(
				level_size, max_decorations, min_distance, border_margin,
				tree_positions, chest_positions, rock_positions, barrel_positions,
				min_distance_to_trees, min_distance_to_chests, min_distance_to_rocks, min_distance_to_barrels
			)
		_:
			push_error("DecorationGeneratorModule: 未知的分布类型: " + distribution_type)
			return

	# 生成装饰物
	for i in range(decoration_positions.size()):
		var pos = decoration_positions[i]
		var decoration = decoration_scene.instantiate()
		decoration.position = Vector3(pos.x, 0, pos.y)  # 放在地面上

		# 设置装饰物名称为"decoration@编号"
		decoration.name = "decoration@" + str(i)

		# 随机旋转
		if random_rotation:
			decoration.rotation.y = randf_range(0, TAU)

		# 随机缩放
		if random_scale:
			var scale_factor = randf_range(min_scale, max_scale)
			decoration.scale = Vector3(scale_factor, scale_factor, scale_factor)

		# 添加到组
		decoration.add_to_group("fog_objects")
		decoration.add_to_group("decorations")

		# 添加到navigation区域
		nav_region.add_child(decoration)
		if level_generator.edited_scene_root:
			decoration.owner = level_generator.edited_scene_root

		# 保存生成的对象
		generated_objects.append(decoration)

# 获取特定类型的对象位置
func _get_object_positions(nav_region, type: String) -> Array:
	var positions = []

	# 安全检查，如果nav_region为空，返回空数组
	if nav_region == null:
		return positions

	# 查找所有指定类型的对象
	var objects = []
	for child in nav_region.get_children():
		if type == "tree" and (child.is_in_group("tree") or child.is_in_group("fog_objects") or child.name.begins_with("Tree")):
			objects.append(child)
		elif type == "chest" and (child.is_in_group("chest") or child.is_in_group("chests")):
			objects.append(child)
		elif type == "rock" and (child.is_in_group("rock") or child.is_in_group("rocks")):
			objects.append(child)
		elif type == "barrel" and (child.is_in_group("barrel") or child.is_in_group("barrels")):
			objects.append(child)

	# 提取位置
	for obj in objects:
		positions.append(Vector2(obj.position.x, obj.position.z))

	return positions

# 生成随机位置
func _generate_random_positions(level_size: Vector2, max_count: int, min_distance: float,
								border_margin: float, tree_positions: Array, chest_positions: Array,
								rock_positions: Array, barrel_positions: Array,
								min_distance_to_trees: float, min_distance_to_chests: float,
								min_distance_to_rocks: float, min_distance_to_barrels: float) -> Array:
	var positions = []
	var half_size = level_size / 2.0
	var available_area = Rect2(
		-half_size.x + border_margin,
		-half_size.y + border_margin,
		level_size.x - 2 * border_margin,
		level_size.y - 2 * border_margin
	)

	# 尝试生成装饰物位置，直到达到最大数量或尝试了足够多的次数
	var attempts = 0
	var max_attempts = max_count * 10  # 设置最大尝试次数，避免无限循环

	while positions.size() < max_count and attempts < max_attempts:
		attempts += 1

		# 随机生成位置
		var pos = Vector2(
			randf_range(available_area.position.x, available_area.end.x),
			randf_range(available_area.position.y, available_area.end.y)
		)

		# 检查与现有装饰物的距离
		var too_close_to_decoration = false
		for existing_pos in positions:
			if existing_pos.distance_to(pos) < min_distance:
				too_close_to_decoration = true
				break

		if too_close_to_decoration:
			continue

		# 检查与树木的距离
		var too_close_to_tree = false
		for tree_pos in tree_positions:
			if tree_pos.distance_to(pos) < min_distance_to_trees:
				too_close_to_tree = true
				break

		if too_close_to_tree:
			continue

		# 检查与宝箱的距离
		var too_close_to_chest = false
		for chest_pos in chest_positions:
			if chest_pos.distance_to(pos) < min_distance_to_chests:
				too_close_to_chest = true
				break

		if too_close_to_chest:
			continue

		# 检查与岩石的距离
		var too_close_to_rock = false
		for rock_pos in rock_positions:
			if rock_pos.distance_to(pos) < min_distance_to_rocks:
				too_close_to_rock = true
				break

		if too_close_to_rock:
			continue

		# 检查与木桶的距离
		var too_close_to_barrel = false
		for barrel_pos in barrel_positions:
			if barrel_pos.distance_to(pos) < min_distance_to_barrels:
				too_close_to_barrel = true
				break

		if not too_close_to_barrel:
			positions.append(pos)

	return positions

# 生成分组位置
func _generate_grouped_positions(level_size: Vector2, max_count: int, min_distance: float,
								border_margin: float, tree_positions: Array, chest_positions: Array,
								rock_positions: Array, barrel_positions: Array,
								min_distance_to_trees: float, min_distance_to_chests: float,
								min_distance_to_rocks: float, min_distance_to_barrels: float) -> Array:
	var positions = []
	var half_size = level_size / 2.0
	var available_area = Rect2(
		-half_size.x + border_margin,
		-half_size.y + border_margin,
		level_size.x - 2 * border_margin,
		level_size.y - 2 * border_margin
	)

	# 创建分组中心
	var group_count = min(8, max_count / 3.0)  # 至少每组3个装饰物
	var group_centers = []

	for i in range(group_count):
		var center_pos = Vector2(
			randf_range(available_area.position.x, available_area.end.x),
			randf_range(available_area.position.y, available_area.end.y)
		)

		# 检查与其他物体的距离
		var too_close = false

		# 检查与宝箱的距离 (装饰物分组应该远离宝箱)
		for chest_pos in chest_positions:
			if chest_pos.distance_to(center_pos) < min_distance_to_chests:
				too_close = true
				break

		if not too_close:
			group_centers.append(center_pos)

	# 在每个组内生成装饰物
	for center in group_centers:
		var decorations_per_group = max_count / max(1, group_centers.size())
		for i in range(decorations_per_group):
			var angle = randf_range(0, TAU)
			var distance = randf_range(0.2, 2.5)  # 装饰物可以更靠近组中心
			var pos = center + Vector2(cos(angle), sin(angle)) * distance

			# 检查是否在可用区域内
			if not available_area.has_point(pos):
				continue

			# 检查与其他装饰物的距离
			var too_close = false
			for existing_pos in positions:
				if existing_pos.distance_to(pos) < min_distance:
					too_close = true
					break

			if too_close:
				continue

			# 检查与树木的距离
			for tree_pos in tree_positions:
				if tree_pos.distance_to(pos) < min_distance_to_trees:
					too_close = true
					break

			if too_close:
				continue

			# 检查与岩石的距离
			for rock_pos in rock_positions:
				if rock_pos.distance_to(pos) < min_distance_to_rocks:
					too_close = true
					break

			if too_close:
				continue

			# 检查与木桶的距离
			for barrel_pos in barrel_positions:
				if barrel_pos.distance_to(pos) < min_distance_to_barrels:
					too_close = true
					break

			if not too_close:
				positions.append(pos)

	return positions

# 在其他物体附近生成装饰物
func _generate_near_objects_positions(level_size: Vector2, max_count: int, min_distance: float,
									 border_margin: float, tree_positions: Array, chest_positions: Array,
									 rock_positions: Array, barrel_positions: Array,
									 min_distance_to_trees: float, min_distance_to_chests: float,
									 min_distance_to_rocks: float, min_distance_to_barrels: float) -> Array:
	var positions = []
	var half_size = level_size / 2.0
	var available_area = Rect2(
		-half_size.x + border_margin,
		-half_size.y + border_margin,
		level_size.x - 2 * border_margin,
		level_size.y - 2 * border_margin
	)

	# 合并所有对象位置，优先在它们附近放置装饰物
	var all_object_positions = []
	all_object_positions.append_array(tree_positions)
	all_object_positions.append_array(rock_positions)
	all_object_positions.append_array(barrel_positions)

	# 如果没有对象，使用随机分布
	if all_object_positions.size() == 0:
		return _generate_random_positions(
			level_size, max_count, min_distance, border_margin,
			tree_positions, chest_positions, rock_positions, barrel_positions,
			min_distance_to_trees, min_distance_to_chests, min_distance_to_rocks, min_distance_to_barrels
		)

	# 尝试生成装饰物位置，直到达到最大数量或尝试了足够多的次数
	var attempts = 0
	var max_attempts = max_count * 10  # 设置最大尝试次数，避免无限循环

	while positions.size() < max_count and attempts < max_attempts:
		attempts += 1

		# 选择一个随机对象位置
		var object_pos = all_object_positions[randi() % all_object_positions.size()]

		# 在对象附近生成位置
		var angle = randf_range(0, TAU)
		var min_radius = 0
		var max_radius = 3.0

		# 根据对象类型调整距离
		if object_pos in tree_positions:
			min_radius = min_distance_to_trees
			max_radius = min_distance_to_trees + 2.0
		elif object_pos in rock_positions:
			min_radius = min_distance_to_rocks
			max_radius = min_distance_to_rocks + 1.5
		elif object_pos in barrel_positions:
			min_radius = min_distance_to_barrels
			max_radius = min_distance_to_barrels + 1.0

		var distance = randf_range(min_radius, max_radius)
		var pos = object_pos + Vector2(cos(angle), sin(angle)) * distance

		# 检查是否在可用区域内
		if not available_area.has_point(pos):
			continue

		# 检查与现有装饰物的距离
		var too_close_to_decoration = false
		for existing_pos in positions:
			if existing_pos.distance_to(pos) < min_distance:
				too_close_to_decoration = true
				break

		if too_close_to_decoration:
			continue

		# 检查与宝箱的距离
		var too_close_to_chest = false
		for chest_pos in chest_positions:
			if chest_pos.distance_to(pos) < min_distance_to_chests:
				too_close_to_chest = true
				break

		if not too_close_to_chest:
			positions.append(pos)

	return positions

func validate_config() -> bool:
	# 如果存在覆盖参数，使用它
	var _override_validation = config.get("_override_validation", false)
	if _override_validation:
		return true

	# 检查场景路径
	if not config.has("decoration_scene") or not config.get("decoration_scene") is String:
		push_error("DecorationGeneratorModule: 缺少 decoration_scene 或类型不是 String")
		return false

	var decoration_path = config.get("decoration_scene", "")
	if not ResourceLoader.exists(decoration_path):
		push_error("DecorationGeneratorModule: decoration_scene 路径不存在: " + decoration_path)
		return false

	# 检查数量设置
	if not config.has("max_decorations") or not config.get("max_decorations") is int:
		push_error("DecorationGeneratorModule: 缺少 max_decorations 或类型不是 int")
		return false

	# 检查距离设置
	if not config.has("min_distance") or not config.get("min_distance") is float:
		push_error("DecorationGeneratorModule: 缺少 min_distance 或类型不是 float")
		return false
	if not config.has("border_margin") or not config.get("border_margin") is float:
		push_error("DecorationGeneratorModule: 缺少 border_margin 或类型不是 float")
		return false
	if not config.has("min_distance_to_trees") or not config.get("min_distance_to_trees") is float:
		push_error("DecorationGeneratorModule: 缺少 min_distance_to_trees 或类型不是 float")
		return false
	if not config.has("min_distance_to_chests") or not config.get("min_distance_to_chests") is float:
		push_error("DecorationGeneratorModule: 缺少 min_distance_to_chests 或类型不是 float")
		return false
	if not config.has("min_distance_to_rocks") or not config.get("min_distance_to_rocks") is float:
		push_error("DecorationGeneratorModule: 缺少 min_distance_to_rocks 或类型不是 float")
		return false
	if not config.has("min_distance_to_barrels") or not config.get("min_distance_to_barrels") is float:
		push_error("DecorationGeneratorModule: 缺少 min_distance_to_barrels 或类型不是 float")
		return false

	# 检查分布类型
	if not config.has("distribution_type") or not config.get("distribution_type") is String:
		push_error("DecorationGeneratorModule: 缺少 distribution_type 或类型不是 String")
		return false
	var distribution = config.get("distribution_type", "")
	if not distribution in ["random", "grouped", "near_objects"]:
		push_error("DecorationGeneratorModule: distribution_type 值无效: " + distribution)
		return false

	# 检查旋转设置
	if not config.has("random_rotation") or not config.get("random_rotation") is bool:
		push_error("DecorationGeneratorModule: 缺少 random_rotation 或类型不是 bool")
		return false

	# 检查缩放设置
	if not config.has("random_scale") or not config.get("random_scale") is bool:
		push_error("DecorationGeneratorModule: 缺少 random_scale 或类型不是 bool")
		return false
	if not config.has("min_scale") or not config.get("min_scale") is float:
		push_error("DecorationGeneratorModule: 缺少 min_scale 或类型不是 float")
		return false
	if not config.has("max_scale") or not config.get("max_scale") is float:
		push_error("DecorationGeneratorModule: 缺少 max_scale 或类型不是 float")
		return false

	return true
