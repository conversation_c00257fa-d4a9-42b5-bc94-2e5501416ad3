class_name BarrelGeneratorModule
extends GeneratorModule

func _init() -> void:
	module_id = "barrel_generator"
	module_name = "木桶生成器"
	description = "在关卡中生成木桶"

func get_default_config() -> Dictionary:
	return {
		"barrel_scene": "res://Scenes/Prefabs/Barrel.tscn",
		"max_barrels": 10,
		"min_distance": 3.0,  # 木桶间的最小距离
		"border_margin": 5.0,  # 距边界的最小距离
		"distribution_type": "random", # random, grouped
		"min_distance_to_trees": 3.0, # 与树木的最小距离
		"min_distance_to_chests": 3.0, # 与宝箱的最小距离
		"min_distance_to_rocks": 2.0, # 与岩石的最小距离
		"random_rotation": true,  # 是否随机旋转木桶
		"random_scale": true, # 是否随机缩放
		"min_scale": 0.8, # 最小缩放
		"max_scale": 1.2 # 最大缩放
	}

func generate(level_generator: Node3D, nav_region: NavigationRegion3D) -> void:
	# 清除现有木桶
	clear()

	# 加载木桶场景
	var barrel_scene_path = config.get("barrel_scene", "res://Scenes/Prefabs/Barrel.tscn")
	var barrel_scene = load(barrel_scene_path)
	if not barrel_scene:
		push_error("BarrelGeneratorModule: 无法加载木桶场景: " + barrel_scene_path)
		return

	# 获取关卡参数
	var level_size = level_generator.level_size
	# 木桶生成范围：围墙范围内 (level_size - Vector2(50, 50))
	var barrel_area = level_size - Vector2(50, 50)
	var max_barrels = config.get("max_barrels", 10)
	var min_distance = config.get("min_distance", 3.0)
	var border_margin = config.get("border_margin", 5.0)
	var distribution_type = config.get("distribution_type", "random")
	var min_distance_to_trees = config.get("min_distance_to_trees", 3.0)
	var min_distance_to_chests = config.get("min_distance_to_chests", 3.0)
	var min_distance_to_rocks = config.get("min_distance_to_rocks", 2.0)
	var random_rotation = config.get("random_rotation", true)
	var random_scale = config.get("random_scale", true)
	var min_scale = config.get("min_scale", 0.8)
	var max_scale = config.get("max_scale", 1.2)

	# 获取树木、宝箱和岩石位置
	var tree_positions = _get_object_positions(nav_region, "tree")
	var chest_positions = _get_object_positions(nav_region, "chest")
	var rock_positions = _get_object_positions(nav_region, "rock")

	# 根据分布类型生成木桶位置
	var barrel_positions = []

	match distribution_type:
		"random":
			barrel_positions = _generate_random_positions(
				barrel_area, max_barrels, min_distance, border_margin,
				tree_positions, chest_positions, rock_positions,
				min_distance_to_trees, min_distance_to_chests, min_distance_to_rocks
			)
		"grouped":
			barrel_positions = _generate_grouped_positions(
				barrel_area, max_barrels, min_distance, border_margin,
				tree_positions, chest_positions, rock_positions,
				min_distance_to_trees, min_distance_to_chests, min_distance_to_rocks
			)
		_:
			push_error("BarrelGeneratorModule: 未知的分布类型: " + distribution_type)
			return

	# 生成木桶
	for i in range(barrel_positions.size()):
		var pos = barrel_positions[i]
		var barrel = barrel_scene.instantiate()
		barrel.position = Vector3(pos.x, 0, pos.y)  # 放在地面上

		# 设置木桶名称为"barrel@编号"
		barrel.name = "barrel@" + str(i)

		# 随机旋转
		if random_rotation:
			barrel.rotation.y = randf_range(0, TAU)

		# 随机缩放
		if random_scale:
			var scale_factor = randf_range(min_scale, max_scale)
			barrel.scale = Vector3(scale_factor, scale_factor, scale_factor)

		# 添加到组
		barrel.add_to_group("fog_objects")
		barrel.add_to_group("barrels")

		# 添加到navigation区域
		nav_region.add_child(barrel)
		if level_generator.edited_scene_root:
			barrel.owner = level_generator.edited_scene_root

		# 保存生成的对象
		generated_objects.append(barrel)

# 获取特定类型的对象位置
func _get_object_positions(nav_region, type: String) -> Array:
	var positions = []

	# 安全检查，如果nav_region为空，返回空数组
	if nav_region == null:
		return positions

	# 查找所有指定类型的对象
	var objects = []
	for child in nav_region.get_children():
		if type == "tree" and (child.is_in_group("tree") or child.is_in_group("fog_objects") or child.name.begins_with("Tree")):
			objects.append(child)
		elif type == "chest" and (child.is_in_group("chest") or child.is_in_group("chests")):
			objects.append(child)
		elif type == "rock" and (child.is_in_group("rock") or child.is_in_group("rocks")):
			objects.append(child)

	# 提取位置
	for obj in objects:
		positions.append(Vector2(obj.position.x, obj.position.z))

	return positions

# 生成随机位置
func _generate_random_positions(level_size: Vector2, max_count: int, min_distance: float,
								border_margin: float, tree_positions: Array, chest_positions: Array, rock_positions: Array,
								min_distance_to_trees: float, min_distance_to_chests: float, min_distance_to_rocks: float) -> Array:
	var positions = []
	var half_size = level_size / 2.0
	var available_area = Rect2(
		-half_size.x + border_margin,
		-half_size.y + border_margin,
		level_size.x - 2 * border_margin,
		level_size.y - 2 * border_margin
	)

	# 尝试生成木桶位置，直到达到最大数量或尝试了足够多的次数
	var attempts = 0
	var max_attempts = max_count * 20  # 设置最大尝试次数，避免无限循环

	while positions.size() < max_count and attempts < max_attempts:
		attempts += 1

		# 随机生成位置
		var pos = Vector2(
			randf_range(available_area.position.x, available_area.end.x),
			randf_range(available_area.position.y, available_area.end.y)
		)

		# 检查与现有木桶的距离
		var too_close_to_barrel = false
		for existing_pos in positions:
			if existing_pos.distance_to(pos) < min_distance:
				too_close_to_barrel = true
				break

		if too_close_to_barrel:
			continue

		# 检查与树木的距离
		var too_close_to_tree = false
		for tree_pos in tree_positions:
			if tree_pos.distance_to(pos) < min_distance_to_trees:
				too_close_to_tree = true
				break

		if too_close_to_tree:
			continue

		# 检查与宝箱的距离
		var too_close_to_chest = false
		for chest_pos in chest_positions:
			if chest_pos.distance_to(pos) < min_distance_to_chests:
				too_close_to_chest = true
				break

		if too_close_to_chest:
			continue

		# 检查与岩石的距离
		var too_close_to_rock = false
		for rock_pos in rock_positions:
			if rock_pos.distance_to(pos) < min_distance_to_rocks:
				too_close_to_rock = true
				break

		if not too_close_to_rock:
			positions.append(pos)

	return positions

# 生成分组位置
func _generate_grouped_positions(level_size: Vector2, max_count: int, min_distance: float,
								border_margin: float, tree_positions: Array, chest_positions: Array, rock_positions: Array,
								min_distance_to_trees: float, min_distance_to_chests: float, min_distance_to_rocks: float) -> Array:
	var positions = []
	var half_size = level_size / 2.0
	var available_area = Rect2(
		-half_size.x + border_margin,
		-half_size.y + border_margin,
		level_size.x - 2 * border_margin,
		level_size.y - 2 * border_margin
	)

	# 创建分组中心
	var group_count = min(5, max_count / 2.0)  # 至少每组2个木桶
	var group_centers = []

	for i in range(group_count):
		var center_pos = Vector2(
			randf_range(available_area.position.x, available_area.end.x),
			randf_range(available_area.position.y, available_area.end.y)
		)

		# 检查与树木、宝箱和岩石的距离
		var too_close = false

		# 检查与树木的距离
		for tree_pos in tree_positions:
			if tree_pos.distance_to(center_pos) < min_distance_to_trees:
				too_close = true
				break

		if too_close:
			continue

		# 检查与宝箱的距离
		for chest_pos in chest_positions:
			if chest_pos.distance_to(center_pos) < min_distance_to_chests:
				too_close = true
				break

		if too_close:
			continue

		# 检查与岩石的距离
		for rock_pos in rock_positions:
			if rock_pos.distance_to(center_pos) < min_distance_to_rocks:
				too_close = true
				break

		if not too_close:
			group_centers.append(center_pos)

	# 在每个组内生成木桶
	for center in group_centers:
		var barrels_per_group = max_count / max(1, group_centers.size())
		for i in range(barrels_per_group):
			var angle = randf_range(0, TAU)
			var distance = randf_range(0.5, 3.0)
			var pos = center + Vector2(cos(angle), sin(angle)) * distance

			# 检查是否在可用区域内
			if not available_area.has_point(pos):
				continue

			# 检查与其他木桶的距离
			var too_close = false
			for existing_pos in positions:
				if existing_pos.distance_to(pos) < min_distance:
					too_close = true
					break

			if not too_close:
				positions.append(pos)

	return positions

func validate_config() -> bool:
	# 如果存在覆盖参数，使用它
	var _override_validation = config.get("_override_validation", false)
	if _override_validation:
		return true

	# 检查场景路径
	if not config.has("barrel_scene") or not config.get("barrel_scene") is String:
		push_error("BarrelGeneratorModule: 缺少 barrel_scene 或类型不是 String")
		return false

	var barrel_path = config.get("barrel_scene", "")
	if not ResourceLoader.exists(barrel_path):
		push_error("BarrelGeneratorModule: barrel_scene 路径不存在: " + barrel_path)
		return false

	# 检查数量设置
	if not config.has("max_barrels") or not config.get("max_barrels") is int:
		push_error("BarrelGeneratorModule: 缺少 max_barrels 或类型不是 int")
		return false

	# 检查距离设置
	if not config.has("min_distance") or not config.get("min_distance") is float:
		push_error("BarrelGeneratorModule: 缺少 min_distance 或类型不是 float")
		return false
	if not config.has("border_margin") or not config.get("border_margin") is float:
		push_error("BarrelGeneratorModule: 缺少 border_margin 或类型不是 float")
		return false
	if not config.has("min_distance_to_trees") or not config.get("min_distance_to_trees") is float:
		push_error("BarrelGeneratorModule: 缺少 min_distance_to_trees 或类型不是 float")
		return false
	if not config.has("min_distance_to_chests") or not config.get("min_distance_to_chests") is float:
		push_error("BarrelGeneratorModule: 缺少 min_distance_to_chests 或类型不是 float")
		return false
	if not config.has("min_distance_to_rocks") or not config.get("min_distance_to_rocks") is float:
		push_error("BarrelGeneratorModule: 缺少 min_distance_to_rocks 或类型不是 float")
		return false

	# 检查分布类型
	if not config.has("distribution_type") or not config.get("distribution_type") is String:
		push_error("BarrelGeneratorModule: 缺少 distribution_type 或类型不是 String")
		return false
	var distribution = config.get("distribution_type", "")
	if not distribution in ["random", "grouped"]:
		push_error("BarrelGeneratorModule: distribution_type 值无效: " + distribution)
		return false

	# 检查旋转设置
	if not config.has("random_rotation") or not config.get("random_rotation") is bool:
		push_error("BarrelGeneratorModule: 缺少 random_rotation 或类型不是 bool")
		return false

	# 检查缩放设置
	if not config.has("random_scale") or not config.get("random_scale") is bool:
		push_error("BarrelGeneratorModule: 缺少 random_scale 或类型不是 bool")
		return false
	if not config.has("min_scale") or not config.get("min_scale") is float:
		push_error("BarrelGeneratorModule: 缺少 min_scale 或类型不是 float")
		return false
	if not config.has("max_scale") or not config.get("max_scale") is float:
		push_error("BarrelGeneratorModule: 缺少 max_scale 或类型不是 float")
		return false

	return true
