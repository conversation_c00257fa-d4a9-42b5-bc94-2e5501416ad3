class_name RockGeneratorModule
extends GeneratorModule

func _init() -> void:
	module_id = "rock_generator"
	module_name = "岩石生成器"
	description = "在关卡中生成岩石"

func get_default_config() -> Dictionary:
	return {
		"rock_scene": "res://Scenes/Prefabs/Rock.tscn",
		"max_rocks": 15,
		"min_distance": 3.0,  # 岩石间的最小距离
		"border_margin": 5.0,  # 距边界的最小距离
		"distribution_type": "random", # random, grouped
		"min_distance_to_trees": 3.0, # 与树木的最小距离
		"min_distance_to_chests": 3.0, # 与宝箱的最小距离
		"random_rotation": true,  # 是否随机旋转岩石
		"random_scale": true, # 是否随机缩放
		"min_scale": 0.8, # 最小缩放
		"max_scale": 1.2 # 最大缩放
	}

func generate(level_generator: Node3D, nav_region: NavigationRegion3D) -> void:
	# 清除现有岩石
	clear()

	# 加载岩石场景
	var rock_scene_path = config.get("rock_scene", "res://Scenes/Prefabs/Rock.tscn")
	var rock_scene = load(rock_scene_path)
	if not rock_scene:
		push_error("RockGeneratorModule: 无法加载岩石场景: " + rock_scene_path)
		return

	# 获取关卡参数
	var level_size = level_generator.level_size
	var max_rocks = config.get("max_rocks", 15)
	var min_distance = config.get("min_distance", 3.0)
	var border_margin = config.get("border_margin", 5.0)
	var distribution_type = config.get("distribution_type", "random")
	var min_distance_to_trees = config.get("min_distance_to_trees", 3.0)
	var min_distance_to_chests = config.get("min_distance_to_chests", 3.0)
	var random_rotation = config.get("random_rotation", true)
	var random_scale = config.get("random_scale", true)
	var min_scale = config.get("min_scale", 0.8)
	var max_scale = config.get("max_scale", 1.2)

	# 获取树木和宝箱位置
	var tree_positions = _get_object_positions(nav_region, "tree")
	var chest_positions = _get_object_positions(nav_region, "chest")

	# 根据分布类型生成岩石位置
	var rock_positions = []

	match distribution_type:
		"random":
			rock_positions = _generate_random_positions(
				level_size, max_rocks, min_distance, border_margin,
				tree_positions, chest_positions, min_distance_to_trees, min_distance_to_chests
			)
		"grouped":
			rock_positions = _generate_grouped_positions(
				level_size, max_rocks, min_distance, border_margin,
				tree_positions, chest_positions, min_distance_to_trees, min_distance_to_chests
			)
		_:
			push_error("RockGeneratorModule: 未知的分布类型: " + distribution_type)
			return

	# 生成岩石
	for i in range(rock_positions.size()):
		var pos = rock_positions[i]
		var rock = rock_scene.instantiate()
		rock.position = Vector3(pos.x, 0.01, pos.y)  # 稍微抬高，以防与地面重叠

		# 设置岩石名称为"rock@编号"
		rock.name = "rock@" + str(i)

		# 随机旋转
		if random_rotation:
			rock.rotation.y = randf_range(0, TAU)

		# 随机缩放
		if random_scale:
			var scale_factor = randf_range(min_scale, max_scale)
			rock.scale = Vector3(scale_factor, scale_factor, scale_factor)

		# 添加到组
		rock.add_to_group("fog_objects")
		rock.add_to_group("rocks")

		# 添加到navigation区域
		nav_region.add_child(rock)
		if level_generator.edited_scene_root:
			rock.owner = level_generator.edited_scene_root

		# 保存生成的对象
		generated_objects.append(rock)

# 获取特定类型的对象位置
func _get_object_positions(nav_region, type: String) -> Array:
	var positions = []

	# 安全检查，如果nav_region为空，返回空数组
	if nav_region == null:
		return positions

	# 查找所有指定类型的对象
	var objects = []
	for child in nav_region.get_children():
		if type == "tree" and (child.is_in_group("tree") or child.is_in_group("fog_objects") or child.name.begins_with("Tree")):
			objects.append(child)
		elif type == "chest" and (child.is_in_group("chest") or child.is_in_group("chests")):
			objects.append(child)

	# 提取位置
	for obj in objects:
		positions.append(Vector2(obj.position.x, obj.position.z))

	return positions

# 生成随机位置
func _generate_random_positions(level_size: Vector2, max_count: int, min_distance: float,
								border_margin: float, tree_positions: Array, chest_positions: Array,
								min_distance_to_trees: float, min_distance_to_chests: float) -> Array:
	var positions = []
	var half_size = level_size / 2.0
	var available_area = Rect2(
		-half_size.x + border_margin,
		-half_size.y + border_margin,
		level_size.x - 2 * border_margin,
		level_size.y - 2 * border_margin
	)

	# 尝试生成岩石位置，直到达到最大数量或尝试了足够多的次数
	var attempts = 0
	var max_attempts = max_count * 20  # 设置最大尝试次数，避免无限循环

	while positions.size() < max_count and attempts < max_attempts:
		attempts += 1

		# 随机生成位置
		var pos = Vector2(
			randf_range(available_area.position.x, available_area.end.x),
			randf_range(available_area.position.y, available_area.end.y)
		)

		# 检查与现有岩石的距离
		var too_close_to_rock = false
		for existing_pos in positions:
			if existing_pos.distance_to(pos) < min_distance:
				too_close_to_rock = true
				break

		if too_close_to_rock:
			continue

		# 检查与树木的距离
		var too_close_to_tree = false
		for tree_pos in tree_positions:
			if tree_pos.distance_to(pos) < min_distance_to_trees:
				too_close_to_tree = true
				break

		if too_close_to_tree:
			continue

		# 检查与宝箱的距离
		var too_close_to_chest = false
		for chest_pos in chest_positions:
			if chest_pos.distance_to(pos) < min_distance_to_chests:
				too_close_to_chest = true
				break

		if not too_close_to_chest:
			positions.append(pos)

	return positions

# 生成分组位置
func _generate_grouped_positions(level_size: Vector2, max_count: int, min_distance: float,
								border_margin: float, tree_positions: Array, chest_positions: Array,
								min_distance_to_trees: float, min_distance_to_chests: float) -> Array:
	var positions = []
	var half_size = level_size / 2.0
	var available_area = Rect2(
		-half_size.x + border_margin,
		-half_size.y + border_margin,
		level_size.x - 2 * border_margin,
		level_size.y - 2 * border_margin
	)

	# 创建分组中心
	var group_count = min(5, max_count / 3.0)  # 至少每组3个岩石
	var group_centers = []

	for i in range(group_count):
		var center_pos = Vector2(
			randf_range(available_area.position.x, available_area.end.x),
			randf_range(available_area.position.y, available_area.end.y)
		)

		# 检查与树木和宝箱的距离
		var too_close = false
		for tree_pos in tree_positions:
			if tree_pos.distance_to(center_pos) < min_distance_to_trees:
				too_close = true
				break

		if not too_close:
			for chest_pos in chest_positions:
				if chest_pos.distance_to(center_pos) < min_distance_to_chests:
					too_close = true
					break

		if not too_close:
			group_centers.append(center_pos)

	# 在每个组内生成岩石
	for center in group_centers:
		var rocks_per_group = max_count / float(group_centers.size())
		for i in range(rocks_per_group):
			var angle = randf_range(0, TAU)
			var distance = randf_range(1.0, 5.0)
			var pos = center + Vector2(cos(angle), sin(angle)) * distance

			# 检查是否在可用区域内
			if not available_area.has_point(pos):
				continue

			# 检查与其他岩石的距离
			var too_close = false
			for existing_pos in positions:
				if existing_pos.distance_to(pos) < min_distance:
					too_close = true
					break

			if not too_close:
				positions.append(pos)

	return positions

func validate_config() -> bool:
	# 如果存在覆盖参数，使用它
	var _override_validation = config.get("_override_validation", false)
	if _override_validation:
		return true

	# 检查场景路径
	if not config.has("rock_scene") or not config.get("rock_scene") is String:
		push_error("RockGeneratorModule: 缺少 rock_scene 或类型不是 String")
		return false

	var rock_path = config.get("rock_scene", "")
	if not ResourceLoader.exists(rock_path):
		push_error("RockGeneratorModule: rock_scene 路径不存在: " + rock_path)
		return false

	# 检查数量设置
	if not config.has("max_rocks") or not config.get("max_rocks") is int:
		push_error("RockGeneratorModule: 缺少 max_rocks 或类型不是 int")
		return false

	# 检查距离设置
	if not config.has("min_distance") or not config.get("min_distance") is float:
		push_error("RockGeneratorModule: 缺少 min_distance 或类型不是 float")
		return false
	if not config.has("border_margin") or not config.get("border_margin") is float:
		push_error("RockGeneratorModule: 缺少 border_margin 或类型不是 float")
		return false
	if not config.has("min_distance_to_trees") or not config.get("min_distance_to_trees") is float:
		push_error("RockGeneratorModule: 缺少 min_distance_to_trees 或类型不是 float")
		return false
	if not config.has("min_distance_to_chests") or not config.get("min_distance_to_chests") is float:
		push_error("RockGeneratorModule: 缺少 min_distance_to_chests 或类型不是 float")
		return false

	# 检查分布类型
	if not config.has("distribution_type") or not config.get("distribution_type") is String:
		push_error("RockGeneratorModule: 缺少 distribution_type 或类型不是 String")
		return false
	var distribution = config.get("distribution_type", "")
	if not distribution in ["random", "grouped"]:
		push_error("RockGeneratorModule: distribution_type 值无效: " + distribution)
		return false

	# 检查旋转设置
	if not config.has("random_rotation") or not config.get("random_rotation") is bool:
		push_error("RockGeneratorModule: 缺少 random_rotation 或类型不是 bool")
		return false

	# 检查缩放设置
	if not config.has("random_scale") or not config.get("random_scale") is bool:
		push_error("RockGeneratorModule: 缺少 random_scale 或类型不是 bool")
		return false
	if not config.has("min_scale") or not config.get("min_scale") is float:
		push_error("RockGeneratorModule: 缺少 min_scale 或类型不是 float")
		return false
	if not config.has("max_scale") or not config.get("max_scale") is float:
		push_error("RockGeneratorModule: 缺少 max_scale 或类型不是 float")
		return false

	return true
