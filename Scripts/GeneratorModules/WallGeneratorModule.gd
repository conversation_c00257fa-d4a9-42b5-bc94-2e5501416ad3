class_name WallGeneratorModule
extends GeneratorModule

func _init() -> void:
	module_id = "wall_generator"
	module_name = "围墙生成器"
	description = "在关卡边界生成围墙"

func get_default_config() -> Dictionary:
	return {
		"wall_scene": "res://Scenes/Prefabs/Wall.tscn",
		"spacing": 2.0,  # 墙之间的间距
		"only_border": false,  # 是否只在边界生成墙
		"wall_height": 0.83  # 墙的高度（原高度的1/3）
	}

func generate(level_generator, nav_region) -> void:
	# 清除现有墙壁
	clear()

	# 加载墙壁场景
	var wall_scene_path = config.get("wall_scene", "res://Scenes/Prefabs/Wall.tscn")
	var wall_scene = load(wall_scene_path)
	if not wall_scene:
		push_error("WallGeneratorModule: 无法加载墙壁场景: " + wall_scene_path)
		return

	# 获取关卡参数
	var level_size = level_generator.level_size
	var spacing = config.get("spacing", 2.0)
	var wall_height = config.get("wall_height", 0.83)
	var only_border = config.get("only_border", false)

	# 计算围墙位置
	var wall_positions = _calculate_wall_positions(level_size, spacing, only_border)

	# 生成围墙
	for i in range(wall_positions.size()):
		var pos = wall_positions[i]
		var wall = wall_scene.instantiate()

		# 设置墙的名称为"wall@编号"
		wall.name = "wall@" + str(i)

		# 判断是横向还是纵向的墙
		var is_horizontal = i < wall_positions.size() / 2.0

		wall.position = Vector3(pos.x, 0, pos.y)  # 放在地面上
		wall.scale = Vector3(1.0, wall_height, 1.0)  # 调整高度

		if not is_horizontal:
			# 纵向的墙需要旋转90度
			wall.rotation.y = PI / 2.0

		# 添加到组
		wall.add_to_group("walls")

		# 添加到navigation区域
		nav_region.add_child(wall)
		if level_generator.edited_scene_root:
			wall.owner = level_generator.edited_scene_root

		# 保存生成的对象
		generated_objects.append(wall)

func _calculate_wall_positions(level_size: Vector2, spacing: float, only_border: bool) -> Array:
	var positions = []
	var half_size = level_size / 2.0

	if only_border:
		# 只在边界生成围墙
		# 生成水平方向的墙(顶部和底部边界)
		for x in range(-half_size.x, half_size.x + 1, spacing):
			positions.append(Vector2(x, -half_size.y))  # 底部边界
			positions.append(Vector2(x, half_size.y))   # 顶部边界

		# 生成垂直方向的墙(左侧和右侧边界)
		for z in range(-half_size.y, half_size.y + 1, spacing):
			positions.append(Vector2(-half_size.x, z))  # 左侧边界
			positions.append(Vector2(half_size.x, z))   # 右侧边界
	else:
		# 生成网格状的墙
		# 待实现
		push_error("WallGeneratorModule: 网格状生成模式尚未实现")

	return positions

func validate_config() -> bool:
	# 如果存在覆盖参数，使用它
	var _override_validation = config.get("_override_validation", false)
	if _override_validation:
		return true

	# 检查场景路径
	if not config.has("wall_scene") or not config.get("wall_scene") is String:
		push_error("WallGeneratorModule: 缺少 wall_scene 或类型不是 String")
		return false

	var wall_path = config.get("wall_scene", "")
	if not ResourceLoader.exists(wall_path):
		push_error("WallGeneratorModule: wall_scene 路径不存在: " + wall_path)
		return false

	# 检查间距
	if not config.has("spacing") or not config.get("spacing") is float:
		push_error("WallGeneratorModule: 缺少 spacing 或类型不是 float")
		return false

	# 检查边界设置
	if not config.has("only_border") or not config.get("only_border") is bool:
		push_error("WallGeneratorModule: 缺少 only_border 或类型不是 bool")
		return false

	# 检查墙高
	if not config.has("wall_height") or not config.get("wall_height") is float:
		push_error("WallGeneratorModule: 缺少 wall_height 或类型不是 float")
		return false

	return true