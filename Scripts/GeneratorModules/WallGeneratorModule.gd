class_name WallGeneratorModule
extends GeneratorModule

func _init() -> void:
	module_id = "wall_generator"
	module_name = "围墙生成器"
	description = "在关卡边界生成围墙"

func get_default_config() -> Dictionary:
	return {
		"wall_scene": "res://Scenes/Prefabs/Wall.tscn",
		"spacing": 3.5,  # 墙之间的间距 (增大减少数量)
		"only_border": true,  # 是否只在边界生成墙
		"wall_height": 0.83,  # 墙的高度（原高度的1/3）
		"wall_density": 0.35,  # 密度因子 (0.25-0.4，控制跳过比例)
		"random_scale_range": 0.4,  # 随机缩放范围 (±0.4，更明显变化)
		"random_rotation_range": 0.35,  # 随机旋转范围 (±0.35弧度，约±20度)
		"large_wall_ratio": 0.25,  # 大型wall比例
		"overlap_wall_ratio": 0.15,  # 搭接wall比例
		"height_variation": 0.5,  # 高度变化范围
		"tilt_range": 0.2  # 倾斜角度范围 (±0.2弧度，约±11度)
	}

func generate(level_generator, nav_region) -> void:
	# 清除现有墙壁
	clear()

	# 加载墙壁场景
	var wall_scene_path = config.get("wall_scene", "res://Scenes/Prefabs/Wall.tscn")
	var wall_scene = load(wall_scene_path)
	if not wall_scene:
		push_error("WallGeneratorModule: 无法加载墙壁场景: " + wall_scene_path)
		return

	# 获取关卡参数
	var level_size = level_generator.level_size
	var spacing = config.get("spacing", 3.5)
	var wall_height = config.get("wall_height", 0.83)
	var only_border = config.get("only_border", true)
	var wall_density = config.get("wall_density", 0.35)
	var random_scale_range = config.get("random_scale_range", 0.4)
	var random_rotation_range = config.get("random_rotation_range", 0.35)
	var large_wall_ratio = config.get("large_wall_ratio", 0.25)
	var overlap_wall_ratio = config.get("overlap_wall_ratio", 0.15)
	var height_variation = config.get("height_variation", 0.5)
	var tilt_range = config.get("tilt_range", 0.2)

	# 计算围墙位置 (优化数量)
	var wall_positions = _calculate_optimized_wall_positions(level_size, spacing, only_border, wall_density)

	# 验证封闭性
	_validate_wall_closure(wall_positions, level_size, spacing)

	# 生成围墙 (增强随机化)
	for i in range(wall_positions.size()):
		var pos_data = wall_positions[i]
		var wall = wall_scene.instantiate()

		# 设置墙的名称为"wall@编号"
		wall.name = "wall@" + str(i)

		# 确定wall类型
		var wall_type = _determine_wall_type(i, wall_positions.size(), large_wall_ratio, overlap_wall_ratio)

		# 应用wall类型特定的变换
		_apply_wall_transform(wall, pos_data, wall_type, {
			"wall_height": wall_height,
			"random_scale_range": random_scale_range,
			"random_rotation_range": random_rotation_range,
			"height_variation": height_variation,
			"tilt_range": tilt_range
		})

		# 添加到组
		wall.add_to_group("walls")

		# 添加到navigation区域
		nav_region.add_child(wall)
		if level_generator.edited_scene_root:
			wall.owner = level_generator.edited_scene_root

		# 保存生成的对象
		generated_objects.append(wall)

func _calculate_optimized_wall_positions(level_size: Vector2, spacing: float, only_border: bool, density: float) -> Array:
	var positions = []
	var half_size = level_size / 2.0

	if only_border:
		# 只在边界生成围墙，优化数量但确保完全闭合
		var all_positions = []

		# 下边界 (从左到右)
		var x = -half_size.x
		while x <= half_size.x:
			all_positions.append({
				"position": Vector2(x, -half_size.y),
				"rotation": 0.0,  # 水平方向
				"type": "horizontal"
			})
			x += spacing

		# 上边界 (从左到右)
		x = -half_size.x
		while x <= half_size.x:
			all_positions.append({
				"position": Vector2(x, half_size.y),
				"rotation": 0.0,  # 水平方向
				"type": "horizontal"
			})
			x += spacing

		# 左边界 (从下到上，排除角落避免重复)
		var z = -half_size.y + spacing
		while z < half_size.y:
			all_positions.append({
				"position": Vector2(-half_size.x, z),
				"rotation": PI / 2.0,  # 垂直方向
				"type": "vertical"
			})
			z += spacing

		# 右边界 (从下到上，排除角落避免重复)
		z = -half_size.y + spacing
		while z < half_size.y:
			all_positions.append({
				"position": Vector2(half_size.x, z),
				"rotation": PI / 2.0,  # 垂直方向
				"type": "vertical"
			})
			z += spacing

		# 应用密度控制 - 智能跳过部分位置
		positions = _apply_density_filter(all_positions, density)
	else:
		# 生成网格状的墙
		push_error("WallGeneratorModule: 网格状生成模式尚未实现")

	return positions

# 智能分布算法，确保均匀分布和完全闭合
func _apply_density_filter(all_positions: Array, density: float) -> Array:
	# 按边界类型分组
	var borders = _group_positions_by_border(all_positions)
	var filtered_positions = []

	# 对每条边应用智能分段选择
	for border_type in borders.keys():
		var border_positions = borders[border_type]
		var selected = _apply_smart_segment_selection(border_positions, density)
		filtered_positions.append_array(selected)

	return filtered_positions

# 按边界类型分组位置
func _group_positions_by_border(all_positions: Array) -> Dictionary:
	var borders = {
		"bottom": [],  # 下边界
		"top": [],     # 上边界
		"left": [],    # 左边界
		"right": []    # 右边界
	}

	for pos in all_positions:
		var position = pos.position
		var type = pos.type

		if type == "horizontal":
			if position.y < 0:
				borders["bottom"].append(pos)
			else:
				borders["top"].append(pos)
		else:  # vertical
			if position.x < 0:
				borders["left"].append(pos)
			else:
				borders["right"].append(pos)

	return borders

# 智能分段选择算法
func _apply_smart_segment_selection(border_positions: Array, density: float) -> Array:
	if border_positions.is_empty():
		return []

	# 按位置排序
	border_positions.sort_custom(_compare_positions)

	var selected_positions = []
	var total_positions = border_positions.size()
	var target_count = max(1, int(total_positions * density))

	# 确保最小数量以保证封闭性
	var min_count_per_border = 8  # 每边至少8个wall
	target_count = max(target_count, min_count_per_border)
	target_count = min(target_count, total_positions)  # 不超过总数

	if target_count >= total_positions:
		# 如果目标数量大于等于总数，全部保留
		return border_positions

	# 分段选择：将边界分成若干段，每段选择最优位置
	var segment_count = target_count
	var positions_per_segment = float(total_positions) / segment_count

	for i in range(segment_count):
		var segment_start = int(i * positions_per_segment)
		var segment_end = int((i + 1) * positions_per_segment)
		segment_end = min(segment_end, total_positions)

		# 在段内选择中心位置（最均匀分布）
		var segment_center = (segment_start + segment_end) / 2
		var selected_index = int(segment_center)
		selected_index = clamp(selected_index, segment_start, segment_end - 1)

		if selected_index < border_positions.size():
			selected_positions.append(border_positions[selected_index])

	return selected_positions

# 位置比较函数，用于排序
func _compare_positions(a: Dictionary, b: Dictionary) -> bool:
	var pos_a = a.position
	var pos_b = b.position
	var type_a = a.type

	if type_a == "horizontal":
		# 水平边界按X坐标排序
		return pos_a.x < pos_b.x
	else:
		# 垂直边界按Y坐标排序
		return pos_a.y < pos_b.y

# 确定wall类型
func _determine_wall_type(index: int, total_count: int, large_ratio: float, overlap_ratio: float) -> String:
	var rand_val = randf()

	# 15% 搭接wall
	if rand_val < overlap_ratio:
		return "overlap"
	# 25% 大型wall
	elif rand_val < overlap_ratio + large_ratio:
		return "large"
	# 60% 普通wall
	else:
		return "normal"

# 应用wall变换 (位置、缩放、旋转等)
func _apply_wall_transform(wall: Node3D, pos_data: Dictionary, wall_type: String, params: Dictionary) -> void:
	var wall_height = params.wall_height
	var scale_range = params.random_scale_range
	var rotation_range = params.random_rotation_range
	var height_var = params.height_variation
	var tilt_range = params.tilt_range

	# 基础位置
	var base_y = 0.0
	var base_position = Vector3(pos_data.position.x, base_y, pos_data.position.y)

	# 根据wall类型应用不同的变换
	match wall_type:
		"normal":
			# 普通wall: 标准随机化
			var scale_x = 1.0 + randf_range(-scale_range * 0.5, scale_range * 0.5)  # ±0.2
			var scale_z = 1.0 + randf_range(-scale_range * 0.5, scale_range * 0.5)
			wall.scale = Vector3(scale_x, wall_height, scale_z)
			wall.position = base_position

		"large":
			# 大型wall: 更大的缩放
			var scale_x = 1.2 + randf_range(0, scale_range * 0.5)  # 1.2-1.4
			var scale_z = 1.2 + randf_range(0, scale_range * 0.5)
			wall.scale = Vector3(scale_x, wall_height, scale_z)
			wall.position = base_position

		"overlap":
			# 搭接wall: 一端抬高，形成搭接效果
			var scale_x = 1.0 + randf_range(-scale_range * 0.3, scale_range * 0.3)  # ±0.12
			var scale_z = 1.0 + randf_range(-scale_range * 0.3, scale_range * 0.3)
			wall.scale = Vector3(scale_x, wall_height, scale_z)

			# 抬高一端 (Y轴位置偏移)
			var height_offset = randf_range(0.3, height_var)
			wall.position = Vector3(base_position.x, base_y + height_offset, base_position.z)

			# 添加倾斜效果 (X轴旋转)
			wall.rotation.x = randf_range(-tilt_range * 0.5, tilt_range * 0.5)

	# 所有类型都应用基础旋转和随机旋转
	var base_rotation = pos_data.rotation
	var random_rotation = randf_range(-rotation_range, rotation_range)
	wall.rotation.y = base_rotation + random_rotation

# 验证围墙封闭性
func _validate_wall_closure(wall_positions: Array, level_size: Vector2, spacing: float) -> void:
	var borders = _group_positions_by_border(wall_positions)
	var half_size = level_size / 2.0
	var max_gap = spacing * 1.5  # 允许的最大间隙

	print("[WallGenerator] 围墙分布验证:")
	print("  - 下边界: ", borders["bottom"].size(), " 个wall")
	print("  - 上边界: ", borders["top"].size(), " 个wall")
	print("  - 左边界: ", borders["left"].size(), " 个wall")
	print("  - 右边界: ", borders["right"].size(), " 个wall")
	print("  - 总计: ", wall_positions.size(), " 个wall")

	# 检查每边的覆盖情况
	_check_border_coverage("下边界", borders["bottom"], -half_size.x, half_size.x, max_gap, true)
	_check_border_coverage("上边界", borders["top"], -half_size.x, half_size.x, max_gap, true)
	_check_border_coverage("左边界", borders["left"], -half_size.y, half_size.y, max_gap, false)
	_check_border_coverage("右边界", borders["right"], -half_size.y, half_size.y, max_gap, false)

# 检查单边的覆盖情况
func _check_border_coverage(border_name: String, positions: Array, start: float, end: float, max_gap: float, is_horizontal: bool) -> void:
	if positions.is_empty():
		push_warning("[WallGenerator] " + border_name + " 没有wall，可能存在逃逸路径！")
		return

	# 按坐标排序
	var sorted_positions = positions.duplicate()
	if is_horizontal:
		sorted_positions.sort_custom(func(a, b): return a.position.x < b.position.x)
	else:
		sorted_positions.sort_custom(func(a, b): return a.position.y < b.position.y)

	# 检查间隙
	var coord_key = "x" if is_horizontal else "y"
	var prev_coord = start
	var max_gap_found = 0.0

	for pos in sorted_positions:
		var current_coord = pos.position.x if is_horizontal else pos.position.y
		var gap = current_coord - prev_coord
		max_gap_found = max(max_gap_found, gap)

		if gap > max_gap:
			push_warning("[WallGenerator] " + border_name + " 在坐标 " + str(prev_coord) + " 到 " + str(current_coord) + " 之间存在过大间隙: " + str(gap))

		prev_coord = current_coord

	# 检查最后一段到边界端点的距离
	var last_coord = sorted_positions[-1].position.x if is_horizontal else sorted_positions[-1].position.y
	var final_gap = end - last_coord
	max_gap_found = max(max_gap_found, final_gap)

	if final_gap > max_gap:
		push_warning("[WallGenerator] " + border_name + " 末端存在过大间隙: " + str(final_gap))

	print("  - " + border_name + " 最大间隙: ", max_gap_found, " (允许: ", max_gap, ")")

func validate_config() -> bool:
	# 如果存在覆盖参数，使用它
	var _override_validation = config.get("_override_validation", false)
	if _override_validation:
		return true

	# 检查场景路径
	if not config.has("wall_scene") or not config.get("wall_scene") is String:
		push_error("WallGeneratorModule: 缺少 wall_scene 或类型不是 String")
		return false

	var wall_path = config.get("wall_scene", "")
	if not ResourceLoader.exists(wall_path):
		push_error("WallGeneratorModule: wall_scene 路径不存在: " + wall_path)
		return false

	# 检查间距
	if not config.has("spacing") or not config.get("spacing") is float:
		push_error("WallGeneratorModule: 缺少 spacing 或类型不是 float")
		return false

	# 检查边界设置
	if not config.has("only_border") or not config.get("only_border") is bool:
		push_error("WallGeneratorModule: 缺少 only_border 或类型不是 bool")
		return false

	# 检查墙高
	if not config.has("wall_height") or not config.get("wall_height") is float:
		push_error("WallGeneratorModule: 缺少 wall_height 或类型不是 float")
		return false

	# 检查随机缩放范围
	if config.has("random_scale_range") and not config.get("random_scale_range") is float:
		push_error("WallGeneratorModule: random_scale_range 类型不是 float")
		return false

	# 检查随机旋转范围
	if config.has("random_rotation_range") and not config.get("random_rotation_range") is float:
		push_error("WallGeneratorModule: random_rotation_range 类型不是 float")
		return false

	# 检查新增的参数
	if config.has("wall_density") and not config.get("wall_density") is float:
		push_error("WallGeneratorModule: wall_density 类型不是 float")
		return false

	if config.has("large_wall_ratio") and not config.get("large_wall_ratio") is float:
		push_error("WallGeneratorModule: large_wall_ratio 类型不是 float")
		return false

	if config.has("overlap_wall_ratio") and not config.get("overlap_wall_ratio") is float:
		push_error("WallGeneratorModule: overlap_wall_ratio 类型不是 float")
		return false

	if config.has("height_variation") and not config.get("height_variation") is float:
		push_error("WallGeneratorModule: height_variation 类型不是 float")
		return false

	if config.has("tilt_range") and not config.get("tilt_range") is float:
		push_error("WallGeneratorModule: tilt_range 类型不是 float")
		return false

	return true
