class_name WallGeneratorModule
extends GeneratorModule

func _init() -> void:
	module_id = "wall_generator"
	module_name = "围墙生成器"
	description = "在关卡边界生成围墙"

func get_default_config() -> Dictionary:
	return {
		"wall_scene": "res://Scenes/Prefabs/Wall.tscn",
		"spacing": 2.0,  # 墙之间的间距
		"only_border": true,  # 是否只在边界生成墙
		"wall_height": 0.83,  # 墙的高度（原高度的1/3）
		"random_scale_range": 0.2,  # 随机缩放范围 (±0.2)
		"random_rotation_range": 0.3  # 随机旋转范围 (±0.3弧度，约±17度)
	}

func generate(level_generator, nav_region) -> void:
	# 清除现有墙壁
	clear()

	# 加载墙壁场景
	var wall_scene_path = config.get("wall_scene", "res://Scenes/Prefabs/Wall.tscn")
	var wall_scene = load(wall_scene_path)
	if not wall_scene:
		push_error("WallGeneratorModule: 无法加载墙壁场景: " + wall_scene_path)
		return

	# 获取关卡参数
	var level_size = level_generator.level_size
	var spacing = config.get("spacing", 2.0)
	var wall_height = config.get("wall_height", 0.83)
	var only_border = config.get("only_border", true)
	var random_scale_range = config.get("random_scale_range", 0.2)
	var random_rotation_range = config.get("random_rotation_range", 0.3)

	# 计算围墙位置
	var wall_positions = _calculate_wall_positions(level_size, spacing, only_border)

	# 生成围墙
	for i in range(wall_positions.size()):
		var pos_data = wall_positions[i]
		var wall = wall_scene.instantiate()

		# 设置墙的名称为"wall@编号"
		wall.name = "wall@" + str(i)

		# 设置位置
		wall.position = Vector3(pos_data.position.x, 0, pos_data.position.y)

		# 随机缩放 (在0.8-1.2范围内)
		var random_scale_x = 1.0 + randf_range(-random_scale_range, random_scale_range)
		var random_scale_z = 1.0 + randf_range(-random_scale_range, random_scale_range)
		wall.scale = Vector3(random_scale_x, wall_height, random_scale_z)

		# 设置旋转 (基础旋转 + 随机旋转)
		var base_rotation = pos_data.rotation
		var random_rotation = randf_range(-random_rotation_range, random_rotation_range)
		wall.rotation.y = base_rotation + random_rotation

		# 添加到组
		wall.add_to_group("walls")

		# 添加到navigation区域
		nav_region.add_child(wall)
		if level_generator.edited_scene_root:
			wall.owner = level_generator.edited_scene_root

		# 保存生成的对象
		generated_objects.append(wall)

func _calculate_wall_positions(level_size: Vector2, spacing: float, only_border: bool) -> Array:
	var positions = []
	var half_size = level_size / 2.0

	if only_border:
		# 只在边界生成围墙，确保完全闭合

		# 下边界 (从左到右)
		var x = -half_size.x
		while x <= half_size.x:
			positions.append({
				"position": Vector2(x, -half_size.y),
				"rotation": 0.0,  # 水平方向
				"type": "horizontal"
			})
			x += spacing

		# 上边界 (从左到右)
		x = -half_size.x
		while x <= half_size.x:
			positions.append({
				"position": Vector2(x, half_size.y),
				"rotation": 0.0,  # 水平方向
				"type": "horizontal"
			})
			x += spacing

		# 左边界 (从下到上，排除角落避免重复)
		var z = -half_size.y + spacing
		while z < half_size.y:
			positions.append({
				"position": Vector2(-half_size.x, z),
				"rotation": PI / 2.0,  # 垂直方向
				"type": "vertical"
			})
			z += spacing

		# 右边界 (从下到上，排除角落避免重复)
		z = -half_size.y + spacing
		while z < half_size.y:
			positions.append({
				"position": Vector2(half_size.x, z),
				"rotation": PI / 2.0,  # 垂直方向
				"type": "vertical"
			})
			z += spacing
	else:
		# 生成网格状的墙
		push_error("WallGeneratorModule: 网格状生成模式尚未实现")

	return positions

func validate_config() -> bool:
	# 如果存在覆盖参数，使用它
	var _override_validation = config.get("_override_validation", false)
	if _override_validation:
		return true

	# 检查场景路径
	if not config.has("wall_scene") or not config.get("wall_scene") is String:
		push_error("WallGeneratorModule: 缺少 wall_scene 或类型不是 String")
		return false

	var wall_path = config.get("wall_scene", "")
	if not ResourceLoader.exists(wall_path):
		push_error("WallGeneratorModule: wall_scene 路径不存在: " + wall_path)
		return false

	# 检查间距
	if not config.has("spacing") or not config.get("spacing") is float:
		push_error("WallGeneratorModule: 缺少 spacing 或类型不是 float")
		return false

	# 检查边界设置
	if not config.has("only_border") or not config.get("only_border") is bool:
		push_error("WallGeneratorModule: 缺少 only_border 或类型不是 bool")
		return false

	# 检查墙高
	if not config.has("wall_height") or not config.get("wall_height") is float:
		push_error("WallGeneratorModule: 缺少 wall_height 或类型不是 float")
		return false

	# 检查随机缩放范围
	if config.has("random_scale_range") and not config.get("random_scale_range") is float:
		push_error("WallGeneratorModule: random_scale_range 类型不是 float")
		return false

	# 检查随机旋转范围
	if config.has("random_rotation_range") and not config.get("random_rotation_range") is float:
		push_error("WallGeneratorModule: random_rotation_range 类型不是 float")
		return false

	return true
