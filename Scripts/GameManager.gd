extends Node

# 游戏状态相关的信号
signal game_over
signal game_win
# 以下信号暂未使用，已注释掉以避免警告
# signal game_paused
# signal game_resumed

# UI管理
var game_over_ui: CanvasLayer
var game_win_ui: CanvasLayer

func _ready() -> void:
	await get_tree().process_frame
	initialize()

func initialize() -> void:
	# 先尝试查找已存在的GameOverUI
	game_over_ui = get_tree().root.get_node_or_null("GameOverUI")

	# 如果找不到GameOverUI，则创建一个并添加到根节点
	if not game_over_ui:
		var game_over_ui_path = "res://Scenes/UI/GameOverUI.tscn"
		if ResourceLoader.exists(game_over_ui_path):
			var game_over_ui_scene = load(game_over_ui_path)
			game_over_ui = game_over_ui_scene.instantiate()
			game_over_ui.name = "GameOverUI"

			# 设置属性
			if game_over_ui.has_method("set_game_duration"):
				game_over_ui.set_game_duration(0)  # 设置为0表示不使用倒计时

			# 将GameOverUI添加到根节点
			get_tree().root.add_child(game_over_ui)
			print("GameManager: 已创建并添加GameOverUI到根节点")
		else:
			push_error("GameManager: 找不到GameOverUI场景: " + game_over_ui_path)

	# 查找GameWinUI
	game_win_ui = get_tree().root.get_node_or_null("GameWinUI")

	# 如果找不到GameWinUI，可以采用类似的方法创建它
	if not game_win_ui:
		var game_win_ui_path = "res://Scenes/UI/GameWinUI.tscn"
		if ResourceLoader.exists(game_win_ui_path):
			var game_win_ui_scene = load(game_win_ui_path)
			game_win_ui = game_win_ui_scene.instantiate()
			game_win_ui.name = "GameWinUI"
			get_tree().root.add_child(game_win_ui)
			print("GameManager: 已创建并添加GameWinUI到根节点")
		else:
			push_error("GameManager: 找不到GameWinUI场景: " + game_win_ui_path)

# 游戏状态管理方法
func show_game_over(force_pause: bool = false) -> void:
	print("GameManager: 尝试显示游戏结束界面")

	# 如果GameOverUI不存在，尝试重新初始化
	if not game_over_ui:
		print("GameManager: GameOverUI不存在，尝试重新初始化")
		initialize()

	if game_over_ui:
		print("GameManager: 找到GameOverUI，调用show_game_over方法")
		game_over_ui.show_game_over(force_pause)
		emit_signal("game_over")
	else:
		push_error("GameManager: 无法显示游戏结束界面，GameOverUI不存在")

func show_game_win() -> void:
	if game_win_ui:
		game_win_ui.show_game_win()
		emit_signal("game_win")

# 效果状态管理
var _status_effects = {}

func add_status_effect(entity: Node, effect_type: String, data: Dictionary = {}) -> void:
	if not _status_effects.has(entity):
		_status_effects[entity] = {}
	_status_effects[entity][effect_type] = data

func remove_status_effect(entity: Node, effect_type: String) -> void:
	if _status_effects.has(entity):
		_status_effects[entity].erase(effect_type)
		if _status_effects[entity].is_empty():
			_status_effects.erase(entity)

func has_status_effect(entity: Node, effect_type: String) -> bool:
	return _status_effects.has(entity) and _status_effects[entity].has(effect_type)

func get_status_effect_data(entity: Node, effect_type: String) -> Dictionary:
	if has_status_effect(entity, effect_type):
		return _status_effects[entity][effect_type]
	return {}
