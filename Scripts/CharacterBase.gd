extends CharacterBody3D

# 基础属性
var move_speed: float = 5.0
var jump_impulse: float = 6.0
var gravity: float = -15.0

# 健康系统
signal health_changed(new_health: int, max_health: int)
var health: int = 1
var max_health: int = 1
var original_move_speed: float = 5.0  # 保存原始移动速度
var speed_boost_active: bool = false
var speed_boost_timer: float = 0.0
var speed_boost_duration: float = 5.0  # 速度提升持续5秒
var speed_boost_multiplier: float = 1.3  # 速度提升30%

# 无敌期系统
var invincible: bool = false  # 是否处于无敌状态
var invincible_timer: float = 0.0  # 无敌时间计时器
var invincible_duration: float = 5.0  # 无敌持续5秒

# Camera相关代码已移除，Camera现在在Level01中管理



# 动画相关
@onready var _animation_player = _find_model_animation_player()
@onready var _dust_particles = get_node_or_null("DustParticles")
@onready var _landing_sound = $LandingSound

# 输入相关
var _last_input_direction: Vector2 = Vector2.ZERO
var _skill_input_processed_this_frame: bool = false

# 交互相关
var _current_tree: Node = null

# 装备相关
var equipped_items: Array = []
var equipped_skill_1 = null
var equipped_skill_2 = null

# 钩子方法 - 子类可以覆盖这些方法来改变行为
func _on_enemy_capture() -> void:
	# 默认实现（Player的行为）
	decrease_health(1)
	if health <= 0:
		show_game_over_ui()

func _on_initialize_camera() -> void:
	# Camera初始化已移到Level01，此函数保留作为兼容性钩子
	pass

# 健康系统方法
func initialize_health() -> void:
	# 基类默认实现，子类可以覆盖
	health = 1
	max_health = 1
	emit_signal("health_changed", health, max_health)

# 增加血量
func increase_health(amount: int) -> void:
	health = min(health + amount, max_health)
	emit_signal("health_changed", health, max_health)

# 减少血量
func decrease_health(amount: int) -> void:
	# 如果处于无敌状态，不减少血量
	if invincible:
		return

	health = max(health - amount, 0)
	emit_signal("health_changed", health, max_health)

	# 如果血量大于0，激活速度提升效果和无敌期
	if health > 0:
		activate_speed_boost()
		activate_invincibility()

# 激活速度提升效果
func activate_speed_boost() -> void:
	# 保存原始速度（如果尚未保存）
	if not speed_boost_active:
		original_move_speed = move_speed

	# 设置提升后的速度
	move_speed = original_move_speed * speed_boost_multiplier

	# 重置计时器并激活效果
	speed_boost_timer = 0.0
	speed_boost_active = true

# 激活无敌期
func activate_invincibility() -> void:
	# 重置计时器并激活无敌效果
	invincible_timer = 0.0
	invincible = true

	# 添加视觉反馈，让玩家知道处于无敌状态
	# 尝试获取玩家模型
	var player_model = null

	# 首先尝试直接获取Player01节点
	player_model = get_node_or_null("Player01")

	# 如果没有找到，尝试在子节点中查找
	if not player_model:
		for child in get_children():
			if child.name == "Player01" or child.name.begins_with("Player"):
				player_model = child
				break

	# 如果找到了玩家模型，创建闪烁效果
	if player_model:
		# 创建闪烁效果 - 使用visibility属性
		var tween = create_tween()
		tween.set_loops(10)  # 闪烁5次（每次需要两个值变化）
		tween.tween_property(player_model, "visible", false, 0.25)  # 0.25秒隐藏
		tween.tween_property(player_model, "visible", true, 0.25)   # 0.25秒显示

# 检查是否处于无敌状态
func is_invincible() -> bool:
	return invincible



func _ready():
	# 添加到玩家组
	add_to_group("player")

	# 确保equipped_items为空，防止编辑器中设置的默认道具被加载
	if equipped_items.size() > 0:
		equipped_items.clear()

	# 设置输入映射
	setup_input_actions()

	# 确保鼠标始终可见（固定Camera不需要捕获鼠标）
	Input.set_mouse_mode(Input.MOUSE_MODE_VISIBLE)

	# 调用钩子方法，允许子类自定义相机初始化（现在为空实现）
	_on_initialize_camera()

	# 初始化各个系统
	initialize_interaction()
	initialize_skills()
	initialize_items()

	# 初始化健康系统
	initialize_health()

	# 确保玩家初始化时在地面上
	position.y = 0
	velocity.y = 0

	# 保存原始移动速度
	original_move_speed = move_speed

	# 打印玩家速度配置
	print("玩家初始速度配置: ", move_speed)

# 设置输入映射
func setup_input_actions() -> void:
	# 检查是否已经存在这些输入映射
	if not InputMap.has_action("move_left"):
		# 移动 - WASD
		var move_left = InputEventKey.new()
		move_left.keycode = KEY_A
		InputMap.add_action("move_left")
		InputMap.action_add_event("move_left", move_left)

		var move_right = InputEventKey.new()
		move_right.keycode = KEY_D
		InputMap.add_action("move_right")
		InputMap.action_add_event("move_right", move_right)

		var move_up = InputEventKey.new()
		move_up.keycode = KEY_W
		InputMap.add_action("move_up")
		InputMap.action_add_event("move_up", move_up)

		var move_down = InputEventKey.new()
		move_down.keycode = KEY_S
		InputMap.add_action("move_down")
		InputMap.action_add_event("move_down", move_down)

		# 跳跃 - 空格
		var jump = InputEventKey.new()
		jump.keycode = KEY_SPACE
		InputMap.add_action("jump")
		InputMap.action_add_event("jump", jump)

		# 交互 - E
		var interact = InputEventKey.new()
		interact.keycode = KEY_E
		InputMap.add_action("interact")
		InputMap.action_add_event("interact", interact)

		# 技能 - E和F
		var skill_1 = InputEventKey.new()
		skill_1.keycode = KEY_E
		InputMap.add_action("skill_1")
		InputMap.action_add_event("skill_1", skill_1)

		var skill_2 = InputEventKey.new()
		skill_2.keycode = KEY_F
		InputMap.add_action("skill_2")
		InputMap.action_add_event("skill_2", skill_2)

# 处理输入
func _input(event: InputEvent) -> void:
	# 重置每帧技能输入处理标志
	_skill_input_processed_this_frame = false

	# Camera旋转处理已移到CameraController

	# 注释掉这部分，避免重复调用process_skill_inputs
	# 现在在_process中调用process_skill_inputs，不需要在这里调用
	# if event is InputEventKey:
	#     process_skill_inputs()

func _physics_process(delta):
	# 处理移动
	var input_dir = Input.get_vector("move_left", "move_right", "move_up", "move_down", 0.4)
	# 只在有输入时更新_last_input_direction，保持最后的有效方向
	if input_dir.length() > 0.1:
		_last_input_direction = input_dir

	# 获取相机方向
	var camera = get_viewport().get_camera_3d()
	var forward = camera.global_basis.z if camera else Vector3.FORWARD
	var right = camera.global_basis.x if camera else Vector3.RIGHT

	# 确保移动方向在水平面上
	forward.y = 0
	forward = forward.normalized()
	right.y = 0
	right = right.normalized()

	# 计算移动方向
	var direction = forward * input_dir.y + right * input_dir.x

	if direction:
		# 设置水平速度
		velocity.x = direction.x * move_speed
		velocity.z = direction.z * move_speed

		# 播放跑步动画
		if is_on_floor() and _animation_player and _animation_player.has_animation("run"):
			if not _animation_player.is_playing() or _animation_player.current_animation != "run":
				_animation_player.play("run")

		# 播放粒子效果
		if is_on_floor() and _dust_particles:
			_dust_particles.emitting = true
	else:
		# 停止移动
		velocity.x = 0
		velocity.z = 0

		# 播放待机动画
		if is_on_floor() and _animation_player and _animation_player.has_animation("idle"):
			if not _animation_player.is_playing() or _animation_player.current_animation != "idle":
				_animation_player.play("idle")

		# 停止粒子效果
		if _dust_particles:
			_dust_particles.emitting = false

	# 应用重力
	velocity.y += gravity * delta

	# 处理跳跃
	if is_on_floor() and Input.is_action_just_pressed("jump"):
		velocity.y = jump_impulse

		# 播放跳跃动画
		if _animation_player and _animation_player.has_animation("jump"):
			_animation_player.play("jump")

	# 检测落地 - 已在Player.gd中重写，这里禁用避免重复播放
	if is_on_floor() and velocity.y < 0:
		# 播放落地音效
		if _landing_sound:
			# _landing_sound.play()  # 注释掉，避免与Player.gd中的逻辑冲突
			pass

	# 更新速度提升效果
	if speed_boost_active:
		speed_boost_timer += delta
		if speed_boost_timer >= speed_boost_duration:
			# 速度提升效果结束
			speed_boost_active = false
			move_speed = original_move_speed

	# 更新无敌期
	if invincible:
		invincible_timer += delta
		if invincible_timer >= invincible_duration:
			# 无敌期结束
			invincible = false

	# Camera位置更新已移到CameraController

	# 更新技能冷却时间
	update_skills(delta)

	# 应用移动
	move_and_slide()

	# 打印实时速度（每秒只打印一次）
	if Engine.get_frames_drawn() % int(Engine.get_frames_per_second()) == 0:
		var _current_speed = Vector2(velocity.x, velocity.z).length()
		var raw_velocity = Vector2(velocity.x, velocity.z)
		var _move_dir = raw_velocity.normalized() if raw_velocity.length() > 0.01 else Vector2.ZERO


		# 如果是玩家，检查输入
		if is_in_group("player"):
			var _current_input = Input.get_vector("move_left", "move_right", "move_up", "move_down", 0.4)


	# 处理树木交互
	handle_tree_interaction()



# 初始化交互系统
func initialize_interaction() -> void:
	# 获取PlayerInteractor节点并连接信号（现在直接在Player下）
	var interactor = $PlayerInteractor
	if interactor:
		# 连接信号用于处理交互
		if not interactor.is_connected("interactor_area_entered", _on_interactor_area_entered):
			interactor.connect("interactor_area_entered", _on_interactor_area_entered)
		if not interactor.is_connected("interactor_area_exited", _on_interactor_area_exited):
			interactor.connect("interactor_area_exited", _on_interactor_area_exited)

# 初始化技能系统
func initialize_skills() -> void:
	# 这里可以添加技能初始化逻辑
	pass

# 更新技能冷却时间
func update_skills(delta: float) -> void:
	# 更新技能1的冷却时间
	if equipped_skill_1:
		equipped_skill_1.update(delta)

	# 更新技能2的冷却时间
	if equipped_skill_2:
		equipped_skill_2.update(delta)

# 初始化物品系统
func initialize_items() -> void:
	# 这里可以添加物品初始化逻辑
	pass

# 处理技能输入
func process_skill_inputs() -> void:
	# 基类中的process_skill_inputs不做任何处理
	# 由子类Player.gd中的process_skill_inputs处理技能输入
	pass

# 处理树木交互
func handle_tree_interaction() -> void:
	# 如果没有当前树，直接返回
	if _current_tree == null:
		return

	# 检查是否按下了点火键（F键）
	if Input.is_action_just_pressed("skill_2"):
		# 开始点火
		if _current_tree.has_method("start_igniting"):
			_current_tree.start_igniting()

	# 检查是否释放了点火键
	if Input.is_action_just_released("skill_2"):
		# 停止点火
		if _current_tree.has_method("stop_igniting"):
			_current_tree.stop_igniting()

# 处理交互器进入区域
func _on_interactor_area_entered(area: Area3D) -> void:
	# 检查是否是树木
	var parent = area.get_parent()
	if not parent or not parent.is_in_group("trees"):
		return

	# 设置当前树
	_current_tree = parent

	# 添加高亮
	if _current_tree.has_method("add_highlight"):
		_current_tree.add_highlight()

# 处理交互器离开区域
func _on_interactor_area_exited(area: Area3D) -> void:
	# 检查是否是当前树
	var parent = area.get_parent()
	if not parent or not _current_tree or parent != _current_tree:
		return

	# 移除高亮
	if _current_tree.has_method("remove_highlight"):
		_current_tree.remove_highlight()

	# 停止点火
	if _current_tree.has_method("stop_igniting"):
		_current_tree.stop_igniting()

	# 清除当前树引用
	_current_tree = null

# 查找模型动画播放器
func _find_model_animation_player() -> AnimationPlayer:
	# 首先检查直接子节点
	for child in get_children():
		if child is AnimationPlayer:
			return child

	# 然后检查模型子节点
	var model = get_node_or_null("Player01")
	if model:
		for child in model.get_children():
			if child is AnimationPlayer:
				return child

	# 最后递归检查所有子节点
	return _find_animation_player_recursive(self)

# 递归查找动画播放器
func _find_animation_player_recursive(node: Node) -> AnimationPlayer:
	for child in node.get_children():
		if child is AnimationPlayer:
			return child

		var result = _find_animation_player_recursive(child)
		if result:
			return result

	return null

# 显示游戏结束界面
func show_game_over_ui() -> void:
	# 首先尝试在主场景中查找GameOverUI
	var main = get_tree().get_root().get_node_or_null("Main")
	if main and main.has_node("GameOverUI"):
		var game_over_ui_main = main.get_node("GameOverUI")
		# 抓到玩家时强制暂停游戏，无论game_duration设置如何
		game_over_ui_main.show_game_over(true)
		return

	# 如果在Main中找不到，则尝试在根节点查找
	var game_over_ui_root = get_tree().get_root().get_node_or_null("GameOverUI")
	if game_over_ui_root:
		game_over_ui_root.show_game_over(true)
		return

	# 如果仍找不到，尝试在当前关卡或父节点中查找
	var current_level = get_tree().current_scene
	if current_level:
		# 查找当前关卡的GameOverUI
		var game_over_ui_level = current_level.get_node_or_null("GameOverUI")
		if game_over_ui_level:
			game_over_ui_level.show_game_over(true)
			return

		# 尝试在父节点中查找
		var parent = current_level.get_parent()
		if parent and parent.has_node("GameOverUI"):
			var game_over_ui_parent = parent.get_node("GameOverUI")
			game_over_ui_parent.show_game_over(true)
			return

	# 如果都找不到，尝试实例化一个新的
	var game_over_ui_path = "res://Scenes/UI/game_over_ui.tscn"
	if ResourceLoader.exists(game_over_ui_path):
		var game_over_ui_scene = load(game_over_ui_path)
		var game_over_ui_instance = game_over_ui_scene.instantiate()
		get_tree().root.add_child(game_over_ui_instance)
		game_over_ui_instance.show_game_over(true)
		return
