extends ItemPickup
class_name GasItemPickup

# 汽油特有的视觉效果配置
@export var gas_color: Color = Color(0.8, 0.1, 0.0, 0.8)  # 汽油颜色（红色）
@export var enable_particles: bool = true  # 是否启用粒子效果
@export var particle_emission_rate: float = 10.0  # 粒子发射率

func _ready() -> void:
	# 调用父类的初始化
	super._ready()
	
	# 确保有正确的道具资源
	if not item_resource or not item_resource is GasItem:
		item_resource = load("res://Resources/GasItem.tres")
		
	# 创建汽油特有的视觉效果
	_create_gas_visuals()

# 创建汽油特有的视觉效果
func _create_gas_visuals() -> void:
	# 如果没有现有的网格实例，创建一个
	var mesh_instance = get_node_or_null("MeshInstance3D")
	if not mesh_instance:
		mesh_instance = MeshInstance3D.new()
		mesh_instance.name = "MeshInstance3D"
		add_child(mesh_instance)
		
		# 创建汽油罐网格
		var cylinder = CylinderMesh.new()
		cylinder.height = 0.4
		cylinder.top_radius = 0.15
		cylinder.bottom_radius = 0.15
		mesh_instance.mesh = cylinder
	
	# 创建汽油材质
	var gas_material = StandardMaterial3D.new()
	gas_material.albedo_color = gas_color
	gas_material.metallic = 0.7
	gas_material.roughness = 0.2
	
	# 应用到网格
	mesh_instance.set_surface_override_material(0, gas_material)
	
	# 添加粒子效果（如果启用）
	if enable_particles:
		_add_gas_particles()

# 添加汽油粒子效果
func _add_gas_particles() -> void:
	# 创建粒子系统
	var particles = GPUParticles3D.new()
	particles.name = "GasParticles"
	add_child(particles)
	
	# 设置粒子发射位置
	particles.position = Vector3(0, 0.25, 0)
	
	# 设置粒子属性
	particles.amount = 16
	particles.lifetime = 1.5
	particles.explosiveness = 0.0
	particles.randomness = 0.5
	particles.emitting = true
	
	# 创建粒子材质
	var particle_material = ParticleProcessMaterial.new()
	particle_material.emission_shape = ParticleProcessMaterial.EMISSION_SHAPE_SPHERE
	particle_material.emission_sphere_radius = 0.05
	particle_material.direction = Vector3(0, 1, 0)
	particle_material.spread = 15.0
	particle_material.gravity = Vector3(0, -0.5, 0)
	particle_material.initial_velocity_min = 0.2
	particle_material.initial_velocity_max = 0.5
	particle_material.scale_min = 0.05
	particle_material.scale_max = 0.1
	particle_material.color = gas_color
	
	# 应用材质到粒子系统
	particles.process_material = particle_material
	
	# 创建粒子网格
	var particle_mesh = SphereMesh.new()
	particle_mesh.radius = 0.05
	particle_mesh.height = 0.1
	particles.draw_pass_1 = particle_mesh 