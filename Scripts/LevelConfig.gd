class_name LevelConfig
extends Resource

@export var level_id: String = "level_01"
@export var level_name: String = "第一关"
@export var level_size: Vector2 = Vector2(100, 100)
@export var cell_size: float = 2.5
@export var wall_height: float = 2.0

# 启用的生成器模块
@export var enabled_modules: Array[String] = []

# 各个模块的配置参数
@export var module_configs: Dictionary = {}

# 获取一个模块的配置
func get_module_config(module_id: String) -> Dictionary:
	if module_configs.has(module_id):
		return module_configs[module_id]
	return {}

# 设置一个模块的配置
func set_module_config(module_id: String, config: Dictionary) -> void:
	module_configs[module_id] = config.duplicate()

# 启用一个模块
func enable_module(module_id: String) -> void:
	if not module_id in enabled_modules:
		enabled_modules.append(module_id)

# 禁用一个模块
func disable_module(module_id: String) -> void:
	if module_id in enabled_modules:
		enabled_modules.erase(module_id) 
