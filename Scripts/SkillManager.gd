extends Node

# 所有可用技能的字典
var all_skills = {
	"blink": preload("res://Resources/Skills/BlinkSkill.tres"),
	"disguise": preload("res://Resources/Skills/DisguiseSkill.tres"),
	"fire": preload("res://Resources/Skills/FireSkill.tres"),
	"flame_waltz": preload("res://Resources/Skills/FlameWaltzSkill.tres"),
	"sacred_blaze": preload("res://Resources/Skills/SacredBlazeSkill.tres"),
	"ember_echo": preload("res://Resources/Skills/EmberEchoSkill.tres"),
	"sixth_sense": preload("res://Resources/Skills/SixthSenseSkill.tres"),
	"crimson_cage": preload("res://Resources/Skills/CrimsonCageSkill.tres"),
	"wall_of_sighs": preload("res://Resources/Skills/WallofSighsSkill.tres"),
	"burning_spirit": preload("res://Resources/Skills/BurningSpiritSkill.tres"),
	"flame_mirage": preload("res://Resources/Skills/FlameMirageSkill.tres"),
	"molten_veil": preload("res://Resources/Skills/MoltenVeilSkill.tres")
}

# 技能解锁状态
var unlocked_skills = {
	"blink": true,
	"disguise": true,
	"fire": true,
	"flame_waltz": true,
	"sacred_blaze": true,
	"ember_echo": true,
	"sixth_sense": true,
	"crimson_cage": true,
	"wall_of_sighs": true,
	"burning_spirit": true,
	"flame_mirage": true,
	"molten_veil": true
}

# 当前装备的技能
var equipped_skill_1_id: String = "blink"
var equipped_skill_2_id: String = "disguise"

# 获取技能资源
func get_skill_resource(skill_id: String) -> Resource:
	if all_skills.has(skill_id):
		return all_skills[skill_id]
	return null

# 检查技能是否已解锁
func is_skill_unlocked(skill_id: String) -> bool:
	if unlocked_skills.has(skill_id):
		return unlocked_skills[skill_id]
	return false

# 解锁技能
func unlock_skill(skill_id: String) -> void:
	if all_skills.has(skill_id):
		unlocked_skills[skill_id] = true

# 装备技能到槽位1
func equip_skill_1(skill_id: String) -> bool:
	if all_skills.has(skill_id) and unlocked_skills.has(skill_id) and unlocked_skills[skill_id]:
		equipped_skill_1_id = skill_id
		return true
	return false

# 装备技能到槽位2
func equip_skill_2(skill_id: String) -> bool:
	if all_skills.has(skill_id) and unlocked_skills.has(skill_id) and unlocked_skills[skill_id]:
		equipped_skill_2_id = skill_id
		return true
	return false

# 获取当前装备的技能1
func get_equipped_skill_1() -> Resource:
	return get_skill_resource(equipped_skill_1_id)

# 获取当前装备的技能2
func get_equipped_skill_2() -> Resource:
	return get_skill_resource(equipped_skill_2_id)

# 获取所有已解锁的技能ID列表
func get_unlocked_skill_ids() -> Array:
	var result = []
	for skill_id in unlocked_skills.keys():
		if unlocked_skills[skill_id]:
			result.append(skill_id)
	return result

# 获取技能名称
func get_skill_name(skill_id: String) -> String:
	if skill_id == "none" or skill_id.is_empty():
		return "无技能"

	var skill = get_skill_resource(skill_id)
	if skill:
		# 使用get方法安全地检查属性是否存在
		if skill.get("skill_name") != null:
			return skill.skill_name
	return "Unknown Skill"

# 获取技能描述
func get_skill_description(skill_id: String) -> String:
	if skill_id == "none" or skill_id.is_empty():
		return ""

	var skill = get_skill_resource(skill_id)
	if skill:
		# 使用get方法安全地检查属性是否存在
		if skill.get("skill_description") != null:
			return skill.skill_description
	return "No description available"

# 获取技能图标
func get_skill_icon(skill_id: String) -> Texture2D:
	if skill_id == "none" or skill_id.is_empty():
		return null

	var skill = get_skill_resource(skill_id)
	if skill:
		# 使用get方法安全地检查属性是否存在
		if skill.get("icon") != null:
			return skill.icon
	return null

# 获取当前装备的所有技能
func get_equipped_skills() -> Array:
	var result = []
	if equipped_skill_1_id != "none" and not equipped_skill_1_id.is_empty():
		result.append(equipped_skill_1_id)
	if equipped_skill_2_id != "none" and not equipped_skill_2_id.is_empty():
		result.append(equipped_skill_2_id)
	return result
