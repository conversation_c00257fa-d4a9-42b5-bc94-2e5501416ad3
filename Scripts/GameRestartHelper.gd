extends Node
# 游戏重启辅助脚本
# 用于在场景切换后保持状态并初始化技能

func _ready():
	# 立即确保游戏不处于暂停状态
	get_tree().paused = false
	
	# 打印调试信息
	print("游戏重启助手：已加载，确保游戏未暂停")
	
	# 设置为不受暂停影响的处理模式
	process_mode = Node.PROCESS_MODE_ALWAYS
	
	# 延迟一帧以确保场景完全加载
	call_deferred("delayed_initialization")

func delayed_initialization():
	# 再次确保游戏未暂停
	get_tree().paused = false
	
	# 等待两帧确保场景初始化完成
	await get_tree().process_frame
	await get_tree().process_frame
	
	print("游戏重启助手：场景已重新加载，开始初始化（暂停状态：", get_tree().paused, "）")
	
	# 查找玩家并初始化技能
	var players = get_tree().get_nodes_in_group("player")
	for player in players:
		if player.has_method("initialize_skills"):
			print("游戏重启助手：初始化玩家技能")
			player.initialize_skills()
		
		if player.has_method("reset_skills"):
			print("游戏重启助手：重置玩家技能状态")
			player.reset_skills()
	
	# 确保技能UI正确初始化
	var skill_uis = get_tree().get_nodes_in_group("skill_ui")
	print("游戏重启助手：找到 ", skill_uis.size(), " 个技能UI")
	
	# 如果没有找到任何技能UI，尝试手动创建
	if skill_uis.size() == 0:
		print("游戏重启助手：未找到技能UI，将尝试重新创建")
		recreate_skill_ui()
	else:
		# 对所有现有技能UI进行初始化
		for ui in skill_uis:
			if not is_instance_valid(ui):
				continue
				
			print("游戏重启助手：发现UI节点: ", ui.name)
			# 确保UI节点可见
			ui.visible = true
			
			# 标记UI为需要重新初始化
			ui.set_meta("needs_reinit", true)
			
			# 如果有强制初始化方法，调用它
			if ui.has_method("force_initialization"):
				print("游戏重启助手：强制刷新技能UI")
				ui.force_initialization()
			elif ui.has_method("delayed_initialization"):
				print("游戏重启助手：延迟初始化技能UI")
				ui.delayed_initialization()
	
	# 如果仍处于暂停状态，强制取消暂停
	if get_tree().paused:
		print("游戏重启助手：检测到游戏仍处于暂停状态，强制取消暂停")
		get_tree().paused = false
	
	# 任务完成后删除自身，但是确保在5秒后继续检查暂停状态
	print("游戏重启助手：初始化完成，5秒后将删除自身")
	
	# 创建一个定时器以检查暂停状态
	var timer = Timer.new()
	timer.wait_time = 5.0
	timer.one_shot = true
	timer.timeout.connect(self.final_cleanup)
	add_child(timer)
	timer.start()

# 尝试重新创建技能UI
func recreate_skill_ui():
	print("游戏重启助手：尝试重新创建技能UI")
	
	# 查找玩家场景节点
	var players = get_tree().get_nodes_in_group("player")
	if players.size() == 0:
		print("游戏重启助手：无法找到玩家节点，无法创建技能UI")
		return
		
	var player = players[0]
	
	# 检查预制体路径
	var ui_scene_path = "res://Scenes/UI/skill_box_ui.tscn"
	if not ResourceLoader.exists(ui_scene_path):
		print("游戏重启助手：找不到技能UI场景文件，无法创建")
		return
		
	# 加载并实例化技能UI
	var ui_scene = load(ui_scene_path)
	if not ui_scene:
		print("游戏重启助手：无法加载技能UI场景")
		return
		
	var ui_instance = ui_scene.instantiate()
	if not ui_instance:
		print("游戏重启助手：无法实例化技能UI")
		return
		
	# 设置UI相关属性
	ui_instance.name = "SkillBoxUI"
	if ui_instance.has_method("set_player_path"):
		ui_instance.set_player_path(player.get_path())
	
	# 添加到场景
	get_tree().root.add_child(ui_instance)
	print("游戏重启助手：成功创建了技能UI")

# 最终清理
func final_cleanup():
	# 再次确认游戏未暂停
	if get_tree().paused:
		print("游戏重启助手：最终检查 - 游戏仍处于暂停状态，强制取消暂停")
		get_tree().paused = false
	
	print("游戏重启助手：所有工作完成，删除自身")
	queue_free() 