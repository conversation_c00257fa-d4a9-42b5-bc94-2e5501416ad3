extends Control

# 颜色配置
@export var player_dot_color: Color = Color(1, 1, 0)  # 玩家标记颜色
@export var tree_dot_color: Color = Color(0, 1, 0)   # 树木标记颜色
@export var enemy_dot_color: Color = Color(1, 0, 0)  # 敌人标记颜色
@export var map_background_color: Color = Color(0, 0, 0, 0.5)  # 地图背景颜色
@export var map_border_color: Color = Color(1, 1, 1, 0.3)  # 地图边框颜色

# 大小配置
@export var player_dot_size: float = 4.0  # 玩家标记大小
@export var enemy_dot_size: float = 3.0  # 敌人标记大小
@export var tree_dot_size: float = 3.0  # 树木标记大小
@export var player_arrow_length: float = 8.0  # 玩家朝向指示器长度

# 图标配置
@export var player_icon: Texture2D  # 玩家图标
@export var enemy_icon: Texture2D  # 敌人图标
@export var tree_icon: Texture2D  # 树木图标
@export var burnt_tree_icon: Texture2D  # 燃尽树木图标

var player: Node3D
var trees: Array[Node] = []
var enemies: Array[Node] = []  # 敌人节点数组
var map_bounds: AABB  # 地图边界
var is_initialized := false
var base_size := Vector2(300, 300)  # 基础大小
var margin := 20.0  # 边距

# 迷雾系统节点和状态
@export var fog_of_war_path: NodePath  # 允许在编辑器中指定迷雾系统节点
var _fog_of_war: Node  # 迷雾系统节点引用
var _fog_enabled := false  # 迷雾系统启用状态

func _ready() -> void:
	# 确保Control节点可见
	visible = true
	show()

	# 延迟初始化，确保所有节点都已准备就绪
	# 等待3帧以确保所有节点都已准备就绪
	await get_tree().process_frame
	await get_tree().process_frame
	await get_tree().process_frame

	# 再等待0.5秒确保其他脚本有时间生成实体
	var timer = get_tree().create_timer(0.5)
	await timer.timeout

	# 获取迷雾系统节点
	_init_fog_of_war()

	# 初始化小地图
	initialize_minimap()

	# 如果没有找到树木，设置定时器稍后再试
	if trees.is_empty():
		timer = get_tree().create_timer(1.0)
		await timer.timeout
		find_all_trees()

		# 如果还是没有找到，再试一次
		if trees.is_empty():
			timer = get_tree().create_timer(2.0)
			await timer.timeout
			find_all_trees()

	# 连接窗口大小改变信号
	get_tree().root.size_changed.connect(_on_window_size_changed)

# 打印场景树结构（未使用的调试函数）
func _print_scene_tree(node: Node, indent: int = 0) -> void:
	var _indent_str = ""
	for i in range(indent):
		_indent_str += "  "

	var _groups_str = ""
	for group in node.get_groups():
		_groups_str += group + ", "
	if _groups_str.length() > 0:
		_groups_str = " [组: " + _groups_str.trim_suffix(", ") + "]"

	if indent < 2:  # 限制递归深度，避免输出过多
		for child in node.get_children():
			_print_scene_tree(child, indent + 1)

# 列出场景中所有可用的组（未使用的调试函数）
func _list_all_groups() -> void:
	# 不依赖_all组，直接从根节点收集
	var _groups = {}  # 添加下划线前缀表示未使用的变量

	for node in get_tree().get_root().get_children():
		_collect_groups(node, _groups)

# 收集所有组（未使用的调试函数）
func _collect_groups(node: Node, groups: Dictionary) -> void:
	for group in node.get_groups():
		if not groups.has(group):
			groups[group] = 1
		else:
			groups[group] += 1

	for child in node.get_children():
		_collect_groups(child, groups)

func _on_window_size_changed() -> void:
	update_minimap_size()

func update_minimap_size() -> void:
	# 获取视口大小
	var viewport_size = get_viewport_rect().size

	# 计算缩放比例（基于屏幕高度）
	var scale_factor = viewport_size.y / 1080.0  # 1080p作为基准分辨率

	# 计算新的小地图大小
	var new_size = base_size * scale_factor

	# 确保小地图不会太大
	var max_size = min(viewport_size.x * 0.25, viewport_size.y * 0.25)
	new_size = new_size.clamp(Vector2.ZERO, Vector2(max_size, max_size))

	# 设置小地图大小
	size = new_size

	# 更新位置以确保始终在右上角
	position = Vector2(viewport_size.x - size.x - margin, margin)

func initialize_minimap() -> void:
	# 获取玩家节点
	player = get_tree().get_first_node_in_group("player")
	if not player:
		return

	# 使用增强的树木检测
	find_all_trees()

	# 获取所有敌人节点 - 只从enemies组获取
	enemies.clear()  # 清空现有数组
	enemies = get_tree().get_nodes_in_group("enemies")


	# 计算地图边界
	calculate_map_bounds()

	# 更新小地图大小
	update_minimap_size()

	is_initialized = true

	# 强制重绘
	queue_redraw()

func find_all_trees() -> void:
	# 首先尝试原来的组名
	trees = get_tree().get_nodes_in_group("generated_trees")

	# 如果没有找到，尝试其他组名和方法
	if trees.is_empty():
		# 尝试从fog_objects组中过滤出树木
		var fog_objects = get_tree().get_nodes_in_group("fog_objects")

		var found_trees: Array[Node] = []
		for obj in fog_objects:
			if "tree" in obj.name.to_lower() or \
			   (obj.get_script() and "tree" in obj.get_script().resource_path.to_lower()):
				found_trees.append(obj)

		if not found_trees.is_empty():
			trees = found_trees
		else:
			# 尝试interactable组
			var interactables = get_tree().get_nodes_in_group("interactable")

			for obj in interactables:
				if "tree" in obj.name.to_lower() or \
				   (obj.get_script() and "tree" in obj.get_script().resource_path.to_lower()):
					found_trees.append(obj)

			if not found_trees.is_empty():
				trees = found_trees

	# 最后一种方法：搜索整个场景树
	if trees.is_empty():
		var found_trees = find_all_tree_nodes(get_tree().root)
		if not found_trees.is_empty():
			trees = found_trees

	# 检查树木图标是否存在
	if not tree_icon:
		# 尝试动态加载图标
		attempt_load_tree_icon()

func find_all_tree_nodes(node: Node) -> Array[Node]:
	var result: Array[Node] = []

	# 检查当前节点是否是树木
	if "tree" in node.name.to_lower() or \
		(node.get_script() and "tree" in node.get_script().resource_path.to_lower()):
		if node is Node3D:  # 确保是3D节点
			result.append(node)

	# 递归检查所有子节点
	for child in node.get_children():
		var child_trees = find_all_tree_nodes(child)
		for tree in child_trees:
			result.append(tree)

	return result

func attempt_load_tree_icon() -> void:
	# 尝试从常见路径加载
	var possible_paths = [
		"res://treeicon.png",
		"res://Assets/UI/treeicon.png",
		"res://Assets/Textures/treeicon.png",
		"res://UI/treeicon.png",
		"res://Assets/Icons/treeicon.png",
		"res://icon.png"  # 最后尝试使用默认图标
	]

	for path in possible_paths:
		if ResourceLoader.exists(path):
			var texture = load(path)
			if texture:
				tree_icon = texture
				return

	# 列出项目中所有的png文件
	var dir = DirAccess.open("res://")
	if dir:
		find_all_icon_files(dir, "res://")

# 查找所有可能的图标文件
func find_all_icon_files(dir: DirAccess, path: String) -> void:
	dir.list_dir_begin()
	var file_name = dir.get_next()

	while file_name != "":
		if dir.current_is_dir():
			if not file_name.begins_with("."):  # 忽略隐藏目录
				var subdir = DirAccess.open(path + file_name + "/")
				if subdir:
					find_all_icon_files(subdir, path + file_name + "/")
		else:
			if file_name.ends_with(".png") and ("icon" in file_name.to_lower() or "tree" in file_name.to_lower()):
				var file_path = path + file_name

				# 尝试加载找到的图标
				if not tree_icon and "tree" in file_name.to_lower():
					var texture = load(file_path)
					if texture:
						tree_icon = texture

		file_name = dir.get_next()

	dir.list_dir_end()

func calculate_map_bounds() -> void:
	# 使用固定的、足够大的边界
	var level_size = Vector3(100, 0, 100)  # 关卡大小
	var map_margin = 10.0  # 边界边距，避免与类变量margin冲突

	# 设置地图边界，以原点为中心
	map_bounds = AABB(
		Vector3(-level_size.x/2 - map_margin, 0, -level_size.z/2 - map_margin),
		Vector3(level_size.x + map_margin*2, 0, level_size.z + map_margin*2)
	)

# 设置迷雾系统节点
func set_fog_of_war(fog_node: Node) -> void:
	_fog_of_war = fog_node
	if _fog_of_war:
		_check_fog_system_state()

# 初始化迷雾系统节点
func _init_fog_of_war() -> void:
	# 先尝试使用导出变量指定的路径
	if fog_of_war_path:
		_fog_of_war = get_node_or_null(fog_of_war_path)

	# 如果没有指定路径或者路径无效，尝试其他方法
	if not _fog_of_war:
		# 方法1：通过组查找
		var fog_nodes = get_tree().get_nodes_in_group("fog_of_war")
		if fog_nodes.size() > 0:
			_fog_of_war = fog_nodes[0]

	# 如果还是没找到，尝试直接查找
	if not _fog_of_war:
		_fog_of_war = get_node_or_null("/root/FogOfWar")

	# 如果还是没找到，尝试在整个场景树中查找
	if not _fog_of_war:
		_fog_of_war = get_tree().root.find_child("FogOfWar", true, false)

	# 检查是否找到了迷雾系统节点
	if _fog_of_war:
		# 初始化迷雾系统状态
		_check_fog_system_state()


# 检查迷雾系统的启用状态
func _check_fog_system_state() -> void:
	# 如果没有迷雾系统节点，直接返回
	if not _fog_of_war:
		return

	# 获取启用状态
	var _old_fog_enabled = _fog_enabled

	# 尝试不同的方法获取启用状态
	if _fog_of_war.has_method("is_enabled"):
		_fog_enabled = _fog_of_war.is_enabled()
	elif "enabled" in _fog_of_war:
		_fog_enabled = _fog_of_war.enabled


func _draw() -> void:
	if not is_initialized or not is_inside_tree():
		return

	# 绘制半透明背景
	draw_rect(Rect2(Vector2.ZERO, size), map_background_color)

	# 绘制边框
	draw_rect(Rect2(Vector2.ZERO, size), map_border_color, false)

	# 绘制树木标记
	for tree in trees:
		if is_instance_valid(tree) and tree.is_inside_tree():
			var tree_pos = world_to_map_position(tree.global_position)

			# 检查树木是否可见（如果有fog_state方法）
			var should_draw = true
			if tree.has_method("get_fog_state"):
				var fog_state = tree.get_fog_state()
				if fog_state == "unexplored":  # 如果未探索则不绘制
					should_draw = false

			if should_draw:
				# 检查树木是否燃尽
				var is_burnt_out = false
				if tree.has_method("get_state") and tree.get_state() == "burnt_out":
					is_burnt_out = true
				elif "current_state" in tree and tree.current_state == 5:  # TreeState.BURNT_OUT = 5
					is_burnt_out = true

				# 根据树木状态选择图标
				if is_burnt_out and burnt_tree_icon and burnt_tree_icon.get_size().x > 0:
					# 使用燃尽图标
					var rect_pos = tree_pos - Vector2(tree_dot_size, tree_dot_size)
					var rect_size = Vector2(tree_dot_size * 2, tree_dot_size * 2)

					draw_texture_rect_region(burnt_tree_icon,
						Rect2(rect_pos, rect_size),
						Rect2(Vector2.ZERO, burnt_tree_icon.get_size()))
				elif tree_icon and tree_icon.get_size().x > 0:
					# 使用普通树图标
					var rect_pos = tree_pos - Vector2(tree_dot_size, tree_dot_size)
					var rect_size = Vector2(tree_dot_size * 2, tree_dot_size * 2)

					draw_texture_rect_region(tree_icon,
						Rect2(rect_pos, rect_size),
						Rect2(Vector2.ZERO, tree_icon.get_size()))
				else:
					# 图标无效时使用圆形作为备用
					if is_burnt_out:
						draw_circle(tree_pos, tree_dot_size, Color(0.3, 0.3, 0.3))  # 灰色表示燃尽
					else:
						draw_circle(tree_pos, tree_dot_size, tree_dot_color)

	# 绘制敌人标记
	for enemy in enemies:
		if is_instance_valid(enemy) and enemy.is_inside_tree():
			# 检查敌人的迷雾状态
			var should_draw = false

			# 检查是否有特殊标记（由SixthSenseSkill添加）
			if enemy.has_meta("sixth_sense_visible"):
				should_draw = true

			else:
				# 只根据敌人的迷雾状态决定是否显示
				if enemy.has_method("get_fog_state"):
					var fog_state = enemy.get_fog_state()



					# 检查敌人的迷雾状态，支持字符串和枚举值
					if typeof(fog_state) == TYPE_STRING and fog_state == "visible":
						should_draw = true
					elif typeof(fog_state) == TYPE_INT and fog_state == 2:  # 2对应FogState.VISIBLE
						should_draw = true

			if should_draw:
				var enemy_pos = world_to_map_position(enemy.global_position)
				if enemy_icon:
					# 使用图标
					draw_texture_rect_region(enemy_icon,
						Rect2(enemy_pos - Vector2(enemy_dot_size, enemy_dot_size),
							Vector2(enemy_dot_size * 2, enemy_dot_size * 2)),
						Rect2(Vector2.ZERO, enemy_icon.get_size()))
				else:
					# 使用圆形
					draw_circle(enemy_pos, enemy_dot_size, enemy_dot_color)

	# 绘制玩家标记
	if is_instance_valid(player) and player.is_inside_tree():
		var player_pos = world_to_map_position(player.global_position)
		if player_icon:
			# 使用图标
			draw_texture_rect_region(player_icon,
				Rect2(player_pos - Vector2(player_dot_size, player_dot_size),
					Vector2(player_dot_size * 2, player_dot_size * 2)),
				Rect2(Vector2.ZERO, player_icon.get_size()))
		else:
			# 使用圆形
			draw_circle(player_pos, player_dot_size, player_dot_color)

		# 绘制玩家朝向指示器
		var forward = -player.global_transform.basis.z
		var direction = Vector2(forward.x, forward.z).normalized()
		var arrow_pos = player_pos + direction * player_arrow_length
		draw_line(player_pos, arrow_pos, player_dot_color, 2.0)

func world_to_map_position(world_pos: Vector3) -> Vector2:
	# 将世界坐标转换为地图坐标
	var relative_pos = world_pos - map_bounds.position

	# 检查坐标是否在地图边界内
	if relative_pos.x < 0 or relative_pos.x > map_bounds.size.x or \
	   relative_pos.z < 0 or relative_pos.z > map_bounds.size.z:
		pass # 超出边界的情况，无需打印警告

	var map_pos = Vector2(
		relative_pos.x / map_bounds.size.x * size.x,
		relative_pos.z / map_bounds.size.z * size.y
	)
	return map_pos

var log_counter = 0
func _process(_delta: float) -> void:
	if not is_inside_tree():
		return

	if is_initialized:
		log_counter += 1
		if log_counter >= 60:  # 每60帧(约1秒)检查一次
			log_counter = 0
			if trees.is_empty():
				# 每隔几秒尝试重新查找树木
				log_counter = -300  # 设置为负值，5秒后再次尝试
				find_all_trees()

			# 检查树木图标状态
			if not tree_icon:
				attempt_load_tree_icon()

		queue_redraw()  # 持续更新小地图显示
	else:
		log_counter += 1
