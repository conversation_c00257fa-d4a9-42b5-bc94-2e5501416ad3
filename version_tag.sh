#!/bin/bash

# 生成标签名称格式：v年年年年月月日日时时分分秒秒
# 例如：v20250326153101 表示 2025年3月26日15:31:01

# 获取当前时间
CURRENT_DATE=$(date +"%Y%m%d%H%M%S")
TAG_NAME="v$CURRENT_DATE"

# 输出将要创建的标签
echo "正在创建标签: $TAG_NAME"

# 提示用户输入提交信息
echo "请输入提交信息 (如果为空，将使用默认信息):"
read COMMIT_MESSAGE

# 如果提交信息为空，使用默认信息
if [ -z "$COMMIT_MESSAGE" ]; then
    COMMIT_MESSAGE="版本更新: $TAG_NAME"
fi

# 添加所有更改的文件
git add .

# 提交更改
git commit -m "$COMMIT_MESSAGE"

# 创建标签
git tag -a "$TAG_NAME" -m "$COMMIT_MESSAGE"

# 输出结果
echo "✅ 成功创建标签: $TAG_NAME"
echo "✅ 提交信息: $COMMIT_MESSAGE"
echo ""
echo "使用以下命令推送提交和标签到远程仓库:"
echo "git push"
echo "git push --tags" 