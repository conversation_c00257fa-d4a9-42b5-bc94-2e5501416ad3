[remap]

importer="texture"
type="CompressedTexture2D"
uid="uid://b1pcst24f5wcf"
path.s3tc="res://.godot/imported/Player01_Image_2.jpg-273d413d56dec49d64a1c465a451a892.s3tc.ctex"
metadata={
"imported_formats": ["s3tc_bptc"],
"vram_texture": true
}
generator_parameters={
"md5": "0e41ba90ae54db97fb70b52d285c7c82"
}

[deps]

source_file="res://Player01_Image_2.jpg"
dest_files=["res://.godot/imported/Player01_Image_2.jpg-273d413d56dec49d64a1c465a451a892.s3tc.ctex"]

[params]

compress/mode=2
compress/high_quality=false
compress/lossy_quality=0.7
compress/hdr_compression=1
compress/normal_map=1
compress/channel_pack=0
mipmaps/generate=true
mipmaps/limit=-1
roughness/mode=1
roughness/src_normal="res://Player01_Image_2.jpg"
process/fix_alpha_border=true
process/premult_alpha=false
process/normal_map_invert_y=false
process/hdr_as_srgb=false
process/hdr_clamp_exposure=false
process/size_limit=0
detect_3d/compress_to=0
