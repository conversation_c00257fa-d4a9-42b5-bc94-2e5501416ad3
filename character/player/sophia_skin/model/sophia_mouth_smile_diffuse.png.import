[remap]

importer="texture"
type="CompressedTexture2D"
uid="uid://d3gigd8y5hjg"
path="res://.godot/imported/sophia_mouth_smile_diffuse.png-e93f3c895a5378aa03fb3d744de3de7b.ctex"
metadata={
"vram_texture": false
}
generator_parameters={}

[deps]

source_file="res://character/player/sophia_skin/model/sophia_mouth_smile_diffuse.png"
dest_files=["res://.godot/imported/sophia_mouth_smile_diffuse.png-e93f3c895a5378aa03fb3d744de3de7b.ctex"]

[params]

compress/mode=3
compress/high_quality=false
compress/lossy_quality=0.7
compress/hdr_compression=1
compress/normal_map=0
compress/channel_pack=0
mipmaps/generate=true
mipmaps/limit=-1
roughness/mode=0
roughness/src_normal=""
process/fix_alpha_border=true
process/premult_alpha=false
process/normal_map_invert_y=false
process/hdr_as_srgb=false
process/hdr_clamp_exposure=false
process/size_limit=0
detect_3d/compress_to=0
