[remap]

importer="texture"
type="CompressedTexture2D"
uid="uid://bjvo8w2ptm7fc"
path.s3tc="res://.godot/imported/eyes_normal_map.png-6f4fa6ed21f6b3ccb4b7023ee2b2b4dd.s3tc.ctex"
metadata={
"imported_formats": ["s3tc_bptc"],
"vram_texture": true
}

[deps]

source_file="res://character/player/sophia_skin/model/textures/eyes_normal_map.png"
dest_files=["res://.godot/imported/eyes_normal_map.png-6f4fa6ed21f6b3ccb4b7023ee2b2b4dd.s3tc.ctex"]

[params]

compress/mode=2
compress/high_quality=false
compress/lossy_quality=0.7
compress/hdr_compression=1
compress/normal_map=1
compress/channel_pack=0
mipmaps/generate=true
mipmaps/limit=-1
roughness/mode=1
roughness/src_normal="res://sophia/model/eyes_normal_map.png"
process/fix_alpha_border=true
process/premult_alpha=false
process/normal_map_invert_y=false
process/hdr_as_srgb=false
process/hdr_clamp_exposure=false
process/size_limit=0
detect_3d/compress_to=0
