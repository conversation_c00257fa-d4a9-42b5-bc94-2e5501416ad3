[remap]

importer="texture"
type="CompressedTexture2D"
uid="uid://pcq7xge7vncv"
path="res://.godot/imported/sophia_eye_open_normal.png-e09023e744d7efd159a30b6fec3c3ced.ctex"
metadata={
"vram_texture": false
}
generator_parameters={}

[deps]

source_file="res://character/player/sophia_skin/model/sophia_eye_open_normal.png"
dest_files=["res://.godot/imported/sophia_eye_open_normal.png-e09023e744d7efd159a30b6fec3c3ced.ctex"]

[params]

compress/mode=3
compress/high_quality=false
compress/lossy_quality=0.7
compress/hdr_compression=1
compress/normal_map=1
compress/channel_pack=0
mipmaps/generate=true
mipmaps/limit=-1
roughness/mode=1
roughness/src_normal="res://sophia/model/sophia_eye_open_normal.png"
process/fix_alpha_border=true
process/premult_alpha=false
process/normal_map_invert_y=false
process/hdr_as_srgb=false
process/hdr_clamp_exposure=false
process/size_limit=0
detect_3d/compress_to=0
