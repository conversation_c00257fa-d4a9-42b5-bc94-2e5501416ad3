[remap]

importer="texture"
type="CompressedTexture2D"
uid="uid://6jb7dt4d54ck"
path="res://.godot/imported/sophia_diffuse.png-f5548e2aa1d55fff1fd438edebb7645b.ctex"
metadata={
"vram_texture": false
}
generator_parameters={}

[deps]

source_file="res://character/player/sophia_skin/model/sophia_diffuse.png"
dest_files=["res://.godot/imported/sophia_diffuse.png-f5548e2aa1d55fff1fd438edebb7645b.ctex"]

[params]

compress/mode=3
compress/high_quality=false
compress/lossy_quality=0.7
compress/hdr_compression=1
compress/normal_map=0
compress/channel_pack=0
mipmaps/generate=true
mipmaps/limit=-1
roughness/mode=0
roughness/src_normal=""
process/fix_alpha_border=true
process/premult_alpha=false
process/normal_map_invert_y=false
process/hdr_as_srgb=false
process/hdr_clamp_exposure=false
process/size_limit=0
detect_3d/compress_to=0
