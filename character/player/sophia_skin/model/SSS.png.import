[remap]

importer="texture"
type="CompressedTexture2D"
uid="uid://dxqmgxq6o21yr"
path="res://.godot/imported/SSS.png-6c0aabb2025332a8aacdd9e24feece1f.ctex"
metadata={
"vram_texture": false
}

[deps]

source_file="res://character/player/sophia_skin/model/SSS.png"
dest_files=["res://.godot/imported/SSS.png-6c0aabb2025332a8aacdd9e24feece1f.ctex"]

[params]

compress/mode=3
compress/high_quality=false
compress/lossy_quality=0.7
compress/hdr_compression=1
compress/normal_map=0
compress/channel_pack=0
mipmaps/generate=true
mipmaps/limit=-1
roughness/mode=0
roughness/src_normal=""
process/fix_alpha_border=true
process/premult_alpha=false
process/normal_map_invert_y=false
process/hdr_as_srgb=false
process/hdr_clamp_exposure=false
process/size_limit=0
detect_3d/compress_to=0
