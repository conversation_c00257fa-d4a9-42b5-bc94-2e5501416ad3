[gd_scene load_steps=9 format=3 uid="uid://b88l8pk1ebe1x"]

[ext_resource type="Script" uid="uid://b2s814mk1ii6d" path="res://Scripts/player.gd" id="1_dpokc"]
[ext_resource type="PackedScene" uid="uid://bb5wvwiamcjcs" path="res://Player01.glb" id="2_direct"]
[ext_resource type="AudioStream" uid="uid://bhoyegbjust84" path="res://character/player/sounds/robot_land.wav" id="3_kbqxf"]
[ext_resource type="AudioStream" uid="uid://b4x0f4pgfifoo" path="res://character/player/sounds/robot_jump.wav" id="4_1v0dq"]
[ext_resource type="PackedScene" uid="uid://clslt62nkjmbe" path="res://Scenes/Interaction/PlayerInteractor.tscn" id="5_w8c7q"]

[sub_resource type="SphereShape3D" id="SphereShape3D_e8tdt"]
radius = 0.2

[sub_resource type="BoxShape3D" id="BoxShape3D_poaj1"]
size = Vector3(1.5, 1.81201, 1.5)

[sub_resource type="CapsuleShape3D" id="CapsuleShape3D_e8pdu"]
radius = 0.4
height = 2.17689

[node name="Player" type="CharacterBody3D"]
collision_layer = 2
collision_mask = 5
axis_lock_angular_x = true
axis_lock_angular_y = true
axis_lock_angular_z = true
script = ExtResource("1_dpokc")
camera_distance = 6.0

[node name="PlayerInteractor" parent="." instance=ExtResource("5_w8c7q")]

[node name="CollisionShape3D2" type="CollisionShape3D" parent="PlayerInteractor"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 0, 0.881151, 0)
shape = SubResource("BoxShape3D_poaj1")

[node name="CharacterCollisionShape" type="CollisionShape3D" parent="."]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 0, 1.1, 0)
shape = SubResource("CapsuleShape3D_e8pdu")

[node name="LandingSound" type="AudioStreamPlayer3D" parent="."]
unique_name_in_owner = true
stream = ExtResource("3_kbqxf")
volume_db = 1.0

[node name="JumpSound" type="AudioStreamPlayer3D" parent="."]
unique_name_in_owner = true
stream = ExtResource("4_1v0dq")
volume_db = 1.0

[node name="Player01" parent="." instance=ExtResource("2_direct")]
transform = Transform3D(0, 0, 4.5, 0, 4.5, 0, -4.5, 0, 0, 0, 0, 0)
