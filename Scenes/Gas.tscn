[gd_scene load_steps=6 format=3 uid="uid://cbg81yijaaxk5"]

[ext_resource type="Script" uid="uid://1si73cu2sh2f" path="res://Scripts/ItemPickup.gd" id="1_l7kgd"]
[ext_resource type="Resource" uid="uid://bjih53ivkm0qi" path="res://Resources/GasItem.tres" id="2_yvl87"]

[sub_resource type="SphereShape3D" id="SphereShape3D_bjnxy"]

[sub_resource type="CylinderMesh" id="CylinderMesh_o0l1e"]
top_radius = 0.1
bottom_radius = 0.1
height = 0.3

[sub_resource type="StandardMaterial3D" id="StandardMaterial3D_smnkh"]
albedo_color = Color(1, 0.47, 0, 1)
emission_enabled = true
emission = Color(1, 0.47, 0, 1)
emission_energy_multiplier = 0.5

[node name="Gas" type="Area3D"]
script = ExtResource("1_l7kgd")
item_resource = ExtResource("2_yvl87")
rotation_speed = 1.5
hover_amplitude = 0.15

[node name="CollisionShape3D" type="CollisionShape3D" parent="."]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 0, 0.15, 0)
shape = SubResource("SphereShape3D_bjnxy")

[node name="MeshInstance3D" type="MeshInstance3D" parent="."]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 0, 0.15, 0)
mesh = SubResource("CylinderMesh_o0l1e")
skeleton = NodePath("../CollisionShape3D")
surface_material_override/0 = SubResource("StandardMaterial3D_smnkh")

[node name="Label3D" type="Label3D" parent="."]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 0, 0.4, 0)
billboard = 1
text = "汽油"
font_size = 24
