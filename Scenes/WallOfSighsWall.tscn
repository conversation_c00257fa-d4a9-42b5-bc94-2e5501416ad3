[gd_scene load_steps=3 format=3 uid="uid://mucfamac66m7"]

[ext_resource type="PackedScene" uid="uid://4y8l2yalrbjd" path="res://Assets/rocks/rubble_large.gltf" id="1_yjnxs"]

[sub_resource type="BoxShape3D" id="BoxShape3D_qnuqc"]
size = Vector3(19.9127, 6.87976, 5.96814)

[node name="WallOfSighsWall" type="Node3D"]

[node name="WallModel" parent="." instance=ExtResource("1_yjnxs")]
transform = Transform3D(2.5, 0, 0, 0, 2, 0, 0, 0, 2, 0, 0, 0)

[node name="WallCollision" type="StaticBody3D" parent="."]
collision_layer = 8

[node name="CollisionShape3D" type="CollisionShape3D" parent="WallCollision"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -0.052887, 3.43988, -0.145325)
shape = SubResource("BoxShape3D_qnuqc")
