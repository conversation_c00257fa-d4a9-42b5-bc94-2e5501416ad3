[gd_scene load_steps=10 format=3 uid="uid://ervckea7fk57"]

[ext_resource type="Script" uid="uid://bcvsrkkkwys3b" path="res://Scripts/Enemy/Enemy02.gd" id="1_enemy02"]
[ext_resource type="PackedScene" uid="uid://bqr8vise0qji2" path="res://Scenes/Enemy02Skin.tscn" id="2_skin"]
[ext_resource type="Script" uid="uid://cgtsrx4knqe1w" path="res://Scripts/Enemy/Components/MovementComponent.gd" id="3_movement"]
[ext_resource type="Script" uid="uid://bvdn7iwoi22qq" path="res://Scripts/Enemy/Components/NavigationComponent.gd" id="4_navigation"]
[ext_resource type="Script" uid="uid://6gv1ecrefm3a" path="res://Scripts/Enemy/Components/TreeInteractionComponent.gd" id="5_tree"]
[ext_resource type="Script" uid="uid://cns044h5bh1ch" path="res://Scripts/Enemy/Components/PlayerInteractionComponent.gd" id="6_player"]
[ext_resource type="Script" uid="uid://durs8g1l7iq5v" path="res://Scripts/Enemy/Components/ItemInteractionComponent.gd" id="7_item"]
[ext_resource type="Script" uid="uid://cmbpur45sb4dx" path="res://Scripts/Enemy/Components/BlinkComponent.gd" id="8_blink"]

[sub_resource type="CapsuleShape3D" id="CapsuleShape3D_enemy02"]
radius = 0.506214
height = 2.10583

[node name="Enemy02" type="CharacterBody3D" groups=["enemies", "fog_objects"]]
transform = Transform3D(0.8, 0, 0, 0, 0.8, 0, 0, 0, 0.8, 0, 0, 0)
collision_layer = 4
collision_mask = 3
script = ExtResource("1_enemy02")

[node name="CollisionShape3D" type="CollisionShape3D" parent="."]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -0.0205719, 0.965054, 0)
shape = SubResource("CapsuleShape3D_enemy02")

[node name="Skin" parent="." instance=ExtResource("2_skin")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 0, 2.98023e-07, 0)

[node name="MovementComponent" type="Node" parent="."]
script = ExtResource("3_movement")
move_speed = 5.0

[node name="NavigationComponent" type="Node" parent="." node_paths=PackedStringArray("host")]
script = ExtResource("4_navigation")
host = NodePath("..")

[node name="TreeInteractionComponent" type="Node" parent="." node_paths=PackedStringArray("host")]
script = ExtResource("5_tree")
host = NodePath("..")

[node name="PlayerInteractionComponent" type="Node" parent="." node_paths=PackedStringArray("host")]
script = ExtResource("6_player")
host = NodePath("..")

[node name="ItemInteractionComponent" type="Node" parent="." node_paths=PackedStringArray("host")]
script = ExtResource("7_item")
host = NodePath("..")

[node name="BlinkComponent" type="Node" parent="." node_paths=PackedStringArray("host")]
script = ExtResource("8_blink")
host = NodePath("..")

[node name="EnemyNavigationAgent3D" type="NavigationAgent3D" parent="."]
path_desired_distance = 0.5
target_desired_distance = 0.5
avoidance_enabled = true
max_speed = 8.0
