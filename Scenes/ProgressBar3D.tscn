[gd_scene load_steps=5 format=3 uid="uid://brxqv3iv3op6u"]

[ext_resource type="Script" path="res://Scripts/ProgressBar3D.gd" id="1_b037r"]

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_bg"]
bg_color = Color(0, 0, 0, 0.5)
corner_radius_top_left = 4
corner_radius_top_right = 4
corner_radius_bottom_right = 4
corner_radius_bottom_left = 4

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_fill"]
bg_color = Color(1, 1, 1, 1)
corner_radius_top_left = 4
corner_radius_top_right = 4
corner_radius_bottom_right = 4
corner_radius_bottom_left = 4

[sub_resource type="ViewportTexture" id="ViewportTexture_viewport"]
viewport_path = NodePath("SubViewport")

[node name="ProgressBar3D" type="Node3D"]
script = ExtResource("1_b037r")

[node name="SubViewport" type="SubViewport" parent="."]
transparent_bg = true
size = Vector2i(100, 10)

[node name="ProgressBar" type="ProgressBar" parent="SubViewport"]
custom_minimum_size = Vector2(100, 10)
offset_right = 100.0
offset_bottom = 10.0
theme_override_styles/background = SubResource("StyleBoxFlat_bg")
theme_override_styles/fill = SubResource("StyleBoxFlat_fill")
value = 50.0
show_percentage = false

[node name="Sprite3D" type="Sprite3D" parent="."]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 0, 2, 0)
billboard = 1
texture = SubResource("ViewportTexture_viewport")
