[gd_scene load_steps=10 format=3 uid="uid://rvgn0irsuwao"]

[ext_resource type="Script" uid="uid://bt7k5qv5glyen" path="res://Scripts/Enemy/EnemyBoy01.gd" id="1_mfhn2"]
[ext_resource type="PackedScene" uid="uid://o2rvq01lry1l" path="res://Scenes/EnemyBoy01Skin.tscn" id="2_model"]
[ext_resource type="Script" uid="uid://cgtsrx4knqe1w" path="res://Scripts/Enemy/Components/MovementComponent.gd" id="4_movement"]
[ext_resource type="Script" uid="uid://bvdn7iwoi22qq" path="res://Scripts/Enemy/Components/NavigationComponent.gd" id="5_navigation"]
[ext_resource type="Script" uid="uid://6gv1ecrefm3a" path="res://Scripts/Enemy/Components/TreeInteractionComponent.gd" id="6_tree"]
[ext_resource type="Script" uid="uid://cns044h5bh1ch" path="res://Scripts/Enemy/Components/PlayerInteractionComponent.gd" id="7_player"]
[ext_resource type="Script" uid="uid://durs8g1l7iq5v" path="res://Scripts/Enemy/Components/ItemInteractionComponent.gd" id="8_item"]
[ext_resource type="Script" uid="uid://cmbpur45sb4dx" path="res://Scripts/Enemy/Components/BlinkComponent.gd" id="9_blink"]

[sub_resource type="CapsuleShape3D" id="CapsuleShape3D_enemy"]
radius = 1.78502
height = 5.62818

[node name="EnemyBoy01" type="CharacterBody3D" groups=["enemies", "fog_objects"]]
transform = Transform3D(0.8, 0, 0, 0, 0.8, 0, 0, 0, 0.8, 0, 0, 0)
collision_layer = 4
collision_mask = 3
script = ExtResource("1_mfhn2")

[node name="CollisionShape3D" type="CollisionShape3D" parent="."]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 0.12553, 2.97482, 0)
shape = SubResource("CapsuleShape3D_enemy")

[node name="Skin" parent="." instance=ExtResource("2_model")]
transform = Transform3D(-3, 0, -2.62268e-07, 0, 3, 0, 2.62268e-07, 0, -3, 0, 0, 0)

[node name="MinimapIcon" type="Sprite3D" parent="."]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 0, 2.5, 0)
modulate = Color(1, 0, 0, 1)
billboard = 1

[node name="MovementComponent" type="Node" parent="." node_paths=PackedStringArray("host")]
script = ExtResource("4_movement")
host = NodePath("..")
move_speed = 5.0

[node name="NavigationComponent" type="Node" parent="." node_paths=PackedStringArray("host")]
script = ExtResource("5_navigation")
host = NodePath("..")

[node name="TreeInteractionComponent" type="Node" parent="." node_paths=PackedStringArray("host")]
script = ExtResource("6_tree")
host = NodePath("..")

[node name="PlayerInteractionComponent" type="Node" parent="." node_paths=PackedStringArray("host")]
script = ExtResource("7_player")
host = NodePath("..")

[node name="ItemInteractionComponent" type="Node" parent="." node_paths=PackedStringArray("host")]
script = ExtResource("8_item")
host = NodePath("..")

[node name="BlinkComponent" type="Node" parent="." node_paths=PackedStringArray("host")]
script = ExtResource("9_blink")
host = NodePath("..")

[node name="EnemyNavigationAgent3D" type="NavigationAgent3D" parent="."]
path_desired_distance = 0.5
target_desired_distance = 0.5
avoidance_enabled = true
max_speed = 8.0
