[gd_scene load_steps=6 format=3 uid="uid://dkn3eadp24w7e"]

[ext_resource type="Script" path="res://Scripts/UI/GameWinUI.gd" id="1_m4uhy"]

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_qnuqc"]
bg_color = Color(0.12549, 0.12549, 0.12549, 0.564706)

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_q85co"]
bg_color = Color(0.141176, 0.439216, 0.2, 0.878431)
border_width_left = 2
border_width_top = 2
border_width_right = 2
border_width_bottom = 2
border_color = Color(0.211765, 1, 0.341176, 1)
corner_radius_top_left = 8
corner_radius_top_right = 8
corner_radius_bottom_right = 8
corner_radius_bottom_left = 8

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_f4uy8"]
bg_color = Color(0.270588, 0.270588, 0.270588, 0.878431)
border_width_left = 2
border_width_top = 2
border_width_right = 2
border_width_bottom = 2
border_color = Color(0.8, 0.8, 0.8, 1)
corner_radius_top_left = 8
corner_radius_top_right = 8
corner_radius_bottom_right = 8
corner_radius_bottom_left = 8

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_p60wd"]
bg_color = Color(0.211765, 0.211765, 0.211765, 0.878431)
border_width_left = 2
border_width_top = 2
border_width_right = 2
border_width_bottom = 2
border_color = Color(0.8, 0.8, 0.8, 1)
corner_radius_top_left = 8
corner_radius_top_right = 8
corner_radius_bottom_right = 8
corner_radius_bottom_left = 8

[node name="GameWinUI" type="CanvasLayer"]
process_mode = 3
layer = 10
script = ExtResource("1_m4uhy")

[node name="BlurRect" type="ColorRect" parent="."]
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
color = Color(0, 0, 0, 0.376471)

[node name="GameWinPanel" type="Panel" parent="."]
anchors_preset = 8
anchor_left = 0.5
anchor_top = 0.5
anchor_right = 0.5
anchor_bottom = 0.5
offset_left = -300.0
offset_top = -200.0
offset_right = 300.0
offset_bottom = 200.0
grow_horizontal = 2
grow_vertical = 2
theme_override_styles/panel = SubResource("StyleBoxFlat_qnuqc")

[node name="VBoxContainer" type="VBoxContainer" parent="GameWinPanel"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
offset_left = 20.0
offset_top = 20.0
offset_right = -20.0
offset_bottom = -20.0
grow_horizontal = 2
grow_vertical = 2
theme_override_constants/separation = 20

[node name="GameWinTitle" type="Label" parent="GameWinPanel/VBoxContainer"]
layout_mode = 2
theme_override_colors/font_color = Color(0.207843, 0.921569, 0.345098, 1)
theme_override_colors/font_shadow_color = Color(0, 0, 0, 1)
theme_override_constants/shadow_offset_x = 2
theme_override_constants/shadow_offset_y = 2
theme_override_font_sizes/font_size = 48
text = "Game Win"
horizontal_alignment = 1

[node name="StatisticsLabel" type="Label" parent="GameWinPanel/VBoxContainer"]
layout_mode = 2
theme_override_colors/font_color = Color(1, 1, 1, 1)
theme_override_font_sizes/font_size = 24
text = "你成功燃尽了所有树木！"
horizontal_alignment = 1

[node name="TipsLabel" type="Label" parent="GameWinPanel/VBoxContainer"]
layout_mode = 2
theme_override_colors/font_color = Color(0.839216, 0.839216, 0.839216, 1)
theme_override_font_sizes/font_size = 20
text = "恭喜你的精彩表现！"
horizontal_alignment = 1

[node name="Spacer" type="Control" parent="GameWinPanel/VBoxContainer"]
layout_mode = 2
size_flags_vertical = 3

[node name="ButtonsContainer" type="HBoxContainer" parent="GameWinPanel/VBoxContainer"]
layout_mode = 2
theme_override_constants/separation = 20
alignment = 1

[node name="RestartButton" type="Button" parent="GameWinPanel/VBoxContainer/ButtonsContainer"]
custom_minimum_size = Vector2(180, 60)
layout_mode = 2
theme_override_font_sizes/font_size = 20
theme_override_styles/normal = SubResource("StyleBoxFlat_q85co")
theme_override_styles/hover = SubResource("StyleBoxFlat_q85co")
theme_override_styles/pressed = SubResource("StyleBoxFlat_q85co")
text = "重新开始"

[node name="MainMenuButton" type="Button" parent="GameWinPanel/VBoxContainer/ButtonsContainer"]
custom_minimum_size = Vector2(180, 60)
layout_mode = 2
theme_override_font_sizes/font_size = 20
theme_override_styles/normal = SubResource("StyleBoxFlat_f4uy8")
theme_override_styles/hover = SubResource("StyleBoxFlat_f4uy8")
theme_override_styles/pressed = SubResource("StyleBoxFlat_p60wd")
text = "主菜单"