[gd_scene load_steps=6 format=3 uid="uid://d3a5x5oqq5doy"]

[ext_resource type="Script" path="res://Scripts/UI/LoadingScreen.gd" id="1_3rwux"]

[sub_resource type="Animation" id="Animation_n4q1s"]
length = 0.001
tracks/0/type = "value"
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/path = NodePath("LoadingIcon:rotation")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 0,
"values": [0.0]
}
tracks/1/type = "value"
tracks/1/imported = false
tracks/1/enabled = true
tracks/1/path = NodePath(".:modulate")
tracks/1/interp = 1
tracks/1/loop_wrap = true
tracks/1/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 0,
"values": [Color(1, 1, 1, 1)]
}

[sub_resource type="Animation" id="Animation_mkc2f"]
resource_name = "fade_out"
length = 0.5
tracks/0/type = "value"
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/path = NodePath(".:modulate")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/keys = {
"times": PackedFloat32Array(0, 0.5),
"transitions": PackedFloat32Array(1, 1),
"update": 0,
"values": [Color(1, 1, 1, 1), Color(1, 1, 1, 0)]
}

[sub_resource type="Animation" id="Animation_fjegw"]
resource_name = "loading"
length = 2.0
loop_mode = 1
tracks/0/type = "value"
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/path = NodePath("LoadingIcon:rotation")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/keys = {
"times": PackedFloat32Array(0, 2),
"transitions": PackedFloat32Array(1, 1),
"update": 0,
"values": [0.0, 6.28319]
}

[sub_resource type="AnimationLibrary" id="AnimationLibrary_6wtcs"]
_data = {
&"RESET": SubResource("Animation_n4q1s"),
&"fade_out": SubResource("Animation_mkc2f"),
&"loading": SubResource("Animation_fjegw")
}

[node name="LoadingScreen" type="Control"]
layout_mode = 3
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
script = ExtResource("1_3rwux")

[node name="ColorRect" type="ColorRect" parent="."]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
color = Color(0.0784314, 0.0784314, 0.113725, 1)

[node name="Title" type="Label" parent="."]
layout_mode = 1
anchors_preset = 8
anchor_left = 0.5
anchor_top = 0.5
anchor_right = 0.5
anchor_bottom = 0.5
offset_left = -209.0
offset_top = -190.0
offset_right = 209.0
offset_bottom = -114.0
grow_horizontal = 2
grow_vertical = 2
theme_override_font_sizes/font_size = 52
text = "火焰冲突"
horizontal_alignment = 1

[node name="Subtitle" type="Label" parent="."]
layout_mode = 1
anchors_preset = 8
anchor_left = 0.5
anchor_top = 0.5
anchor_right = 0.5
anchor_bottom = 0.5
offset_left = -146.0
offset_top = -114.0
offset_right = 146.0
offset_bottom = -81.0
grow_horizontal = 2
grow_vertical = 2
theme_override_colors/font_color = Color(0.611765, 0.611765, 0.611765, 1)
theme_override_font_sizes/font_size = 22
text = "FLAME CLASH"
horizontal_alignment = 1

[node name="ProgressBar" type="ProgressBar" parent="."]
layout_mode = 1
anchors_preset = 8
anchor_left = 0.5
anchor_top = 0.5
anchor_right = 0.5
anchor_bottom = 0.5
offset_left = -304.0
offset_top = 30.0
offset_right = 304.0
offset_bottom = 57.0
grow_horizontal = 2
grow_vertical = 2
theme_override_font_sizes/font_size = 14

[node name="StatusLabel" type="Label" parent="."]
layout_mode = 1
anchors_preset = 8
anchor_left = 0.5
anchor_top = 0.5
anchor_right = 0.5
anchor_bottom = 0.5
offset_left = -304.0
offset_top = -10.0
offset_right = 304.0
offset_bottom = 23.0
grow_horizontal = 2
grow_vertical = 2
theme_override_colors/font_color = Color(0.878431, 0.878431, 0.878431, 1)
theme_override_font_sizes/font_size = 18
text = "准备加载..."
horizontal_alignment = 1

[node name="LoadingIcon" type="TextureRect" parent="."]
layout_mode = 1
anchors_preset = 8
anchor_left = 0.5
anchor_top = 0.5
anchor_right = 0.5
anchor_bottom = 0.5
offset_left = -32.0
offset_top = 70.0
offset_right = 32.0
offset_bottom = 134.0
grow_horizontal = 2
grow_vertical = 2
expand_mode = 1
stretch_mode = 5

[node name="AnimationPlayer" type="AnimationPlayer" parent="."]
libraries = {
&"": SubResource("AnimationLibrary_6wtcs")
}

[node name="Version" type="Label" parent="."]
layout_mode = 1
anchors_preset = 3
anchor_left = 1.0
anchor_top = 1.0
anchor_right = 1.0
anchor_bottom = 1.0
offset_left = -173.0
offset_top = -36.0
offset_right = -16.0
offset_bottom = -16.0
grow_horizontal = 0
grow_vertical = 0
theme_override_colors/font_color = Color(0.611765, 0.611765, 0.611765, 1)
text = "版本 0.1.0"
horizontal_alignment = 2
