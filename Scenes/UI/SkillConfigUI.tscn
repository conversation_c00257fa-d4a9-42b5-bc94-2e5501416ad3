[gd_scene load_steps=2 format=3 uid="uid://c8yvt1ykfqvqm"]

[ext_resource type="Script" path="res://Scripts/UI/SkillConfigUI.gd" id="1_skill_config"]

[node name="SkillConfigUI" type="Control"]
layout_mode = 3
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
script = ExtResource("1_skill_config")

[node name="BackgroundDim" type="Button" parent="."]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
flat = true
focus_mode = 0

[node name="DimColor" type="ColorRect" parent="BackgroundDim"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
mouse_filter = 2
color = Color(0, 0, 0, 0.7)

[node name="Panel" type="Panel" parent="."]
layout_mode = 1
anchors_preset = 8
anchor_left = 0.5
anchor_top = 0.5
anchor_right = 0.5
anchor_bottom = 0.5
offset_left = -250.0
offset_top = -200.0
offset_right = 250.0
offset_bottom = 200.0
grow_horizontal = 2
grow_vertical = 2
mouse_filter = 0

[node name="CloseButton" type="Button" parent="Panel"]
layout_mode = 1
anchors_preset = 1
anchor_left = 1.0
anchor_right = 1.0
offset_left = -30.0
offset_top = 10.0
offset_right = -10.0
offset_bottom = 30.0
grow_horizontal = 0
text = "X"

[node name="Title" type="Label" parent="."]
layout_mode = 1
anchors_preset = 5
anchor_left = 0.5
anchor_right = 0.5
offset_left = -100.0
offset_top = 120.0
offset_right = 100.0
offset_bottom = 146.0
grow_horizontal = 2
theme_override_font_sizes/font_size = 24
text = "技能配置"
horizontal_alignment = 1

[node name="VBoxContainer" type="VBoxContainer" parent="."]
layout_mode = 1
anchors_preset = 8
anchor_left = 0.5
anchor_top = 0.5
anchor_right = 0.5
anchor_bottom = 0.5
offset_left = -200.0
offset_top = -150.0
offset_right = 200.0
offset_bottom = 150.0
grow_horizontal = 2
grow_vertical = 2
theme_override_constants/separation = 20

[node name="Skill1Container" type="VBoxContainer" parent="VBoxContainer"]
layout_mode = 2

[node name="Skill1Label" type="Label" parent="VBoxContainer/Skill1Container"]
layout_mode = 2
text = "技能1 (按键E)"

[node name="Skill1Dropdown" type="OptionButton" parent="VBoxContainer/Skill1Container"]
layout_mode = 2
alignment = 1

[node name="Skill1Icon" type="TextureRect" parent="VBoxContainer/Skill1Container"]
custom_minimum_size = Vector2(64, 64)
layout_mode = 2
size_flags_horizontal = 4
expand_mode = 1
stretch_mode = 5

[node name="Skill1Description" type="Label" parent="VBoxContainer/Skill1Container"]
layout_mode = 2
text = "技能描述"
horizontal_alignment = 1
autowrap_mode = 3

[node name="HSeparator" type="HSeparator" parent="VBoxContainer"]
layout_mode = 2

[node name="Skill2Container" type="VBoxContainer" parent="VBoxContainer"]
layout_mode = 2

[node name="Skill2Label" type="Label" parent="VBoxContainer/Skill2Container"]
layout_mode = 2
text = "技能2 (按键F)"

[node name="Skill2Dropdown" type="OptionButton" parent="VBoxContainer/Skill2Container"]
layout_mode = 2
alignment = 1

[node name="Skill2Icon" type="TextureRect" parent="VBoxContainer/Skill2Container"]
custom_minimum_size = Vector2(64, 64)
layout_mode = 2
size_flags_horizontal = 4
expand_mode = 1
stretch_mode = 5

[node name="Skill2Description" type="Label" parent="VBoxContainer/Skill2Container"]
layout_mode = 2
text = "技能描述"
horizontal_alignment = 1
autowrap_mode = 3

[node name="HSeparator2" type="HSeparator" parent="VBoxContainer"]
layout_mode = 2

[node name="ButtonContainer" type="HBoxContainer" parent="VBoxContainer"]
layout_mode = 2
alignment = 1

[node name="ApplyButton" type="Button" parent="VBoxContainer/ButtonContainer"]
layout_mode = 2
text = "应用"

[node name="ConfirmationLabel" type="Label" parent="."]
visible = false
layout_mode = 1
anchors_preset = 7
anchor_left = 0.5
anchor_top = 1.0
anchor_right = 0.5
anchor_bottom = 1.0
offset_left = -100.0
offset_top = -100.0
offset_right = 100.0
offset_bottom = -74.0
grow_horizontal = 2
grow_vertical = 0
theme_override_colors/font_color = Color(0, 1, 0, 1)
text = "技能配置已应用！"
horizontal_alignment = 1

[connection signal="pressed" from="VBoxContainer/ButtonContainer/ApplyButton" to="." method="_on_apply_button_pressed"]
[connection signal="pressed" from="VBoxContainer/ButtonContainer/CloseButton" to="." method="_on_close_button_pressed"]
