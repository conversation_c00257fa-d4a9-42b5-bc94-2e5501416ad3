[gd_scene load_steps=18 format=3 uid="uid://dxnvp4eqfq8ov"]

[ext_resource type="Script" uid="uid://bmirnynoiwcjo" path="res://Scripts/UI/MainMenu.gd" id="1_uxnqm"]
[ext_resource type="PackedScene" uid="uid://prh35jb6tjtd" path="res://character/player/sophia_skin/sophia_skin.tscn" id="2_4dj54"]

[sub_resource type="Gradient" id="Gradient_yvnqm"]
offsets = PackedFloat32Array(0, 0.236287, 0.338144, 0.470103, 0.738144, 1)
colors = PackedColorArray(0, 0, 0, 1, 1, 0.28125, 0, 1, 1, 0.962646, 0.402344, 0.990493, 1, 0.140625, 0, 0.998312, 0, 0, 0, 1, 0, 0, 0, 0)

[sub_resource type="GradientTexture1D" id="GradientTexture1D_ixnqj"]
gradient = SubResource("Gradient_yvnqm")

[sub_resource type="ParticleProcessMaterial" id="ParticleProcessMaterial_ixnqj"]
particle_flag_disable_z = true
emission_shape = 3
emission_box_extents = Vector3(600, 400, 1)
direction = Vector3(0, -1, 0)
spread = 10.0
initial_velocity_min = 20.0
initial_velocity_max = 40.0
gravity = Vector3(0, 0, 0)
scale_min = 0.5
scale_max = 2.0
color_ramp = SubResource("GradientTexture1D_ixnqj")

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_q85co"]
bg_color = Color(0.141176, 0.439216, 0.2, 0.878431)
border_width_left = 2
border_width_top = 2
border_width_right = 2
border_width_bottom = 2
border_color = Color(0.211765, 1, 0.341176, 1)
corner_radius_top_left = 8
corner_radius_top_right = 8
corner_radius_bottom_right = 8
corner_radius_bottom_left = 8

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_p60wd"]
bg_color = Color(0.0784314, 0.266667, 0.121569, 0.878431)
border_width_left = 2
border_width_top = 2
border_width_right = 2
border_width_bottom = 2
border_color = Color(0.211765, 1, 0.341176, 1)
corner_radius_top_left = 8
corner_radius_top_right = 8
corner_radius_bottom_right = 8
corner_radius_bottom_left = 8

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_options"]
bg_color = Color(0.141176, 0.341176, 0.439216, 0.878431)
border_width_left = 2
border_width_top = 2
border_width_right = 2
border_width_bottom = 2
border_color = Color(0.211765, 0.341176, 1, 1)
corner_radius_top_left = 8
corner_radius_top_right = 8
corner_radius_bottom_right = 8
corner_radius_bottom_left = 8

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_options_pressed"]
bg_color = Color(0.0784314, 0.121569, 0.266667, 0.878431)
border_width_left = 2
border_width_top = 2
border_width_right = 2
border_width_bottom = 2
border_color = Color(0.211765, 0.341176, 1, 1)
corner_radius_top_left = 8
corner_radius_top_right = 8
corner_radius_bottom_right = 8
corner_radius_bottom_left = 8

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_gallery"]
bg_color = Color(0.439216, 0.341176, 0.141176, 0.878431)
border_width_left = 2
border_width_top = 2
border_width_right = 2
border_width_bottom = 2
border_color = Color(1, 0.843137, 0.211765, 1)
corner_radius_top_left = 8
corner_radius_top_right = 8
corner_radius_bottom_right = 8
corner_radius_bottom_left = 8

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_gallery_pressed"]
bg_color = Color(0.266667, 0.196078, 0.0784314, 0.878431)
border_width_left = 2
border_width_top = 2
border_width_right = 2
border_width_bottom = 2
border_color = Color(1, 0.843137, 0.211765, 1)
corner_radius_top_left = 8
corner_radius_top_right = 8
corner_radius_bottom_right = 8
corner_radius_bottom_left = 8

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_quit"]
bg_color = Color(0.439216, 0.137255, 0.2, 0.878431)
border_width_left = 2
border_width_top = 2
border_width_right = 2
border_width_bottom = 2
border_color = Color(1, 0.356863, 0.211765, 1)
corner_radius_top_left = 8
corner_radius_top_right = 8
corner_radius_bottom_right = 8
corner_radius_bottom_left = 8

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_quit_pressed"]
bg_color = Color(0.266667, 0.0784314, 0.121569, 0.878431)
border_width_left = 2
border_width_top = 2
border_width_right = 2
border_width_bottom = 2
border_color = Color(1, 0.356863, 0.211765, 1)
corner_radius_top_left = 8
corner_radius_top_right = 8
corner_radius_bottom_right = 8
corner_radius_bottom_left = 8

[sub_resource type="Animation" id="Animation_n4q1s"]
length = 0.001
tracks/0/type = "value"
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/path = NodePath("CharacterViewport/SubViewport/CharacterContainer/SophiaSkin:rotation")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 0,
"values": [Vector3(0, 0, 0)]
}

[sub_resource type="Animation" id="Animation_idle"]
resource_name = "idle"
length = 4.0
loop_mode = 1
tracks/0/type = "value"
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/path = NodePath("CharacterViewport/SubViewport/CharacterContainer/SophiaSkin:rotation")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/keys = {
"times": PackedFloat32Array(0, 1, 2, 3, 4),
"transitions": PackedFloat32Array(1, 1, 1, 1, 1),
"update": 0,
"values": [Vector3(0, 0, 0), Vector3(0, 0.785398, 0), Vector3(0, 1.5708, 0), Vector3(0, 0.785398, 0), Vector3(0, 0, 0)]
}

[sub_resource type="AnimationLibrary" id="AnimationLibrary_6wtcs"]
_data = {
&"RESET": SubResource("Animation_n4q1s"),
&"idle": SubResource("Animation_idle")
}

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_f4uy8"]
bg_color = Color(0.141176, 0.439216, 0.2, 0.878431)
border_width_left = 2
border_width_top = 2
border_width_right = 2
border_width_bottom = 2
border_color = Color(0.211765, 1, 0.341176, 1)
corner_radius_top_left = 8
corner_radius_top_right = 8
corner_radius_bottom_right = 8
corner_radius_bottom_left = 8

[node name="MainMenu" type="Control"]
layout_mode = 3
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
script = ExtResource("1_uxnqm")

[node name="Background" type="ColorRect" parent="."]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
color = Color(0.0784314, 0.0784314, 0.113725, 1)

[node name="BackgroundParticles" type="GPUParticles2D" parent="."]
position = Vector2(576, 648)
amount = 50
lifetime = 5.0
preprocess = 2.0
visibility_rect = Rect2(-600, -400, 1200, 800)
process_material = SubResource("ParticleProcessMaterial_ixnqj")

[node name="Logo" type="Label" parent="."]
layout_mode = 1
anchors_preset = 5
anchor_left = 0.5
anchor_right = 0.5
offset_left = -209.0
offset_top = 80.0
offset_right = 209.0
offset_bottom = 156.0
grow_horizontal = 2
theme_override_font_sizes/font_size = 72
text = "火焰冲突"
horizontal_alignment = 1

[node name="Subtitle" type="Label" parent="."]
layout_mode = 1
anchors_preset = 5
anchor_left = 0.5
anchor_right = 0.5
offset_left = -209.0
offset_top = 160.0
offset_right = 209.0
offset_bottom = 196.0
grow_horizontal = 2
theme_override_font_sizes/font_size = 24
text = "Flame Clash"
horizontal_alignment = 1

[node name="MenuContainer" type="VBoxContainer" parent="."]
layout_mode = 1
anchors_preset = 8
anchor_left = 0.5
anchor_top = 0.5
anchor_right = 0.5
anchor_bottom = 0.5
offset_left = -150.0
offset_top = -120.0
offset_right = 150.0
offset_bottom = 180.0
grow_horizontal = 2
grow_vertical = 2
theme_override_constants/separation = 20
alignment = 1

[node name="StartButton" type="Button" parent="MenuContainer"]
custom_minimum_size = Vector2(300, 60)
layout_mode = 2
theme_override_font_sizes/font_size = 24
theme_override_styles/hover = SubResource("StyleBoxFlat_q85co")
theme_override_styles/pressed = SubResource("StyleBoxFlat_p60wd")
theme_override_styles/normal = SubResource("StyleBoxFlat_q85co")
text = "开始游戏"

[node name="OptionsButton" type="Button" parent="MenuContainer"]
custom_minimum_size = Vector2(300, 60)
layout_mode = 2
theme_override_font_sizes/font_size = 24
theme_override_styles/hover = SubResource("StyleBoxFlat_options")
theme_override_styles/pressed = SubResource("StyleBoxFlat_options_pressed")
theme_override_styles/normal = SubResource("StyleBoxFlat_options")
text = "选项"

[node name="GalleryButton" type="Button" parent="MenuContainer"]
custom_minimum_size = Vector2(300, 60)
layout_mode = 2
theme_override_font_sizes/font_size = 24
theme_override_styles/hover = SubResource("StyleBoxFlat_gallery")
theme_override_styles/pressed = SubResource("StyleBoxFlat_gallery_pressed")
theme_override_styles/normal = SubResource("StyleBoxFlat_gallery")
text = "图鉴"

[node name="QuitButton" type="Button" parent="MenuContainer"]
custom_minimum_size = Vector2(300, 60)
layout_mode = 2
theme_override_font_sizes/font_size = 24
theme_override_styles/hover = SubResource("StyleBoxFlat_quit")
theme_override_styles/pressed = SubResource("StyleBoxFlat_quit_pressed")
theme_override_styles/normal = SubResource("StyleBoxFlat_quit")
text = "退出游戏"

[node name="CharacterViewport" type="SubViewportContainer" parent="."]
layout_mode = 1
anchors_preset = 3
anchor_left = 1.0
anchor_top = 1.0
anchor_right = 1.0
anchor_bottom = 1.0
offset_left = -400.0
offset_top = -400.0
grow_horizontal = 0
grow_vertical = 0
stretch = true

[node name="SubViewport" type="SubViewport" parent="CharacterViewport"]
transparent_bg = true
handle_input_locally = false
size = Vector2i(400, 400)
render_target_update_mode = 4

[node name="CharacterContainer" type="Node3D" parent="CharacterViewport/SubViewport"]

[node name="SophiaSkin" parent="CharacterViewport/SubViewport/CharacterContainer" instance=ExtResource("2_4dj54")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 0, -1, -3)

[node name="Camera3D" type="Camera3D" parent="CharacterViewport/SubViewport/CharacterContainer"]

[node name="DirectionalLight3D" type="DirectionalLight3D" parent="CharacterViewport/SubViewport/CharacterContainer"]
transform = Transform3D(0.866025, -0.433013, 0.25, 0, 0.5, 0.866025, -0.5, -0.75, 0.433013, 0, 5, 0)
shadow_enabled = true

[node name="AnimationPlayer" type="AnimationPlayer" parent="."]
libraries = {
&"": SubResource("AnimationLibrary_6wtcs")
}
autoplay = "idle"

[node name="AudioStreamPlayer" type="AudioStreamPlayer" parent="."]
autoplay = true

[node name="ButtonSound" type="AudioStreamPlayer" parent="."]
volume_db = -10.0

[node name="OptionsPanel" type="Panel" parent="."]
visible = false
layout_mode = 1
anchors_preset = 8
anchor_left = 0.5
anchor_top = 0.5
anchor_right = 0.5
anchor_bottom = 0.5
offset_left = -300.0
offset_top = -200.0
offset_right = 300.0
offset_bottom = 200.0
grow_horizontal = 2
grow_vertical = 2

[node name="OptionsTitle" type="Label" parent="OptionsPanel"]
layout_mode = 1
anchors_preset = 5
anchor_left = 0.5
anchor_right = 0.5
offset_left = -100.0
offset_top = 20.0
offset_right = 100.0
offset_bottom = 60.0
grow_horizontal = 2
theme_override_font_sizes/font_size = 28
text = "选项"
horizontal_alignment = 1

[node name="OptionsContainer" type="VBoxContainer" parent="OptionsPanel"]
layout_mode = 1
anchors_preset = 8
anchor_left = 0.5
anchor_top = 0.5
anchor_right = 0.5
anchor_bottom = 0.5
offset_left = -250.0
offset_top = -120.0
offset_right = 250.0
offset_bottom = 120.0
grow_horizontal = 2
grow_vertical = 2
theme_override_constants/separation = 20

[node name="MusicVolumeContainer" type="HBoxContainer" parent="OptionsPanel/OptionsContainer"]
layout_mode = 2

[node name="Label" type="Label" parent="OptionsPanel/OptionsContainer/MusicVolumeContainer"]
custom_minimum_size = Vector2(120, 0)
layout_mode = 2
text = "音乐音量"

[node name="MusicSlider" type="HSlider" parent="OptionsPanel/OptionsContainer/MusicVolumeContainer"]
custom_minimum_size = Vector2(0, 30)
layout_mode = 2
size_flags_horizontal = 3
size_flags_vertical = 4
max_value = 1.0
step = 0.01
value = 0.8

[node name="SFXVolumeContainer" type="HBoxContainer" parent="OptionsPanel/OptionsContainer"]
layout_mode = 2

[node name="Label" type="Label" parent="OptionsPanel/OptionsContainer/SFXVolumeContainer"]
custom_minimum_size = Vector2(120, 0)
layout_mode = 2
text = "音效音量"

[node name="SFXSlider" type="HSlider" parent="OptionsPanel/OptionsContainer/SFXVolumeContainer"]
custom_minimum_size = Vector2(0, 30)
layout_mode = 2
size_flags_horizontal = 3
size_flags_vertical = 4
max_value = 1.0
step = 0.01
value = 0.8

[node name="QualityContainer" type="HBoxContainer" parent="OptionsPanel/OptionsContainer"]
layout_mode = 2

[node name="Label" type="Label" parent="OptionsPanel/OptionsContainer/QualityContainer"]
custom_minimum_size = Vector2(120, 0)
layout_mode = 2
text = "画面质量"

[node name="QualityOption" type="OptionButton" parent="OptionsPanel/OptionsContainer/QualityContainer"]
layout_mode = 2
size_flags_horizontal = 3
selected = 1
item_count = 3
popup/item_0/text = "低"
popup/item_0/id = 0
popup/item_1/text = "中"
popup/item_1/id = 1
popup/item_2/text = "高"
popup/item_2/id = 2

[node name="LanguageContainer" type="HBoxContainer" parent="OptionsPanel/OptionsContainer"]
layout_mode = 2

[node name="Label" type="Label" parent="OptionsPanel/OptionsContainer/LanguageContainer"]
custom_minimum_size = Vector2(120, 0)
layout_mode = 2
text = "语言"

[node name="LanguageOption" type="OptionButton" parent="OptionsPanel/OptionsContainer/LanguageContainer"]
layout_mode = 2
size_flags_horizontal = 3
selected = 0
item_count = 2
popup/item_0/text = "中文"
popup/item_0/id = 0
popup/item_1/text = "English"
popup/item_1/id = 1

[node name="CloseOptionsButton" type="Button" parent="OptionsPanel"]
custom_minimum_size = Vector2(120, 40)
layout_mode = 1
anchors_preset = 7
anchor_left = 0.5
anchor_top = 1.0
anchor_right = 0.5
anchor_bottom = 1.0
offset_left = -60.0
offset_top = -60.0
offset_right = 60.0
offset_bottom = -20.0
grow_horizontal = 2
grow_vertical = 0
theme_override_font_sizes/font_size = 16
theme_override_styles/hover = SubResource("StyleBoxFlat_f4uy8")
theme_override_styles/pressed = SubResource("StyleBoxFlat_p60wd")
theme_override_styles/normal = SubResource("StyleBoxFlat_f4uy8")
text = "确定"

[node name="GalleryPanel" type="Panel" parent="."]
visible = false
layout_mode = 1
anchors_preset = 8
anchor_left = 0.5
anchor_top = 0.5
anchor_right = 0.5
anchor_bottom = 0.5
offset_left = -400.0
offset_top = -250.0
offset_right = 400.0
offset_bottom = 250.0
grow_horizontal = 2
grow_vertical = 2

[node name="GalleryTitle" type="Label" parent="GalleryPanel"]
layout_mode = 1
anchors_preset = 5
anchor_left = 0.5
anchor_right = 0.5
offset_left = -100.0
offset_top = 20.0
offset_right = 100.0
offset_bottom = 60.0
grow_horizontal = 2
theme_override_font_sizes/font_size = 28
text = "图鉴"
horizontal_alignment = 1

[node name="TabContainer" type="TabContainer" parent="GalleryPanel"]
layout_mode = 1
anchors_preset = 8
anchor_left = 0.5
anchor_top = 0.5
anchor_right = 0.5
anchor_bottom = 0.5
offset_left = -380.0
offset_top = -200.0
offset_right = 380.0
offset_bottom = 200.0
grow_horizontal = 2
grow_vertical = 2
current_tab = 0

[node name="角色" type="ScrollContainer" parent="GalleryPanel/TabContainer"]
layout_mode = 2
metadata/_tab_index = 0

[node name="CharacterGrid" type="GridContainer" parent="GalleryPanel/TabContainer/角色"]
layout_mode = 2
theme_override_constants/h_separation = 20
theme_override_constants/v_separation = 20
columns = 3

[node name="CharacterItem" type="VBoxContainer" parent="GalleryPanel/TabContainer/角色/CharacterGrid"]
layout_mode = 2

[node name="CharacterIcon" type="TextureRect" parent="GalleryPanel/TabContainer/角色/CharacterGrid/CharacterItem"]
custom_minimum_size = Vector2(200, 200)
layout_mode = 2
expand_mode = 1
stretch_mode = 5

[node name="CharacterName" type="Label" parent="GalleryPanel/TabContainer/角色/CharacterGrid/CharacterItem"]
layout_mode = 2
text = "索菲亚"
horizontal_alignment = 1

[node name="技能" type="ScrollContainer" parent="GalleryPanel/TabContainer"]
visible = false
layout_mode = 2
metadata/_tab_index = 1

[node name="SkillGrid" type="GridContainer" parent="GalleryPanel/TabContainer/技能"]
layout_mode = 2
theme_override_constants/h_separation = 20
theme_override_constants/v_separation = 20
columns = 3

[node name="SkillItem" type="VBoxContainer" parent="GalleryPanel/TabContainer/技能/SkillGrid"]
layout_mode = 2

[node name="SkillIcon" type="TextureRect" parent="GalleryPanel/TabContainer/技能/SkillGrid/SkillItem"]
custom_minimum_size = Vector2(200, 200)
layout_mode = 2
expand_mode = 1
stretch_mode = 5

[node name="SkillName" type="Label" parent="GalleryPanel/TabContainer/技能/SkillGrid/SkillItem"]
layout_mode = 2
text = "闪烁"
horizontal_alignment = 1

[node name="道具" type="ScrollContainer" parent="GalleryPanel/TabContainer"]
visible = false
layout_mode = 2
metadata/_tab_index = 2

[node name="ItemGrid" type="GridContainer" parent="GalleryPanel/TabContainer/道具"]
layout_mode = 2
theme_override_constants/h_separation = 20
theme_override_constants/v_separation = 20
columns = 3

[node name="ItemEntry" type="VBoxContainer" parent="GalleryPanel/TabContainer/道具/ItemGrid"]
layout_mode = 2

[node name="ItemIcon" type="TextureRect" parent="GalleryPanel/TabContainer/道具/ItemGrid/ItemEntry"]
custom_minimum_size = Vector2(200, 200)
layout_mode = 2
expand_mode = 1
stretch_mode = 5

[node name="ItemName" type="Label" parent="GalleryPanel/TabContainer/道具/ItemGrid/ItemEntry"]
layout_mode = 2
text = "汽油"
horizontal_alignment = 1

[node name="CloseGalleryButton" type="Button" parent="GalleryPanel"]
custom_minimum_size = Vector2(120, 40)
layout_mode = 1
anchors_preset = 7
anchor_left = 0.5
anchor_top = 1.0
anchor_right = 0.5
anchor_bottom = 1.0
offset_left = -60.0
offset_top = -60.0
offset_right = 60.0
offset_bottom = -20.0
grow_horizontal = 2
grow_vertical = 0
theme_override_font_sizes/font_size = 16
theme_override_styles/hover = SubResource("StyleBoxFlat_f4uy8")
theme_override_styles/pressed = SubResource("StyleBoxFlat_p60wd")
theme_override_styles/normal = SubResource("StyleBoxFlat_f4uy8")
text = "返回"
