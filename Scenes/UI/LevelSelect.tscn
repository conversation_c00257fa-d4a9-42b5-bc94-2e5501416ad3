[gd_scene load_steps=15 format=3 uid="uid://dxnvp4eqfq8ov"]

[ext_resource type="Script" path="res://Scripts/UI/LevelSelect.gd" id="1_uxnqm"]
[ext_resource type="PackedScene" uid="uid://b8yvt1ykfqvqm" path="res://Scenes/UI/SkillConfigButton.tscn" id="2_skill_config"]

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_q85co"]
bg_color = Color(0.141176, 0.439216, 0.2, 0.878431)
border_width_left = 2
border_width_top = 2
border_width_right = 2
border_width_bottom = 2
border_color = Color(0.211765, 1, 0.341176, 1)
corner_radius_top_left = 8
corner_radius_top_right = 8
corner_radius_bottom_right = 8
corner_radius_bottom_left = 8

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_p60wd"]
bg_color = Color(0.0784314, 0.266667, 0.121569, 0.878431)
border_width_left = 2
border_width_top = 2
border_width_right = 2
border_width_bottom = 2
border_color = Color(0.211765, 1, 0.341176, 1)
corner_radius_top_left = 8
corner_radius_top_right = 8
corner_radius_bottom_right = 8
corner_radius_bottom_left = 8

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_f4uy8"]
bg_color = Color(0.141176, 0.439216, 0.2, 0.878431)
border_width_left = 2
border_width_top = 2
border_width_right = 2
border_width_bottom = 2
border_color = Color(0.211765, 1, 0.341176, 1)
corner_radius_top_left = 8
corner_radius_top_right = 8
corner_radius_bottom_right = 8
corner_radius_bottom_left = 8

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_level1"]
bg_color = Color(0.141176, 0.341176, 0.439216, 0.878431)
border_width_left = 2
border_width_top = 2
border_width_right = 2
border_width_bottom = 2
border_color = Color(0.211765, 0.341176, 1, 1)
corner_radius_top_left = 8
corner_radius_top_right = 8
corner_radius_bottom_right = 8
corner_radius_bottom_left = 8

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_level1_pressed"]
bg_color = Color(0.0784314, 0.121569, 0.266667, 0.878431)
border_width_left = 2
border_width_top = 2
border_width_right = 2
border_width_bottom = 2
border_color = Color(0.211765, 0.341176, 1, 1)
corner_radius_top_left = 8
corner_radius_top_right = 8
corner_radius_bottom_right = 8
corner_radius_bottom_left = 8

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_level2"]
bg_color = Color(0.439216, 0.341176, 0.141176, 0.878431)
border_width_left = 2
border_width_top = 2
border_width_right = 2
border_width_bottom = 2
border_color = Color(1, 0.843137, 0.211765, 1)
corner_radius_top_left = 8
corner_radius_top_right = 8
corner_radius_bottom_right = 8
corner_radius_bottom_left = 8

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_level2_pressed"]
bg_color = Color(0.266667, 0.196078, 0.0784314, 0.878431)
border_width_left = 2
border_width_top = 2
border_width_right = 2
border_width_bottom = 2
border_color = Color(1, 0.843137, 0.211765, 1)
corner_radius_top_left = 8
corner_radius_top_right = 8
corner_radius_bottom_right = 8
corner_radius_bottom_left = 8

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_demo"]
bg_color = Color(0.439216, 0.137255, 0.2, 0.878431)
border_width_left = 2
border_width_top = 2
border_width_right = 2
border_width_bottom = 2
border_color = Color(1, 0.356863, 0.211765, 1)
corner_radius_top_left = 8
corner_radius_top_right = 8
corner_radius_bottom_right = 8
corner_radius_bottom_left = 8

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_demo_pressed"]
bg_color = Color(0.266667, 0.0784314, 0.121569, 0.878431)
border_width_left = 2
border_width_top = 2
border_width_right = 2
border_width_bottom = 2
border_color = Color(1, 0.356863, 0.211765, 1)
corner_radius_top_left = 8
corner_radius_top_right = 8
corner_radius_bottom_right = 8
corner_radius_bottom_left = 8

[sub_resource type="Gradient" id="Gradient_yvnqm"]
offsets = PackedFloat32Array(0, 0.236287, 0.338144, 0.470103, 0.738144, 1)
colors = PackedColorArray(0, 0, 0, 1, 1, 0.28125, 0, 1, 1, 0.962646, 0.402344, 0.990493, 1, 0.140625, 0, 0.998312, 0, 0, 0, 1, 0, 0, 0, 0)

[sub_resource type="GradientTexture1D" id="GradientTexture1D_ixnqj"]
gradient = SubResource("Gradient_yvnqm")

[sub_resource type="ParticleProcessMaterial" id="ParticleProcessMaterial_ixnqj"]
emission_shape = 3
emission_box_extents = Vector3(600, 400, 1)
particle_flag_disable_z = true
direction = Vector3(0, -1, 0)
spread = 10.0
gravity = Vector3(0, 0, 0)
initial_velocity_min = 20.0
initial_velocity_max = 40.0
orbit_velocity_min = 0.0
orbit_velocity_max = 0.0
scale_min = 0.5
scale_max = 2.0
color_ramp = SubResource("GradientTexture1D_ixnqj")

[node name="LevelSelect" type="Control"]
layout_mode = 3
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
script = ExtResource("1_uxnqm")

[node name="Background" type="ColorRect" parent="."]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
color = Color(0.0784314, 0.0784314, 0.113725, 1)

[node name="BackgroundParticles" type="GPUParticles2D" parent="."]
position = Vector2(576, 648)
amount = 50
process_material = SubResource("ParticleProcessMaterial_ixnqj")
lifetime = 5.0
preprocess = 2.0
visibility_rect = Rect2(-600, -400, 1200, 800)

[node name="Title" type="Label" parent="."]
layout_mode = 1
anchors_preset = 5
anchor_left = 0.5
anchor_right = 0.5
offset_left = -209.0
offset_top = 80.0
offset_right = 209.0
offset_bottom = 156.0
grow_horizontal = 2
theme_override_font_sizes/font_size = 48
text = "选择关卡"
horizontal_alignment = 1

[node name="LevelsContainer" type="GridContainer" parent="."]
layout_mode = 1
anchors_preset = 8
anchor_left = 0.5
anchor_top = 0.5
anchor_right = 0.5
anchor_bottom = 0.5
offset_left = -400.0
offset_top = -150.0
offset_right = 400.0
offset_bottom = 150.0
grow_horizontal = 2
grow_vertical = 2
theme_override_constants/h_separation = 40
theme_override_constants/v_separation = 40
columns = 2

[node name="Level1Button" type="Button" parent="LevelsContainer"]
custom_minimum_size = Vector2(380, 140)
layout_mode = 2
theme_override_font_sizes/font_size = 24
theme_override_styles/normal = SubResource("StyleBoxFlat_level1")
theme_override_styles/hover = SubResource("StyleBoxFlat_level1")
theme_override_styles/pressed = SubResource("StyleBoxFlat_level1_pressed")

[node name="VBoxContainer" type="VBoxContainer" parent="LevelsContainer/Level1Button"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
alignment = 1

[node name="LevelName" type="Label" parent="LevelsContainer/Level1Button/VBoxContainer"]
layout_mode = 2
theme_override_font_sizes/font_size = 28
text = "第一关"
horizontal_alignment = 1

[node name="LevelDescription" type="Label" parent="LevelsContainer/Level1Button/VBoxContainer"]
layout_mode = 2
theme_override_font_sizes/font_size = 18
text = "基础关卡"
horizontal_alignment = 1

[node name="Level2Button" type="Button" parent="LevelsContainer"]
custom_minimum_size = Vector2(380, 140)
layout_mode = 2
theme_override_font_sizes/font_size = 24
theme_override_styles/normal = SubResource("StyleBoxFlat_level2")
theme_override_styles/hover = SubResource("StyleBoxFlat_level2")
theme_override_styles/pressed = SubResource("StyleBoxFlat_level2_pressed")

[node name="VBoxContainer" type="VBoxContainer" parent="LevelsContainer/Level2Button"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
alignment = 1

[node name="LevelName" type="Label" parent="LevelsContainer/Level2Button/VBoxContainer"]
layout_mode = 2
theme_override_font_sizes/font_size = 28
text = "第二关"
horizontal_alignment = 1

[node name="LevelDescription" type="Label" parent="LevelsContainer/Level2Button/VBoxContainer"]
layout_mode = 2
theme_override_font_sizes/font_size = 18
text = "进阶挑战"
horizontal_alignment = 1

[node name="DemoLevelButton" type="Button" parent="LevelsContainer"]
custom_minimum_size = Vector2(380, 140)
layout_mode = 2
theme_override_font_sizes/font_size = 24
theme_override_styles/normal = SubResource("StyleBoxFlat_demo")
theme_override_styles/hover = SubResource("StyleBoxFlat_demo")
theme_override_styles/pressed = SubResource("StyleBoxFlat_demo_pressed")

[node name="VBoxContainer" type="VBoxContainer" parent="LevelsContainer/DemoLevelButton"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
alignment = 1

[node name="LevelName" type="Label" parent="LevelsContainer/DemoLevelButton/VBoxContainer"]
layout_mode = 2
theme_override_font_sizes/font_size = 28
text = "演示关卡"
horizontal_alignment = 1

[node name="LevelDescription" type="Label" parent="LevelsContainer/DemoLevelButton/VBoxContainer"]
layout_mode = 2
theme_override_font_sizes/font_size = 18
text = "包含所有功能"
horizontal_alignment = 1

[node name="AllModulesButton" type="Button" parent="LevelsContainer"]
custom_minimum_size = Vector2(380, 140)
layout_mode = 2
theme_override_font_sizes/font_size = 24
theme_override_styles/normal = SubResource("StyleBoxFlat_level1")
theme_override_styles/hover = SubResource("StyleBoxFlat_level1")
theme_override_styles/pressed = SubResource("StyleBoxFlat_level1_pressed")

[node name="VBoxContainer" type="VBoxContainer" parent="LevelsContainer/AllModulesButton"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
alignment = 1

[node name="LevelName" type="Label" parent="LevelsContainer/AllModulesButton/VBoxContainer"]
layout_mode = 2
theme_override_font_sizes/font_size = 28
text = "全模块关卡"
horizontal_alignment = 1

[node name="LevelDescription" type="Label" parent="LevelsContainer/AllModulesButton/VBoxContainer"]
layout_mode = 2
theme_override_font_sizes/font_size = 18
text = "包含所有生成器模块"
horizontal_alignment = 1

[node name="BackButton" type="Button" parent="."]
custom_minimum_size = Vector2(200, 60)
layout_mode = 1
anchors_preset = 7
anchor_left = 0.5
anchor_top = 1.0
anchor_right = 0.5
anchor_bottom = 1.0
offset_left = -100.0
offset_top = -100.0
offset_right = 100.0
offset_bottom = -40.0
grow_horizontal = 2
grow_vertical = 0
theme_override_font_sizes/font_size = 24
theme_override_styles/normal = SubResource("StyleBoxFlat_q85co")
theme_override_styles/hover = SubResource("StyleBoxFlat_q85co")
theme_override_styles/pressed = SubResource("StyleBoxFlat_p60wd")
text = "返回"

[node name="AudioStreamPlayer" type="AudioStreamPlayer" parent="."]
autoplay = true
bus = &"Music"

[node name="ButtonSound" type="AudioStreamPlayer" parent="."]
volume_db = -10.0
bus = &"SFX"

[node name="SkillConfigButton" parent="." instance=ExtResource("2_skill_config")]
layout_mode = 1
anchors_preset = 1
anchor_left = 1.0
anchor_right = 1.0
offset_left = -120.0
offset_top = 20.0
offset_right = -20.0
offset_bottom = 60.0
grow_horizontal = 0
