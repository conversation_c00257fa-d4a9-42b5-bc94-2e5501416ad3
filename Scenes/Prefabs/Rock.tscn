[gd_scene load_steps=6 format=4 uid="uid://b5dqjsb63wbhl"]

[ext_resource type="Texture2D" uid="uid://w3jfu3qn6486" path="res://Assets/character/Textures/colormap.png" id="1_ekkuk"]

[sub_resource type="StandardMaterial3D" id="StandardMaterial3D_dokly"]
resource_name = "colormap"
cull_mode = 2
albedo_texture = ExtResource("1_ekkuk")
texture_filter = 2

[sub_resource type="ArrayMesh" id="ArrayMesh_26jjw"]
_surfaces = [{
"aabb": AABB(-0.5, 0, -0.5, 1, 0.5, 1),
"format": 34359742465,
"index_count": 570,
"index_data": PackedByteArray("AwAAAEkASQAAAAEAAQBKAEkASgABAAIASQBLAAMAAgBMAEoASQBKAEwATAACAAMATABLAEkAAwBLAEwATQAGAAcABgBNAE4ATgAFAAYABwBPAE0ABQBOAFAAUABOAE0ATQBPAFAAUABPAAcAUAAEAAUABwAEAFAAUQBSAFMACgBTAFIACQBSAFEAUgAJAAoAUQAIAAkAVABTAAoAUwBUAFEACABRAFQACgALAFQAVAALAAgADgAPAFUAVQAPAAwAVQBWAA4ADgBWAFcAVwANAA4AVgBVAFgAWABXAFYADABYAFUADQBXAFgAWAAMAA0AEgATAFkAWQATABAAWQBaABIAEgBaAFsAWwARABIAWgBZAFwAXABbAFoAEABcAFkAEQBbAFwAXAAQABEALQAiACMAIwAiACYAIgApACYAJgApAEQARAApACIAJgBEACgAIgBdAEQAJwAoAEQARABdACcAXQAiAEUAJwBdACoAXQBFACoAXgAmACgAQwAoACcAKABDAF4AXgBDAF8AXwBCAF4AJQBfAEMAQgBfACUAJwAlAEMAXgBCAEEAQQAmAF4AJQBBAEIAJgBBACMAYAAlACcAJwBAAGAAPwBAACcAJwAqAD8AYQBgAEAAQAA/AGEAJQBgAGEAYQA/ACoAYQAkACUAKgAkAGEAJQAkAEEAJAAqACsAJAAwAEEAKwAqAEUAMAAkAGIAYgAkACsAKwBIAGIAMABiAEgARwBIACsASABHADAARQBGACsAKwBGAEcALABGAEUARgBjAEcARgAsAGMAMABHAGMARQAfACwAYwAsAB8AIgAfAEUAYwBkADAAMABkAEEAZABjAB8AZAAjAEEAIgAtAB8AZAAdACMAHwAdAGQAHQBlACMAIwBlAC0AHQAfAC4ALQAuAB8AHQBmAGUALgBmAB0ALQBlAGcAZgBnAGUALQAvAC4AZwAvAC0AZgAuAGgALwBoAC4AZgAxAGcAaAAxAGYALwBnABwAMQAcAGcALwAeAGgAHAAeAC8AFwAxAGgAMQAXABwAaAAaABcAHgAaAGgAFwBpABwAHgAcAGkAFwAzAGkAaQAyAB4AFwAaADQANAAzABcAMwA4AGkAMgBpADgAMgAhAB4ANQAeACEAHgA1ABoANgAhADIAIQA2ADUAMgAgADYANQA2ACAAOAA3ADIAMgA3ACAAagA3ADgANwBqACAAOAA5AGoAIABqADkAOQA4ABQAMwAUADgAGwAgADkAIAAbADUAOQAUABsANQBrABoANQAbAGsAGgBrAD4APgAZABoAGgAZADQANAAZAD4AGwAWAGsAPgBrABYAPgAYADQAFgAYAD4AGAA7ADQAMwA0ADsAGABsADsAOwA6ADMAMwA6ABQAOwBsAG0AbQA6ADsAbAAYABUAFQBtAGwAOgBtABUAOgAVABQAGAAWABUAPQAVABYAbgAUABUAFQA9AG4AGwAUAG4AFgBvAD0AbgA9AG8AGwA8ABYAbgA8ABsAPABvABYAbwA8AG4A"),
"lods": [0.0752736, PackedByteArray("AAABAAIAAgADAAAABAAFAAYABgAHAAQACAAJAAoACgALAAgADAANAA4ADgAPAAwAEAARABIAEgATABAALQAiACMAIgAtAB8ALQAjABwALQAuAB8ALQAvAC4AHAAvAC0ALwAaAC4AHQAfAC4AHQAuABoAHQAcACMAHwAdADAAMAAdACMAHQAxABwAGgAxAB0AHAAeAC8ALwAeABoAMQAXABwAFwAxABoAHAAyAB4AFwAzABwAMgAhAB4AFwAaADQANAAzABcANQAeACEAHgA1ABoAIQA2ADUANgAhADIANQA2ACAAMgAgADYANQAbABoAIAAbADUAMgA3ACAAOAA3ADIAIAA3ADgAMgAcADgAMwA4ABwAOAA5ACAAGwAgADkAOQA4ABQAOQAUABsAMwAUADgAMwA6ABQAOwA6ADMAMwA0ADsAFQA6ADsAOgAVABQAGAA7ADQAOwAYABUAFAA8ABsAGwA8ABYAFgA8ABQAGAAWABUAFQA9ABQAFAA9ABYAPQAVABYAGwAWABoAFgAYAD4APgAaABYAPgAYADQAGgAZADQANAAZAD4APgAZABoAJAA/ACoAQAA/ACQAPwBAACcAJwAqAD8AJAAlAEAAJwBAACUAJAAqACsAJQAkAEEAJwAiACoAJQBBAEIAJQBCAEMAJwAlAEMAQwBCAEEAQwAoACcAJAAwAEEAMAAkACsAQQAmAEMAQwAmACgAMAAjAEEAJgBBACMAIwAiACYAJwAoAEQAJgBEACgARAAiACcAJgApAEQARAApACIAIgApACYAIgBFACoAKwAqAEUAIgAfAEUARQBGACsARQAfACwALABGAEUAMAAsAB8ARgAsAEcAMABHACwAKwBGAEcASABHADAARwBIACsAKwBIADAA"), 0.26823, PackedByteArray("AAABAAIAAgADAAAABAAFAAYABgAHAAQACAAJAAoACgALAAgADAANAA4ADgAPAAwAEAARABIAEgATABAAFAAVABYAFwAVABQAGAAWABUAGAAVABcAFgAYABkAGgAZABgAGQAaABYAFwAaABgAFgAbABQAGwAWABoAFwAUABwAHAAUABsAGgAXAB0AHQAXABwAHgAbABoAHQAfABoAHgAaAB8AGwAgABwAHAAgAB4AGwAhACAAIAAhAB4AGwAeACEAHAAeACIAIgAeAB8AHQAcACMAIgAjABwAJAAdACMAHwAdACQAJQAkACMAJQAjACYAIwAiACYAJwAlACYAJAAlACcAJgAoACcAKAAiACcAJgApACgAKAApACIAIgApACYAJwAiACoAIgAfACoAJwAqACQAKwAqAB8AJAAqACsALAArAB8AKwAsACQAJAAsAB8A")],
"name": "colormap",
"primitive": 3,
"uv_scale": Vector4(0, 0, 0, 0),
"vertex_count": 112,
"vertex_data": PackedByteArray("FqK2Ps3MzD7kOB49OdKVPs3MzD52ctU648OoPs3MzD5X03+9wJPJPs3MzD4PjNC8nIlvPs3MzD7LuTy98lcmPs3MzD5gloW9sPQ5Ps3MzD7a/Au+LZOBPs3MzD44wPC9/9Xrvc3MzD5c5I4+fCQdvs3MzD5mZQs+eBIsvM3MzD7UV8g9nMLjPM3MzD4+j3Y+vhRjvc3MzD7TT56+B1G9vc3MzD6wH7++TEbovM3MzD5bEdK+sKgNPM3MzD59QbG+0CXqPM3MzD4rINY+wH8aPM3MzD5Wh7E+TLOlPc3MzD73uKc+x+zMPc3MzD7MUcw+ZmbmvgAAAACamRk+ZmbmvgAAAACamRm+Zmbmvs3MzD6amRm+mpkZvgAAAABmZua+ZmbmvgAAAD+amRm+mpkZvgAAAD9mZua+mpkZvs3MzD5mZua+Zmbmvs3MzD6amRk+mpkZvgAAAABmZuY+mpkZPgAAAABmZua+mpkZvs3MzD5mZuY+mpkZPs3MzD5mZua+ZmbmvmZm5j6amRk+mpkZvmZm5j5mZuY+mpkZPs3MzD5mZuY+mpkZPgAAAABmZuY+ZmbmPgAAAACamRm+ZmbmPgAAAACamRk+mpmZPgAAAD8AAAA/ZmbmPs3MzD6amRk+ZmbmPgAAAD+amRk+mpkZPgAAAD9mZuY+ZmbmPs3MzD6amRm+ZmbmPmZm5j6amRm+mpkZPmZm5j5mZua+zczMPc3MzD4AAAA/zczMPc3MzD4AAAC/zczMvc3MzD4AAAA/ZmbmPgAAAABmZua+zczMvQAAAAAAAAC/mpmZvmZm5j4AAAA/mpmZvgAAAAAAAAC/mpmZvgAAAD8AAAC/zcxMvs3MzD7NzEw+zcxMvmZm5j7NzEw+ZmbmvmZm5j5mZuY+ZmbmvgAAAABmZuY+AAAAvwAAAACamZk+ZmbmvgAAAABmZua+ZmbmvgAAAD9mZua+AAAAv83MzD7NzMw9AAAAvwAAAADNzMy9zcxMvgAAAD/NzEy+AAAAP83MzD7NzMy9AAAAP83MzD7NzMw9mpmZPgAAAAAAAAA/ZmbmPgAAAABmZuY+AAAAPwAAAD+amZk+zcxMPgAAAD/NzEw+zcxMPs3MzD7NzEy+zcxMPmZm5j7NzEy+ZmbmPmZm5j5mZua+AAAAP2Zm5j6amZm+UOa0Pne+3z657tQ8akqcPne+3z70U+a6jxvDPne+3z6oz7S8qX+qPne+3z7PEUy9TadAPne+3z4NYwC+v/ExPne+3z6c+5K9jIx3Pne+3z7+WuO9/tZoPne+3z79IGu9iwvRvXe+3z6PSoM+4vAFvne+3z6gyhg+FDPBvHe+3z4Gv/Y9UJeoO3e+3z4CKmk+4LqCu3e+3z5D/bK+WgECvXe+3z4qmcu+RXCjvXe+3z7qY72+jjZVvXe+3z4EyKS+Lbm1PXe+3z5++Mg+EE6YPXe+3z7dha0+RA6qPHe+3z6l4LQ+XN0PPXe+3z5EU9A+zcxMPs3MzD7NzEw+ZmbmPgAAAD9mZuY+AAAAPwAAAACamZk+AAAAPwAAAADNzMw9AAAAPwAAAADNzMy9AAAAPwAAAACamZm+mpmZPmZm5j4AAAC/mpmZPgAAAAAAAAC/zczMPQAAAAAAAAA/zczMPQAAAAAAAAC/zczMvQAAAAAAAAA/zczMvc3MzD4AAAC/mpmZvgAAAAAAAAA/AAAAv2Zm5j6amZk+zcxMvs3MzD7NzEy+AAAAvwAAAD+amZm+AAAAvwAAAACamZm+AAAAvwAAAADNzMw9AAAAv83MzD7NzMy9")
}]
blend_shape_mode = 0

[sub_resource type="ArrayMesh" id="ArrayMesh_gim66"]
resource_name = "rocks_rocks"
_surfaces = [{
"aabb": AABB(0, 0, 0, 0, 0, 0),
"format": 34359738368,
"material": SubResource("StandardMaterial3D_dokly"),
"name": "colormap",
"primitive": 5,
"uv_scale": Vector4(0, 0, 0, 0),
"vertex_count": 0,
"vertex_data": PackedByteArray()
}]
blend_shape_mode = 0
shadow_mesh = SubResource("ArrayMesh_26jjw")

[sub_resource type="BoxShape3D" id="BoxShape3D_lf0x5"]
size = Vector3(1, 0.50293, 1)

[node name="Rock" type="StaticBody3D"]
collision_layer = 2
collision_mask = 0

[node name="rocks" type="MeshInstance3D" parent="."]
mesh = SubResource("ArrayMesh_gim66")
skeleton = NodePath("")

[node name="LOD" type="Node" parent="."]

[node name="HighDetail" type="MeshInstance3D" parent="LOD"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 0, 0.25, 0)
visible = false

[node name="CollisionShape3D" type="CollisionShape3D" parent="."]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 0, 0.248535, 0)
shape = SubResource("BoxShape3D_lf0x5")
