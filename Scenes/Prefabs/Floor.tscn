[gd_scene load_steps=4 format=3 uid="uid://c60avs3xicxfp"]

[sub_resource type="StandardMaterial3D" id="StandardMaterial3D_iauxi"]
albedo_color = Color(0.626654, 0.501327, 0.190292, 1)
metallic = 0.2
roughness = 0.8
uv1_scale = Vector3(10, 10, 1)

[sub_resource type="BoxMesh" id="BoxMesh_iauxi"]
size = Vector3(1, 0.1, 1)

[sub_resource type="BoxShape3D" id="BoxShape3D_jmowq"]
size = Vector3(10, 0.1, 10)

[node name="Floor" type="Node3D" groups=["floor"]]

[node name="StaticBody3D" type="StaticBody3D" parent="."]

[node name="MeshInstance3D" type="MeshInstance3D" parent="StaticBody3D"]
material_override = SubResource("StandardMaterial3D_iauxi")
mesh = SubResource("BoxMesh_iauxi")

[node name="CollisionShape3D" type="CollisionShape3D" parent="StaticBody3D"]
shape = SubResource("BoxShape3D_jmowq")
