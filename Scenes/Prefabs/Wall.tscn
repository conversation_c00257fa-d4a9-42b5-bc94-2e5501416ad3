[gd_scene load_steps=3 format=3 uid="uid://dwusy8dd8usvo"]

[ext_resource type="PackedScene" uid="uid://c7bwqubk084hh" path="res://Assets/rocks/rock.gltf" id="1_bmn3o"]

[sub_resource type="BoxShape3D" id="BoxShape3D_0fcuj"]
size = Vector3(5, 4, 3.5)

[node name="Wall" type="StaticBody3D"]

[node name="rock" parent="." instance=ExtResource("1_bmn3o")]

[node name="CollisionShape3D" type="CollisionShape3D" parent="."]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 2, 2, 0)
shape = SubResource("BoxShape3D_0fcuj")
