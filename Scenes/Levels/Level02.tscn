[gd_scene load_steps=24 format=3 uid="uid://dtgsem6v7vcof"]

[ext_resource type="Script" uid="uid://bo0h8t35yls55" path="res://Scripts/Levels/Level01.gd" id="1_ttrr8"]
[ext_resource type="PackedScene" uid="uid://d2xyw4d37ymv4" path="res://Scenes/Minimap.tscn" id="2_yij7j"]
[ext_resource type="PackedScene" uid="uid://ctxvyh1qr52ue" path="res://Scenes/PlayerAvatar.tscn" id="3_s4q4e"]
[ext_resource type="PackedScene" uid="uid://do1sypgxbecbd" path="res://Scenes/ItemBoxes.tscn" id="4_6mqy0"]
[ext_resource type="PackedScene" uid="uid://b4l4phqqbvprm" path="res://Scenes/SkillBoxUI.tscn" id="5_6cewc"]
[ext_resource type="Script" uid="uid://f54r1khulcvh" path="res://Scripts/ModularLevelGenerator.gd" id="6_yber6"]
[ext_resource type="Resource" uid="uid://cvji54t3pjwfe" path="res://Resources/LevelConfigs/AllModulesConfig.tres" id="7_itug7"]
[ext_resource type="PackedScene" uid="uid://b88l8pk1ebe1x" path="res://Scenes/player.tscn" id="8_81wtm"]
[ext_resource type="PackedScene" uid="uid://rvgn0irsuwao" path="res://Scenes/EnemyBoy01.tscn" id="9_fbsmd"]
[ext_resource type="PackedScene" uid="uid://d2b5h1mlxxy7d" path="res://Scenes/FogOfWar.tscn" id="10_n6yg5"]
[ext_resource type="PackedScene" uid="uid://cbg81yijaaxk5" path="res://Scenes/Gas.tscn" id="11_f1fl4"]
[ext_resource type="PackedScene" uid="uid://dwusy8dd8usvo" path="res://Scenes/Prefabs/Wall.tscn" id="12_rhbo5"]
[ext_resource type="PackedScene" uid="uid://crtnthqkksmri" path="res://Scenes/Prefabs/Tree.tscn" id="13_y7831"]
[ext_resource type="PackedScene" uid="uid://c3h8fj2xsp5oy" path="res://Scenes/Prefabs/Chest.tscn" id="16_hrvyy"]
[ext_resource type="PackedScene" uid="uid://b5dqjsb63wbhl" path="res://Scenes/Prefabs/Rock.tscn" id="17_r7f8x"]
[ext_resource type="PackedScene" uid="uid://bdq3b4e0mlgo4" path="res://Scenes/Prefabs/Barrel.tscn" id="18_78r4o"]
[ext_resource type="PackedScene" uid="uid://c6k7j3t3flhst" path="res://Scenes/Prefabs/Decoration.tscn" id="19_atd4b"]

[sub_resource type="ProceduralSkyMaterial" id="ProceduralSkyMaterial_gbvua"]
sky_horizon_color = Color(0.662243, 0.671743, 0.686743, 1)
ground_horizon_color = Color(0.662243, 0.671743, 0.686743, 1)

[sub_resource type="Sky" id="Sky_m0r78"]
sky_material = SubResource("ProceduralSkyMaterial_gbvua")

[sub_resource type="Environment" id="Environment_j4i7h"]
background_mode = 2
sky = SubResource("Sky_m0r78")
tonemap_mode = 2
glow_enabled = true

[sub_resource type="NavigationMesh" id="NavigationMesh_p3na8"]
vertices = PackedVector3Array(-48.75, 8.4, -51.5, -48.75, 8.4, -53.75, -51, 8.4, -53.75, -51, 8.4, -49, -48.5, 8.4, -48.75, 34.75, 8.4, -48.75, 34.75, 8.4, -51, 23, 8.4, -51, 22.75, 8.4, -48.75, -48.25, 8.4, -51, -36.75, 8.4, -48.75, -36.5, 8.4, -51, 11, 8.4, -51, 10.75, 8.4, -48.75, -24.75, 8.4, -48.75, -24.5, 8.4, -51, 46.75, 8.4, -48.75, 46.75, 8.4, -51, -13, 8.4, -48.75, -12.75, 8.4, -51, -0.75, 8.4, -51, -1, 8.4, -48.75, 49, 8.4, -48.25, 49, 8.4, -46.5, 51.25, 8.4, -46.75, 51.5, 8.4, -48.75, 53.75, 8.4, -48.75, 53.75, 8.4, -51, 51.75, 8.4, -51, 48.5, 8.4, -48.75, 48.5, 8.4, -51, 51.25, 8.4, -51.5, 49, 8.4, -51.5, 51.25, 8.4, -53.75, 49, 8.4, -53.75, -49.75, 8.4, 51, -46.25, 8.4, 51, -46.25, 8.4, 49, -48.25, 8.4, 49, -50.25, 8.4, 50, -48.75, 8.4, -42.75, -51, 8.4, -43, -51, 8.4, -36.75, -48.75, 8.4, -36.75, -48.75, 8.4, 48.5, -51, 8.4, 50, -48.75, 8.4, 42.25, -51, 8.4, 43.75, -48.75, 8.4, -6.5, -48.75, 8.4, -12.5, -51, 8.4, -12, -51, 8.4, -5.75, -48.75, 8.4, -0.25, -51, 8.4, 0.5, -51, 8.4, 6.5, -48.75, 8.4, 11.75, -51, 8.4, 37.5, -48.75, 8.4, 36.25, -48.75, 8.4, -30.75, -51, 8.4, -30.5, -51, 8.4, 31.25, -48.75, 8.4, 30, -48.75, 8.4, -24.5, -51, 8.4, -24.25, -51, 8.4, -18.25, -51, 8.4, 25.25, -48.75, 8.4, 24, -51, 8.4, 19, -48.75, 8.4, 17.75, -51, 8.4, 12.75, -39.75, 0.4, -47.5, -47.5, 0.4, -47.5, -47.5, 0.4, -36.25, -47.5, 0.4, -25, -33.5, 0.4, -25, -33.5, 0.4, -26, -31.75, 0.4, -26.75, -31.75, 0.4, -47.5, -30.25, 0.4, -25.5, -12.75, 0.4, -25.75, -12.75, 0.4, -27, -30.75, 0.4, -26.5, -11.75, 0.4, -27.25, -11.75, 0.4, -47.5, -21.75, 0.4, -47.5, -24.7394, 0.9, -36.5147, -24.7545, 0.4, -38.0135, -24.7743, 0.4, -35.0158, -23.2393, 0.4, -35.0048, -10.75, 0.4, -27.25, -10.5, 0.4, -26.75, -4.25, 0.4, -27, -4, 0.4, -28.25, -2.25, 0.4, -28.75, 4.75, 0.4, -36.75, 4.25, 0.4, -37.25, 4.25, 0.4, -39.5, 5.75, 0.4, -40, 5.75, 0.4, -47.5, -3, 0.4, -47.5, -6.74466, 0.9, -39.5174, -5.25965, 0.4, -39.5064, -2.26912, 0.9, -32.0229, -5.22943, 0.4, -36.5086, -6.77476, 0.4, -41.0053, -3.75413, 0.4, -31.9839, -0.729191, 0.4, -33.4889, -8.22968, 0.4, -39.4783, -2.25905, 0.4, -30.507, 7.5, 0.4, -39.25, 7.5, 0.4, -37.5, 11.75, 0.4, -37.25, 12.5, 0.4, -38.25, 6.75, 0.4, -40, 13.25, 0.4, -38.25, 13.25, 0.4, -47.5, 14.75, 0.4, -35.75, 18, 0.4, -29, 19.25, 0.4, -29, 30.5, 0.4, -47.5, 21.75, 0.4, -47.5, 14.75, 0.4, -37.5, 14, 0.4, -38.25, 47.75, 0.4, -37.75, 47.75, 0.4, -47.5, 39, 0.4, -47.5, 19.75, 0.4, -27.75, 47.75, 0.4, -27.75, 36.7251, 0.9, -44.0151, 36.7718, 0.9, -29.0099, 36.7405, 0.4, -39.4965, 35.2368, 0.4, -29.0209, 38.2568, 0.4, -28.9989, 36.7266, 0.4, -30.4967, 38.2402, 0.4, -42.5162, 36.7552, 0.4, -42.4772, 36.765, 0.4, -45.481, 51.25, 8.4, -35.25, 49, 8.4, -35, 49, 8.4, 23.25, 49, 8.4, 35, 51.25, 8.4, 35, 51.25, 8.4, 23.25, 51.25, 8.4, -23.5, 49, 8.4, -23.25, 49, 8.4, 0, 49, 8.4, 11.5, 51.25, 8.4, 11.5, 51.25, 8.4, 0, 51.25, 8.4, -11.75, 49, 8.4, -11.75, 49, 8.4, 46.75, 51.25, 8.4, 46.75, 5.5, 2.65, -38.75, 5.5, 2.65, -38, 6.25, 2.65, -38, 6.25, 2.65, -38.75, 11.75, 0.4, -35.5, 6.75, 0.4, -36.75, 10.5, 0.4, -17, 11, 0.4, -16.25, 18, 0.4, -26.25, 17, 0.4, -27, 12.25, 0.4, -35, 14.25, 0.4, -35, 17, 0.4, -28.25, -1, 0.4, -27, -1, 0.4, -26, -1.5, 0.4, -25.75, 8.25, 0.4, -16.25, 8.75, 0.4, -17, 13, 2.65, -37, 13, 2.4, -36.25, 13.5, 2.65, -36.25, 13.5, 2.65, -37, 40.25, 0.4, -14, 40.5, 0.4, -13, 47.75, 0.4, -13, 47.75, 0.4, -20.5, 19.75, 0.4, -26.75, 19, 0.4, -26.25, 38.75, 0.4, -14, -10.5, 0.4, -25.25, -3.75, 0.4, -26, -10.5, 0.4, -9, -6.5, 0.4, -4.25, -5.25, 0.4, -4.25, -11, 0.4, -25, 11, 0.4, -14.5, 38.25, 0.4, -13.75, 20, 0.4, -3.25, 20.75, 0.4, -3.25, 38.25, 0.4, -12.25, 17.3, 0.4, -6.625, 18.2, 0.9, -5.5, 8, 0.4, -15, -3.25, 0.4, -25.25, 9, 0.4, -14, -5, 0.4, -4.25, -31, 0.4, -23.5, -29.25, 0.4, -12.25, -28.75, 0.4, -13, -30.25, 0.4, -24.25, -27, 0.4, -12.5, -12.25, 0.4, -25, -32.25, 2.65, -25.25, -32, 2.65, -24.5, -31.5, 2.65, -24.75, -31.75, 2.65, -25.5, -33, 0.4, -23.5, -29.75, 0.4, -11, -47.5, 0.4, -18, -47.5, 0.4, -11, -26.5, 0.4, -12.25, -26.75, 0.4, -10.75, 10, 0.4, 6, 19.25, 0.4, -1.25, 19.25, 0.4, -2.75, 8.25, 0.4, 5.75, 10.5, 0.4, -14, -4.5, 0.4, -4, -4.5, 0.4, -2.5, -2.18182, 0.4, -1, -1.02273, 0.9, -0.25, 0.136364, 0.4, 0.5, 40.25, 0.4, -11.75, 47.75, 0.4, -1.75, 39.75, 0.4, 8, 41, 0.4, 9.75, 47.75, 0.4, 9.75, 38.75, 0.4, 8, 38.75, 0.4, -11.75, 21.5, 0.4, -2.5, 21.5, 0.4, -1.25, 41.241, 0.9, 0.992981, 39.7559, 0.4, 0.981995, 41.2708, 0.4, -0.488865, 41.2561, 0.4, 2.49186, -47.5, 0.4, 7.75, -45, 0.4, 7.75, -44.75, 0.4, 6.5, -47.5, 0.4, -1.75, -29.75, 0.4, 2, -29, 0.4, -9.75, -43.5, 0.4, 6.5, -28, 0.4, 1.75, -28, 0.4, -9.5, -29.75, 0.4, 4, -6.75, 0.4, -2.25, -12, 0.4, 4.75, -10.5, 0.4, 4.75, -27.5, 0.4, -9.5, -26.75, 0.4, 2, -26.75, 0.4, 4, -10, 0.4, 6.75, -10.5, 0.4, 7, -10.25, 0.4, 11, -8.75, 0.4, 11, -8.5, 0.4, 11.5, 8.25, 0.4, 8, 7.75, 0.4, 7.75, -4.75, 0.4, -2, -10, 0.4, 5, 7.75, 0.4, 6, 0.400002, 0.4, 0.900002, -0.825001, 0.9, 0.0499992, -2.05, 0.4, -0.799999, 21, 0.4, -0.5, 21, 0.4, -0.75, 19.5, 0.4, -0.75, 10, 0.4, 7.75, 20, 0.4, 21, 21, 0.4, 20.25, 9.5, 0.4, 8, 38, 0.4, 8.5, 23.25, 0.4, 21.25, 39.25, 0.4, 11.5, 37.5, 0.4, 10, 22.5, 0.4, 20.25, 24.7552, 0.9, 8.52277, 26.2402, 0.4, 8.48375, 24.7703, 0.4, 10.0217, -42.75, 0.4, 7.25, -29.25, 0.4, 4.5, -10.75, 0.4, 11.25, -12.25, 0.4, 6.75, -10.75, 0.4, 12, -27, 0.4, 4.5, -28, 0.4, 14.5, -24.775, 0.9, 5.475, -24.755, 0.4, 6.99697, -18.7649, 0.9, 6.99092, -20.2698, 0.4, 8.51783, -20.2499, 0.4, 6.97993, -17.2299, 0.4, 7.00191, -28.5, 0.4, 4.5, -28.5, 0.4, 14.5, -42.75, 0.4, 8.75, -30, 0.4, 14.75, -43.25, 0.4, 9, -44.75, 0.4, 9, -47.5, 0.4, 16, -30.25, 0.4, 15.75, -15, 0.4, 39.25, -16, 0.4, 39.25, -16, 0.4, 42.25, -15.5, 0.4, 42.5, 3.75, 0.4, 47.75, 23.25, 0.4, 37, 20, 0.4, 22.75, -8.75, 0.4, 13.25, -14.25, 0.4, 36.75, 8.2551, 0.9, 11.5129, 11.2473, 0.9, 27.9845, 9.73112, 0.4, 20.4968, 11.2676, 0.4, 29.4762, 3.75859, 0.9, 35.4992, 5.23833, 0.4, 34.0074, 2.2683, 0.4, 33.9855, 3.76387, 0.4, 37.0019, 9.74023, 0.4, 12.9838, 12.7458, 0.9, 17.5062, 6.77008, 0.4, 11.5019, 8.25522, 0.9, 13.0228, 9.76038, 0.4, 14.5156, 11.2621, 0.4, 26.5038, 12.7559, 0.4, 18.982, 9.73257, 0.4, 29.5153, 11.2454, 0.4, 14.4766, 8.24503, 0.4, 9.99697, 11.2709, 0.9, 19.021, 11.2607, 0.4, 17.4952, 14.241, 0.4, 18.993, -15.5, 0.4, 44.25, -16.5, 0.4, 44.5, -16.5, 0.4, 47.75, -6.5, 0.4, 47.75, -14.25, 0.4, 38.5, -10.25, 0.4, 13.25, -15, 0.4, 27.25, -16.25, 0.4, 29.25, -16, 0.4, 36.25, -14.75, 0.4, 36.25, 14, 0.4, 47.75, 24.25, 0.4, 47.75, 24.25, 0.4, 37.25, 23.25, 0.4, 35.5, 22.25, 0.4, 23.5, 20.75, 0.4, 23.5, 42, 0.4, 40.25, 42.5, 0.4, 41.75, 47.75, 0.4, 41.75, 47.75, 0.4, 33.75, 39.75, 0.4, 11.5, 47.75, 0.4, 17.75, 23.25, 0.4, 22.75, 25, 0.4, 34.75, 25.5, 0.4, 35.25, 40.25, 0.4, 40.25, 47.75, 0.4, 25.75, 36.7267, 0.9, 31.0132, 36.7715, 0.4, 29.5204, 36.7468, 0.4, 32.495, 39.7416, 0.4, 29.4923, 23.75, 0.4, 34.75, 25.5, 0.4, 36.75, -27, 0.4, 16.75, -27.75, 0.65, 17.5, -17.75, 0.4, 26.5, -16.75, 0.4, 25.75, -10.75, 0.4, 12.75, -27, 0.4, 15.5, -39.75, 0.4, 47.75, -39.25, 0.4, 44.75, -39.25, 0.4, 43.5, -47.5, 0.4, 39.75, -47.5, 0.4, 47.75, -38.5, 0.4, 45.75, -36.75, 0.4, 45.75, -32, 0.4, 47.75, -17.75, 0.4, 42.5, -17.25, 0.4, 38.5, -17.75, 0.4, 44.25, -24.25, 0.4, 47.75, -36.75, 0.4, 42.5, -36, 0.4, 43.25, -38.25, 0.4, 42.5, -47.5, 0.4, 31.75, -36, 0.4, 45, -17.25, 0.4, 36.75, -17.25, 0.4, 29.25, -30, 0.4, 17.5, -47.5, 0.4, 23.75, -18.5, 0.4, 28.25, -18.5, 0.4, 27.25, -38.2299, 0.9, 22.0019, -36.7449, 0.4, 22.0129, -39.7298, 0.4, 23.5118, -39.7649, 0.4, 21.9909, -35.24, 0.4, 20.486, -29, 2.65, 15.75, -29, 2.65, 16.5, -28.5, 2.65, 16.5, -28.5, 2.65, 15.75, 21.25, 2.65, 21.5, 21.25, 2.65, 22.25, 22, 2.65, 22, 22, 2.65, 21.5, 41, 0.4, 47.75, 41, 0.4, 43.25, 40, 0.4, 43.25, 39.25, 0.4, 42.25, 32.5, 0.4, 47.75, 39.25, 0.4, 41.25, 25.25, 0.4, 37.25, -16, 2.4, 37.5, -16, 2.4, 38, -15.5, 2.4, 38, -15.5, 2.4, 37.5, 40.75, 2.4, 41.5, 40.75, 2.4, 42, 41.25, 2.4, 42, 41.25, 2.65, 41.5, 42, 0.4, 43.25, 47.75, 0.4, 47.75, -38, 2.4, 44, -37.25, 2.4, 44.5, -37.25, 2.4, 43.75, 53.75, 8.4, 51, 53.75, 8.4, 49, 51.75, 8.4, 49, 51.25, 8.4, 48.5, 49, 8.4, 48.5, 28.75, 8.4, 51, 35, 8.4, 51, 36.5, 8.4, 49, 24.75, 8.4, 49, 48.5, 8.4, 49, 47.5, 8.4, 51, 16.25, 8.4, 51, 22.5, 8.4, 51, 12.75, 8.4, 49, -40, 8.4, 51, -34.5, 8.4, 49, -11, 8.4, 49, -8.75, 8.4, 51, -2.5, 8.4, 51, 1, 8.4, 49, -33.75, 8.4, 51, -27.5, 8.4, 51, -22.75, 8.4, 49, 3.75, 8.4, 51, 10, 8.4, 51, -21.25, 8.4, 51, 41.25, 8.4, 51, -15, 8.4, 51)
polygons = [PackedInt32Array(2, 1, 0), PackedInt32Array(4, 3, 0), PackedInt32Array(0, 3, 2), PackedInt32Array(6, 5, 7), PackedInt32Array(7, 5, 8), PackedInt32Array(4, 0, 9), PackedInt32Array(11, 10, 9), PackedInt32Array(9, 10, 4), PackedInt32Array(7, 8, 12), PackedInt32Array(12, 8, 13), PackedInt32Array(15, 14, 11), PackedInt32Array(11, 14, 10), PackedInt32Array(6, 17, 5), PackedInt32Array(5, 17, 16), PackedInt32Array(19, 18, 15), PackedInt32Array(15, 18, 14), PackedInt32Array(21, 20, 13), PackedInt32Array(13, 20, 12), PackedInt32Array(20, 21, 19), PackedInt32Array(19, 21, 18), PackedInt32Array(23, 22, 24), PackedInt32Array(24, 22, 25), PackedInt32Array(27, 26, 28), PackedInt32Array(28, 26, 25), PackedInt32Array(30, 29, 17), PackedInt32Array(17, 29, 16), PackedInt32Array(31, 28, 25), PackedInt32Array(32, 31, 30), PackedInt32Array(30, 31, 29), PackedInt32Array(29, 31, 22), PackedInt32Array(22, 31, 25), PackedInt32Array(32, 34, 31), PackedInt32Array(31, 34, 33), PackedInt32Array(39, 38, 35), PackedInt32Array(35, 38, 37), PackedInt32Array(35, 37, 36), PackedInt32Array(43, 42, 40), PackedInt32Array(40, 42, 41), PackedInt32Array(40, 41, 4), PackedInt32Array(4, 41, 3), PackedInt32Array(44, 38, 39), PackedInt32Array(39, 45, 44), PackedInt32Array(44, 45, 47), PackedInt32Array(44, 47, 46), PackedInt32Array(49, 48, 50), PackedInt32Array(50, 48, 51), PackedInt32Array(53, 52, 54), PackedInt32Array(54, 52, 55), PackedInt32Array(57, 46, 56), PackedInt32Array(56, 46, 47), PackedInt32Array(43, 58, 42), PackedInt32Array(42, 58, 59), PackedInt32Array(61, 57, 60), PackedInt32Array(60, 57, 56), PackedInt32Array(63, 62, 64), PackedInt32Array(64, 62, 49), PackedInt32Array(66, 61, 65), PackedInt32Array(65, 61, 60), PackedInt32Array(48, 52, 51), PackedInt32Array(51, 52, 53), PackedInt32Array(68, 66, 67), PackedInt32Array(67, 66, 65), PackedInt32Array(50, 64, 49), PackedInt32Array(55, 68, 69), PackedInt32Array(69, 68, 67), PackedInt32Array(63, 59, 62), PackedInt32Array(62, 59, 58), PackedInt32Array(55, 69, 54), PackedInt32Array(72, 71, 70), PackedInt32Array(75, 74, 73), PackedInt32Array(75, 73, 76), PackedInt32Array(76, 73, 72), PackedInt32Array(76, 72, 70), PackedInt32Array(76, 70, 77), PackedInt32Array(79, 78, 80), PackedInt32Array(80, 78, 81), PackedInt32Array(84, 83, 82), PackedInt32Array(84, 86, 77), PackedInt32Array(77, 86, 76), PackedInt32Array(76, 87, 81), PackedInt32Array(81, 88, 80), PackedInt32Array(80, 88, 82), PackedInt32Array(82, 88, 84), PackedInt32Array(84, 88, 86), PackedInt32Array(86, 85, 76), PackedInt32Array(87, 88, 81), PackedInt32Array(76, 85, 87), PackedInt32Array(88, 85, 86), PackedInt32Array(87, 85, 88), PackedInt32Array(90, 89, 91), PackedInt32Array(91, 89, 92), PackedInt32Array(95, 94, 93), PackedInt32Array(89, 82, 92), PackedInt32Array(92, 82, 93), PackedInt32Array(97, 96, 98), PackedInt32Array(98, 96, 99), PackedInt32Array(82, 105, 93), PackedInt32Array(93, 106, 95), PackedInt32Array(95, 106, 96), PackedInt32Array(96, 101, 99), PackedInt32Array(99, 104, 83), PackedInt32Array(83, 107, 82), PackedInt32Array(105, 108, 93), PackedInt32Array(82, 103, 105), PackedInt32Array(93, 108, 106), PackedInt32Array(106, 101, 96), PackedInt32Array(101, 104, 99), PackedInt32Array(104, 107, 83), PackedInt32Array(107, 103, 82), PackedInt32Array(105, 102, 108), PackedInt32Array(103, 106, 105), PackedInt32Array(108, 102, 106), PackedInt32Array(106, 103, 101), PackedInt32Array(101, 100, 104), PackedInt32Array(104, 100, 107), PackedInt32Array(107, 100, 103), PackedInt32Array(105, 106, 102), PackedInt32Array(103, 100, 101), PackedInt32Array(111, 110, 112), PackedInt32Array(112, 110, 109), PackedInt32Array(113, 97, 98), PackedInt32Array(112, 109, 114), PackedInt32Array(114, 109, 113), PackedInt32Array(114, 113, 115), PackedInt32Array(115, 113, 98), PackedInt32Array(117, 116, 118), PackedInt32Array(118, 116, 121), PackedInt32Array(118, 121, 120), PackedInt32Array(118, 120, 119), PackedInt32Array(122, 114, 115), PackedInt32Array(125, 124, 123), PackedInt32Array(122, 115, 121), PackedInt32Array(121, 115, 120), PackedInt32Array(126, 131, 127), PackedInt32Array(127, 132, 123), PackedInt32Array(123, 134, 125), PackedInt32Array(125, 136, 119), PackedInt32Array(119, 130, 118), PackedInt32Array(118, 131, 126), PackedInt32Array(131, 132, 127), PackedInt32Array(132, 133, 123), PackedInt32Array(134, 136, 125), PackedInt32Array(123, 130, 134), PackedInt32Array(136, 128, 119), PackedInt32Array(130, 131, 118), PackedInt32Array(119, 135, 130), PackedInt32Array(131, 133, 132), PackedInt32Array(133, 130, 123), PackedInt32Array(134, 128, 136), PackedInt32Array(130, 135, 134), PackedInt32Array(128, 135, 119), PackedInt32Array(130, 133, 131), PackedInt32Array(134, 135, 128), PackedInt32Array(138, 23, 137), PackedInt32Array(137, 23, 24), PackedInt32Array(142, 141, 139), PackedInt32Array(139, 141, 140), PackedInt32Array(144, 138, 143), PackedInt32Array(143, 138, 137), PackedInt32Array(148, 147, 145), PackedInt32Array(145, 147, 146), PackedInt32Array(150, 144, 149), PackedInt32Array(149, 144, 143), PackedInt32Array(147, 142, 146), PackedInt32Array(146, 142, 139), PackedInt32Array(141, 152, 140), PackedInt32Array(140, 152, 151), PackedInt32Array(145, 150, 148), PackedInt32Array(148, 150, 149), PackedInt32Array(156, 155, 153), PackedInt32Array(153, 155, 154), PackedInt32Array(110, 111, 158), PackedInt32Array(158, 111, 157), PackedInt32Array(160, 159, 161), PackedInt32Array(161, 159, 162), PackedInt32Array(157, 163, 158), PackedInt32Array(158, 163, 94), PackedInt32Array(116, 117, 164), PackedInt32Array(164, 117, 165), PackedInt32Array(167, 166, 94), PackedInt32Array(167, 170, 168), PackedInt32Array(168, 170, 169), PackedInt32Array(165, 162, 164), PackedInt32Array(164, 162, 163), PackedInt32Array(159, 170, 162), PackedInt32Array(162, 170, 163), PackedInt32Array(163, 170, 94), PackedInt32Array(94, 170, 167), PackedInt32Array(166, 93, 94), PackedInt32Array(174, 173, 171), PackedInt32Array(171, 173, 172), PackedInt32Array(176, 175, 177), PackedInt32Array(177, 175, 178), PackedInt32Array(181, 180, 179), PackedInt32Array(175, 181, 178), PackedInt32Array(178, 181, 127), PackedInt32Array(127, 181, 179), PackedInt32Array(127, 179, 126), PackedInt32Array(91, 183, 90), PackedInt32Array(90, 183, 182), PackedInt32Array(185, 184, 186), PackedInt32Array(186, 184, 187), PackedInt32Array(186, 187, 182), PackedInt32Array(186, 182, 183), PackedInt32Array(161, 180, 160), PackedInt32Array(160, 180, 188), PackedInt32Array(180, 181, 189), PackedInt32Array(190, 194, 191), PackedInt32Array(191, 194, 193), PackedInt32Array(191, 193, 188), PackedInt32Array(191, 188, 180), PackedInt32Array(191, 180, 189), PackedInt32Array(191, 189, 192), PackedInt32Array(169, 195, 168), PackedInt32Array(168, 195, 196), PackedInt32Array(186, 183, 196), PackedInt32Array(195, 197, 196), PackedInt32Array(196, 197, 198), PackedInt32Array(196, 198, 186), PackedInt32Array(200, 199, 201), PackedInt32Array(201, 199, 202), PackedInt32Array(202, 78, 201), PackedInt32Array(201, 78, 203), PackedInt32Array(203, 78, 79), PackedInt32Array(203, 79, 204), PackedInt32Array(208, 207, 205), PackedInt32Array(205, 207, 206), PackedInt32Array(199, 200, 209), PackedInt32Array(209, 200, 210), PackedInt32Array(211, 73, 74), PackedInt32Array(209, 210, 74), PackedInt32Array(74, 210, 211), PackedInt32Array(211, 210, 212), PackedInt32Array(187, 184, 204), PackedInt32Array(204, 184, 213), PackedInt32Array(204, 213, 203), PackedInt32Array(184, 214, 213), PackedInt32Array(216, 215, 217), PackedInt32Array(217, 215, 218), PackedInt32Array(221, 220, 222), PackedInt32Array(222, 220, 223), PackedInt32Array(223, 220, 224), PackedInt32Array(224, 220, 218), PackedInt32Array(218, 220, 197), PackedInt32Array(218, 197, 219), PackedInt32Array(218, 219, 217), PackedInt32Array(190, 217, 194), PackedInt32Array(194, 217, 193), PackedInt32Array(193, 217, 219), PackedInt32Array(193, 219, 188), PackedInt32Array(220, 198, 197), PackedInt32Array(176, 177, 225), PackedInt32Array(225, 177, 226), PackedInt32Array(228, 227, 229), PackedInt32Array(229, 227, 226), PackedInt32Array(226, 227, 230), PackedInt32Array(233, 235, 230), PackedInt32Array(230, 237, 226), PackedInt32Array(226, 236, 225), PackedInt32Array(225, 236, 231), PackedInt32Array(231, 235, 232), PackedInt32Array(232, 235, 233), PackedInt32Array(235, 237, 230), PackedInt32Array(237, 234, 226), PackedInt32Array(226, 234, 236), PackedInt32Array(236, 235, 231), PackedInt32Array(235, 234, 237), PackedInt32Array(234, 235, 236), PackedInt32Array(231, 232, 192), PackedInt32Array(192, 232, 191), PackedInt32Array(239, 238, 240), PackedInt32Array(240, 238, 241), PackedInt32Array(243, 242, 210), PackedInt32Array(210, 242, 244), PackedInt32Array(210, 244, 241), PackedInt32Array(210, 241, 212), PackedInt32Array(244, 240, 241), PackedInt32Array(246, 245, 243), PackedInt32Array(243, 245, 242), PackedInt32Array(242, 247, 244), PackedInt32Array(185, 248, 184), PackedInt32Array(248, 250, 249), PackedInt32Array(246, 251, 245), PackedInt32Array(245, 251, 252), PackedInt32Array(249, 253, 252), PackedInt32Array(251, 214, 252), PackedInt32Array(252, 214, 184), PackedInt32Array(252, 184, 248), PackedInt32Array(252, 248, 249), PackedInt32Array(255, 254, 256), PackedInt32Array(256, 254, 257), PackedInt32Array(260, 259, 258), PackedInt32Array(257, 254, 258), PackedInt32Array(258, 254, 262), PackedInt32Array(258, 262, 248), PackedInt32Array(258, 248, 261), PackedInt32Array(221, 266, 261), PackedInt32Array(261, 266, 265), PackedInt32Array(261, 265, 264), PackedInt32Array(261, 264, 263), PackedInt32Array(261, 263, 260), PackedInt32Array(261, 260, 258), PackedInt32Array(262, 250, 248), PackedInt32Array(221, 222, 266), PackedInt32Array(266, 222, 223), PackedInt32Array(266, 223, 265), PackedInt32Array(265, 223, 224), PackedInt32Array(265, 224, 264), PackedInt32Array(264, 224, 263), PackedInt32Array(263, 224, 218), PackedInt32Array(269, 268, 267), PackedInt32Array(215, 216, 269), PackedInt32Array(269, 267, 215), PackedInt32Array(215, 267, 270), PackedInt32Array(270, 267, 272), PackedInt32Array(270, 272, 271), PackedInt32Array(270, 271, 273), PackedInt32Array(268, 233, 267), PackedInt32Array(267, 233, 274), PackedInt32Array(274, 233, 230), PackedInt32Array(278, 277, 275), PackedInt32Array(275, 277, 276), PackedInt32Array(274, 280, 267), PackedInt32Array(267, 281, 272), PackedInt32Array(272, 281, 278), PackedInt32Array(278, 281, 277), PackedInt32Array(277, 280, 274), PackedInt32Array(280, 279, 267), PackedInt32Array(267, 279, 281), PackedInt32Array(281, 280, 277), PackedInt32Array(280, 281, 279), PackedInt32Array(283, 282, 247), PackedInt32Array(247, 282, 244), PackedInt32Array(256, 284, 255), PackedInt32Array(255, 284, 285), PackedInt32Array(253, 249, 285), PackedInt32Array(287, 290, 288), PackedInt32Array(288, 292, 286), PackedInt32Array(286, 294, 284), PackedInt32Array(284, 294, 285), PackedInt32Array(285, 294, 253), PackedInt32Array(253, 289, 287), PackedInt32Array(290, 292, 288), PackedInt32Array(287, 289, 290), PackedInt32Array(292, 294, 286), PackedInt32Array(294, 291, 253), PackedInt32Array(253, 291, 289), PackedInt32Array(290, 293, 292), PackedInt32Array(289, 293, 290), PackedInt32Array(292, 291, 294), PackedInt32Array(291, 293, 289), PackedInt32Array(293, 291, 292), PackedInt32Array(296, 295, 288), PackedInt32Array(288, 295, 287), PackedInt32Array(295, 296, 283), PackedInt32Array(283, 296, 298), PackedInt32Array(283, 298, 297), PackedInt32Array(283, 297, 282), PackedInt32Array(298, 299, 297), PackedInt32Array(239, 300, 238), PackedInt32Array(238, 300, 301), PackedInt32Array(300, 299, 301), PackedInt32Array(301, 299, 302), PackedInt32Array(302, 299, 298), PackedInt32Array(305, 304, 306), PackedInt32Array(306, 304, 303), PackedInt32Array(310, 318, 311), PackedInt32Array(311, 319, 307), PackedInt32Array(307, 319, 308), PackedInt32Array(308, 315, 309), PackedInt32Array(309, 328, 259), PackedInt32Array(259, 322, 310), PackedInt32Array(318, 319, 311), PackedInt32Array(310, 314, 318), PackedInt32Array(319, 317, 308), PackedInt32Array(315, 313, 309), PackedInt32Array(308, 317, 315), PackedInt32Array(328, 320, 259), PackedInt32Array(309, 332, 328), PackedInt32Array(322, 314, 310), PackedInt32Array(259, 329, 322), PackedInt32Array(318, 316, 319), PackedInt32Array(314, 327, 318), PackedInt32Array(319, 316, 317), PackedInt32Array(313, 325, 309), PackedInt32Array(315, 327, 313), PackedInt32Array(317, 327, 315), PackedInt32Array(320, 329, 259), PackedInt32Array(328, 324, 320), PackedInt32Array(332, 321, 328), PackedInt32Array(309, 325, 332), PackedInt32Array(322, 323, 314), PackedInt32Array(329, 312, 322), PackedInt32Array(318, 317, 316), PackedInt32Array(327, 317, 318), PackedInt32Array(314, 325, 327), PackedInt32Array(313, 327, 325), PackedInt32Array(320, 312, 329), PackedInt32Array(324, 323, 320), PackedInt32Array(328, 331, 324), PackedInt32Array(321, 331, 328), PackedInt32Array(332, 326, 321), PackedInt32Array(325, 326, 332), PackedInt32Array(323, 324, 314), PackedInt32Array(322, 312, 323), PackedInt32Array(314, 326, 325), PackedInt32Array(320, 323, 312), PackedInt32Array(331, 314, 324), PackedInt32Array(321, 326, 331), PackedInt32Array(314, 330, 326), PackedInt32Array(331, 330, 314), PackedInt32Array(326, 330, 331), PackedInt32Array(334, 333, 335), PackedInt32Array(335, 333, 336), PackedInt32Array(303, 337, 306), PackedInt32Array(306, 337, 333), PackedInt32Array(333, 337, 336), PackedInt32Array(338, 310, 339), PackedInt32Array(339, 310, 311), PackedInt32Array(342, 341, 340), PackedInt32Array(342, 340, 311), PackedInt32Array(311, 340, 339), PackedInt32Array(310, 258, 259), PackedInt32Array(308, 343, 307), PackedInt32Array(345, 344, 308), PackedInt32Array(308, 344, 343), PackedInt32Array(273, 271, 259), PackedInt32Array(259, 271, 309), PackedInt32Array(348, 347, 346), PackedInt32Array(348, 346, 309), PackedInt32Array(309, 346, 308), PackedInt32Array(337, 311, 336), PackedInt32Array(336, 311, 307), PackedInt32Array(350, 349, 351), PackedInt32Array(351, 349, 352), PackedInt32Array(228, 229, 353), PackedInt32Array(353, 229, 354), PackedInt32Array(356, 355, 357), PackedInt32Array(357, 355, 275), PackedInt32Array(349, 358, 352), PackedInt32Array(352, 358, 359), PackedInt32Array(357, 362, 358), PackedInt32Array(358, 363, 359), PackedInt32Array(359, 363, 354), PackedInt32Array(354, 363, 276), PackedInt32Array(276, 361, 275), PackedInt32Array(275, 361, 357), PackedInt32Array(362, 363, 358), PackedInt32Array(357, 360, 362), PackedInt32Array(363, 361, 276), PackedInt32Array(361, 360, 357), PackedInt32Array(362, 360, 363), PackedInt32Array(363, 360, 361), PackedInt32Array(276, 353, 354), PackedInt32Array(364, 346, 347), PackedInt32Array(364, 347, 356), PackedInt32Array(356, 347, 355), PackedInt32Array(358, 365, 357), PackedInt32Array(368, 367, 366), PackedInt32Array(368, 366, 369), PackedInt32Array(369, 366, 371), PackedInt32Array(369, 371, 370), PackedInt32Array(370, 371, 286), PackedInt32Array(288, 286, 371), PackedInt32Array(370, 338, 369), PackedInt32Array(369, 338, 339), PackedInt32Array(373, 372, 374), PackedInt32Array(374, 372, 376), PackedInt32Array(374, 376, 375), PackedInt32Array(372, 373, 377), PackedInt32Array(377, 378, 372), PackedInt32Array(372, 378, 379), PackedInt32Array(304, 305, 381), PackedInt32Array(381, 305, 380), PackedInt32Array(334, 335, 382), PackedInt32Array(382, 335, 383), PackedInt32Array(383, 385, 384), PackedInt32Array(386, 375, 384), PackedInt32Array(384, 375, 387), PackedInt32Array(379, 378, 388), PackedInt32Array(389, 381, 380), PackedInt32Array(388, 385, 379), PackedInt32Array(379, 385, 383), PackedInt32Array(340, 341, 390), PackedInt32Array(390, 341, 389), PackedInt32Array(302, 391, 301), PackedInt32Array(301, 391, 392), PackedInt32Array(394, 393, 367), PackedInt32Array(367, 393, 391), PackedInt32Array(380, 382, 383), PackedInt32Array(390, 389, 393), PackedInt32Array(393, 389, 380), PackedInt32Array(393, 380, 383), PackedInt32Array(367, 368, 394), PackedInt32Array(393, 399, 391), PackedInt32Array(391, 398, 392), PackedInt32Array(392, 397, 387), PackedInt32Array(387, 397, 384), PackedInt32Array(384, 393, 383), PackedInt32Array(399, 398, 391), PackedInt32Array(393, 396, 399), PackedInt32Array(398, 397, 392), PackedInt32Array(397, 396, 384), PackedInt32Array(384, 396, 393), PackedInt32Array(399, 395, 398), PackedInt32Array(396, 395, 399), PackedInt32Array(398, 395, 397), PackedInt32Array(397, 395, 396), PackedInt32Array(386, 374, 375), PackedInt32Array(403, 402, 400), PackedInt32Array(400, 402, 401), PackedInt32Array(407, 406, 404), PackedInt32Array(404, 406, 405), PackedInt32Array(410, 409, 408), PackedInt32Array(410, 408, 411), PackedInt32Array(411, 408, 412), PackedInt32Array(413, 412, 358), PackedInt32Array(358, 412, 344), PackedInt32Array(358, 344, 414), PackedInt32Array(358, 414, 365), PackedInt32Array(413, 411, 412), PackedInt32Array(344, 345, 414), PackedInt32Array(418, 417, 415), PackedInt32Array(415, 417, 416), PackedInt32Array(422, 421, 419), PackedInt32Array(419, 421, 420), PackedInt32Array(408, 409, 423), PackedInt32Array(423, 350, 408), PackedInt32Array(408, 350, 351), PackedInt32Array(408, 351, 424), PackedInt32Array(427, 426, 425), PackedInt32Array(430, 429, 428), PackedInt32Array(432, 151, 431), PackedInt32Array(431, 151, 152), PackedInt32Array(434, 433, 435), PackedInt32Array(435, 433, 436), PackedInt32Array(432, 431, 430), PackedInt32Array(437, 432, 438), PackedInt32Array(438, 432, 430), PackedInt32Array(438, 430, 428), PackedInt32Array(440, 439, 436), PackedInt32Array(436, 439, 441), PackedInt32Array(436, 433, 440), PackedInt32Array(36, 37, 442), PackedInt32Array(442, 37, 443), PackedInt32Array(445, 444, 446), PackedInt32Array(446, 444, 447), PackedInt32Array(448, 442, 443), PackedInt32Array(448, 443, 449), PackedInt32Array(449, 443, 450), PackedInt32Array(452, 451, 441), PackedInt32Array(441, 451, 447), PackedInt32Array(453, 449, 450), PackedInt32Array(438, 454, 437), PackedInt32Array(437, 454, 435), PackedInt32Array(451, 446, 447), PackedInt32Array(435, 454, 434), PackedInt32Array(453, 450, 455), PackedInt32Array(455, 450, 444), PackedInt32Array(441, 439, 452), PackedInt32Array(445, 455, 444)]
agent_height = 1.75
agent_radius = 0.375
agent_max_climb = 0.5
edge_max_length = 12.0
filter_low_hanging_obstacles = true
filter_ledge_spans = true
filter_walkable_low_height_spans = true

[sub_resource type="StandardMaterial3D" id="StandardMaterial3D_um0fc"]
albedo_color = Color(0.627, 0.5, 0.19, 1)
metallic = 0.2
roughness = 0.8
uv1_scale = Vector3(10, 10, 1)

[sub_resource type="StandardMaterial3D" id="StandardMaterial3D_01ds2"]
transparency = 1
albedo_color = Color(1, 1, 0, 0.3)
emission_enabled = true
emission = Color(1, 1, 0, 1)

[node name="level01" type="Node3D"]
script = ExtResource("1_ttrr8")
minimap_scene = ExtResource("2_yij7j")
player_avatar_scene = ExtResource("3_s4q4e")
item_boxes_scene = ExtResource("4_6mqy0")
skill_box_ui_scene = ExtResource("5_6cewc")

[node name="WorldEnvironment" type="WorldEnvironment" parent="."]
environment = SubResource("Environment_j4i7h")

[node name="DirectionalLight3D" type="DirectionalLight3D" parent="."]
transform = Transform3D(-0.866023, -0.433016, 0.250001, 0, 0.499998, 0.866027, -0.500003, 0.749999, -0.43301, 0, 0, 0)
shadow_enabled = true

[node name="ModularLevelGenerator" type="Node3D" parent="."]
script = ExtResource("6_yber6")
level_config = ExtResource("7_itug7")
mesh_x_rotation_degrees = null
apply_mesh_rotation = null

[node name="Player" parent="." instance=ExtResource("8_81wtm")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 0, 0.209875, 0.62)

[node name="Enemy" parent="." instance=ExtResource("9_fbsmd")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, 0, 0, -13.0066)

[node name="FogOfWar" parent="." instance=ExtResource("10_n6yg5")]
player_vision_radius = 8.0
enabled = false

[node name="GasItemPickup" parent="." instance=ExtResource("11_f1fl4")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -10.8027, 0, 0)

[node name="GasItemPickup2" parent="." instance=ExtResource("11_f1fl4")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 10.101, 0, 0)

[node name="GasItemPickup3" parent="." instance=ExtResource("11_f1fl4")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 0, 0, -0.848371)

[node name="NavigationRegion3D" type="NavigationRegion3D" parent="."]
navigation_mesh = SubResource("NavigationMesh_p3na8")

[node name="Floor" type="Node3D" parent="NavigationRegion3D"]

[node name="@CSGBox3D@26341" type="CSGBox3D" parent="NavigationRegion3D/Floor"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 0, -0.05, 0)
use_collision = true
size = Vector3(100, 0.1, 100)
material = SubResource("StandardMaterial3D_um0fc")

[node name="Wall" parent="NavigationRegion3D" instance=ExtResource("12_rhbo5")]
transform = Transform3D(1, 0, 0, 0, 2, 0, 0, 0, 1, -50, 0, -50)

[node name="@StaticBody3D@26342" parent="NavigationRegion3D" instance=ExtResource("12_rhbo5")]
transform = Transform3D(1, 0, 0, 0, 2, 0, 0, 0, 1, -50, 0, 50)

[node name="@StaticBody3D@26343" parent="NavigationRegion3D" instance=ExtResource("12_rhbo5")]
transform = Transform3D(1, 0, 0, 0, 2, 0, 0, 0, 1, -48, 0, -50)

[node name="@StaticBody3D@26344" parent="NavigationRegion3D" instance=ExtResource("12_rhbo5")]
transform = Transform3D(1, 0, 0, 0, 2, 0, 0, 0, 1, -48, 0, 50)

[node name="@StaticBody3D@26345" parent="NavigationRegion3D" instance=ExtResource("12_rhbo5")]
transform = Transform3D(1, 0, 0, 0, 2, 0, 0, 0, 1, -46, 0, -50)

[node name="@StaticBody3D@26346" parent="NavigationRegion3D" instance=ExtResource("12_rhbo5")]
transform = Transform3D(1, 0, 0, 0, 2, 0, 0, 0, 1, -46, 0, 50)

[node name="@StaticBody3D@26347" parent="NavigationRegion3D" instance=ExtResource("12_rhbo5")]
transform = Transform3D(1, 0, 0, 0, 2, 0, 0, 0, 1, -44, 0, -50)

[node name="@StaticBody3D@26348" parent="NavigationRegion3D" instance=ExtResource("12_rhbo5")]
transform = Transform3D(1, 0, 0, 0, 2, 0, 0, 0, 1, -44, 0, 50)

[node name="@StaticBody3D@26349" parent="NavigationRegion3D" instance=ExtResource("12_rhbo5")]
transform = Transform3D(1, 0, 0, 0, 2, 0, 0, 0, 1, -42, 0, -50)

[node name="@StaticBody3D@26350" parent="NavigationRegion3D" instance=ExtResource("12_rhbo5")]
transform = Transform3D(1, 0, 0, 0, 2, 0, 0, 0, 1, -42, 0, 50)

[node name="@StaticBody3D@26351" parent="NavigationRegion3D" instance=ExtResource("12_rhbo5")]
transform = Transform3D(1, 0, 0, 0, 2, 0, 0, 0, 1, -40, 0, -50)

[node name="@StaticBody3D@26352" parent="NavigationRegion3D" instance=ExtResource("12_rhbo5")]
transform = Transform3D(1, 0, 0, 0, 2, 0, 0, 0, 1, -40, 0, 50)

[node name="@StaticBody3D@26353" parent="NavigationRegion3D" instance=ExtResource("12_rhbo5")]
transform = Transform3D(1, 0, 0, 0, 2, 0, 0, 0, 1, -38, 0, -50)

[node name="@StaticBody3D@26354" parent="NavigationRegion3D" instance=ExtResource("12_rhbo5")]
transform = Transform3D(1, 0, 0, 0, 2, 0, 0, 0, 1, -38, 0, 50)

[node name="@StaticBody3D@26355" parent="NavigationRegion3D" instance=ExtResource("12_rhbo5")]
transform = Transform3D(1, 0, 0, 0, 2, 0, 0, 0, 1, -36, 0, -50)

[node name="@StaticBody3D@26356" parent="NavigationRegion3D" instance=ExtResource("12_rhbo5")]
transform = Transform3D(1, 0, 0, 0, 2, 0, 0, 0, 1, -36, 0, 50)

[node name="@StaticBody3D@26357" parent="NavigationRegion3D" instance=ExtResource("12_rhbo5")]
transform = Transform3D(1, 0, 0, 0, 2, 0, 0, 0, 1, -34, 0, -50)

[node name="@StaticBody3D@26358" parent="NavigationRegion3D" instance=ExtResource("12_rhbo5")]
transform = Transform3D(1, 0, 0, 0, 2, 0, 0, 0, 1, -34, 0, 50)

[node name="@StaticBody3D@26359" parent="NavigationRegion3D" instance=ExtResource("12_rhbo5")]
transform = Transform3D(1, 0, 0, 0, 2, 0, 0, 0, 1, -32, 0, -50)

[node name="@StaticBody3D@26360" parent="NavigationRegion3D" instance=ExtResource("12_rhbo5")]
transform = Transform3D(1, 0, 0, 0, 2, 0, 0, 0, 1, -32, 0, 50)

[node name="@StaticBody3D@26361" parent="NavigationRegion3D" instance=ExtResource("12_rhbo5")]
transform = Transform3D(1, 0, 0, 0, 2, 0, 0, 0, 1, -30, 0, -50)

[node name="@StaticBody3D@26362" parent="NavigationRegion3D" instance=ExtResource("12_rhbo5")]
transform = Transform3D(1, 0, 0, 0, 2, 0, 0, 0, 1, -30, 0, 50)

[node name="@StaticBody3D@26363" parent="NavigationRegion3D" instance=ExtResource("12_rhbo5")]
transform = Transform3D(1, 0, 0, 0, 2, 0, 0, 0, 1, -28, 0, -50)

[node name="@StaticBody3D@26364" parent="NavigationRegion3D" instance=ExtResource("12_rhbo5")]
transform = Transform3D(1, 0, 0, 0, 2, 0, 0, 0, 1, -28, 0, 50)

[node name="@StaticBody3D@26365" parent="NavigationRegion3D" instance=ExtResource("12_rhbo5")]
transform = Transform3D(1, 0, 0, 0, 2, 0, 0, 0, 1, -26, 0, -50)

[node name="@StaticBody3D@26366" parent="NavigationRegion3D" instance=ExtResource("12_rhbo5")]
transform = Transform3D(1, 0, 0, 0, 2, 0, 0, 0, 1, -26, 0, 50)

[node name="@StaticBody3D@26367" parent="NavigationRegion3D" instance=ExtResource("12_rhbo5")]
transform = Transform3D(1, 0, 0, 0, 2, 0, 0, 0, 1, -24, 0, -50)

[node name="@StaticBody3D@26368" parent="NavigationRegion3D" instance=ExtResource("12_rhbo5")]
transform = Transform3D(1, 0, 0, 0, 2, 0, 0, 0, 1, -24, 0, 50)

[node name="@StaticBody3D@26369" parent="NavigationRegion3D" instance=ExtResource("12_rhbo5")]
transform = Transform3D(1, 0, 0, 0, 2, 0, 0, 0, 1, -22, 0, -50)

[node name="@StaticBody3D@26370" parent="NavigationRegion3D" instance=ExtResource("12_rhbo5")]
transform = Transform3D(1, 0, 0, 0, 2, 0, 0, 0, 1, -22, 0, 50)

[node name="@StaticBody3D@26371" parent="NavigationRegion3D" instance=ExtResource("12_rhbo5")]
transform = Transform3D(1, 0, 0, 0, 2, 0, 0, 0, 1, -20, 0, -50)

[node name="@StaticBody3D@26372" parent="NavigationRegion3D" instance=ExtResource("12_rhbo5")]
transform = Transform3D(1, 0, 0, 0, 2, 0, 0, 0, 1, -20, 0, 50)

[node name="@StaticBody3D@26373" parent="NavigationRegion3D" instance=ExtResource("12_rhbo5")]
transform = Transform3D(1, 0, 0, 0, 2, 0, 0, 0, 1, -18, 0, -50)

[node name="@StaticBody3D@26374" parent="NavigationRegion3D" instance=ExtResource("12_rhbo5")]
transform = Transform3D(1, 0, 0, 0, 2, 0, 0, 0, 1, -18, 0, 50)

[node name="@StaticBody3D@26375" parent="NavigationRegion3D" instance=ExtResource("12_rhbo5")]
transform = Transform3D(1, 0, 0, 0, 2, 0, 0, 0, 1, -16, 0, -50)

[node name="@StaticBody3D@26376" parent="NavigationRegion3D" instance=ExtResource("12_rhbo5")]
transform = Transform3D(1, 0, 0, 0, 2, 0, 0, 0, 1, -16, 0, 50)

[node name="@StaticBody3D@26377" parent="NavigationRegion3D" instance=ExtResource("12_rhbo5")]
transform = Transform3D(1, 0, 0, 0, 2, 0, 0, 0, 1, -14, 0, -50)

[node name="@StaticBody3D@26378" parent="NavigationRegion3D" instance=ExtResource("12_rhbo5")]
transform = Transform3D(1, 0, 0, 0, 2, 0, 0, 0, 1, -14, 0, 50)

[node name="@StaticBody3D@26379" parent="NavigationRegion3D" instance=ExtResource("12_rhbo5")]
transform = Transform3D(1, 0, 0, 0, 2, 0, 0, 0, 1, -12, 0, -50)

[node name="@StaticBody3D@26380" parent="NavigationRegion3D" instance=ExtResource("12_rhbo5")]
transform = Transform3D(1, 0, 0, 0, 2, 0, 0, 0, 1, -12, 0, 50)

[node name="@StaticBody3D@26381" parent="NavigationRegion3D" instance=ExtResource("12_rhbo5")]
transform = Transform3D(1, 0, 0, 0, 2, 0, 0, 0, 1, -10, 0, -50)

[node name="@StaticBody3D@26382" parent="NavigationRegion3D" instance=ExtResource("12_rhbo5")]
transform = Transform3D(1, 0, 0, 0, 2, 0, 0, 0, 1, -10, 0, 50)

[node name="@StaticBody3D@26383" parent="NavigationRegion3D" instance=ExtResource("12_rhbo5")]
transform = Transform3D(1, 0, 0, 0, 2, 0, 0, 0, 1, -8, 0, -50)

[node name="@StaticBody3D@26384" parent="NavigationRegion3D" instance=ExtResource("12_rhbo5")]
transform = Transform3D(1, 0, 0, 0, 2, 0, 0, 0, 1, -8, 0, 50)

[node name="@StaticBody3D@26385" parent="NavigationRegion3D" instance=ExtResource("12_rhbo5")]
transform = Transform3D(1, 0, 0, 0, 2, 0, 0, 0, 1, -6, 0, -50)

[node name="@StaticBody3D@26386" parent="NavigationRegion3D" instance=ExtResource("12_rhbo5")]
transform = Transform3D(1, 0, 0, 0, 2, 0, 0, 0, 1, -6, 0, 50)

[node name="@StaticBody3D@26387" parent="NavigationRegion3D" instance=ExtResource("12_rhbo5")]
transform = Transform3D(1, 0, 0, 0, 2, 0, 0, 0, 1, -4, 0, -50)

[node name="@StaticBody3D@26388" parent="NavigationRegion3D" instance=ExtResource("12_rhbo5")]
transform = Transform3D(1, 0, 0, 0, 2, 0, 0, 0, 1, -4, 0, 50)

[node name="@StaticBody3D@26389" parent="NavigationRegion3D" instance=ExtResource("12_rhbo5")]
transform = Transform3D(1, 0, 0, 0, 2, 0, 0, 0, 1, -2, 0, -50)

[node name="@StaticBody3D@26390" parent="NavigationRegion3D" instance=ExtResource("12_rhbo5")]
transform = Transform3D(1, 0, 0, 0, 2, 0, 0, 0, 1, -2, 0, 50)

[node name="@StaticBody3D@26391" parent="NavigationRegion3D" instance=ExtResource("12_rhbo5")]
transform = Transform3D(1, 0, 0, 0, 2, 0, 0, 0, 1, 0, 0, -50)

[node name="@StaticBody3D@26392" parent="NavigationRegion3D" instance=ExtResource("12_rhbo5")]
transform = Transform3D(1, 0, 0, 0, 2, 0, 0, 0, 1, 0, 0, 50)

[node name="@StaticBody3D@26393" parent="NavigationRegion3D" instance=ExtResource("12_rhbo5")]
transform = Transform3D(1, 0, 0, 0, 2, 0, 0, 0, 1, 2, 0, -50)

[node name="@StaticBody3D@26394" parent="NavigationRegion3D" instance=ExtResource("12_rhbo5")]
transform = Transform3D(1, 0, 0, 0, 2, 0, 0, 0, 1, 2, 0, 50)

[node name="@StaticBody3D@26395" parent="NavigationRegion3D" instance=ExtResource("12_rhbo5")]
transform = Transform3D(1, 0, 0, 0, 2, 0, 0, 0, 1, 4, 0, -50)

[node name="@StaticBody3D@26396" parent="NavigationRegion3D" instance=ExtResource("12_rhbo5")]
transform = Transform3D(1, 0, 0, 0, 2, 0, 0, 0, 1, 4, 0, 50)

[node name="@StaticBody3D@26397" parent="NavigationRegion3D" instance=ExtResource("12_rhbo5")]
transform = Transform3D(1, 0, 0, 0, 2, 0, 0, 0, 1, 6, 0, -50)

[node name="@StaticBody3D@26398" parent="NavigationRegion3D" instance=ExtResource("12_rhbo5")]
transform = Transform3D(1, 0, 0, 0, 2, 0, 0, 0, 1, 6, 0, 50)

[node name="@StaticBody3D@26399" parent="NavigationRegion3D" instance=ExtResource("12_rhbo5")]
transform = Transform3D(1, 0, 0, 0, 2, 0, 0, 0, 1, 8, 0, -50)

[node name="@StaticBody3D@26400" parent="NavigationRegion3D" instance=ExtResource("12_rhbo5")]
transform = Transform3D(1, 0, 0, 0, 2, 0, 0, 0, 1, 8, 0, 50)

[node name="@StaticBody3D@26401" parent="NavigationRegion3D" instance=ExtResource("12_rhbo5")]
transform = Transform3D(1, 0, 0, 0, 2, 0, 0, 0, 1, 10, 0, -50)

[node name="@StaticBody3D@26402" parent="NavigationRegion3D" instance=ExtResource("12_rhbo5")]
transform = Transform3D(1, 0, 0, 0, 2, 0, 0, 0, 1, 10, 0, 50)

[node name="@StaticBody3D@26403" parent="NavigationRegion3D" instance=ExtResource("12_rhbo5")]
transform = Transform3D(1, 0, 0, 0, 2, 0, 0, 0, 1, 12, 0, -50)

[node name="@StaticBody3D@26404" parent="NavigationRegion3D" instance=ExtResource("12_rhbo5")]
transform = Transform3D(1, 0, 0, 0, 2, 0, 0, 0, 1, 12, 0, 50)

[node name="@StaticBody3D@26405" parent="NavigationRegion3D" instance=ExtResource("12_rhbo5")]
transform = Transform3D(1, 0, 0, 0, 2, 0, 0, 0, 1, 14, 0, -50)

[node name="@StaticBody3D@26406" parent="NavigationRegion3D" instance=ExtResource("12_rhbo5")]
transform = Transform3D(1, 0, 0, 0, 2, 0, 0, 0, 1, 14, 0, 50)

[node name="@StaticBody3D@26407" parent="NavigationRegion3D" instance=ExtResource("12_rhbo5")]
transform = Transform3D(1, 0, 0, 0, 2, 0, 0, 0, 1, 16, 0, -50)

[node name="@StaticBody3D@26408" parent="NavigationRegion3D" instance=ExtResource("12_rhbo5")]
transform = Transform3D(1, 0, 0, 0, 2, 0, 0, 0, 1, 16, 0, 50)

[node name="@StaticBody3D@26409" parent="NavigationRegion3D" instance=ExtResource("12_rhbo5")]
transform = Transform3D(1, 0, 0, 0, 2, 0, 0, 0, 1, 18, 0, -50)

[node name="@StaticBody3D@26410" parent="NavigationRegion3D" instance=ExtResource("12_rhbo5")]
transform = Transform3D(1, 0, 0, 0, 2, 0, 0, 0, 1, 18, 0, 50)

[node name="@StaticBody3D@26411" parent="NavigationRegion3D" instance=ExtResource("12_rhbo5")]
transform = Transform3D(1, 0, 0, 0, 2, 0, 0, 0, 1, 20, 0, -50)

[node name="@StaticBody3D@26412" parent="NavigationRegion3D" instance=ExtResource("12_rhbo5")]
transform = Transform3D(1, 0, 0, 0, 2, 0, 0, 0, 1, 20, 0, 50)

[node name="@StaticBody3D@26413" parent="NavigationRegion3D" instance=ExtResource("12_rhbo5")]
transform = Transform3D(1, 0, 0, 0, 2, 0, 0, 0, 1, 22, 0, -50)

[node name="@StaticBody3D@26414" parent="NavigationRegion3D" instance=ExtResource("12_rhbo5")]
transform = Transform3D(1, 0, 0, 0, 2, 0, 0, 0, 1, 22, 0, 50)

[node name="@StaticBody3D@26415" parent="NavigationRegion3D" instance=ExtResource("12_rhbo5")]
transform = Transform3D(1, 0, 0, 0, 2, 0, 0, 0, 1, 24, 0, -50)

[node name="@StaticBody3D@26416" parent="NavigationRegion3D" instance=ExtResource("12_rhbo5")]
transform = Transform3D(1, 0, 0, 0, 2, 0, 0, 0, 1, 24, 0, 50)

[node name="@StaticBody3D@26417" parent="NavigationRegion3D" instance=ExtResource("12_rhbo5")]
transform = Transform3D(1, 0, 0, 0, 2, 0, 0, 0, 1, 26, 0, -50)

[node name="@StaticBody3D@26418" parent="NavigationRegion3D" instance=ExtResource("12_rhbo5")]
transform = Transform3D(1, 0, 0, 0, 2, 0, 0, 0, 1, 26, 0, 50)

[node name="@StaticBody3D@26419" parent="NavigationRegion3D" instance=ExtResource("12_rhbo5")]
transform = Transform3D(1, 0, 0, 0, 2, 0, 0, 0, 1, 28, 0, -50)

[node name="@StaticBody3D@26420" parent="NavigationRegion3D" instance=ExtResource("12_rhbo5")]
transform = Transform3D(1, 0, 0, 0, 2, 0, 0, 0, 1, 28, 0, 50)

[node name="@StaticBody3D@26421" parent="NavigationRegion3D" instance=ExtResource("12_rhbo5")]
transform = Transform3D(1, 0, 0, 0, 2, 0, 0, 0, 1, 30, 0, -50)

[node name="@StaticBody3D@26422" parent="NavigationRegion3D" instance=ExtResource("12_rhbo5")]
transform = Transform3D(1, 0, 0, 0, 2, 0, 0, 0, 1, 30, 0, 50)

[node name="@StaticBody3D@26423" parent="NavigationRegion3D" instance=ExtResource("12_rhbo5")]
transform = Transform3D(1, 0, 0, 0, 2, 0, 0, 0, 1, 32, 0, -50)

[node name="@StaticBody3D@26424" parent="NavigationRegion3D" instance=ExtResource("12_rhbo5")]
transform = Transform3D(1, 0, 0, 0, 2, 0, 0, 0, 1, 32, 0, 50)

[node name="@StaticBody3D@26425" parent="NavigationRegion3D" instance=ExtResource("12_rhbo5")]
transform = Transform3D(1, 0, 0, 0, 2, 0, 0, 0, 1, 34, 0, -50)

[node name="@StaticBody3D@26426" parent="NavigationRegion3D" instance=ExtResource("12_rhbo5")]
transform = Transform3D(1, 0, 0, 0, 2, 0, 0, 0, 1, 34, 0, 50)

[node name="@StaticBody3D@26427" parent="NavigationRegion3D" instance=ExtResource("12_rhbo5")]
transform = Transform3D(1, 0, 0, 0, 2, 0, 0, 0, 1, 36, 0, -50)

[node name="@StaticBody3D@26428" parent="NavigationRegion3D" instance=ExtResource("12_rhbo5")]
transform = Transform3D(1, 0, 0, 0, 2, 0, 0, 0, 1, 36, 0, 50)

[node name="@StaticBody3D@26429" parent="NavigationRegion3D" instance=ExtResource("12_rhbo5")]
transform = Transform3D(1, 0, 0, 0, 2, 0, 0, 0, 1, 38, 0, -50)

[node name="@StaticBody3D@26430" parent="NavigationRegion3D" instance=ExtResource("12_rhbo5")]
transform = Transform3D(1, 0, 0, 0, 2, 0, 0, 0, 1, 38, 0, 50)

[node name="@StaticBody3D@26431" parent="NavigationRegion3D" instance=ExtResource("12_rhbo5")]
transform = Transform3D(1, 0, 0, 0, 2, 0, 0, 0, 1, 40, 0, -50)

[node name="@StaticBody3D@26432" parent="NavigationRegion3D" instance=ExtResource("12_rhbo5")]
transform = Transform3D(1, 0, 0, 0, 2, 0, 0, 0, 1, 40, 0, 50)

[node name="@StaticBody3D@26433" parent="NavigationRegion3D" instance=ExtResource("12_rhbo5")]
transform = Transform3D(1, 0, 0, 0, 2, 0, 0, 0, 1, 42, 0, -50)

[node name="@StaticBody3D@26434" parent="NavigationRegion3D" instance=ExtResource("12_rhbo5")]
transform = Transform3D(1, 0, 0, 0, 2, 0, 0, 0, 1, 42, 0, 50)

[node name="@StaticBody3D@26435" parent="NavigationRegion3D" instance=ExtResource("12_rhbo5")]
transform = Transform3D(1, 0, 0, 0, 2, 0, 0, 0, 1, 44, 0, -50)

[node name="@StaticBody3D@26436" parent="NavigationRegion3D" instance=ExtResource("12_rhbo5")]
transform = Transform3D(1, 0, 0, 0, 2, 0, 0, 0, 1, 44, 0, 50)

[node name="@StaticBody3D@26437" parent="NavigationRegion3D" instance=ExtResource("12_rhbo5")]
transform = Transform3D(1, 0, 0, 0, 2, 0, 0, 0, 1, 46, 0, -50)

[node name="@StaticBody3D@26438" parent="NavigationRegion3D" instance=ExtResource("12_rhbo5")]
transform = Transform3D(1, 0, 0, 0, 2, 0, 0, 0, 1, 46, 0, 50)

[node name="@StaticBody3D@26439" parent="NavigationRegion3D" instance=ExtResource("12_rhbo5")]
transform = Transform3D(1, 0, 0, 0, 2, 0, 0, 0, 1, 48, 0, -50)

[node name="@StaticBody3D@26440" parent="NavigationRegion3D" instance=ExtResource("12_rhbo5")]
transform = Transform3D(1, 0, 0, 0, 2, 0, 0, 0, 1, 48, 0, 50)

[node name="@StaticBody3D@26441" parent="NavigationRegion3D" instance=ExtResource("12_rhbo5")]
transform = Transform3D(1, 0, 0, 0, 2, 0, 0, 0, 1, 50, 0, -50)

[node name="@StaticBody3D@26442" parent="NavigationRegion3D" instance=ExtResource("12_rhbo5")]
transform = Transform3D(1, 0, 0, 0, 2, 0, 0, 0, 1, 50, 0, 50)

[node name="@StaticBody3D@26443" parent="NavigationRegion3D" instance=ExtResource("12_rhbo5")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 2, 0, -1, 0, -4.37114e-08, -50, 0, -50)

[node name="@StaticBody3D@26444" parent="NavigationRegion3D" instance=ExtResource("12_rhbo5")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 2, 0, -1, 0, -4.37114e-08, 50, 0, -50)

[node name="@StaticBody3D@26445" parent="NavigationRegion3D" instance=ExtResource("12_rhbo5")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 2, 0, -1, 0, -4.37114e-08, -50, 0, -48)

[node name="@StaticBody3D@26446" parent="NavigationRegion3D" instance=ExtResource("12_rhbo5")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 2, 0, -1, 0, -4.37114e-08, 50, 0, -48)

[node name="@StaticBody3D@26447" parent="NavigationRegion3D" instance=ExtResource("12_rhbo5")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 2, 0, -1, 0, -4.37114e-08, -50, 0, -46)

[node name="@StaticBody3D@26448" parent="NavigationRegion3D" instance=ExtResource("12_rhbo5")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 2, 0, -1, 0, -4.37114e-08, 50, 0, -46)

[node name="@StaticBody3D@26449" parent="NavigationRegion3D" instance=ExtResource("12_rhbo5")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 2, 0, -1, 0, -4.37114e-08, -50, 0, -44)

[node name="@StaticBody3D@26450" parent="NavigationRegion3D" instance=ExtResource("12_rhbo5")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 2, 0, -1, 0, -4.37114e-08, 50, 0, -44)

[node name="@StaticBody3D@26451" parent="NavigationRegion3D" instance=ExtResource("12_rhbo5")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 2, 0, -1, 0, -4.37114e-08, -50, 0, -42)

[node name="@StaticBody3D@26452" parent="NavigationRegion3D" instance=ExtResource("12_rhbo5")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 2, 0, -1, 0, -4.37114e-08, 50, 0, -42)

[node name="@StaticBody3D@26453" parent="NavigationRegion3D" instance=ExtResource("12_rhbo5")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 2, 0, -1, 0, -4.37114e-08, -50, 0, -40)

[node name="@StaticBody3D@26454" parent="NavigationRegion3D" instance=ExtResource("12_rhbo5")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 2, 0, -1, 0, -4.37114e-08, 50, 0, -40)

[node name="@StaticBody3D@26455" parent="NavigationRegion3D" instance=ExtResource("12_rhbo5")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 2, 0, -1, 0, -4.37114e-08, -50, 0, -38)

[node name="@StaticBody3D@26456" parent="NavigationRegion3D" instance=ExtResource("12_rhbo5")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 2, 0, -1, 0, -4.37114e-08, 50, 0, -38)

[node name="@StaticBody3D@26457" parent="NavigationRegion3D" instance=ExtResource("12_rhbo5")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 2, 0, -1, 0, -4.37114e-08, -50, 0, -36)

[node name="@StaticBody3D@26458" parent="NavigationRegion3D" instance=ExtResource("12_rhbo5")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 2, 0, -1, 0, -4.37114e-08, 50, 0, -36)

[node name="@StaticBody3D@26459" parent="NavigationRegion3D" instance=ExtResource("12_rhbo5")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 2, 0, -1, 0, -4.37114e-08, -50, 0, -34)

[node name="@StaticBody3D@26460" parent="NavigationRegion3D" instance=ExtResource("12_rhbo5")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 2, 0, -1, 0, -4.37114e-08, 50, 0, -34)

[node name="@StaticBody3D@26461" parent="NavigationRegion3D" instance=ExtResource("12_rhbo5")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 2, 0, -1, 0, -4.37114e-08, -50, 0, -32)

[node name="@StaticBody3D@26462" parent="NavigationRegion3D" instance=ExtResource("12_rhbo5")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 2, 0, -1, 0, -4.37114e-08, 50, 0, -32)

[node name="@StaticBody3D@26463" parent="NavigationRegion3D" instance=ExtResource("12_rhbo5")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 2, 0, -1, 0, -4.37114e-08, -50, 0, -30)

[node name="@StaticBody3D@26464" parent="NavigationRegion3D" instance=ExtResource("12_rhbo5")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 2, 0, -1, 0, -4.37114e-08, 50, 0, -30)

[node name="@StaticBody3D@26465" parent="NavigationRegion3D" instance=ExtResource("12_rhbo5")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 2, 0, -1, 0, -4.37114e-08, -50, 0, -28)

[node name="@StaticBody3D@26466" parent="NavigationRegion3D" instance=ExtResource("12_rhbo5")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 2, 0, -1, 0, -4.37114e-08, 50, 0, -28)

[node name="@StaticBody3D@26467" parent="NavigationRegion3D" instance=ExtResource("12_rhbo5")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 2, 0, -1, 0, -4.37114e-08, -50, 0, -26)

[node name="@StaticBody3D@26468" parent="NavigationRegion3D" instance=ExtResource("12_rhbo5")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 2, 0, -1, 0, -4.37114e-08, 50, 0, -26)

[node name="@StaticBody3D@26469" parent="NavigationRegion3D" instance=ExtResource("12_rhbo5")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 2, 0, -1, 0, -4.37114e-08, -50, 0, -24)

[node name="@StaticBody3D@26470" parent="NavigationRegion3D" instance=ExtResource("12_rhbo5")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 2, 0, -1, 0, -4.37114e-08, 50, 0, -24)

[node name="@StaticBody3D@26471" parent="NavigationRegion3D" instance=ExtResource("12_rhbo5")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 2, 0, -1, 0, -4.37114e-08, -50, 0, -22)

[node name="@StaticBody3D@26472" parent="NavigationRegion3D" instance=ExtResource("12_rhbo5")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 2, 0, -1, 0, -4.37114e-08, 50, 0, -22)

[node name="@StaticBody3D@26473" parent="NavigationRegion3D" instance=ExtResource("12_rhbo5")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 2, 0, -1, 0, -4.37114e-08, -50, 0, -20)

[node name="@StaticBody3D@26474" parent="NavigationRegion3D" instance=ExtResource("12_rhbo5")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 2, 0, -1, 0, -4.37114e-08, 50, 0, -20)

[node name="@StaticBody3D@26475" parent="NavigationRegion3D" instance=ExtResource("12_rhbo5")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 2, 0, -1, 0, -4.37114e-08, -50, 0, -18)

[node name="@StaticBody3D@26476" parent="NavigationRegion3D" instance=ExtResource("12_rhbo5")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 2, 0, -1, 0, -4.37114e-08, 50, 0, -18)

[node name="@StaticBody3D@26477" parent="NavigationRegion3D" instance=ExtResource("12_rhbo5")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 2, 0, -1, 0, -4.37114e-08, -50, 0, -16)

[node name="@StaticBody3D@26478" parent="NavigationRegion3D" instance=ExtResource("12_rhbo5")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 2, 0, -1, 0, -4.37114e-08, 50, 0, -16)

[node name="@StaticBody3D@26479" parent="NavigationRegion3D" instance=ExtResource("12_rhbo5")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 2, 0, -1, 0, -4.37114e-08, -50, 0, -14)

[node name="@StaticBody3D@26480" parent="NavigationRegion3D" instance=ExtResource("12_rhbo5")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 2, 0, -1, 0, -4.37114e-08, 50, 0, -14)

[node name="@StaticBody3D@26481" parent="NavigationRegion3D" instance=ExtResource("12_rhbo5")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 2, 0, -1, 0, -4.37114e-08, -50, 0, -12)

[node name="@StaticBody3D@26482" parent="NavigationRegion3D" instance=ExtResource("12_rhbo5")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 2, 0, -1, 0, -4.37114e-08, 50, 0, -12)

[node name="@StaticBody3D@26483" parent="NavigationRegion3D" instance=ExtResource("12_rhbo5")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 2, 0, -1, 0, -4.37114e-08, -50, 0, -10)

[node name="@StaticBody3D@26484" parent="NavigationRegion3D" instance=ExtResource("12_rhbo5")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 2, 0, -1, 0, -4.37114e-08, 50, 0, -10)

[node name="@StaticBody3D@26485" parent="NavigationRegion3D" instance=ExtResource("12_rhbo5")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 2, 0, -1, 0, -4.37114e-08, -50, 0, -8)

[node name="@StaticBody3D@26486" parent="NavigationRegion3D" instance=ExtResource("12_rhbo5")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 2, 0, -1, 0, -4.37114e-08, 50, 0, -8)

[node name="@StaticBody3D@26487" parent="NavigationRegion3D" instance=ExtResource("12_rhbo5")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 2, 0, -1, 0, -4.37114e-08, -50, 0, -6)

[node name="@StaticBody3D@26488" parent="NavigationRegion3D" instance=ExtResource("12_rhbo5")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 2, 0, -1, 0, -4.37114e-08, 50, 0, -6)

[node name="@StaticBody3D@26489" parent="NavigationRegion3D" instance=ExtResource("12_rhbo5")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 2, 0, -1, 0, -4.37114e-08, -50, 0, -4)

[node name="@StaticBody3D@26490" parent="NavigationRegion3D" instance=ExtResource("12_rhbo5")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 2, 0, -1, 0, -4.37114e-08, 50, 0, -4)

[node name="@StaticBody3D@26491" parent="NavigationRegion3D" instance=ExtResource("12_rhbo5")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 2, 0, -1, 0, -4.37114e-08, -50, 0, -2)

[node name="@StaticBody3D@26492" parent="NavigationRegion3D" instance=ExtResource("12_rhbo5")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 2, 0, -1, 0, -4.37114e-08, 50, 0, -2)

[node name="@StaticBody3D@26493" parent="NavigationRegion3D" instance=ExtResource("12_rhbo5")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 2, 0, -1, 0, -4.37114e-08, -50, 0, 0)

[node name="@StaticBody3D@26494" parent="NavigationRegion3D" instance=ExtResource("12_rhbo5")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 2, 0, -1, 0, -4.37114e-08, 50, 0, 0)

[node name="@StaticBody3D@26495" parent="NavigationRegion3D" instance=ExtResource("12_rhbo5")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 2, 0, -1, 0, -4.37114e-08, -50, 0, 2)

[node name="@StaticBody3D@26496" parent="NavigationRegion3D" instance=ExtResource("12_rhbo5")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 2, 0, -1, 0, -4.37114e-08, 50, 0, 2)

[node name="@StaticBody3D@26497" parent="NavigationRegion3D" instance=ExtResource("12_rhbo5")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 2, 0, -1, 0, -4.37114e-08, -50, 0, 4)

[node name="@StaticBody3D@26498" parent="NavigationRegion3D" instance=ExtResource("12_rhbo5")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 2, 0, -1, 0, -4.37114e-08, 50, 0, 4)

[node name="@StaticBody3D@26499" parent="NavigationRegion3D" instance=ExtResource("12_rhbo5")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 2, 0, -1, 0, -4.37114e-08, -50, 0, 6)

[node name="@StaticBody3D@26500" parent="NavigationRegion3D" instance=ExtResource("12_rhbo5")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 2, 0, -1, 0, -4.37114e-08, 50, 0, 6)

[node name="@StaticBody3D@26501" parent="NavigationRegion3D" instance=ExtResource("12_rhbo5")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 2, 0, -1, 0, -4.37114e-08, -50, 0, 8)

[node name="@StaticBody3D@26502" parent="NavigationRegion3D" instance=ExtResource("12_rhbo5")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 2, 0, -1, 0, -4.37114e-08, 50, 0, 8)

[node name="@StaticBody3D@26503" parent="NavigationRegion3D" instance=ExtResource("12_rhbo5")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 2, 0, -1, 0, -4.37114e-08, -50, 0, 10)

[node name="@StaticBody3D@26504" parent="NavigationRegion3D" instance=ExtResource("12_rhbo5")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 2, 0, -1, 0, -4.37114e-08, 50, 0, 10)

[node name="@StaticBody3D@26505" parent="NavigationRegion3D" instance=ExtResource("12_rhbo5")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 2, 0, -1, 0, -4.37114e-08, -50, 0, 12)

[node name="@StaticBody3D@26506" parent="NavigationRegion3D" instance=ExtResource("12_rhbo5")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 2, 0, -1, 0, -4.37114e-08, 50, 0, 12)

[node name="@StaticBody3D@26507" parent="NavigationRegion3D" instance=ExtResource("12_rhbo5")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 2, 0, -1, 0, -4.37114e-08, -50, 0, 14)

[node name="@StaticBody3D@26508" parent="NavigationRegion3D" instance=ExtResource("12_rhbo5")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 2, 0, -1, 0, -4.37114e-08, 50, 0, 14)

[node name="@StaticBody3D@26509" parent="NavigationRegion3D" instance=ExtResource("12_rhbo5")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 2, 0, -1, 0, -4.37114e-08, -50, 0, 16)

[node name="@StaticBody3D@26510" parent="NavigationRegion3D" instance=ExtResource("12_rhbo5")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 2, 0, -1, 0, -4.37114e-08, 50, 0, 16)

[node name="@StaticBody3D@26511" parent="NavigationRegion3D" instance=ExtResource("12_rhbo5")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 2, 0, -1, 0, -4.37114e-08, -50, 0, 18)

[node name="@StaticBody3D@26512" parent="NavigationRegion3D" instance=ExtResource("12_rhbo5")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 2, 0, -1, 0, -4.37114e-08, 50, 0, 18)

[node name="@StaticBody3D@26513" parent="NavigationRegion3D" instance=ExtResource("12_rhbo5")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 2, 0, -1, 0, -4.37114e-08, -50, 0, 20)

[node name="@StaticBody3D@26514" parent="NavigationRegion3D" instance=ExtResource("12_rhbo5")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 2, 0, -1, 0, -4.37114e-08, 50, 0, 20)

[node name="@StaticBody3D@26515" parent="NavigationRegion3D" instance=ExtResource("12_rhbo5")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 2, 0, -1, 0, -4.37114e-08, -50, 0, 22)

[node name="@StaticBody3D@26516" parent="NavigationRegion3D" instance=ExtResource("12_rhbo5")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 2, 0, -1, 0, -4.37114e-08, 50, 0, 22)

[node name="@StaticBody3D@26517" parent="NavigationRegion3D" instance=ExtResource("12_rhbo5")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 2, 0, -1, 0, -4.37114e-08, -50, 0, 24)

[node name="@StaticBody3D@26518" parent="NavigationRegion3D" instance=ExtResource("12_rhbo5")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 2, 0, -1, 0, -4.37114e-08, 50, 0, 24)

[node name="@StaticBody3D@26519" parent="NavigationRegion3D" instance=ExtResource("12_rhbo5")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 2, 0, -1, 0, -4.37114e-08, -50, 0, 26)

[node name="@StaticBody3D@26520" parent="NavigationRegion3D" instance=ExtResource("12_rhbo5")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 2, 0, -1, 0, -4.37114e-08, 50, 0, 26)

[node name="@StaticBody3D@26521" parent="NavigationRegion3D" instance=ExtResource("12_rhbo5")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 2, 0, -1, 0, -4.37114e-08, -50, 0, 28)

[node name="@StaticBody3D@26522" parent="NavigationRegion3D" instance=ExtResource("12_rhbo5")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 2, 0, -1, 0, -4.37114e-08, 50, 0, 28)

[node name="@StaticBody3D@26523" parent="NavigationRegion3D" instance=ExtResource("12_rhbo5")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 2, 0, -1, 0, -4.37114e-08, -50, 0, 30)

[node name="@StaticBody3D@26524" parent="NavigationRegion3D" instance=ExtResource("12_rhbo5")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 2, 0, -1, 0, -4.37114e-08, 50, 0, 30)

[node name="@StaticBody3D@26525" parent="NavigationRegion3D" instance=ExtResource("12_rhbo5")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 2, 0, -1, 0, -4.37114e-08, -50, 0, 32)

[node name="@StaticBody3D@26526" parent="NavigationRegion3D" instance=ExtResource("12_rhbo5")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 2, 0, -1, 0, -4.37114e-08, 50, 0, 32)

[node name="@StaticBody3D@26527" parent="NavigationRegion3D" instance=ExtResource("12_rhbo5")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 2, 0, -1, 0, -4.37114e-08, -50, 0, 34)

[node name="@StaticBody3D@26528" parent="NavigationRegion3D" instance=ExtResource("12_rhbo5")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 2, 0, -1, 0, -4.37114e-08, 50, 0, 34)

[node name="@StaticBody3D@26529" parent="NavigationRegion3D" instance=ExtResource("12_rhbo5")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 2, 0, -1, 0, -4.37114e-08, -50, 0, 36)

[node name="@StaticBody3D@26530" parent="NavigationRegion3D" instance=ExtResource("12_rhbo5")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 2, 0, -1, 0, -4.37114e-08, 50, 0, 36)

[node name="@StaticBody3D@26531" parent="NavigationRegion3D" instance=ExtResource("12_rhbo5")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 2, 0, -1, 0, -4.37114e-08, -50, 0, 38)

[node name="@StaticBody3D@26532" parent="NavigationRegion3D" instance=ExtResource("12_rhbo5")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 2, 0, -1, 0, -4.37114e-08, 50, 0, 38)

[node name="@StaticBody3D@26533" parent="NavigationRegion3D" instance=ExtResource("12_rhbo5")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 2, 0, -1, 0, -4.37114e-08, -50, 0, 40)

[node name="@StaticBody3D@26534" parent="NavigationRegion3D" instance=ExtResource("12_rhbo5")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 2, 0, -1, 0, -4.37114e-08, 50, 0, 40)

[node name="@StaticBody3D@26535" parent="NavigationRegion3D" instance=ExtResource("12_rhbo5")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 2, 0, -1, 0, -4.37114e-08, -50, 0, 42)

[node name="@StaticBody3D@26536" parent="NavigationRegion3D" instance=ExtResource("12_rhbo5")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 2, 0, -1, 0, -4.37114e-08, 50, 0, 42)

[node name="@StaticBody3D@26537" parent="NavigationRegion3D" instance=ExtResource("12_rhbo5")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 2, 0, -1, 0, -4.37114e-08, -50, 0, 44)

[node name="@StaticBody3D@26538" parent="NavigationRegion3D" instance=ExtResource("12_rhbo5")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 2, 0, -1, 0, -4.37114e-08, 50, 0, 44)

[node name="@StaticBody3D@26539" parent="NavigationRegion3D" instance=ExtResource("12_rhbo5")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 2, 0, -1, 0, -4.37114e-08, -50, 0, 46)

[node name="@StaticBody3D@26540" parent="NavigationRegion3D" instance=ExtResource("12_rhbo5")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 2, 0, -1, 0, -4.37114e-08, 50, 0, 46)

[node name="@StaticBody3D@26541" parent="NavigationRegion3D" instance=ExtResource("12_rhbo5")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 2, 0, -1, 0, -4.37114e-08, -50, 0, 48)

[node name="@StaticBody3D@26542" parent="NavigationRegion3D" instance=ExtResource("12_rhbo5")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 2, 0, -1, 0, -4.37114e-08, 50, 0, 48)

[node name="@StaticBody3D@26543" parent="NavigationRegion3D" instance=ExtResource("12_rhbo5")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 2, 0, -1, 0, -4.37114e-08, -50, 0, 50)

[node name="@StaticBody3D@26544" parent="NavigationRegion3D" instance=ExtResource("12_rhbo5")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 2, 0, -1, 0, -4.37114e-08, 50, 0, 50)

[node name="Tree" parent="NavigationRegion3D" instance=ExtResource("13_y7831")]
transform = Transform3D(1, -0.000582366, -0.000671913, 0.000582902, 1, 0.000785889, 0.000671448, -0.000786286, 0.999999, -9.56864, 0, 12.1382)
highlight_material = SubResource("StandardMaterial3D_01ds2")

[node name="@Node3D@26545" parent="NavigationRegion3D" instance=ExtResource("13_y7831")]
transform = Transform3D(1, -0.000582366, -0.000671913, 0.000582902, 1, 0.000785889, 0.000671448, -0.000786286, 0.999999, -5.69432, 0, -3.09782)
highlight_material = SubResource("StandardMaterial3D_01ds2")

[node name="@Node3D@26546" parent="NavigationRegion3D" instance=ExtResource("13_y7831")]
transform = Transform3D(1, -0.000582366, -0.000671913, 0.000582902, 1, 0.000785889, 0.000671448, -0.000786286, 0.999999, 39.421, 0, -12.9242)
highlight_material = SubResource("StandardMaterial3D_01ds2")

[node name="@Node3D@26547" parent="NavigationRegion3D" instance=ExtResource("13_y7831")]
transform = Transform3D(1, -0.000582366, -0.000671913, 0.000582902, 1, 0.000785889, 0.000671448, -0.000786286, 0.999999, -43.9072, 0, 7.75169)
highlight_material = SubResource("StandardMaterial3D_01ds2")

[node name="@Node3D@26548" parent="NavigationRegion3D" instance=ExtResource("13_y7831")]
transform = Transform3D(1, -0.000582366, -0.000671913, 0.000582902, 1, 0.000785889, 0.000671448, -0.000786286, 0.999999, 8.90353, 0, 6.95369)
highlight_material = SubResource("StandardMaterial3D_01ds2")

[node name="@Node3D@26549" parent="NavigationRegion3D" instance=ExtResource("13_y7831")]
transform = Transform3D(1, -0.000582366, -0.000671913, 0.000582902, 1, 0.000785889, 0.000671448, -0.000786286, 0.999999, 20.4318, 0, -1.96984)
highlight_material = SubResource("StandardMaterial3D_01ds2")

[node name="@Node3D@26550" parent="NavigationRegion3D" instance=ExtResource("13_y7831")]
transform = Transform3D(1, -0.000582366, -0.000671913, 0.000582902, 1, 0.000785889, 0.000671448, -0.000786286, 0.999999, -16.5731, 0, 43.3531)
highlight_material = SubResource("StandardMaterial3D_01ds2")

[node name="@Node3D@26551" parent="NavigationRegion3D" instance=ExtResource("13_y7831")]
transform = Transform3D(1, -0.000582366, -0.000671913, 0.000582902, 1, 0.000785889, 0.000671448, -0.000786286, 0.999999, -11.506, 0, -26.1965)
highlight_material = SubResource("StandardMaterial3D_01ds2")

[node name="@Node3D@26552" parent="NavigationRegion3D" instance=ExtResource("13_y7831")]
transform = Transform3D(1, -0.000582366, -0.000671913, 0.000582902, 1, 0.000785889, 0.000671448, -0.000786286, 0.999999, -11.1976, 0, 5.92133)
highlight_material = SubResource("StandardMaterial3D_01ds2")

[node name="@Node3D@26553" parent="NavigationRegion3D" instance=ExtResource("13_y7831")]
transform = Transform3D(1, -0.000582366, -0.000671913, 0.000582902, 1, 0.000785889, 0.000671448, -0.000786286, 0.999999, 24.3121, 0, 36.0194)
highlight_material = SubResource("StandardMaterial3D_01ds2")

[node name="Chest" parent="NavigationRegion3D" groups=["interactable"] instance=ExtResource("16_hrvyy")]
transform = Transform3D(0.393613, 0, -0.919276, 0, 1, 0, 0.919276, 0, 0.393613, -2.65523, 0, -27.0219)
collision_layer = 2
collision_mask = 0

[node name="@StaticBody3D@26554" parent="NavigationRegion3D" groups=["interactable"] instance=ExtResource("16_hrvyy")]
transform = Transform3D(0.999334, 0, 0.0365011, 0, 1, 0, -0.0365011, 0, 0.999334, -28.2571, 0, 3.10855)
collision_layer = 2
collision_mask = 0

[node name="@StaticBody3D@26555" parent="NavigationRegion3D" groups=["interactable"] instance=ExtResource("16_hrvyy")]
transform = Transform3D(0.458523, 0, 0.888682, 0, 1, 0, -0.888682, 0, 0.458523, -28.1363, 0, -11.2686)
collision_layer = 2
collision_mask = 0

[node name="@StaticBody3D@26556" parent="NavigationRegion3D" groups=["interactable"] instance=ExtResource("16_hrvyy")]
transform = Transform3D(-0.58085, 0, -0.814011, 0, 1, 0, 0.814011, 0, -0.58085, -16.7852, 0, 27.5966)
collision_layer = 2
collision_mask = 0

[node name="@StaticBody3D@26557" parent="NavigationRegion3D" groups=["interactable"] instance=ExtResource("16_hrvyy")]
transform = Transform3D(-0.721624, 0, 0.692286, 0, 1, 0, -0.692286, 0, -0.721624, 39.2551, 0, 9.69163)
collision_layer = 2
collision_mask = 0

[node name="Rock" parent="NavigationRegion3D" instance=ExtResource("17_r7f8x")]
transform = Transform3D(0.908232, 0, -0.292641, 0, 0.954214, 0, 0.292641, 0, 0.908232, -0.776619, 0.01, -16.1531)

[node name="@StaticBody3D@26558" parent="NavigationRegion3D" instance=ExtResource("17_r7f8x")]
transform = Transform3D(-0.0573218, 0, -1.00441, 0, 1.00604, 0, 1.00441, 0, -0.0573218, -0.577033, 0.01, -0.267938)

[node name="@StaticBody3D@26559" parent="NavigationRegion3D" instance=ExtResource("17_r7f8x")]
transform = Transform3D(0.764283, 0, -0.388501, 0, 0.857358, 0, 0.388501, 0, 0.764283, -18.3381, 0.01, 7.21311)

[node name="@StaticBody3D@26560" parent="NavigationRegion3D" instance=ExtResource("17_r7f8x")]
transform = Transform3D(-0.443018, 0, -0.755179, 0, 0.875535, 0, 0.755179, 0, -0.443018, 37.1152, 0.01, -28.6056)

[node name="@StaticBody3D@26561" parent="NavigationRegion3D" instance=ExtResource("17_r7f8x")]
transform = Transform3D(-0.927538, 0, -0.281067, 0, 0.969188, 0, 0.281067, 0, -0.927538, 37.2178, 0.01, -43.6434)

[node name="@StaticBody3D@26562" parent="NavigationRegion3D" instance=ExtResource("17_r7f8x")]
transform = Transform3D(-0.310934, 0, -0.947657, 0, 0.997363, 0, 0.947657, 0, -0.310934, -37.0908, 0.01, 33.3569)

[node name="@StaticBody3D@26563" parent="NavigationRegion3D" instance=ExtResource("17_r7f8x")]
transform = Transform3D(1.14692, 0, -0.273734, 0, 1.17914, 0, 0.273734, 0, 1.14692, 8.19433, 0.01, 12.337)

[node name="@StaticBody3D@26564" parent="NavigationRegion3D" instance=ExtResource("17_r7f8x")]
transform = Transform3D(-0.730665, 0, -0.743914, 0, 1.04273, 0, 0.743914, 0, -0.730665, 11.3341, 0.01, 27.8815)

[node name="@StaticBody3D@26565" parent="NavigationRegion3D" instance=ExtResource("17_r7f8x")]
transform = Transform3D(0.768982, 0, 0.822331, 0, 1.12586, 0, -0.822331, 0, 0.768982, 41.2081, 0.01, 1.58888)

[node name="@StaticBody3D@26566" parent="NavigationRegion3D" instance=ExtResource("17_r7f8x")]
transform = Transform3D(0.825563, 0, -0.0896016, 0, 0.830411, 0, 0.0896016, 0, 0.825563, 37.0946, 0.01, 31.3906)

[node name="@StaticBody3D@26567" parent="NavigationRegion3D" instance=ExtResource("17_r7f8x")]
transform = Transform3D(0.907968, 0, 0.192416, 0, 0.928132, 0, -0.192416, 0, 0.907968, 18.3653, 0.01, -5.53945)

[node name="@StaticBody3D@26568" parent="NavigationRegion3D" instance=ExtResource("17_r7f8x")]
transform = Transform3D(-0.925904, 0, 0.603507, 0, 1.10522, 0, -0.603507, 0, -0.925904, 11.415, 0.01, 19.7445)

[node name="@StaticBody3D@26569" parent="NavigationRegion3D" instance=ExtResource("17_r7f8x")]
transform = Transform3D(0.619685, 0, -0.544849, 0, 0.825148, 0, 0.544849, 0, 0.619685, -24.5126, 0.01, 4.99456)

[node name="@RigidBody3D@26572" parent="NavigationRegion3D" instance=ExtResource("18_78r4o")]
transform = Transform3D(0.700497, 0, -0.820687, 0, 1.07899, 0, 0.820687, 0, 0.700497, 13.2907, 0, -36.5665)
collision_layer = 2

[node name="@RigidBody3D@26573" parent="NavigationRegion3D" instance=ExtResource("18_78r4o")]
transform = Transform3D(0.934398, 0, -0.239472, 0, 0.964597, 0, 0.239472, 0, 0.934398, 9.60103, 0, -15.4969)
collision_layer = 2

[node name="@RigidBody3D@26574" parent="NavigationRegion3D" instance=ExtResource("18_78r4o")]
transform = Transform3D(1.11177, 0, -0.0573485, 0, 1.11325, 0, 0.0573485, 0, 1.11177, -28.7301, 0, 16.1135)
collision_layer = 2

[node name="@RigidBody3D@26575" parent="NavigationRegion3D" instance=ExtResource("18_78r4o")]
transform = Transform3D(-1.13751, 0, 0.116814, 0, 1.14349, 0, -0.116814, 0, -1.13751, 21.5951, 0, 21.8084)
collision_layer = 2

[node name="@RigidBody3D@26576" parent="NavigationRegion3D" instance=ExtResource("18_78r4o")]
transform = Transform3D(0.823143, 0, 0.703785, 0, 1.083, 0, -0.703785, 0, 0.823143, 40.9712, 0, 41.7118)
collision_layer = 2

[node name="@RigidBody3D@26577" parent="NavigationRegion3D" instance=ExtResource("18_78r4o")]
transform = Transform3D(-0.750676, 0, -0.760016, 0, 1.06824, 0, 0.760016, 0, -0.750676, -37.5946, 0, 44.1467)
collision_layer = 2

[node name="@RigidBody3D@26578" parent="NavigationRegion3D" instance=ExtResource("18_78r4o")]
transform = Transform3D(-0.843465, 0, -0.543253, 0, 1.00327, 0, 0.543253, 0, -0.843465, -15.763, 0, 37.6888)
collision_layer = 2

[node name="@RigidBody3D@26579" parent="NavigationRegion3D" instance=ExtResource("18_78r4o")]
transform = Transform3D(-0.645288, 0, 0.938838, 0, 1.13922, 0, -0.938838, 0, -0.645288, -31.9165, 0, -24.9962)
collision_layer = 2

[node name="@RigidBody3D@26580" parent="NavigationRegion3D" instance=ExtResource("18_78r4o")]
transform = Transform3D(-0.383996, 0, -1.0859, 0, 1.15179, 0, 1.0859, 0, -0.383996, 5.82824, 0, -38.3606)
collision_layer = 2

[node name="Flower" parent="NavigationRegion3D" instance=ExtResource("19_atd4b")]
transform = Transform3D(-0.578193, 0, -0.859689, 0, 1.03604, 0, 0.859689, 0, -0.578193, -23.7502, 0, -36.7452)

[node name="@Node3D@26581" parent="NavigationRegion3D" instance=ExtResource("19_atd4b")]
transform = Transform3D(-0.50202, 0, 0.733605, 0, 0.888932, 0, -0.733605, 0, -0.50202, 1.23977, 0, -43.9016)

[node name="@Node3D@26582" parent="NavigationRegion3D" instance=ExtResource("19_atd4b")]
transform = Transform3D(-0.608907, 0, -0.607068, 0, 0.859825, 0, 0.607068, 0, -0.608907, -4.31671, 0, -12.9249)

[node name="@Node3D@26583" parent="NavigationRegion3D" instance=ExtResource("19_atd4b")]
transform = Transform3D(-0.972058, 0, -0.000191516, 0, 0.972058, 0, 0.000191516, 0, -0.972058, -3.19209, 0, -0.729164)

[node name="@Node3D@26584" parent="NavigationRegion3D" instance=ExtResource("19_atd4b")]
transform = Transform3D(1.03631, 0, -0.338259, 0, 1.09012, 0, 0.338259, 0, 1.03631, 13.3029, 0, 17.738)

[node name="@Node3D@26585" parent="NavigationRegion3D" instance=ExtResource("19_atd4b")]
transform = Transform3D(-1.00075, 0, 0.863991, 0, 1.32211, 0, -0.863991, 0, -1.00075, -6.36741, 0, -38.4941)

[node name="@Node3D@26586" parent="NavigationRegion3D" instance=ExtResource("19_atd4b")]
transform = Transform3D(-0.555012, 0, 0.753859, 0, 0.936131, 0, -0.753859, 0, -0.555012, -24.1716, 0, 9.38628)

[node name="@Node3D@26587" parent="NavigationRegion3D" instance=ExtResource("19_atd4b")]
transform = Transform3D(-0.443192, 0, -0.752811, 0, 0.873581, 0, 0.752811, 0, -0.443192, -2.37317, 0, -31.29)

[node name="@Node3D@26588" parent="NavigationRegion3D" instance=ExtResource("19_atd4b")]
transform = Transform3D(1.10726, 0, -0.399286, 0, 1.17706, 0, 0.399286, 0, 1.10726, 3.34605, 0, 35.0123)

[node name="@Node3D@26589" parent="NavigationRegion3D" instance=ExtResource("19_atd4b")]
transform = Transform3D(-0.101873, 0, -0.68984, 0, 0.697321, 0, 0.68984, 0, -0.101873, -41.3215, 0, -30.2898)

[node name="@Node3D@26590" parent="NavigationRegion3D" instance=ExtResource("19_atd4b")]
transform = Transform3D(-0.20739, 0, -1.07072, 0, 1.09062, 0, 1.07072, 0, -0.20739, 27.2044, 0, 21.8001)

[node name="@Node3D@26591" parent="NavigationRegion3D" instance=ExtResource("19_atd4b")]
transform = Transform3D(1.06061, 0, 0.52628, 0, 1.184, 0, -0.52628, 0, 1.06061, 30.3305, 0, -2.60699)

[node name="@Node3D@26592" parent="NavigationRegion3D" instance=ExtResource("19_atd4b")]
transform = Transform3D(-0.908385, 0, 0.978055, 0, 1.33482, 0, -0.978055, 0, -0.908385, 24.9999, 0, 9.96357)

[node name="@Node3D@26593" parent="NavigationRegion3D" instance=ExtResource("19_atd4b")]
transform = Transform3D(0.526875, 0, -0.5194, 0, 0.739847, 0, 0.5194, 0, 0.526875, 4.9735, 0, 26.9232)

[node name="@Node3D@26594" parent="NavigationRegion3D" instance=ExtResource("19_atd4b")]
transform = Transform3D(-0.812738, 0, -0.853556, 0, 1.1786, 0, 0.853556, 0, -0.812738, -38.664, 0, 22.2627)

[node name="@Node3D@26595" parent="NavigationRegion3D" instance=ExtResource("19_atd4b")]
transform = Transform3D(-0.161383, 0, 0.838524, 0, 0.853913, 0, -0.838524, 0, -0.161383, 18.0338, 0, -38.7519)

[node name="@Node3D@26596" parent="NavigationRegion3D" instance=ExtResource("19_atd4b")]
transform = Transform3D(-0.827259, 0, -0.853458, 0, 1.18859, 0, 0.853458, 0, -0.827259, 17.3966, 0, -8.57801)

[node name="@Node3D@26597" parent="NavigationRegion3D" instance=ExtResource("19_atd4b")]
transform = Transform3D(0.434089, 0, 0.418108, 0, 0.6027, 0, -0.418108, 0, 0.434089, -27.9285, 0, 17.4948)

[node name="@Node3D@26598" parent="NavigationRegion3D" instance=ExtResource("19_atd4b")]
transform = Transform3D(0.702028, 0, 0.348929, 0, 0.783961, 0, -0.348929, 0, 0.702028, 18.5966, 0, -34.4604)

[node name="@Node3D@26599" parent="NavigationRegion3D" instance=ExtResource("19_atd4b")]
transform = Transform3D(0.674094, 0, -0.109638, 0, 0.682952, 0, 0.109638, 0, 0.674094, -3.90285, 0, 16.6744)
