[gd_scene load_steps=32 format=3 uid="uid://c0rj4dwj18nv8"]

[ext_resource type="Script" uid="uid://bo0h8t35yls55" path="res://Scripts/Levels/Level01.gd" id="1_hk2b6"]
[ext_resource type="PackedScene" uid="uid://d2xyw4d37ymv4" path="res://Scenes/Minimap.tscn" id="2_j32d5"]
[ext_resource type="PackedScene" uid="uid://ctxvyh1qr52ue" path="res://Scenes/PlayerAvatar.tscn" id="3_0f42i"]
[ext_resource type="PackedScene" uid="uid://do1sypgxbecbd" path="res://Scenes/ItemBoxes.tscn" id="4_swvig"]
[ext_resource type="PackedScene" uid="uid://b4l4phqqbvprm" path="res://Scenes/SkillBoxUI.tscn" id="5_dbohx"]
[ext_resource type="Script" uid="uid://f54r1khulcvh" path="res://Scripts/ModularLevelGenerator.gd" id="6_6ly8b"]
[ext_resource type="Resource" uid="uid://cvji54t3pjwfe" path="res://Resources/LevelConfigs/AllModulesConfig.tres" id="7_r5102"]
[ext_resource type="PackedScene" uid="uid://d2b5h1mlxxy7d" path="res://Scenes/FogOfWar.tscn" id="9_003du"]
[ext_resource type="PackedScene" uid="uid://b88l8pk1ebe1x" path="res://Scenes/player.tscn" id="11_82jtv"]
[ext_resource type="PackedScene" uid="uid://c3h8fj2xsp5oy" path="res://Scenes/Prefabs/Chest.tscn" id="14_1ks1q"]
[ext_resource type="PackedScene" uid="uid://dwusy8dd8usvo" path="res://Scenes/Prefabs/Wall.tscn" id="14_df8b0"]
[ext_resource type="PackedScene" uid="uid://crtnthqkksmri" path="res://Scenes/Prefabs/Tree.tscn" id="15_003du"]
[ext_resource type="Script" uid="uid://c3tr23vwvnmwf" path="res://Scripts/PatrolPoint.gd" id="15_6ly8b"]
[ext_resource type="Texture2D" uid="uid://c2ny0yi07rvcf" path="res://Environment/Floor/Floor01_Rocks_BaseColor.png" id="15_df8b0"]
[ext_resource type="Texture2D" uid="uid://dhfikoo16s5n0" path="res://Environment/Floor/Rocks_Metallic.png" id="16_003du"]
[ext_resource type="PackedScene" uid="uid://ddttv643pel23" path="res://Environment/Floor/Floor01_Custom.tscn" id="17_2bvpm"]
[ext_resource type="Resource" path="res://Resources/Items/Trap.tres" id="17_j32d5"]
[ext_resource type="Resource" path="res://Resources/Items/Torch.tres" id="18_j32d5"]
[ext_resource type="PackedScene" uid="uid://rvgn0irsuwao" path="res://Scenes/EnemyBoy01.tscn" id="19_0f42i"]
[ext_resource type="PackedScene" uid="uid://ervckea7fk57" path="res://Scenes/Enemy02.tscn" id="21_dbohx"]
[ext_resource type="PackedScene" uid="uid://b5dqjsb63wbhl" path="res://Scenes/Prefabs/Rock.tscn" id="21_xcdtp"]
[ext_resource type="PackedScene" uid="uid://bdq3b4e0mlgo4" path="res://Scenes/Prefabs/Barrel.tscn" id="22_cin4e"]
[ext_resource type="PackedScene" uid="uid://c6k7j3t3flhst" path="res://Scenes/Prefabs/Decoration.tscn" id="23_6j2fk"]
[ext_resource type="Script" uid="uid://de18ote8otj6u" path="res://Scripts/PatrolPointManager.gd" id="24_j2uky"]
[ext_resource type="Script" uid="uid://dt3st7cac7lok" path="res://Scripts/UI/UIManager.gd" id="25_ui_manager"]
[ext_resource type="Script" path="res://Scripts/CameraFollower.gd" id="26_camera_follower"]

[sub_resource type="ProceduralSkyMaterial" id="ProceduralSkyMaterial_gbvua"]
sky_top_color = Color(0.2, 0.4, 0.8, 1)
sky_horizon_color = Color(0.5, 0.7, 0.9, 1)
ground_bottom_color = Color(0.3, 0.5, 0.7, 1)
ground_horizon_color = Color(0.5, 0.7, 0.9, 1)

[sub_resource type="Sky" id="Sky_m0r78"]
sky_material = SubResource("ProceduralSkyMaterial_gbvua")

[sub_resource type="Environment" id="Environment_j4i7h"]
background_mode = 2
sky = SubResource("Sky_m0r78")
ambient_light_source = 3
ambient_light_color = Color(0.9, 0.9, 0.95, 1)
ambient_light_energy = 0.5
tonemap_mode = 2
ssao_enabled = true
ssao_radius = 2.0
ssao_intensity = 1.5
glow_enabled = true
glow_strength = 1.2

[sub_resource type="NavigationMesh" id="NavigationMesh_xcdtp"]
vertices = PackedVector3Array(-40.0197, 0.340398, -52.7552, -41.2697, 0.340398, -53.0052, -42.0197, 0.590398, -52.5052, -35.2697, 0.590398, -52.2552, -35.0197, 0.340398, -53.2552, -35.7697, 0.340398, -55.0052, -37.7697, 0.590398, -55.5052, -39.5197, 0.340398, -54.5052, -39.5197, 0.340398, -53.2552, -7.01971, 0.590398, -54.2552, -7.26971, 0.590398, -54.7552, -9.51971, 0.340398, -53.7552, -5.01971, 0.340398, -52.2552, -5.51971, 0.340398, -54.0052, -9.76971, 0.340398, -52.5052, -26.2697, 0.590398, -54.0052, -26.5197, 0.590398, -54.5052, -28.5197, 0.340398, -53.5052, -24.0197, 0.340398, -52.5052, -24.7697, 0.340398, -54.0052, -28.5197, 0.340398, -52.2552, 12.9803, 0.590398, -53.5052, 11.4803, 0.340398, -53.2552, 11.4803, 0.340398, -52.2552, 15.9803, 0.340398, -52.2552, 14.9803, 0.340398, -53.7552, 13.2303, 0.590398, -54.0052, -48.7697, 4.3404, -51.5052, -48.7697, 4.3404, -53.7552, -51.0197, 4.3404, -53.7552, -51.0197, 4.3404, -48.7552, -48.5197, 4.3404, -48.5052, 34.7303, 4.3404, -48.7552, 46.7303, 4.3404, -48.7552, 46.7303, 4.3404, -51.0052, 34.7303, 4.3404, -51.0052, -48.2697, 4.3404, -51.0052, -37.0197, 4.3404, -48.7552, -36.5197, 4.3404, -51.0052, -0.769714, 4.3404, -51.0052, -1.01971, 4.3404, -48.7552, 10.7303, 4.3404, -48.7552, 10.9803, 4.3404, -51.0052, -25.0197, 4.3404, -48.7552, -24.5197, 4.3404, -51.0052, 22.9803, 4.3404, -51.0052, 22.7303, 4.3404, -48.7552, -13.0197, 4.3404, -48.7552, -12.7697, 4.3404, -51.0052, 47.7303, 0.590398, -52.2552, 47.7303, 0.340398, -53.2552, 46.7303, 0.590398, -53.2552, 44.4803, 0.340398, -52.7552, 44.4803, 0.340398, -52.2552, 46.4803, 0.590398, -53.7552, 48.4803, 4.3404, -48.7552, 48.4803, 4.3404, -51.0052, 51.2303, 4.3404, -48.2552, 51.7303, 4.3404, -51.0052, 51.2303, 4.3404, -51.5052, 48.9803, 4.3404, -51.5052, 51.7303, 4.3404, -48.7552, 48.9803, 4.3404, -48.2552, 48.9803, 4.3404, -46.5052, 51.2303, 4.3404, -46.5052, 51.2303, 4.3404, -53.7552, 48.9803, 4.3404, -53.7552, 53.9803, 4.3404, -48.7552, 53.9803, 4.3404, -51.0052, -47.5197, 0.340398, -52.2552, -44.2697, 0.340398, -52.2552, -46.0197, 0.590398, -53.2552, -47.5197, 0.340398, -52.7552, 40.2303, 0.340398, -52.2552, 39.9803, 0.340398, -53.0052, 38.7303, 0.590398, -53.2552, 36.4803, 0.340398, -52.5052, -52.2697, 0.340398, -50.0052, -52.7697, 0.340398, -49.7552, -53.0197, 0.590398, -48.5052, -52.2697, 0.340398, -41.2552, -54.2697, 0.340398, -47.7552, -54.0197, 0.340398, -46.5052, -55.2697, 0.340398, -45.7552, -55.0197, 0.340398, -44.5052, -55.5197, 0.340398, -42.7552, -55.5197, 0.340398, -41.0052, -53.5197, 0.340398, -40.5052, -52.7697, 0.340398, -39.7552, -52.7697, 0.340398, -38.2552, -53.7697, 0.340398, -37.7552, -54.0197, 0.340398, -33.0052, -52.2697, 0.340398, -32.5052, -51.0197, 4.3404, -42.7552, -48.7697, 4.3404, -42.5052, -48.7697, 4.3404, 36.2448, -51.0197, 4.3404, 31.2448, -51.0197, 4.3404, 37.4948, -51.0197, 4.3404, 43.7448, -51.0197, 4.3404, 49.9948, -50.2697, 4.3404, 49.9948, -48.7697, 4.3404, 48.4948, -48.7697, 4.3404, 42.2448, -51.0197, 4.3404, -36.5052, -48.7697, 4.3404, -36.5052, -19.2697, 4.3404, 51.2448, -13.0197, 4.3404, 51.2448, -12.0197, 4.3404, 48.9948, -24.0197, 4.3404, 48.9948, -48.7697, 4.3404, -30.5052, -51.0197, 4.3404, -30.5052, -25.2697, 4.3404, 51.2448, 42.7303, 4.3404, 51.2448, 48.9803, 4.3404, 51.2448, 48.7303, 4.3404, 48.7448, 42.7303, 4.3404, 48.9948, -36.2697, 4.3404, 48.9948, -48.2697, 4.3404, 48.9948, -43.7697, 4.3404, 51.2448, -37.5197, 4.3404, 51.2448, 36.7303, 4.3404, 48.9948, 36.4803, 4.3404, 51.2448, 30.4803, 4.3404, 48.9948, 30.2303, 4.3404, 51.2448, 24.4803, 4.3404, 48.9948, 24.2303, 4.3404, 51.2448, -31.5197, 4.3404, 51.2448, -30.2697, 4.3404, 48.9948, 18.2303, 4.3404, 48.9948, 17.9803, 4.3404, 51.2448, -0.519714, 4.3404, 51.2448, 5.48029, 4.3404, 51.2448, 0.230286, 4.3404, 48.9948, 12.2303, 4.3404, 48.9948, 11.7303, 4.3404, 51.2448, -48.7697, 4.3404, 24.2448, -51.0197, 4.3404, 25.2448, -49.7697, 4.3404, 51.2448, -48.7697, 4.3404, -24.2552, -51.0197, 4.3404, -24.2552, -51.0197, 4.3404, -18.2552, -48.7697, 4.3404, -12.2552, -48.7697, 4.3404, 17.9948, -51.0197, 4.3404, 18.9948, -51.0197, 4.3404, -12.0052, -48.7697, 4.3404, -6.25518, -51.0197, 4.3404, -5.75518, -6.76971, 4.3404, 51.2448, -6.01971, 4.3404, 48.9948, -48.7697, 4.3404, -0.00517654, -51.0197, 4.3404, 0.494823, -51.0197, 4.3404, 6.49482, -48.7697, 4.3404, 11.9948, -51.0197, 4.3404, 12.7448, -11.2697, 0.590398, -19.0052, -10.0197, 0.590398, -19.0052, -9.01971, 0.340398, -19.7552, -12.5197, 0.340398, -15.7552, -11.5197, 0.590398, -17.0052, -11.7697, 0.340398, -18.2552, -37.7697, 0.590398, -17.2552, -37.0197, 0.590398, -15.7552, -47.5197, 0.590398, -15.2552, -41.7697, 0.590398, -15.0052, -41.2697, 0.590398, -17.5052, -47.5197, 0.590398, -23.5052, -6.76971, 0.340398, -19.2552, -0.769714, 0.590398, -20.7552, -1.01971, 0.590398, -23.2552, -11.5197, 0.340398, -47.5052, -23.5197, 0.340398, -47.5052, -47.5197, 0.340398, -31.5052, -11.5014, 0.840398, -26.2697, -13.0364, 0.340398, -26.2307, -11.5117, 0.340398, -27.7554, -11.5411, 0.340398, -24.734, -6.99638, 0.840398, -26.2368, -10.0164, 0.340398, -26.2587, -7.00669, 0.340398, -27.7724, -7.03606, 0.340398, -24.7511, -28.0106, 0.840398, -21.7665, -28.0009, 0.340398, -23.2302, -25.0406, 0.340398, -21.7446, -31.0104, 0.340398, -20.2468, -5.50105, 0.840398, -24.7401, -8.50548, 0.340398, -21.7737, -35.5197, 0.590398, -47.5052, -47.5197, 0.590398, -47.5052, -47.5197, 0.340398, -39.5052, -5.51971, 0.340398, -47.5052, 0.730286, 0.590398, -23.7552, 0.730286, 0.590398, -47.5052, 16.2303, 0.590398, -26.0052, 16.4803, 0.590398, -26.2552, 16.2303, 0.590398, -27.2552, 14.7303, 0.590398, -26.5052, 17.2303, 0.840398, -27.7552, 8.98029, 0.340398, -47.5052, 16.6803, 0.340398, -29.0718, 1.48029, 0.590398, -23.7552, 14.4803, 0.590398, -25.7552, 17.2303, 0.340398, -47.5052, 17.2303, 0.340398, -30.5766, 15.4654, 0.840398, -44.2643, 15.4904, 0.340398, -41.2593, 47.7303, 0.340398, -37.2552, 47.7303, 0.340398, -47.5052, 39.9803, 0.590398, -47.5052, 32.4803, 0.590398, -47.5052, 17.7303, 0.840398, -27.0052, 24.7303, 0.590398, -47.5052, 18.4555, 0.340398, -33.7604, 47.7303, 0.340398, -27.0052, 21.4718, 0.340398, -30.7628, 53.9803, 4.3404, 51.2448, 53.9803, 4.3404, 48.9948, 51.7303, 4.3404, 48.9948, 48.9803, 4.3404, -10.7552, 48.9803, 4.3404, 1.24482, 51.2303, 4.3404, 0.994823, 51.2303, 4.3404, -11.0052, 51.2303, 4.3404, 48.4948, 48.9803, 4.3404, 37.2448, 51.2303, 4.3404, 36.4948, 51.2303, 4.3404, -22.7552, 51.2303, 4.3404, -34.7552, 48.9803, 4.3404, -34.7552, 48.9803, 4.3404, -22.7552, 48.9803, 4.3404, 25.2448, 51.2303, 4.3404, 24.7448, 48.9803, 4.3404, 13.2448, 51.2303, 4.3404, 12.7448, 52.4803, 0.340398, -39.5052, 52.4803, 0.340398, -37.5052, 52.9803, 0.340398, -37.7552, 52.9803, 0.340398, -39.5052, 54.9803, 0.340398, -27.2552, 55.4803, 0.340398, -27.7552, 55.4803, 0.340398, -29.7552, 52.4803, 0.340398, -30.0052, 52.4803, 0.340398, -23.5052, 54.4803, 0.340398, -23.5052, 16.2303, 0.840398, -25.2552, 17.2303, 0.590398, -26.2552, 14.9803, 0.590398, -16.5052, 15.4803, 0.590398, -25.0052, 15.4803, 0.590398, -15.0052, 47.7303, 0.590398, -15.0052, 18.4705, 0.340398, -23.2714, 13.2303, 0.340398, -17.0052, 13.4803, 0.590398, -16.2552, 13.9803, 0.590398, -16.7552, 9.98029, 0.590398, -16.2552, 11.4803, 0.590398, -16.2552, 12.4803, 0.340398, -17.0052, 10.7303, 0.840398, -16.2552, 2.23029, 0.590398, -22.7552, 2.23029, 0.340398, -21.2552, 0.230286, 2.3404, -22.2552, 0.980286, 2.3404, -21.7552, 0.980286, 2.3404, -22.2552, -4.26971, 0.590398, -10.2552, -3.76971, 0.840398, -9.50518, -3.26971, 0.590398, -9.50518, -2.76971, 0.590398, -10.5052, -3.01971, 0.340398, -13.0052, -4.26971, 0.340398, -12.7552, 1.73029, 0.340398, -20.5052, -0.269714, 0.590398, -20.2552, -3.01971, 0.590398, -14.5052, -2.01971, 0.590398, -10.5052, 8.73029, 0.590398, -12.5052, 9.23029, 0.590398, -14.0052, 9.98029, 0.590398, -14.5052, -7.01971, 0.590398, -15.0052, -5.26971, 0.340398, -14.2552, -4.76971, 0.590398, -15.0052, -6.01971, 0.340398, -17.2552, -6.51971, 0.340398, -17.0052, -3.26971, 0.340398, -15.0052, -6.26971, 0.590398, -19.0052, -10.0197, 5.0904, -17.5052, -10.0197, 5.0904, -16.2552, -8.76971, 5.0904, -16.2552, -8.76971, 5.0904, -17.5052, -53.7697, 0.340398, -15.5052, -54.7697, 0.340398, -15.0052, -54.5197, 0.340398, -13.7552, -52.2697, 0.340398, -17.2552, -53.2697, 0.340398, -17.0052, -55.7697, 0.340398, -8.25518, -52.2697, 0.590398, -7.00518, -55.7697, 0.340398, -13.0052, -40.5197, 3.5904, -13.7552, -40.0197, 3.8404, -13.5052, -39.0197, 3.8404, -13.5052, -38.5197, 3.5904, -15.2552, -38.5197, 3.5904, -16.0052, -40.5197, 3.5904, -16.0052, -39.5197, 3.8404, -16.0052, -25.5197, 0.340398, 12.4948, -24.0197, 0.340398, 12.2448, -22.2697, 0.340398, 9.99482, -12.5197, 0.590398, -15.2552, -37.5197, 0.590398, -12.7552, -41.7697, 0.340398, -12.7552, -41.2697, 0.340398, -12.2552, -47.5197, 0.590398, -8.00518, -47.5197, 0.340398, 13.7448, -26.2697, 0.590398, 12.9948, -37.7697, 0.340398, -12.2552, -47.5197, 0.340398, 6.49482, -47.5197, 0.340398, -0.755177, -26.2697, 0.590398, 13.7448, -3.01971, 0.590398, -9.00518, -4.01971, 0.590398, -8.50518, -5.01971, 0.590398, -8.75518, -5.01971, 0.590398, -10.0052, -5.26971, 0.590398, -13.2552, -7.51971, 0.590398, -14.5052, -11.2697, 0.340398, -14.5052, -1.76971, 0.840398, -9.00518, 0.230286, 0.590398, -2.75518, -1.01971, 0.590398, 0.744823, -8.50443, 0.840398, -5.26649, -8.53932, 0.340398, -3.7676, -7.0145, 0.340398, -6.73241, -10.0346, 0.340398, -9.76426, 11.2303, 0.590398, -11.7552, 11.2303, 0.340398, -10.0052, 12.2303, 0.340398, -9.50518, 14.2303, 0.590398, -12.0052, 16.4803, 0.590398, 2.74482, 47.7303, 0.340398, 2.74482, 47.7303, 0.340398, -6.25518, 23.9208, 0.340398, 2.74482, 29.8731, 0.840398, 2.74482, 34.3374, 0.340398, 2.74482, 33.4606, 0.340398, 0.722527, 12.2303, 0.590398, -8.25518, 13.9803, 0.340398, 0.744823, 15.9803, 0.590398, 1.24482, 11.4803, 5.0904, -14.7552, 11.4803, 5.0904, -13.5052, 12.7303, 5.0904, -13.5052, 12.7303, 5.0904, -14.7552, 3.48029, 0.590398, -4.00518, 3.73029, 0.590398, -3.25518, 4.98029, 0.590398, -3.50518, 9.73029, 0.590398, -11.7552, 9.98029, 0.590398, -9.25518, -1.26971, 0.340398, -9.25518, 2.73029, 0.590398, -4.00518, 9.98029, 0.340398, -8.50518, -1.26971, 0.340398, -10.0052, 5.73029, 0.590398, -2.25518, 4.98029, 0.590398, -0.00517654, 11.4803, 0.840398, 1.49482, 10.3303, 0.340398, 0.744823, 10.1803, 0.340398, 1.19482, 10.4803, 0.590398, -7.75518, 11.9803, 0.590398, -7.75518, 55.9803, 0.340398, -1.25518, 56.4803, 0.340398, -1.50518, 56.2303, 0.340398, -3.25518, 53.7303, 0.340398, -3.25518, 52.7303, 0.340398, -4.00518, 52.9803, 0.340398, -6.50518, 52.4803, 0.590398, -6.75518, 52.4803, 0.340398, 3.24482, 55.4803, 0.340398, 2.99482, -52.5197, 0.340398, -0.505177, -53.5197, 0.340398, -0.00517654, -53.2697, 0.340398, 1.24482, -53.7697, 0.340398, 1.74482, -53.7697, 0.340398, 4.74482, -52.2697, 0.340398, 5.24482, -52.2697, 0.340398, -2.00518, 1.73029, 5.0904, -2.00518, 1.73029, 5.0904, -0.755177, 2.98029, 5.0904, -0.755177, 2.98029, 5.0904, -2.00518, 4.48029, 0.590398, 0.994823, 3.98029, 0.590398, 0.994823, 3.98029, 0.590398, 2.99482, 5.23029, 0.590398, 3.49482, 10.7303, 0.590398, 1.99482, 9.73029, 0.340398, 4.74482, 10.7303, 0.590398, 3.24482, 5.23029, 0.340398, 4.49482, -23.5197, 0.340398, 12.7448, -0.519714, 0.590398, 1.24482, 2.98029, 0.590398, 3.49482, 13.9803, 0.590398, 9.74482, 14.7303, 0.590398, 9.24482, 14.7303, 0.340398, 5.74482, 10.9803, 0.590398, 5.99482, 14.9803, 0.340398, 47.7448, 14.9803, 0.590398, 11.4948, 13.9803, 0.590398, 10.9948, 4.98029, 0.340398, 47.7448, 9.46584, 0.840398, 41.2252, 9.49078, 0.340398, 39.7203, 10.9805, 0.840398, 29.2346, 10.9855, 0.340398, 30.7676, 10.9754, 0.340398, 27.7517, 9.4909, 0.340398, 42.7302, 9.49557, 0.340398, 33.7335, 10.9759, 0.340398, 42.7412, 9.73029, 0.340398, 5.49482, 4.73029, 0.590398, 5.24482, 3.48029, 0.590398, 5.24482, 2.98029, 0.340398, 4.74482, -23.2697, 0.340398, 14.7448, -25.0197, 0.340398, 15.4948, -15.0197, 0.340398, 47.7448, -5.01971, 0.340398, 47.7448, -17.5047, 0.840398, 29.2638, -17.5346, 0.340398, 30.7357, -11.5237, 0.840398, 15.7208, -10.0387, 0.340398, 15.7318, -11.5184, 0.340398, 17.2235, -10.044, 0.340398, 14.229, -4.03944, 0.840398, 11.2225, -2.50443, 0.340398, 11.2335, -5.52445, 0.340398, 11.2615, -13.0087, 0.340398, 15.7598, -25.0197, 0.340398, 47.7448, 12.2303, 5.0904, 2.74482, 12.2303, 5.0904, 3.99482, 13.4803, 5.0904, 3.99482, 13.4803, 5.0904, 2.74482, 15.9803, 0.590398, 9.24482, 15.4803, 0.340398, 5.49482, 16.2303, 0.340398, 10.2448, 47.7303, 0.340398, 10.2448, 29.0004, 0.340398, 3.74673, 53.2303, 0.340398, 16.2448, 53.4803, 0.590398, 13.9948, 52.9803, 0.590398, 13.7448, 52.4803, 0.340398, 15.4948, 56.9803, 0.340398, 19.2448, 56.9803, 0.340398, 17.2448, 54.4803, 0.340398, 17.2448, 53.4803, 0.340398, 23.4948, 56.2303, 0.340398, 23.2448, 52.9803, 0.340398, 12.9948, 52.4803, 0.340398, 5.99482, 54.4803, 0.340398, 11.9948, 55.2303, 0.340398, 6.24482, 52.4803, 0.340398, 25.2448, 39.4803, 0.340398, 47.7448, 47.7303, 0.340398, 47.7448, 47.7303, 0.340398, 38.2448, 31.2303, 0.340398, 47.7448, 47.7303, 0.340398, 28.9948, 22.9803, 0.340398, 47.7448, 47.7303, 0.340398, 19.4948, 15.7303, 0.340398, 11.4948, 28.9621, 0.840398, 27.7509, 27.4769, 0.340398, 26.2201, 30.4971, 0.340398, 27.7618, 27.4776, 0.340398, 30.7293, -47.5197, 0.340398, 39.2448, -47.5197, 0.340398, 47.7448, -36.2697, 0.590398, 47.7448, -47.5197, 0.340398, 30.7448, -26.0197, 0.340398, 15.2448, -47.5197, 0.590398, 22.2448, -53.2697, 0.340398, 20.7448, -54.2697, 0.340398, 21.2448, -54.5197, 0.340398, 22.7448, -54.5197, 0.340398, 25.9948, -52.2697, 0.340398, 26.7448, -52.2697, 0.340398, 18.4948, -53.5197, 0.340398, 19.2448, 56.9803, 0.340398, 36.9948, 56.9803, 0.340398, 34.9948, 54.4803, 0.340398, 34.9948, 52.4803, 0.340398, 36.7448, 53.4803, 0.340398, 41.2448, 56.2303, 0.340398, 41.2448, 53.4803, 0.340398, 34.2448, 53.7303, 0.340398, 31.7448, 52.4803, 0.340398, 30.4948, 52.4803, 0.340398, 42.9948)
polygons = [PackedInt32Array(2, 1, 0), PackedInt32Array(4, 3, 5), PackedInt32Array(5, 3, 6), PackedInt32Array(6, 3, 7), PackedInt32Array(7, 3, 8), PackedInt32Array(0, 3, 2), PackedInt32Array(0, 8, 3), PackedInt32Array(9, 11, 10), PackedInt32Array(13, 12, 9), PackedInt32Array(9, 12, 11), PackedInt32Array(11, 12, 14), PackedInt32Array(15, 17, 16), PackedInt32Array(19, 18, 15), PackedInt32Array(15, 18, 17), PackedInt32Array(17, 18, 20), PackedInt32Array(22, 21, 23), PackedInt32Array(23, 21, 25), PackedInt32Array(23, 25, 24), PackedInt32Array(21, 26, 25), PackedInt32Array(29, 28, 27), PackedInt32Array(31, 30, 27), PackedInt32Array(27, 30, 29), PackedInt32Array(35, 34, 32), PackedInt32Array(32, 34, 33), PackedInt32Array(31, 27, 36), PackedInt32Array(31, 36, 37), PackedInt32Array(37, 36, 38), PackedInt32Array(42, 41, 39), PackedInt32Array(39, 41, 40), PackedInt32Array(44, 43, 38), PackedInt32Array(38, 43, 37), PackedInt32Array(35, 32, 45), PackedInt32Array(45, 32, 46), PackedInt32Array(48, 47, 44), PackedInt32Array(44, 47, 43), PackedInt32Array(45, 46, 42), PackedInt32Array(42, 46, 41), PackedInt32Array(39, 40, 48), PackedInt32Array(48, 40, 47), PackedInt32Array(50, 49, 51), PackedInt32Array(51, 49, 52), PackedInt32Array(52, 49, 53), PackedInt32Array(51, 52, 54), PackedInt32Array(56, 55, 34), PackedInt32Array(34, 55, 33), PackedInt32Array(56, 60, 55), PackedInt32Array(55, 60, 59), PackedInt32Array(55, 59, 58), PackedInt32Array(55, 58, 57), PackedInt32Array(58, 61, 57), PackedInt32Array(57, 64, 62), PackedInt32Array(62, 64, 63), PackedInt32Array(60, 66, 59), PackedInt32Array(59, 66, 65), PackedInt32Array(57, 62, 55), PackedInt32Array(58, 68, 61), PackedInt32Array(61, 68, 67), PackedInt32Array(72, 71, 69), PackedInt32Array(69, 71, 70), PackedInt32Array(74, 73, 75), PackedInt32Array(75, 73, 76), PackedInt32Array(78, 77, 79), PackedInt32Array(79, 77, 80), PackedInt32Array(82, 81, 79), PackedInt32Array(84, 83, 82), PackedInt32Array(87, 86, 85), PackedInt32Array(85, 84, 82), PackedInt32Array(88, 87, 80), PackedInt32Array(80, 87, 85), PackedInt32Array(80, 85, 82), PackedInt32Array(80, 82, 79), PackedInt32Array(90, 89, 91), PackedInt32Array(91, 89, 92), PackedInt32Array(88, 80, 89), PackedInt32Array(89, 80, 92), PackedInt32Array(94, 93, 31), PackedInt32Array(31, 93, 30), PackedInt32Array(97, 96, 95), PackedInt32Array(100, 99, 101), PackedInt32Array(101, 99, 98), PackedInt32Array(101, 98, 102), PackedInt32Array(104, 103, 94), PackedInt32Array(94, 103, 93), PackedInt32Array(106, 105, 107), PackedInt32Array(107, 105, 108), PackedInt32Array(110, 103, 109), PackedInt32Array(109, 103, 104), PackedInt32Array(108, 105, 111), PackedInt32Array(115, 114, 112), PackedInt32Array(112, 114, 113), PackedInt32Array(119, 118, 116), PackedInt32Array(116, 118, 117), PackedInt32Array(115, 112, 120), PackedInt32Array(120, 112, 121), PackedInt32Array(100, 101, 117), PackedInt32Array(123, 122, 121), PackedInt32Array(121, 122, 120), PackedInt32Array(95, 102, 97), PackedInt32Array(97, 102, 98), PackedInt32Array(125, 124, 123), PackedInt32Array(123, 124, 122), PackedInt32Array(127, 126, 116), PackedInt32Array(116, 126, 119), PackedInt32Array(129, 128, 125), PackedInt32Array(125, 128, 124), PackedInt32Array(132, 131, 130), PackedInt32Array(128, 129, 133), PackedInt32Array(133, 129, 134), PackedInt32Array(136, 135, 96), PackedInt32Array(96, 135, 95), PackedInt32Array(100, 117, 137), PackedInt32Array(137, 117, 118), PackedInt32Array(139, 110, 138), PackedInt32Array(138, 110, 109), PackedInt32Array(126, 127, 111), PackedInt32Array(111, 127, 108), PackedInt32Array(139, 138, 140), PackedInt32Array(140, 138, 141), PackedInt32Array(136, 143, 135), PackedInt32Array(135, 143, 142), PackedInt32Array(144, 140, 141), PackedInt32Array(141, 145, 144), PackedInt32Array(144, 145, 146), PackedInt32Array(148, 147, 107), PackedInt32Array(107, 147, 106), PackedInt32Array(150, 146, 149), PackedInt32Array(149, 146, 145), PackedInt32Array(147, 148, 130), PackedInt32Array(130, 148, 132), PackedInt32Array(150, 149, 151), PackedInt32Array(151, 149, 152), PackedInt32Array(134, 131, 133), PackedInt32Array(133, 131, 132), PackedInt32Array(153, 151, 152), PackedInt32Array(152, 142, 153), PackedInt32Array(153, 142, 143), PackedInt32Array(156, 155, 154), PackedInt32Array(158, 157, 159), PackedInt32Array(159, 157, 161), PackedInt32Array(159, 161, 160), PackedInt32Array(163, 162, 164), PackedInt32Array(164, 162, 165), PackedInt32Array(167, 166, 168), PackedInt32Array(168, 166, 156), PackedInt32Array(171, 183, 160), PackedInt32Array(160, 183, 154), PackedInt32Array(154, 185, 168), PackedInt32Array(168, 178, 169), PackedInt32Array(169, 174, 170), PackedInt32Array(170, 181, 171), PackedInt32Array(171, 181, 183), PackedInt32Array(183, 182, 154), PackedInt32Array(185, 184, 168), PackedInt32Array(154, 175, 185), PackedInt32Array(178, 174, 169), PackedInt32Array(168, 184, 178), PackedInt32Array(174, 173, 170), PackedInt32Array(170, 173, 181), PackedInt32Array(181, 180, 183), PackedInt32Array(182, 173, 154), PackedInt32Array(183, 180, 182), PackedInt32Array(185, 179, 184), PackedInt32Array(175, 177, 185), PackedInt32Array(154, 173, 175), PackedInt32Array(178, 177, 174), PackedInt32Array(184, 176, 178), PackedInt32Array(174, 172, 173), PackedInt32Array(173, 182, 181), PackedInt32Array(181, 182, 180), PackedInt32Array(179, 176, 184), PackedInt32Array(185, 177, 179), PackedInt32Array(175, 172, 177), PackedInt32Array(173, 172, 175), PackedInt32Array(177, 172, 174), PackedInt32Array(178, 176, 177), PackedInt32Array(179, 177, 176), PackedInt32Array(160, 154, 159), PackedInt32Array(168, 156, 154), PackedInt32Array(164, 165, 160), PackedInt32Array(160, 165, 171), PackedInt32Array(188, 187, 186), PackedInt32Array(168, 169, 189), PackedInt32Array(188, 186, 171), PackedInt32Array(171, 186, 170), PackedInt32Array(190, 168, 191), PackedInt32Array(191, 168, 189), PackedInt32Array(193, 192, 194), PackedInt32Array(194, 192, 195), PackedInt32Array(196, 194, 198), PackedInt32Array(198, 194, 190), PackedInt32Array(198, 190, 197), PackedInt32Array(197, 190, 191), PackedInt32Array(195, 200, 199), PackedInt32Array(199, 190, 195), PackedInt32Array(195, 190, 194), PackedInt32Array(201, 203, 197), PackedInt32Array(197, 204, 198), PackedInt32Array(198, 202, 196), PackedInt32Array(202, 204, 201), PackedInt32Array(203, 204, 197), PackedInt32Array(201, 204, 203), PackedInt32Array(204, 202, 198), PackedInt32Array(207, 206, 205), PackedInt32Array(208, 207, 205), PackedInt32Array(209, 211, 210), PackedInt32Array(210, 211, 201), PackedInt32Array(201, 211, 202), PackedInt32Array(202, 209, 196), PackedInt32Array(209, 202, 211), PackedInt32Array(209, 213, 212), PackedInt32Array(212, 213, 205), PackedInt32Array(205, 213, 208), PackedInt32Array(208, 213, 210), PackedInt32Array(210, 213, 209), PackedInt32Array(216, 215, 214), PackedInt32Array(114, 216, 113), PackedInt32Array(113, 216, 214), PackedInt32Array(220, 219, 217), PackedInt32Array(217, 219, 218), PackedInt32Array(114, 221, 216), PackedInt32Array(114, 222, 221), PackedInt32Array(221, 222, 223), PackedInt32Array(227, 226, 224), PackedInt32Array(224, 226, 225), PackedInt32Array(229, 223, 228), PackedInt32Array(228, 223, 222), PackedInt32Array(226, 63, 225), PackedInt32Array(225, 63, 64), PackedInt32Array(231, 229, 230), PackedInt32Array(230, 229, 228), PackedInt32Array(224, 220, 227), PackedInt32Array(227, 220, 217), PackedInt32Array(219, 231, 218), PackedInt32Array(218, 231, 230), PackedInt32Array(235, 234, 232), PackedInt32Array(232, 234, 233), PackedInt32Array(237, 236, 238), PackedInt32Array(238, 236, 239), PackedInt32Array(241, 240, 236), PackedInt32Array(236, 240, 239), PackedInt32Array(242, 192, 193), PackedInt32Array(193, 243, 242), PackedInt32Array(242, 243, 244), PackedInt32Array(244, 245, 242), PackedInt32Array(247, 248, 212), PackedInt32Array(212, 248, 209), PackedInt32Array(209, 248, 243), PackedInt32Array(243, 248, 244), PackedInt32Array(244, 248, 246), PackedInt32Array(246, 248, 247), PackedInt32Array(251, 250, 249), PackedInt32Array(251, 249, 244), PackedInt32Array(244, 249, 245), PackedInt32Array(253, 255, 254), PackedInt32Array(254, 255, 252), PackedInt32Array(252, 257, 256), PackedInt32Array(245, 249, 254), PackedInt32Array(245, 254, 200), PackedInt32Array(200, 254, 252), PackedInt32Array(200, 252, 256), PackedInt32Array(200, 256, 199), PackedInt32Array(260, 259, 258), PackedInt32Array(262, 261, 263), PackedInt32Array(263, 261, 264), PackedInt32Array(264, 261, 266), PackedInt32Array(264, 266, 265), PackedInt32Array(268, 267, 269), PackedInt32Array(269, 267, 270), PackedInt32Array(270, 267, 271), PackedInt32Array(271, 267, 272), PackedInt32Array(273, 272, 252), PackedInt32Array(252, 272, 267), PackedInt32Array(252, 267, 257), PackedInt32Array(264, 265, 270), PackedInt32Array(270, 265, 269), PackedInt32Array(275, 274, 276), PackedInt32Array(276, 274, 278), PackedInt32Array(276, 278, 277), PackedInt32Array(268, 269, 279), PackedInt32Array(280, 166, 167), PackedInt32Array(276, 277, 279), PackedInt32Array(279, 277, 280), PackedInt32Array(279, 280, 167), PackedInt32Array(279, 167, 268), PackedInt32Array(284, 283, 281), PackedInt32Array(281, 283, 282), PackedInt32Array(287, 286, 285), PackedInt32Array(285, 289, 288), PackedInt32Array(285, 288, 287), PackedInt32Array(287, 288, 290), PackedInt32Array(290, 288, 291), PackedInt32Array(290, 292, 287), PackedInt32Array(297, 296, 299), PackedInt32Array(299, 296, 298), PackedInt32Array(298, 296, 295), PackedInt32Array(298, 295, 294), PackedInt32Array(298, 294, 293), PackedInt32Array(300, 302, 301), PackedInt32Array(157, 303, 161), PackedInt32Array(161, 303, 304), PackedInt32Array(304, 303, 302), PackedInt32Array(304, 302, 300), PackedInt32Array(162, 163, 305), PackedInt32Array(305, 306, 162), PackedInt32Array(162, 306, 307), PackedInt32Array(309, 308, 300), PackedInt32Array(300, 308, 311), PackedInt32Array(300, 311, 310), PackedInt32Array(306, 310, 307), PackedInt32Array(307, 310, 312), PackedInt32Array(310, 304, 300), PackedInt32Array(312, 310, 311), PackedInt32Array(309, 313, 308), PackedInt32Array(263, 314, 262), PackedInt32Array(262, 314, 315), PackedInt32Array(317, 316, 318), PackedInt32Array(318, 316, 319), PackedInt32Array(319, 316, 320), PackedInt32Array(274, 275, 319), PackedInt32Array(319, 275, 318), PackedInt32Array(314, 321, 315), PackedInt32Array(315, 321, 322), PackedInt32Array(323, 325, 322), PackedInt32Array(322, 326, 316), PackedInt32Array(316, 327, 320), PackedInt32Array(320, 327, 303), PackedInt32Array(303, 327, 302), PackedInt32Array(302, 325, 323), PackedInt32Array(325, 326, 322), PackedInt32Array(326, 327, 316), PackedInt32Array(327, 325, 302), PackedInt32Array(325, 324, 326), PackedInt32Array(326, 324, 327), PackedInt32Array(327, 324, 325), PackedInt32Array(261, 317, 266), PackedInt32Array(266, 317, 318), PackedInt32Array(316, 315, 322), PackedInt32Array(329, 328, 330), PackedInt32Array(330, 328, 331), PackedInt32Array(333, 338, 334), PackedInt32Array(334, 338, 332), PackedInt32Array(332, 338, 335), PackedInt32Array(335, 338, 336), PackedInt32Array(336, 338, 337), PackedInt32Array(337, 338, 333), PackedInt32Array(330, 331, 339), PackedInt32Array(339, 331, 340), PackedInt32Array(340, 331, 341), PackedInt32Array(341, 331, 332), PackedInt32Array(332, 331, 246), PackedInt32Array(332, 246, 334), PackedInt32Array(334, 246, 247), PackedInt32Array(345, 344, 342), PackedInt32Array(342, 344, 343), PackedInt32Array(348, 347, 346), PackedInt32Array(328, 329, 349), PackedInt32Array(349, 329, 350), PackedInt32Array(346, 352, 351), PackedInt32Array(350, 353, 349), PackedInt32Array(349, 353, 271), PackedInt32Array(351, 354, 346), PackedInt32Array(346, 354, 348), PackedInt32Array(348, 354, 353), PackedInt32Array(353, 354, 271), PackedInt32Array(321, 351, 322), PackedInt32Array(322, 351, 352), PackedInt32Array(271, 354, 270), PackedInt32Array(357, 359, 358), PackedInt32Array(358, 359, 356), PackedInt32Array(358, 356, 355), PackedInt32Array(360, 348, 353), PackedInt32Array(357, 358, 340), PackedInt32Array(340, 358, 355), PackedInt32Array(340, 355, 348), PackedInt32Array(340, 348, 360), PackedInt32Array(340, 360, 361), PackedInt32Array(361, 339, 340), PackedInt32Array(363, 362, 364), PackedInt32Array(364, 362, 365), PackedInt32Array(368, 367, 366), PackedInt32Array(369, 368, 366), PackedInt32Array(365, 362, 366), PackedInt32Array(366, 362, 370), PackedInt32Array(366, 370, 369), PackedInt32Array(373, 372, 371), PackedInt32Array(375, 374, 373), PackedInt32Array(375, 373, 376), PackedInt32Array(376, 373, 371), PackedInt32Array(371, 377, 376), PackedInt32Array(381, 380, 378), PackedInt32Array(378, 380, 379), PackedInt32Array(384, 383, 382), PackedInt32Array(357, 386, 359), PackedInt32Array(359, 386, 385), PackedInt32Array(359, 385, 384), PackedInt32Array(359, 384, 382), PackedInt32Array(359, 382, 356), PackedInt32Array(388, 387, 386), PackedInt32Array(386, 387, 385), PackedInt32Array(385, 387, 389), PackedInt32Array(301, 302, 390), PackedInt32Array(390, 302, 323), PackedInt32Array(390, 323, 391), PackedInt32Array(384, 392, 383), PackedInt32Array(383, 392, 391), PackedInt32Array(394, 393, 395), PackedInt32Array(395, 393, 396), PackedInt32Array(399, 407, 400), PackedInt32Array(400, 406, 397), PackedInt32Array(397, 405, 398), PackedInt32Array(398, 405, 399), PackedInt32Array(407, 402, 400), PackedInt32Array(399, 405, 407), PackedInt32Array(406, 408, 397), PackedInt32Array(400, 401, 406), PackedInt32Array(397, 404, 405), PackedInt32Array(402, 401, 400), PackedInt32Array(407, 404, 402), PackedInt32Array(405, 403, 407), PackedInt32Array(408, 404, 397), PackedInt32Array(406, 401, 408), PackedInt32Array(404, 407, 405), PackedInt32Array(402, 408, 401), PackedInt32Array(404, 408, 402), PackedInt32Array(403, 404, 407), PackedInt32Array(405, 398, 403), PackedInt32Array(403, 397, 404), PackedInt32Array(398, 397, 403), PackedInt32Array(387, 409, 389), PackedInt32Array(389, 409, 410), PackedInt32Array(413, 412, 411), PackedInt32Array(392, 412, 391), PackedInt32Array(391, 412, 390), PackedInt32Array(390, 412, 413), PackedInt32Array(415, 418, 416), PackedInt32Array(416, 417, 414), PackedInt32Array(414, 418, 415), PackedInt32Array(418, 417, 416), PackedInt32Array(417, 418, 414), PackedInt32Array(393, 399, 396), PackedInt32Array(396, 399, 409), PackedInt32Array(409, 399, 410), PackedInt32Array(410, 399, 411), PackedInt32Array(400, 424, 399), PackedInt32Array(399, 424, 411), PackedInt32Array(411, 425, 413), PackedInt32Array(413, 421, 414), PackedInt32Array(414, 421, 416), PackedInt32Array(416, 421, 400), PackedInt32Array(400, 420, 424), PackedInt32Array(424, 423, 411), PackedInt32Array(425, 422, 413), PackedInt32Array(411, 423, 425), PackedInt32Array(413, 426, 421), PackedInt32Array(421, 420, 400), PackedInt32Array(420, 423, 424), PackedInt32Array(422, 426, 413), PackedInt32Array(425, 420, 422), PackedInt32Array(423, 420, 425), PackedInt32Array(426, 419, 421), PackedInt32Array(421, 419, 420), PackedInt32Array(422, 419, 426), PackedInt32Array(420, 419, 422), PackedInt32Array(415, 427, 414), PackedInt32Array(431, 430, 428), PackedInt32Array(428, 430, 429), PackedInt32Array(395, 433, 394), PackedInt32Array(394, 433, 432), PackedInt32Array(335, 433, 332), PackedInt32Array(433, 335, 432), PackedInt32Array(432, 335, 434), PackedInt32Array(434, 436, 435), PackedInt32Array(435, 337, 333), PackedInt32Array(337, 436, 336), PackedInt32Array(336, 436, 335), PackedInt32Array(335, 436, 434), PackedInt32Array(436, 337, 435), PackedInt32Array(439, 438, 440), PackedInt32Array(440, 438, 437), PackedInt32Array(442, 441, 443), PackedInt32Array(443, 441, 445), PackedInt32Array(443, 445, 444), PackedInt32Array(439, 440, 446), PackedInt32Array(446, 440, 447), PackedInt32Array(448, 446, 449), PackedInt32Array(449, 446, 447), PackedInt32Array(437, 443, 444), PackedInt32Array(437, 444, 440), PackedInt32Array(440, 444, 450), PackedInt32Array(453, 452, 451), PackedInt32Array(453, 451, 454), PackedInt32Array(453, 454, 455), PackedInt32Array(455, 454, 456), PackedInt32Array(456, 462, 455), PackedInt32Array(455, 461, 457), PackedInt32Array(457, 460, 458), PackedInt32Array(458, 460, 398), PackedInt32Array(398, 462, 397), PackedInt32Array(397, 462, 456), PackedInt32Array(462, 461, 455), PackedInt32Array(461, 460, 457), PackedInt32Array(460, 462, 398), PackedInt32Array(462, 459, 461), PackedInt32Array(461, 459, 460), PackedInt32Array(460, 459, 462), PackedInt32Array(434, 435, 458), PackedInt32Array(458, 435, 457), PackedInt32Array(465, 464, 463), PackedInt32Array(465, 463, 466), PackedInt32Array(308, 313, 467), PackedInt32Array(467, 414, 308), PackedInt32Array(308, 414, 468), PackedInt32Array(466, 468, 465), PackedInt32Array(465, 468, 427), PackedInt32Array(427, 468, 414), PackedInt32Array(470, 469, 471), PackedInt32Array(471, 469, 472), PackedInt32Array(472, 469, 473), PackedInt32Array(469, 475, 474), PackedInt32Array(469, 474, 473), PackedInt32Array(477, 476, 478), PackedInt32Array(478, 476, 479), PackedInt32Array(479, 476, 480), PackedInt32Array(480, 476, 481), PackedInt32Array(483, 482, 484), PackedInt32Array(484, 482, 479), PackedInt32Array(479, 480, 485), PackedInt32Array(479, 482, 478)]
agent_height = 1.75
agent_radius = 0.375
agent_max_climb = 0.5
edge_max_length = 12.0
filter_low_hanging_obstacles = true
filter_ledge_spans = true
filter_walkable_low_height_spans = true

[sub_resource type="StandardMaterial3D" id="StandardMaterial3D_cin4e"]
albedo_texture = ExtResource("15_df8b0")
metallic = 0.2
metallic_texture = ExtResource("16_003du")
roughness = 0.8
uv1_scale = Vector3(10, 10, 1)

[node name="level01" type="Node3D"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 0, 0, 1.94383)
script = ExtResource("1_hk2b6")
minimap_scene = ExtResource("2_j32d5")
player_avatar_scene = ExtResource("3_0f42i")
item_boxes_scene = ExtResource("4_swvig")
skill_box_ui_scene = ExtResource("5_dbohx")

[node name="WorldEnvironment" type="WorldEnvironment" parent="."]
environment = SubResource("Environment_j4i7h")

[node name="DirectionalLight3D" type="DirectionalLight3D" parent="."]
transform = Transform3D(0.707107, -0.5, 0.5, 0, 0.707107, 0.707107, -0.707107, -0.5, 0.5, 0, 0, 0)
light_color = Color(1, 0.95, 0.8, 1)
light_energy = 1.2
shadow_enabled = true
shadow_opacity = 0.6
shadow_blur = 1.5

[node name="ModularLevelGenerator" type="Node3D" parent="."]
script = ExtResource("6_6ly8b")
level_config = ExtResource("7_r5102")

[node name="FogOfWar" parent="." instance=ExtResource("9_003du")]
player_vision_radius = 8.0
enabled = false

[node name="Camera3D" type="Camera3D" parent="."]
unique_name_in_owner = true
transform = Transform3D(1, 0, 0, 0, 0.866025, 0.5, 0, -0.5, 0.866025, 0, 30, 50)
v_offset = 20.0
projection = 1
current = true
size = 44.805
near = 0.022
script = ExtResource("26_camera_follower")
target_path = NodePath("../Player")
follow_speed = 10.0
position_offset = Vector3(0, 10, 55)

[node name="UIManager" type="Node" parent="."]
script = ExtResource("25_ui_manager")
border_width = 100.0

[node name="Player" parent="." instance=ExtResource("11_82jtv")]

[node name="Chest" parent="." instance=ExtResource("14_1ks1q")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, 4.57862, 0, -14.4123)
interaction_distance = 3.0
item_resource = ExtResource("17_j32d5")

[node name="Chest2" parent="." instance=ExtResource("14_1ks1q")]
transform = Transform3D(1.5, 0, 0, 0, 1.5, 0, 0, 0, 1.5, 20, 0.165, -3.779)
interaction_distance = 3.0
item_resource = ExtResource("18_j32d5")

[node name="EnemyBoy01" parent="." instance=ExtResource("19_0f42i")]
transform = Transform3D(0.8, 0, 0, 0, 0.751754, 0.273616, 0, -0.273616, 0.751754, -10.0014, 0, -8.65824)

[node name="Enemy02" parent="." instance=ExtResource("21_dbohx")]
transform = Transform3D(2.1, 0, 0, 0, 1.97335, 0.718242, 0, -0.718242, 1.97335, 7.86917, 0, -7.768)

[node name="NavigationRegion3D" type="NavigationRegion3D" parent="."]
navigation_mesh = SubResource("NavigationMesh_xcdtp")

[node name="Floor" type="Node3D" parent="NavigationRegion3D"]

[node name="@CSGBox3D@101068" type="CSGBox3D" parent="NavigationRegion3D/Floor"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 0, -0.05, 0)
use_collision = true
size = Vector3(100, 0.1, 100)
material = SubResource("StandardMaterial3D_cin4e")

[node name="FloorTiles" type="Node3D" parent="NavigationRegion3D/Floor"]

[node name="Floor01_0" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -14.3198, 0.1, -43.293)

[node name="Floor01_1" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -27.0999, 0.1, 3.09002)

[node name="Floor01_2" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -5.68043, 0.1, -37.3833)

[node name="Floor01_3" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 21.8444, 0.1, -40.7097)

[node name="Floor01_4" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 19.1296, 0.1, -20.6728)

[node name="Floor01_5" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 39.6346, 0.1, -42.2979)

[node name="Floor01_6" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 0.0180927, 0.1, 7.11834)

[node name="Floor01_7" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 8.91092, 0.1, 9.82171)

[node name="Floor01_8" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 26.3461, 0.1, 15.9173)

[node name="Floor01_9" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -20.3682, 0.1, -2.89531)

[node name="Floor01_10" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 16.0111, 0.1, -7.78243)

[node name="Floor01_11" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 2.54392, 0.1, 35.0237)

[node name="Floor01_12" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 10.9332, 0.1, 20.5644)

[node name="Floor01_13" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 1.89638, 0.1, -27.8923)

[node name="Floor01_14" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 17.7801, 0.1, 38.7866)

[node name="Floor01_15" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 42.4786, 0.1, 12.7615)

[node name="Floor01_16" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -39.4219, 0.1, -20.2968)

[node name="Floor01_17" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -36.1339, 0.1, 15.9468)

[node name="Floor01_18" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -28.8492, 0.1, -9.85026)

[node name="Floor01_19" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 24.0497, 0.1, -30.7031)

[node name="Floor01_20" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -33.4253, 0.1, -43.0588)

[node name="Floor01_21" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 6.65253, 0.1, -42.7326)

[node name="Floor01_22" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -24.4775, 0.1, 21.7138)

[node name="Floor01_23" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -13.625, 0.1, 35.2832)

[node name="Floor01_24" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -42.9227, 0.1, -36.2031)

[node name="Floor01_25" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -22.9064, 0.1, 35.6169)

[node name="Floor01_26" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 28.4075, 0.1, 39.1496)

[node name="Floor01_27" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -20.3159, 0.1, -31.6483)

[node name="Floor01_28" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 32.8467, 0.1, -4.49317)

[node name="Floor01_29" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 2.23358, 0.1, -16.3234)

[node name="Floor01_30" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -32.2539, 0.1, 28.624)

[node name="Floor01_31" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 36.797, 0.1, 30.3734)

[node name="Floor01_32" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 39.6687, 0.1, -13.0585)

[node name="Floor01_33" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 44.3294, 0.1, 41.5137)

[node name="Floor01_34" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -8.91944, 0.1, 9.01563)

[node name="Floor01_35" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 17.908, 0.1, 24.9837)

[node name="Floor01_36" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 8.04492, 0.1, -21.9503)

[node name="Floor01_37" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -40.6354, 0.1, 31.7951)

[node name="Floor01_38" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 18.5987, 0.1, 9.9102)

[node name="Floor01_39" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -42.6254, 0.1, 1.53556)

[node name="Floor01_40" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -35.5305, 0.1, 44.9742)

[node name="Floor01_41" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 31.4959, 0.1, -41.9668)

[node name="Floor01_42" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -17.5282, 0.1, 11.9554)

[node name="Floor01_43" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -35.085, 0.1, 4.59178)

[node name="Floor01_44" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 43.5802, 0.1, 3.26899)

[node name="Floor01_45" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -2.6697, 0.1, 21.2943)

[node name="Floor01_46" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -4.97901, 0.1, 38.8964)

[node name="Floor01_47" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 34.3677, 0.1, 18.3206)

[node name="Floor01_48" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 35.6157, 0.1, 4.91594)

[node name="Floor01_49" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 34.4614, 0.1, -25.9202)

[node name="Floor01_50" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -18.3271, 0.1, -18.6057)

[node name="Floor01_51" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -8.52319, 0.1, 28.4806)

[node name="Floor01_52" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 22.9814, 0.1, 2.67567)

[node name="Floor01_53" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 7.67702, 0.1, -9.26168)

[node name="Floor01_54" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -36.9748, 0.1, -9.67519)

[node name="Floor01_55" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -44.8458, 0.1, -11.4882)

[node name="Floor01_56" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 44.2966, 0.1, 23.7342)

[node name="Floor01_57" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 14.2377, 0.1, -37.906)

[node name="Floor01_58" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -13.5424, 0.1, 21.6231)

[node name="Floor01_59" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -30.8612, 0.1, 37.9566)

[node name="Floor01_60" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -13.6623, 0.1, -11.4656)

[node name="Floor01_61" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 40.1369, 0.1, -33.0654)

[node name="Floor01_62" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -18.8492, 0.1, 43.9882)

[node name="Floor01_63" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -13.3278, 0.1, 0.971238)

[node name="Floor01_64" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 5.54033, 0.1, 43.7747)

[node name="Floor01_65" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 8.67074, 0.1, 0.737619)

[node name="Floor01_66" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -33.6572, 0.1, -26.3989)

[node name="Floor01_67" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -4.63194, 0.1, -0.280982)

[node name="Floor01_68" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -44.3494, 0.1, -44.1771)

[node name="Floor01_69" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 11.591, 0.1, -29.5813)

[node name="Floor01_70" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 42.734, 0.1, -23.2469)

[node name="Floor01_71" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -43.4959, 0.1, 22.805)

[node name="Floor01_72" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 23.1947, 0.1, 32.1257)

[node name="wall_200" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1.96048, -0.391835, -0.0547428, 0.382725, 1.94837, -0.239557, 0.100263, 0.224348, 1.98485, -23.6986, 2.92934, -47.4721)

[node name="tree_0" parent="NavigationRegion3D" instance=ExtResource("15_003du")]
transform = Transform3D(2.5, -0.00145593, -0.00167976, 0.00145725, 2.5, 0.00196474, 0.00167861, -0.00196571, 2.5, 12.9344, 0, 3.48506)

[node name="PatrolPoint_0" type="Node3D" parent="NavigationRegion3D"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 15.1665, 1, 10.3453)
script = ExtResource("15_6ly8b")

[node name="tree_1" parent="NavigationRegion3D" instance=ExtResource("15_003du")]
transform = Transform3D(2.5, -0.00145593, -0.00167976, 0.00145725, 2.5, 0.00196474, 0.00167861, -0.00196571, 2.5, 12.0358, 0, -14.2343)

[node name="PatrolPoint_1" type="Node3D" parent="NavigationRegion3D"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 11.1759, 1, -8.7698)
script = ExtResource("15_6ly8b")
point_id = 1

[node name="tree_2" parent="NavigationRegion3D" instance=ExtResource("15_003du")]
transform = Transform3D(2.5, -0.00145593, -0.00167976, 0.00145725, 2.5, 0.00196474, 0.00167861, -0.00196571, 2.5, -9.44214, 0, -16.9406)

[node name="PatrolPoint_2" type="Node3D" parent="NavigationRegion3D"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -4.05001, 1, -13.8923)
script = ExtResource("15_6ly8b")
point_id = 2

[node name="tree_3" parent="NavigationRegion3D" instance=ExtResource("15_003du")]
transform = Transform3D(2.5, -0.00145593, -0.00167976, 0.00145725, 2.5, 0.00196474, 0.00167861, -0.00196571, 2.5, 2.2368, 0, -1.25862)
preset_burnt_out_type = 1

[node name="PatrolPoint_3" type="Node3D" parent="NavigationRegion3D"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 4.10279, 1, 4.15155)
script = ExtResource("15_6ly8b")
point_id = 3

[node name="chest_0" parent="NavigationRegion3D" instance=ExtResource("14_1ks1q")]
transform = Transform3D(0.0994402, 0, 1.99753, 0, 2, 0, -1.99753, 0, 0.0994402, -39.4895, 0, -14.7556)

[node name="rock_0" parent="NavigationRegion3D" instance=ExtResource("21_xcdtp")]
transform = Transform3D(-0.0633937, 0, -0.9621, 0, 0.964187, 0, 0.9621, 0, -0.0633937, 15.8859, 0.01, -44.0237)

[node name="rock_1" parent="NavigationRegion3D" instance=ExtResource("21_xcdtp")]
transform = Transform3D(-0.122879, 0, -0.798684, 0, 0.808082, 0, 0.798684, 0, -0.122879, -25.6734, 0.01, -40.3689)

[node name="rock_2" parent="NavigationRegion3D" instance=ExtResource("21_xcdtp")]
transform = Transform3D(0.802852, 0, -0.111553, 0, 0.810565, 0, 0.111553, 0, 0.802852, 38.0653, 0.01, 39.0791)

[node name="rock_3" parent="NavigationRegion3D" instance=ExtResource("21_xcdtp")]
transform = Transform3D(-1.00525, 0, -0.030893, 0, 1.00573, 0, 0.030893, 0, -1.00525, -32.1094, 0.01, -19.4419)

[node name="rock_4" parent="NavigationRegion3D" instance=ExtResource("21_xcdtp")]
transform = Transform3D(-0.175602, 0, 0.789727, 0, 0.809014, 0, -0.789727, 0, -0.175602, 4.3692, 0.01, 13.8258)

[node name="rock_5" parent="NavigationRegion3D" instance=ExtResource("21_xcdtp")]
transform = Transform3D(-0.217806, 0, -0.870189, 0, 0.897033, 0, 0.870189, 0, -0.217806, -10.8392, 0.01, -26.3753)

[node name="rock_6" parent="NavigationRegion3D" instance=ExtResource("21_xcdtp")]
transform = Transform3D(0.469469, 0, 0.700645, 0, 0.843389, 0, -0.700645, 0, 0.469469, -4.17091, 0.01, 11.8061)

[node name="rock_7" parent="NavigationRegion3D" instance=ExtResource("21_xcdtp")]
transform = Transform3D(0.950298, 0, -0.401675, 0, 1.0317, 0, 0.401675, 0, 0.950298, 29.3803, 0.01, 28.6457)

[node name="rock_8" parent="NavigationRegion3D" instance=ExtResource("21_xcdtp")]
transform = Transform3D(1.00805, 0, -0.00299875, 0, 1.00805, 0, 0.00299875, 0, 1.00805, 33.9769, 0.01, -19.4685)

[node name="rock_9" parent="NavigationRegion3D" instance=ExtResource("21_xcdtp")]
transform = Transform3D(-0.765682, 0, 0.399578, 0, 0.863673, 0, -0.399578, 0, -0.765682, 10.9383, 0.01, 29.3073)

[node name="rock_10" parent="NavigationRegion3D" instance=ExtResource("21_xcdtp")]
transform = Transform3D(-0.969971, 0, -0.304397, 0, 1.01661, 0, 0.304397, 0, -0.969971, -27.887, 0.01, -21.7914)

[node name="rock_11" parent="NavigationRegion3D" instance=ExtResource("21_xcdtp")]
transform = Transform3D(-0.198803, 0, -0.958427, 0, 0.978828, 0, 0.958427, 0, -0.198803, -24.1721, 0.01, 44.2247)

[node name="rock_12" parent="NavigationRegion3D" instance=ExtResource("21_xcdtp")]
transform = Transform3D(0.190269, 0, -0.97867, 0, 0.996994, 0, 0.97867, 0, 0.190269, -17.4179, 0.01, 29.384)

[node name="rock_13" parent="NavigationRegion3D" instance=ExtResource("21_xcdtp")]
transform = Transform3D(1.0644, 0, 0.440559, 0, 1.15197, 0, -0.440559, 0, 1.0644, -11.8674, 0.01, 16.3352)

[node name="rock_14" parent="NavigationRegion3D" instance=ExtResource("21_xcdtp")]
transform = Transform3D(0.13703, 0, -0.898018, 0, 0.908413, 0, 0.898018, 0, 0.13703, 30.1073, 0.01, 2.27806)

[node name="barrel_0" parent="NavigationRegion3D" instance=ExtResource("22_cin4e")]
transform = Transform3D(0.237322, 0, -0.80995, 0, 0.844003, 0, 0.80995, 0, 0.237322, -24.7881, 0, 13.8716)

[node name="barrel_1" parent="NavigationRegion3D" instance=ExtResource("22_cin4e")]
transform = Transform3D(0.194424, 0, -0.982632, 0, 1.00168, 0, 0.982632, 0, 0.194424, 0.610545, 0, -22.0574)

[node name="decoration_0" parent="NavigationRegion3D" instance=ExtResource("23_6j2fk")]
transform = Transform3D(0.313344, 0, -0.727628, 0, 0.792229, 0, 0.727628, 0, 0.313344, 16.4477, 0, 4.68529)

[node name="decoration_1" parent="NavigationRegion3D" instance=ExtResource("23_6j2fk")]
transform = Transform3D(-0.553349, 0, -0.586866, 0, 0.806602, 0, 0.586866, 0, -0.553349, 24.0409, 0, 21.2563)

[node name="decoration_2" parent="NavigationRegion3D" instance=ExtResource("23_6j2fk")]
transform = Transform3D(-0.841067, 0, 0.494642, 0, 0.975738, 0, -0.494642, 0, -0.841067, 40.5761, 0, -3.40626)

[node name="decoration_3" parent="NavigationRegion3D" instance=ExtResource("23_6j2fk")]
transform = Transform3D(0.143227, 0, -0.6632, 0, 0.678489, 0, 0.6632, 0, 0.143227, 39.3279, 0, -27.6189)

[node name="decoration_4" parent="NavigationRegion3D" instance=ExtResource("23_6j2fk")]
transform = Transform3D(1.18486, 0, 0.559389, 0, 1.31027, 0, -0.559389, 0, 1.18486, -3.42677, 0, -9.58353)

[node name="decoration_5" parent="NavigationRegion3D" instance=ExtResource("23_6j2fk")]
transform = Transform3D(0.876989, 0, -0.660471, 0, 1.09788, 0, 0.660471, 0, 0.876989, -35.6847, 0, 9.90624)

[node name="decoration_6" parent="NavigationRegion3D" instance=ExtResource("23_6j2fk")]
transform = Transform3D(-1.16412, 0, -0.495131, 0, 1.26504, 0, 0.495131, 0, -1.16412, -5.60042, 0, -25.8812)

[node name="decoration_7" parent="NavigationRegion3D" instance=ExtResource("23_6j2fk")]
transform = Transform3D(-0.46181, 0, 1.09887, 0, 1.19196, 0, -1.09887, 0, -0.46181, -9.23411, 0, -5.15603)

[node name="decoration_8" parent="NavigationRegion3D" instance=ExtResource("23_6j2fk")]
transform = Transform3D(0.0849216, 0, 0.908058, 0, 0.91202, 0, -0.908058, 0, 0.0849216, 38.1007, 0, 21.9149)

[node name="decoration_9" parent="NavigationRegion3D" instance=ExtResource("23_6j2fk")]
transform = Transform3D(0.192283, 0, -0.793583, 0, 0.816546, 0, 0.793583, 0, 0.192283, -9.89069, 0, 17.0943)

[node name="decoration_10" parent="NavigationRegion3D" instance=ExtResource("23_6j2fk")]
transform = Transform3D(0.395287, 0, 0.950098, 0, 1.02905, 0, -0.950098, 0, 0.395287, 26.1123, 0, -36.2522)

[node name="decoration_11" parent="NavigationRegion3D" instance=ExtResource("23_6j2fk")]
transform = Transform3D(-0.396296, 0, 0.688372, 0, 0.794296, 0, -0.688372, 0, -0.396296, -21.1575, 0, 4.41185)

[node name="decoration_12" parent="NavigationRegion3D" instance=ExtResource("23_6j2fk")]
transform = Transform3D(-0.448291, 0, 0.560652, 0, 0.717841, 0, -0.560652, 0, -0.448291, -34.8834, 0, -33.3727)

[node name="decoration_13" parent="NavigationRegion3D" instance=ExtResource("23_6j2fk")]
transform = Transform3D(0.765053, 0, 1.1295, 0, 1.36421, 0, -1.1295, 0, 0.765053, 16.0845, 0, -26.6502)

[node name="decoration_14" parent="NavigationRegion3D" instance=ExtResource("23_6j2fk")]
transform = Transform3D(-0.742113, 0, -0.882272, 0, 1.15288, 0, 0.882272, 0, -0.742113, -20.1832, 0, 2.40082)

[node name="decoration_15" parent="NavigationRegion3D" instance=ExtResource("23_6j2fk")]
transform = Transform3D(-0.967506, 0, 0.60347, 0, 1.14028, 0, -0.60347, 0, -0.967506, 27.4356, 0, -13.5797)

[node name="decoration_16" parent="NavigationRegion3D" instance=ExtResource("23_6j2fk")]
transform = Transform3D(-0.020249, 0, 1.17677, 0, 1.17694, 0, -1.17677, 0, -0.020249, 10.5494, 0, 41.4454)

[node name="decoration_17" parent="NavigationRegion3D" instance=ExtResource("23_6j2fk")]
transform = Transform3D(-0.390007, 0, 0.805636, 0, 0.895072, 0, -0.805636, 0, -0.390007, -43.873, 0, 6.77264)

[node name="decoration_18" parent="NavigationRegion3D" instance=ExtResource("23_6j2fk")]
transform = Transform3D(-0.255682, 0, -0.62198, 0, 0.672482, 0, 0.62198, 0, -0.255682, -26.0779, 0, -28.3222)

[node name="decoration_19" parent="NavigationRegion3D" instance=ExtResource("23_6j2fk")]
transform = Transform3D(0.177519, 0, 0.67212, 0, 0.695168, 0, -0.67212, 0, 0.177519, 28.4201, 0, -25.6101)

[node name="wall_201" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(0.995056, 0.0993198, 0, -0.0993198, 0.995056, 0, 0, 0, 1, -53.3195, 0, -47.1316)

[node name="wall_202" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1.99011, 0.198639, 0, -0.198639, 1.99011, 0, 0, 0, 2, -10.2388, 0, -47.4935)

[node name="wall_203" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1.94732, 0.456022, 0, -0.456022, 1.94732, -1.13807e-08, -2.59493e-09, 1.10809e-08, 2, -42.4139, 0.885228, -47.2671)

[node name="PatrolPointManager" type="Node" parent="."]
script = ExtResource("24_j2uky")
