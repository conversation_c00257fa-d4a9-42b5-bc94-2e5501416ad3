[gd_scene load_steps=39 format=3 uid="uid://c0rj4dwj18nv8"]

[ext_resource type="Script" uid="uid://bo0h8t35yls55" path="res://Scripts/Levels/Level01.gd" id="1_hk2b6"]
[ext_resource type="PackedScene" uid="uid://d2xyw4d37ymv4" path="res://Scenes/Minimap.tscn" id="2_j32d5"]
[ext_resource type="PackedScene" uid="uid://ctxvyh1qr52ue" path="res://Scenes/PlayerAvatar.tscn" id="3_0f42i"]
[ext_resource type="PackedScene" uid="uid://do1sypgxbecbd" path="res://Scenes/ItemBoxes.tscn" id="4_swvig"]
[ext_resource type="PackedScene" uid="uid://b4l4phqqbvprm" path="res://Scenes/SkillBoxUI.tscn" id="5_dbohx"]
[ext_resource type="Script" uid="uid://f54r1khulcvh" path="res://Scripts/ModularLevelGenerator.gd" id="6_6ly8b"]
[ext_resource type="Script" path="res://Scripts/CameraController.gd" id="7_camera"]
[ext_resource type="Resource" uid="uid://cvji54t3pjwfe" path="res://Resources/LevelConfigs/AllModulesConfig.tres" id="7_r5102"]
[ext_resource type="Script" path="res://test_camera_refactor.gd" id="8_test"]
[ext_resource type="PackedScene" uid="uid://d2b5h1mlxxy7d" path="res://Scenes/FogOfWar.tscn" id="9_003du"]
[ext_resource type="PackedScene" uid="uid://b88l8pk1ebe1x" path="res://Scenes/player.tscn" id="11_82jtv"]
[ext_resource type="PackedScene" uid="uid://c3h8fj2xsp5oy" path="res://Scenes/Prefabs/Chest.tscn" id="14_1ks1q"]
[ext_resource type="PackedScene" uid="uid://dwusy8dd8usvo" path="res://Scenes/Prefabs/Wall.tscn" id="14_df8b0"]
[ext_resource type="PackedScene" uid="uid://crtnthqkksmri" path="res://Scenes/Prefabs/Tree.tscn" id="15_003du"]
[ext_resource type="Script" uid="uid://c3tr23vwvnmwf" path="res://Scripts/PatrolPoint.gd" id="15_6ly8b"]
[ext_resource type="Texture2D" uid="uid://c2ny0yi07rvcf" path="res://Environment/Floor/Floor01_Rocks_BaseColor.png" id="15_df8b0"]
[ext_resource type="Texture2D" uid="uid://dhfikoo16s5n0" path="res://Environment/Floor/Rocks_Metallic.png" id="16_003du"]
[ext_resource type="PackedScene" uid="uid://ddttv643pel23" path="res://Environment/Floor/Floor01_Custom.tscn" id="17_2bvpm"]
[ext_resource type="Resource" path="res://Resources/Items/Trap.tres" id="17_j32d5"]
[ext_resource type="Resource" path="res://Resources/Items/Torch.tres" id="18_j32d5"]
[ext_resource type="Script" uid="uid://pfva6p6rllgp" path="res://Scripts/Tree.gd" id="18_oh2bt"]
[ext_resource type="PackedScene" uid="uid://rvgn0irsuwao" path="res://Scenes/EnemyBoy01.tscn" id="19_0f42i"]
[ext_resource type="PackedScene" uid="uid://brxqv3iv3op6u" path="res://Scenes/ProgressBar3D.tscn" id="19_fcnrs"]
[ext_resource type="Script" uid="uid://dhw1dgex7wmiy" path="res://Scripts/ChestInteractable.gd" id="21_1ks1q"]
[ext_resource type="PackedScene" uid="uid://ervckea7fk57" path="res://Scenes/Enemy02.tscn" id="21_dbohx"]
[ext_resource type="PackedScene" uid="uid://b5dqjsb63wbhl" path="res://Scenes/Prefabs/Rock.tscn" id="21_xcdtp"]
[ext_resource type="PackedScene" uid="uid://bdq3b4e0mlgo4" path="res://Scenes/Prefabs/Barrel.tscn" id="22_cin4e"]
[ext_resource type="Resource" uid="uid://bjih53ivkm0qi" path="res://Resources/GasItem.tres" id="22_xcdtp"]
[ext_resource type="PackedScene" uid="uid://c6k7j3t3flhst" path="res://Scenes/Prefabs/Decoration.tscn" id="23_6j2fk"]
[ext_resource type="AudioStream" uid="uid://dgskkp7epyn0x" path="res://ChestOpening.mp3" id="23_cin4e"]
[ext_resource type="Script" uid="uid://de18ote8otj6u" path="res://Scripts/PatrolPointManager.gd" id="24_j2uky"]

[sub_resource type="ProceduralSkyMaterial" id="ProceduralSkyMaterial_gbvua"]
sky_top_color = Color(0.2, 0.4, 0.8, 1)
sky_horizon_color = Color(0.5, 0.7, 0.9, 1)
ground_bottom_color = Color(0.3, 0.5, 0.7, 1)
ground_horizon_color = Color(0.5, 0.7, 0.9, 1)

[sub_resource type="Sky" id="Sky_m0r78"]
sky_material = SubResource("ProceduralSkyMaterial_gbvua")

[sub_resource type="Environment" id="Environment_j4i7h"]
background_mode = 2
sky = SubResource("Sky_m0r78")
ambient_light_source = 3
ambient_light_color = Color(0.85, 0.85, 0.9, 1)
ambient_light_energy = 0.4
tonemap_mode = 2
ssao_enabled = true
glow_enabled = true
fog_enabled = true
fog_light_color = Color(0.85, 0.9, 1, 1)
fog_density = 0.001

[sub_resource type="SphereShape3D" id="SphereShape3D_camera"]
radius = 0.2

[sub_resource type="NavigationMesh" id="NavigationMesh_xcdtp"]
vertices = PackedVector3Array(-21.0338, 0.340398, -53.7081, -21.7838, 0.590398, -53.2081, -21.7838, 0.590398, -52.4581, -15.0338, 0.340398, -52.4581, -19.7838, 0.340398, -53.4581, -14.7838, 0.340398, -53.4581, -15.2838, 0.340398, -55.7081, -17.5338, 0.590398, -56.2081, -19.0338, 0.340398, -55.2081, -3.03382, 0.340398, -53.2081, -5.03382, 0.590398, -52.9581, -5.03382, 0.590398, -52.4581, 1.71618, 0.340398, -52.4581, -0.783817, 0.590398, -55.9581, -2.53382, 0.340398, -54.9581, -2.53382, 0.340398, -53.7081, 1.21618, 0.340398, -55.4581, 1.96618, 0.340398, -53.9581, 32.4662, 0.340398, -53.4581, 36.9662, 0.590398, -53.4581, 36.2162, 0.340398, -55.2081, 34.7162, 0.590398, -55.7081, 32.4662, 0.340398, -54.7081, 31.9662, 0.340398, -52.9581, 38.9662, 0.340398, -52.4581, 29.9662, 0.590398, -52.7081, 46.2162, 0.590398, -55.4581, 43.9662, 0.340398, -54.7081, 43.9662, 0.340398, -53.4581, 47.7162, 0.340398, -52.4581, 47.7162, 0.340398, -54.9581, 43.2162, 0.340398, -52.7081, 41.7162, 0.590398, -52.7081, 11.2162, 0.340398, -52.4581, 15.7162, 0.340398, -52.4581, 14.9662, 0.340398, -54.2081, 13.4662, 0.590398, -54.7081, 11.2162, 0.340398, -53.7081, -48.7838, 4.3404, -51.7081, -48.7838, 4.3404, -53.9581, -51.0338, 4.3404, -53.9581, -51.0338, 4.3404, -48.9581, -48.5338, 4.3404, -48.7081, 34.7162, 4.3404, -48.9581, 46.7162, 4.3404, -48.9581, 46.7162, 4.3404, -51.2081, 34.7162, 4.3404, -51.2081, -48.2838, 4.3404, -51.2081, -37.0338, 4.3404, -48.9581, -36.5338, 4.3404, -51.2081, -0.783817, 4.3404, -51.2081, -1.03382, 4.3404, -48.9581, 10.7162, 4.3404, -48.9581, 10.9662, 4.3404, -51.2081, -25.0338, 4.3404, -48.9581, -24.5338, 4.3404, -51.2081, 22.9662, 4.3404, -51.2081, 22.7162, 4.3404, -48.9581, -13.0338, 4.3404, -48.9581, -12.7838, 4.3404, -51.2081, -23.7838, 0.590398, -52.4581, -24.2838, 0.340398, -53.4581, -25.5338, 0.590398, -53.9581, -27.7838, 0.340398, -52.9581, -27.7838, 0.340398, -52.4581, 48.4662, 4.3404, -48.9581, 48.4662, 4.3404, -51.2081, 51.2162, 4.3404, -48.4581, 51.7162, 4.3404, -51.2081, 51.2162, 4.3404, -51.7081, 48.9662, 4.3404, -51.7081, 51.7162, 4.3404, -48.9581, 48.9662, 4.3404, -48.4581, 48.9662, 4.3404, -46.7081, 51.2162, 4.3404, -46.7081, 51.2162, 4.3404, -53.9581, 48.9662, 4.3404, -53.9581, 53.9662, 4.3404, -48.9581, 53.9662, 4.3404, -51.2081, -7.28382, 0.590398, -52.4581, -7.53382, 0.340398, -53.2081, -9.03382, 0.590398, -53.2081, -11.2838, 0.340398, -52.7081, -9.28382, 0.590398, -53.7081, 27.7162, 0.340398, -52.4581, 27.4662, 0.340398, -52.9581, 25.9662, 0.590398, -52.9581, 23.9662, 0.340398, -52.7081, 25.7162, 0.590398, -53.4581, -51.0338, 4.3404, -42.9581, -48.7838, 4.3404, -42.7081, -48.7838, 4.3404, 36.0419, -51.0338, 4.3404, 31.0419, -51.0338, 4.3404, 37.2919, -51.0338, 4.3404, 43.5419, -51.0338, 4.3404, 49.7919, -50.2838, 4.3404, 49.7919, -48.7838, 4.3404, 48.2919, -48.7838, 4.3404, 42.0419, -51.0338, 4.3404, -36.7081, -48.7838, 4.3404, -36.7081, -19.2838, 4.3404, 51.0419, -13.0338, 4.3404, 51.0419, -12.0338, 4.3404, 48.7919, -24.0338, 4.3404, 48.7919, -48.7838, 4.3404, -30.7081, -51.0338, 4.3404, -30.7081, -25.2838, 4.3404, 51.0419, 42.7162, 4.3404, 51.0419, 48.9662, 4.3404, 51.0419, 48.7162, 4.3404, 48.5419, 42.7162, 4.3404, 48.7919, -36.2838, 4.3404, 48.7919, -48.2838, 4.3404, 48.7919, -43.7838, 4.3404, 51.0419, -37.5338, 4.3404, 51.0419, 36.7162, 4.3404, 48.7919, 36.4662, 4.3404, 51.0419, 30.4662, 4.3404, 48.7919, 30.2162, 4.3404, 51.0419, 24.4662, 4.3404, 48.7919, 24.2162, 4.3404, 51.0419, -31.5338, 4.3404, 51.0419, -30.2838, 4.3404, 48.7919, 18.2162, 4.3404, 48.7919, 17.9662, 4.3404, 51.0419, -0.533817, 4.3404, 51.0419, 5.46618, 4.3404, 51.0419, 0.216183, 4.3404, 48.7919, 12.2162, 4.3404, 48.7919, 11.7162, 4.3404, 51.0419, -48.7838, 4.3404, 24.0419, -51.0338, 4.3404, 25.0419, -49.7838, 4.3404, 51.0419, -48.7838, 4.3404, -24.4581, -51.0338, 4.3404, -24.4581, -51.0338, 4.3404, -18.4581, -48.7838, 4.3404, -12.4581, -48.7838, 4.3404, 17.7919, -51.0338, 4.3404, 18.7919, -51.0338, 4.3404, -12.2081, -48.7838, 4.3404, -6.45808, -51.0338, 4.3404, -5.95808, -6.78382, 4.3404, 51.0419, -6.03382, 4.3404, 48.7919, -48.7838, 4.3404, -0.208084, -51.0338, 4.3404, 0.291916, -51.0338, 4.3404, 6.29192, -48.7838, 4.3404, 11.7919, -51.0338, 4.3404, 12.5419, -11.2838, 0.840398, -26.9581, -11.2838, 0.590398, -27.7081, -11.7838, 1.0904, -27.9581, -12.2838, 0.590398, -26.4581, -25.0338, 0.840398, -12.4581, -24.0338, 0.340398, -12.2081, -23.7838, 0.590398, -13.2081, -15.5338, 0.340398, -18.9581, -15.2838, 0.340398, -18.4581, -14.0338, 0.340398, -18.4581, -38.5338, 0.340398, -47.7081, -47.5338, 0.590398, -47.7081, -47.5338, 0.340398, -38.7081, -13.7838, 0.340398, -17.7081, -12.2838, 0.340398, -17.9581, -11.7838, 0.840398, -28.7081, -19.0338, 0.340398, -18.2081, -14.2005, 0.340398, -25.2081, -20.0338, 0.340398, -15.4581, -19.2838, 0.340398, -15.7081, -22.5338, 0.340398, -13.7081, -20.0338, 0.340398, -14.4581, -12.0338, 0.590398, -25.4581, -29.5338, 0.590398, -47.7081, -47.5338, 0.590398, -29.4581, -11.2838, 0.840398, -28.9581, -11.2838, 0.590398, -47.7081, -20.5338, 0.340398, -47.7081, -13.6588, 0.340398, -32.7795, -47.5338, 0.590398, -20.4581, -14.6231, 0.340398, -25.2259, -26.545, 0.340398, -14.9612, -23.552, 0.840398, -29.9521, -25.0167, 0.340398, -28.4713, -25.0177, 0.340398, -34.4503, -34.0278, 0.840398, -34.4662, -35.5326, 0.340398, -32.9794, -35.5128, 0.340398, -34.4772, -32.5183, 0.340398, -38.9497, -20.5319, 0.340398, -29.9801, -31.0275, 0.340398, -32.9465, -47.5338, 0.590398, -11.2081, -25.5338, 0.340398, -11.2081, -31.0335, 0.340398, -13.4584, -10.5338, 0.840398, -26.9581, -10.5338, 0.340398, -27.9581, -10.5338, 0.590398, -28.7081, 0.966183, 0.590398, -25.4581, 1.71618, 0.590398, -25.7081, 1.71618, 0.590398, -47.7081, -4.78382, 0.340398, -47.7081, -8.53819, 0.340398, -31.4618, -10.2838, 0.340398, -26.2081, -10.4088, 0.840398, -27.0831, -10.0487, 0.340398, -43.4672, 28.7162, 0.590398, -22.4581, 29.7162, 0.840398, -22.2081, 29.7162, 0.340398, -22.4581, 29.7162, 0.590398, -23.2081, 3.46618, 0.340398, -23.4581, 4.46618, 0.340398, -21.7081, 6.46618, 0.590398, -21.7081, 3.46618, 0.340398, -25.2081, 13.2162, 0.340398, -47.7081, 30.9662, 0.590398, -23.7081, 37.9662, 0.590398, -25.7081, 37.4662, 0.340398, -27.2081, 24.7162, 0.340398, -14.4581, 26.7162, 0.590398, -14.4581, 28.4662, 0.340398, -21.4581, 39.9662, 0.590398, -28.2081, 40.9662, 0.590398, -26.4581, 47.7162, 0.590398, -26.4581, 47.7162, 0.340398, -37.2081, 38.9662, 0.590398, -28.2081, 6.46618, 0.340398, -19.4581, 24.7162, 0.590398, -47.7081, 19.9838, 0.840398, -25.4818, 21.4688, 0.340398, -25.4709, 19.963, 0.340398, -29.963, 18.479, 0.340398, -23.9451, 36.2162, 0.590398, -47.7081, 47.7162, 0.340398, -47.7081, 53.9662, 4.3404, 51.0419, 53.9662, 4.3404, 48.7919, 51.7162, 4.3404, 48.7919, 48.9662, 4.3404, -10.9581, 48.9662, 4.3404, 1.04192, 51.2162, 4.3404, 0.791916, 51.2162, 4.3404, -11.2081, 51.2162, 4.3404, 48.2919, 48.9662, 4.3404, 37.0419, 51.2162, 4.3404, 36.2919, 51.2162, 4.3404, -22.9581, 51.2162, 4.3404, -34.9581, 48.9662, 4.3404, -34.9581, 48.9662, 4.3404, -22.9581, 48.9662, 4.3404, 25.0419, 51.2162, 4.3404, 24.5419, 48.9662, 4.3404, 13.0419, 51.2162, 4.3404, 12.5419, 56.4662, 0.340398, -33.2081, 56.9662, 0.340398, -33.4581, 56.9662, 0.340398, -35.2081, 54.4662, 0.340398, -35.2081, 52.4662, 0.340398, -27.7081, 53.2162, 0.340398, -28.9581, 53.2162, 0.340398, -35.9581, 52.4662, 0.340398, -39.4581, 53.4662, 0.340398, -38.4581, 55.9662, 0.340398, -29.2081, -53.7838, 0.340398, -30.2081, -54.7838, 0.340398, -29.7081, -54.7838, 0.340398, -28.2081, -52.2838, 0.340398, -31.9581, -53.0338, 0.340398, -31.9581, -53.7838, 0.340398, -22.4581, -52.7838, 0.340398, -20.7081, -52.2838, 0.340398, -20.7081, -55.7838, 0.340398, -27.7081, -55.7838, 0.340398, -22.9581, 55.4662, 0.340398, -24.2081, 55.9662, 0.340398, -24.4581, 55.9662, 0.340398, -26.2081, 53.4662, 0.340398, -26.2081, 52.4662, 0.340398, -26.9581, 52.4662, 0.340398, -19.9581, 54.9662, 0.340398, -20.2081, 36.2162, 0.840398, -4.70808, 36.9662, 0.590398, -4.45808, 37.2162, 0.590398, -4.95808, 36.2162, 0.590398, -5.70808, 37.9662, 1.0904, -4.95808, 38.4662, 0.590398, -3.95808, 47.7162, 0.340398, -3.95808, 31.2162, 0.840398, -22.4581, 29.7162, 0.590398, -20.7081, 29.7162, 0.840398, -21.4581, 28.9662, 0.340398, -20.7081, 27.2162, 0.340398, -14.2081, 31.4662, 0.590398, -23.4581, 38.4662, 0.590398, -24.9581, 26.9662, 0.590398, -11.9581, 35.4662, 0.590398, -5.95808, 47.7162, 0.590398, -15.2081, 39.4662, 0.590398, -24.9581, 31.9812, 0.340398, -20.9391, 43.9769, 0.840398, -13.4629, 42.462, 0.340398, -11.942, 42.4718, 0.340398, -14.9557, 45.4619, 0.340398, -13.4519, 40.4662, 0.340398, -25.4581, 0.716183, 0.590398, -24.2081, 1.21618, 0.590398, -24.4581, -10.7838, 0.590398, -25.2081, -11.5338, 0.590398, -17.2081, 1.46618, 0.340398, -22.9581, 0.716183, 0.340398, -23.4581, 3.21618, 0.340398, -22.9581, 3.96618, 0.590398, -21.4581, -11.5338, 0.590398, -16.2081, 1.46618, 0.590398, -6.20808, 3.21618, 1.0904, -6.20808, 3.96618, 0.590398, -6.95808, 4.21618, 0.340398, -18.9581, 3.71618, 0.590398, -19.7081, 1.21618, 0.590398, -5.70808, -12.2838, 0.590398, -15.7081, 4.96618, 0.590398, -6.70808, 4.96618, 0.590398, -18.9581, -52.7838, 0.340398, -19.9581, -53.5338, 0.340398, -4.45808, -52.7838, 0.590398, -3.70808, -52.2838, 0.590398, -3.70808, -52.2838, 0.340398, -12.2081, -54.5338, 0.340398, -10.4581, -55.7838, 0.340398, -8.45808, -54.0338, 0.340398, -11.9581, -54.7838, 0.340398, -11.9581, -53.7838, 0.340398, -19.4581, -53.5338, 0.340398, -18.4581, -55.5338, 0.340398, -9.95808, -53.0338, 0.340398, -14.2081, -53.0338, 0.340398, -13.4581, -54.0338, 0.340398, -17.9581, -54.0338, 0.340398, -14.7081, -55.7838, 0.340398, -5.20808, 17.9662, 0.340398, -6.95808, 18.2162, 1.0904, -5.70808, 19.4662, 0.590398, -6.45808, 24.2162, 0.340398, -11.9581, 4.96618, 0.590398, -6.20808, 6.96618, 0.590398, -6.20808, 14.7162, 0.590398, -6.20808, 16.2162, 0.590398, -6.45808, 16.9662, 0.590398, -7.20808, 12.9662, 0.590398, -3.70808, 14.4662, 0.590398, -4.45808, 6.71618, 0.340398, -3.95808, 24.2162, 0.340398, -14.2081, 6.21618, 0.590398, -18.9581, -17.5338, 5.0904, -16.7081, -17.5338, 5.0904, -15.4581, -16.2838, 5.0904, -15.4581, -16.2838, 5.0904, -16.7081, -22.2838, 0.590398, 17.0419, -22.0338, 0.590398, 17.0419, -21.2838, 1.0904, 16.5419, -23.0338, 0.590398, -11.7081, -24.0338, 0.590398, -11.9581, -24.0338, 0.590398, -10.7081, -16.2838, 0.340398, -13.7081, -17.5338, 0.840398, -14.2081, -17.7838, 0.590398, -13.7081, -14.0338, 0.590398, -16.2081, -14.0338, 0.340398, -15.4581, 0.216183, 0.590398, -1.45808, 0.466183, 0.590398, -1.95808, 0.216183, 0.590398, -3.45808, -1.28382, 0.590398, -0.958084, 1.46618, 0.590398, -4.45808, -24.5338, 0.590398, -10.4581, -22.5338, 0.840398, -12.2081, -18.7838, 0.590398, -13.7081, -1.28382, 0.590398, 0.541916, -20.1727, 0.340398, 15.653, -19.0378, 0.840398, 1.51786, -19.043, 0.340398, 0.0250244, -16.0178, 0.340398, 1.53983, -20.528, 0.340398, 0.0640373, -20.5372, 0.340398, 9.01721, -0.783817, 0.340398, 0.791916, -20.1449, 0.340398, 15.6669, 53.2162, 0.340398, -13.9581, 52.9662, 0.340398, -15.2081, 52.4662, 0.340398, -15.2081, 52.7162, 0.340398, -13.2081, 52.4662, 0.590398, -7.70808, 52.4662, 0.340398, 0.0419159, 53.2162, 0.590398, -1.70808, 56.4662, 0.340398, -5.70808, 56.9662, 0.340398, -5.95808, 56.9662, 0.340398, -7.95808, 54.4662, 0.340398, -7.95808, 53.2162, 0.340398, -8.95808, 53.4662, 0.340398, -11.4581, 52.9662, 0.590398, -11.7081, 55.9662, 0.340398, -1.95808, 25.4662, 2.3404, -13.2081, 25.4662, 2.3404, -12.7081, 25.9662, 2.3404, -12.7081, 25.9662, 2.3404, -13.2081, 35.2162, 0.340398, -3.20808, 35.4662, 0.590398, -3.95808, 34.9662, 0.340398, -4.20808, 19.7162, 0.590398, -3.95808, 18.4662, 0.840398, -3.95808, 17.9662, 0.340398, -2.45808, 20.7162, 0.590398, -0.208084, 21.4662, 0.340398, -0.958084, 34.9662, 0.340398, -5.70808, 26.7162, 0.340398, -11.4581, 24.4662, 0.340398, -11.4581, 19.9662, 0.590398, -6.20808, 22.9662, 0.590398, -0.208084, 25.6884, 0.840398, -0.874752, 27.0495, 0.340398, -1.20808, 22.9465, 0.340398, -2.98038, 22.4662, 0.590398, -0.958084, -25.2838, 0.590398, -10.4581, -47.5338, 0.590398, -4.20808, -47.5338, 0.590398, 2.79192, -47.5338, 0.590398, 9.79192, -47.5338, 0.590398, 17.0419, 2.71618, 5.0904, -4.95808, 2.71618, 5.0904, -3.70808, 3.96618, 5.0904, -3.70808, 3.96618, 5.0904, -4.95808, 15.7162, 5.0904, -4.95808, 15.7162, 5.0904, -3.70808, 16.9662, 5.0904, -3.70808, 16.9662, 5.0904, -4.95808, 35.9662, 0.840398, -3.95808, 37.4662, 0.590398, -3.45808, 38.2162, 0.590398, -3.45808, 47.7162, 0.590398, 2.54192, 47.7162, 0.340398, 9.04192, 40.4245, 0.340398, 1.89608, 41.4662, 0.840398, 2.91692, 43.5495, 0.340398, 4.95858, 30.2162, 0.340398, 16.5419, 29.7162, 0.340398, 16.7919, 31.7162, 0.340398, 21.0419, 33.7162, 0.340398, 20.2919, 34.7162, 0.590398, 22.0419, 47.7162, 0.340398, 22.0419, 47.7162, 0.340398, 15.5419, 30.2162, 0.340398, 14.2919, 39.4514, 0.340398, 6.04271, 0.966183, 0.590398, -0.958084, 4.21618, 0.590398, -1.70808, 8.96618, 0.340398, 15.0419, 9.21618, 0.340398, 16.2919, 10.4662, 0.340398, 14.7919, 7.21618, 0.590398, 14.2919, 7.46618, 0.590398, 15.2919, 6.46618, 0.590398, -3.45808, 5.46618, 0.590398, -3.45808, 12.9662, 0.340398, -2.70808, 4.21618, 0.340398, 14.7919, 5.46618, 0.590398, 15.0419, 0.966183, 0.590398, 0.0419159, 22.9662, 0.340398, 0.791916, 27.9662, 0.340398, 13.7919, 29.9662, 0.340398, 13.7919, 17.2162, 0.590398, -1.95808, 15.9662, 0.840398, -2.45808, 15.7162, 0.340398, -1.95808, 20.7162, 0.590398, 0.791916, 13.9662, 0.590398, -1.95808, 11.2162, 0.340398, 14.7919, 11.7162, 0.340398, 15.5419, 0.216183, 0.590398, 0.791916, 3.71618, 0.590398, 15.2919, -54.0338, 0.340398, 2.54192, -54.5338, 0.340398, 2.54192, -54.2838, 0.340398, 3.79192, -55.2838, 0.340398, 4.54192, -55.5338, 0.340398, 5.79192, -55.5338, 0.340398, 9.54192, -53.5338, 0.340398, 10.2919, -52.2838, 0.340398, 9.79192, -52.2838, 0.340398, 0.541916, -53.0338, 0.340398, 0.791916, -53.2838, 0.340398, 12.7919, -54.5338, 0.340398, 18.5419, -52.2838, 0.340398, 19.0419, -53.2838, 0.340398, 12.0419, -54.5338, 0.340398, 13.5419, -20.5338, 0.590398, 17.7919, -21.0338, 0.340398, 18.0419, -21.0338, 0.590398, 18.2919, -19.7838, 0.840398, 18.2919, -19.5338, 0.590398, 19.2919, -5.03382, 0.840398, 24.5419, -9.05655, 0.340398, 22.8374, -6.352, 0.340398, 24.0646, -4.03382, 0.590398, 25.0419, -3.78382, 0.590398, 25.0419, -3.28382, 0.590398, 23.7919, -20.5338, 0.840398, 17.0419, -8.90882, 0.340398, 22.6669, 2.71618, 0.590398, 17.7919, -4.0616, 0.340398, 23.5141, 3.71618, 0.590398, 17.2919, -2.53382, 0.590398, 23.7919, -2.28382, 0.590398, 24.2919, 3.46618, 0.340398, 19.7919, 2.71618, 0.590398, 19.0419, 27.4662, 0.340398, 14.2919, 22.4662, 0.590398, 1.29192, 20.9662, 0.590398, 1.29192, 11.7162, 0.340398, 16.0419, 27.4662, 0.340398, 15.7919, 22.9564, 0.840398, 10.5257, 22.9463, 0.340398, 9.05975, 19.9815, 0.340398, 12.0306, 52.4662, 0.590398, 0.791916, 52.4662, 0.340398, 2.54192, 52.9662, 0.340398, 1.29192, 55.7162, 0.340398, 7.29192, 56.4662, 0.340398, 6.04192, 56.2162, 0.340398, 4.29192, 53.7162, 0.340398, 4.29192, 52.4662, 0.590398, 10.7919, 55.4662, 0.340398, 10.2919, 52.4662, 0.340398, 3.29192, 9.96618, 0.340398, 17.0419, 9.46618, 0.340398, 16.5419, 9.46618, 0.340398, 17.0419, -4.03382, 0.590398, 25.7919, -2.53382, 0.590398, 25.2919, -12.2838, 0.590398, 41.0419, -12.2838, 0.840398, 41.2919, -11.2838, 0.590398, 41.5419, -11.7838, 0.340398, 39.7919, -1.28382, 0.340398, 47.5419, 9.71618, 0.340398, 47.5419, -4.78382, 0.590398, 26.0419, 6.71618, 0.340398, 19.5419, 5.71618, 0.840398, 19.2919, -11.2838, 0.340398, 42.2919, -12.2838, 0.340398, 42.7919, -12.2838, 0.340398, 47.5419, 11.4662, 0.590398, 17.0419, 27.7162, 0.590398, 16.7919, 31.9662, 0.590398, 22.7919, 20.7162, 0.590398, 47.5419, 31.7162, 0.340398, 47.5419, 24.4432, 0.840398, 31.5347, 22.9582, 0.340398, 31.5237, 25.948, 0.340398, 30.0479, 25.9782, 0.840398, 31.5457, 24.4688, 0.340398, 36.0291, 27.483, 0.340398, 30.0589, 5.21618, 5.0904, 16.5419, 5.21618, 5.0904, 17.7919, 6.46618, 5.0904, 17.7919, 6.46618, 5.0904, 16.5419, -22.0338, 0.590398, 17.5419, -21.2838, 0.340398, 19.2919, -41.2838, 0.590398, 41.2919, -40.5338, 0.590398, 42.0419, -40.0338, 0.590398, 40.5419, -40.2838, 0.840398, 41.2919, -47.5338, 0.590398, 23.2919, -39.2838, 0.590398, 40.5419, -39.0338, 0.590398, 41.0419, -42.0338, 0.340398, 41.2919, -41.0338, 0.840398, 40.9169, -47.5338, 0.590398, 42.2919, -42.5338, 0.340398, 42.2919, -47.5338, 0.340398, 35.7919, -47.5338, 0.340398, 29.5419, -13.0338, 0.590398, 41.5419, -13.5338, 0.590398, 40.5419, -39.5338, 0.840398, 42.2919, -40.5338, 0.590398, 42.7919, -19.7838, 0.590398, 47.5419, -13.5338, 0.340398, 39.5419, -13.0338, 0.590398, 42.5419, -19.2838, 0.590398, 21.0419, -20.0338, 0.340398, 19.7919, -21.0338, 0.590398, 19.7919, -41.5338, 0.340398, 43.2919, -41.5338, 0.340398, 47.5419, -40.0338, 0.340398, 43.6044, -34.2838, 0.340398, 47.5419, -27.0338, 0.340398, 47.5419, -22.0588, 0.840398, 22.5169, -22.0388, 0.340398, 24.0389, -23.5238, 0.340398, 24.0279, -5.53382, 0.840398, 25.0419, -6.90882, 0.340398, 24.6419, -5.53382, 0.840398, 25.2919, -13.2838, 0.340398, 39.0419, -12.5338, 0.340398, 39.0419, -8.54863, 0.340398, 27.0427, 54.2162, 0.340398, 24.0419, 54.9662, 0.340398, 23.0419, 54.7162, 0.340398, 21.2919, 52.4662, 0.340398, 21.5419, 52.4662, 0.340398, 27.5419, 53.9662, 0.340398, 27.2919, 39.7162, 0.340398, 47.5419, 47.7162, 0.340398, 47.5419, 47.7162, 0.340398, 41.0419, 33.9662, 0.340398, 23.2919, 47.7162, 0.340398, 28.2919, 32.2162, 0.340398, 23.2919, 47.7162, 0.340398, 34.7919, -52.2838, 0.340398, 25.7919, -52.7838, 0.340398, 25.7919, -53.0338, 0.340398, 27.2919, -53.0338, 0.340398, 30.5419, -52.2838, 0.340398, 30.7919, 53.7162, 0.340398, 32.5419, 54.2162, 0.340398, 32.2919, 54.2162, 0.340398, 30.2919, 52.4662, 0.340398, 30.5419, 52.4662, 0.340398, 36.5419, 53.2162, 0.340398, 36.5419, -54.0338, 0.340398, 41.2919, -54.7838, 0.340398, 41.2919, -54.5338, 0.340398, 42.7919, -55.5338, 0.340398, 43.2919, -55.7838, 0.340398, 44.7919, -52.2838, 0.340398, 39.2919, -53.0338, 0.340398, 39.2919, -55.7838, 0.340398, 48.2919, -52.2838, 0.590398, 49.5419, -42.5338, 0.590398, 42.7919, -47.5338, 0.590398, 47.5419)
polygons = [PackedInt32Array(1, 0, 2), PackedInt32Array(2, 0, 4), PackedInt32Array(2, 4, 3), PackedInt32Array(5, 3, 6), PackedInt32Array(6, 3, 7), PackedInt32Array(7, 3, 8), PackedInt32Array(8, 3, 4), PackedInt32Array(10, 9, 11), PackedInt32Array(11, 9, 12), PackedInt32Array(14, 13, 15), PackedInt32Array(15, 13, 16), PackedInt32Array(17, 12, 16), PackedInt32Array(16, 12, 15), PackedInt32Array(15, 12, 9), PackedInt32Array(20, 19, 21), PackedInt32Array(21, 19, 22), PackedInt32Array(22, 19, 18), PackedInt32Array(23, 18, 19), PackedInt32Array(23, 19, 25), PackedInt32Array(25, 19, 24), PackedInt32Array(28, 27, 26), PackedInt32Array(30, 29, 26), PackedInt32Array(26, 29, 28), PackedInt32Array(28, 29, 31), PackedInt32Array(31, 29, 32), PackedInt32Array(35, 34, 36), PackedInt32Array(36, 34, 37), PackedInt32Array(37, 34, 33), PackedInt32Array(40, 39, 38), PackedInt32Array(42, 41, 38), PackedInt32Array(38, 41, 40), PackedInt32Array(46, 45, 43), PackedInt32Array(43, 45, 44), PackedInt32Array(42, 38, 47), PackedInt32Array(42, 47, 48), PackedInt32Array(48, 47, 49), PackedInt32Array(53, 52, 50), PackedInt32Array(50, 52, 51), PackedInt32Array(55, 54, 49), PackedInt32Array(49, 54, 48), PackedInt32Array(46, 43, 56), PackedInt32Array(56, 43, 57), PackedInt32Array(59, 58, 55), PackedInt32Array(55, 58, 54), PackedInt32Array(56, 57, 53), PackedInt32Array(53, 57, 52), PackedInt32Array(50, 51, 59), PackedInt32Array(59, 51, 58), PackedInt32Array(61, 60, 62), PackedInt32Array(62, 60, 63), PackedInt32Array(63, 60, 64), PackedInt32Array(66, 65, 45), PackedInt32Array(45, 65, 44), PackedInt32Array(66, 70, 65), PackedInt32Array(65, 70, 69), PackedInt32Array(65, 69, 68), PackedInt32Array(65, 68, 67), PackedInt32Array(68, 71, 67), PackedInt32Array(67, 74, 72), PackedInt32Array(72, 74, 73), PackedInt32Array(70, 76, 69), PackedInt32Array(69, 76, 75), PackedInt32Array(67, 72, 65), PackedInt32Array(68, 78, 71), PackedInt32Array(71, 78, 77), PackedInt32Array(80, 79, 81), PackedInt32Array(81, 79, 82), PackedInt32Array(83, 81, 82), PackedInt32Array(85, 84, 86), PackedInt32Array(86, 84, 87), PackedInt32Array(88, 86, 87), PackedInt32Array(90, 89, 42), PackedInt32Array(42, 89, 41), PackedInt32Array(93, 92, 91), PackedInt32Array(96, 95, 97), PackedInt32Array(97, 95, 94), PackedInt32Array(97, 94, 98), PackedInt32Array(100, 99, 90), PackedInt32Array(90, 99, 89), PackedInt32Array(102, 101, 103), PackedInt32Array(103, 101, 104), PackedInt32Array(106, 99, 105), PackedInt32Array(105, 99, 100), PackedInt32Array(104, 101, 107), PackedInt32Array(111, 110, 108), PackedInt32Array(108, 110, 109), PackedInt32Array(115, 114, 112), PackedInt32Array(112, 114, 113), PackedInt32Array(111, 108, 116), PackedInt32Array(116, 108, 117), PackedInt32Array(96, 97, 113), PackedInt32Array(119, 118, 117), PackedInt32Array(117, 118, 116), PackedInt32Array(91, 98, 93), PackedInt32Array(93, 98, 94), PackedInt32Array(121, 120, 119), PackedInt32Array(119, 120, 118), PackedInt32Array(123, 122, 112), PackedInt32Array(112, 122, 115), PackedInt32Array(125, 124, 121), PackedInt32Array(121, 124, 120), PackedInt32Array(128, 127, 126), PackedInt32Array(124, 125, 129), PackedInt32Array(129, 125, 130), PackedInt32Array(132, 131, 92), PackedInt32Array(92, 131, 91), PackedInt32Array(96, 113, 133), PackedInt32Array(133, 113, 114), PackedInt32Array(135, 106, 134), PackedInt32Array(134, 106, 105), PackedInt32Array(122, 123, 107), PackedInt32Array(107, 123, 104), PackedInt32Array(135, 134, 136), PackedInt32Array(136, 134, 137), PackedInt32Array(132, 139, 131), PackedInt32Array(131, 139, 138), PackedInt32Array(140, 136, 137), PackedInt32Array(137, 141, 140), PackedInt32Array(140, 141, 142), PackedInt32Array(144, 143, 103), PackedInt32Array(103, 143, 102), PackedInt32Array(146, 142, 145), PackedInt32Array(145, 142, 141), PackedInt32Array(143, 144, 126), PackedInt32Array(126, 144, 128), PackedInt32Array(146, 145, 147), PackedInt32Array(147, 145, 148), PackedInt32Array(130, 127, 129), PackedInt32Array(129, 127, 128), PackedInt32Array(149, 147, 148), PackedInt32Array(148, 138, 149), PackedInt32Array(149, 138, 139), PackedInt32Array(151, 150, 152), PackedInt32Array(152, 150, 153), PackedInt32Array(155, 154, 156), PackedInt32Array(159, 158, 157), PackedInt32Array(162, 161, 160), PackedInt32Array(164, 163, 159), PackedInt32Array(152, 153, 165), PackedInt32Array(165, 153, 167), PackedInt32Array(167, 153, 157), PackedInt32Array(167, 157, 166), PackedInt32Array(166, 169, 168), PackedInt32Array(168, 171, 170), PackedInt32Array(159, 157, 164), PackedInt32Array(164, 157, 172), PackedInt32Array(172, 157, 153), PackedInt32Array(170, 156, 168), PackedInt32Array(168, 156, 166), PackedInt32Array(160, 173, 162), PackedInt32Array(162, 173, 174), PackedInt32Array(165, 178, 175), PackedInt32Array(175, 178, 176), PackedInt32Array(176, 178, 177), PackedInt32Array(179, 181, 154), PackedInt32Array(154, 181, 180), PackedInt32Array(180, 178, 165), PackedInt32Array(178, 184, 177), PackedInt32Array(177, 184, 173), PackedInt32Array(173, 188, 174), PackedInt32Array(174, 186, 179), PackedInt32Array(179, 186, 181), PackedInt32Array(181, 183, 180), PackedInt32Array(180, 189, 178), PackedInt32Array(178, 189, 184), PackedInt32Array(184, 188, 173), PackedInt32Array(188, 187, 174), PackedInt32Array(174, 187, 186), PackedInt32Array(186, 183, 181), PackedInt32Array(183, 189, 180), PackedInt32Array(189, 182, 184), PackedInt32Array(184, 190, 188), PackedInt32Array(188, 185, 187), PackedInt32Array(187, 185, 186), PackedInt32Array(186, 190, 183), PackedInt32Array(183, 182, 189), PackedInt32Array(182, 183, 184), PackedInt32Array(190, 185, 188), PackedInt32Array(184, 183, 190), PackedInt32Array(185, 190, 186), PackedInt32Array(165, 167, 180), PackedInt32Array(180, 167, 166), PackedInt32Array(180, 166, 156), PackedInt32Array(180, 156, 154), PackedInt32Array(154, 193, 179), PackedInt32Array(179, 193, 191), PackedInt32Array(191, 193, 192), PackedInt32Array(192, 193, 154), PackedInt32Array(150, 151, 194), PackedInt32Array(194, 151, 195), PackedInt32Array(200, 201, 175), PackedInt32Array(175, 201, 196), PackedInt32Array(196, 201, 197), PackedInt32Array(197, 201, 198), PackedInt32Array(198, 201, 199), PackedInt32Array(199, 201, 200), PackedInt32Array(202, 194, 203), PackedInt32Array(203, 194, 195), PackedInt32Array(195, 196, 203), PackedInt32Array(203, 196, 202), PackedInt32Array(202, 196, 197), PackedInt32Array(176, 204, 175), PackedInt32Array(175, 204, 200), PackedInt32Array(200, 204, 176), PackedInt32Array(207, 206, 205), PackedInt32Array(208, 207, 205), PackedInt32Array(209, 212, 210), PackedInt32Array(210, 212, 211), PackedInt32Array(198, 199, 212), PackedInt32Array(212, 199, 213), PackedInt32Array(216, 215, 214), PackedInt32Array(219, 218, 217), PackedInt32Array(221, 220, 222), PackedInt32Array(222, 220, 223), PackedInt32Array(214, 208, 216), PackedInt32Array(216, 208, 224), PackedInt32Array(219, 217, 205), PackedInt32Array(205, 217, 225), PackedInt32Array(205, 225, 211), PackedInt32Array(212, 229, 211), PackedInt32Array(211, 230, 208), PackedInt32Array(208, 229, 224), PackedInt32Array(224, 229, 226), PackedInt32Array(226, 229, 213), PackedInt32Array(213, 229, 212), PackedInt32Array(229, 230, 211), PackedInt32Array(230, 228, 208), PackedInt32Array(208, 228, 229), PackedInt32Array(229, 227, 230), PackedInt32Array(230, 227, 228), PackedInt32Array(228, 227, 229), PackedInt32Array(208, 205, 211), PackedInt32Array(220, 224, 223), PackedInt32Array(223, 224, 231), PackedInt32Array(231, 224, 226), PackedInt32Array(231, 232, 223), PackedInt32Array(235, 234, 233), PackedInt32Array(110, 235, 109), PackedInt32Array(109, 235, 233), PackedInt32Array(239, 238, 236), PackedInt32Array(236, 238, 237), PackedInt32Array(110, 240, 235), PackedInt32Array(110, 241, 240), PackedInt32Array(240, 241, 242), PackedInt32Array(246, 245, 243), PackedInt32Array(243, 245, 244), PackedInt32Array(248, 242, 247), PackedInt32Array(247, 242, 241), PackedInt32Array(245, 73, 244), PackedInt32Array(244, 73, 74), PackedInt32Array(250, 248, 249), PackedInt32Array(249, 248, 247), PackedInt32Array(243, 239, 246), PackedInt32Array(246, 239, 236), PackedInt32Array(238, 250, 237), PackedInt32Array(237, 250, 249), PackedInt32Array(252, 251, 253), PackedInt32Array(253, 251, 254), PackedInt32Array(256, 255, 257), PackedInt32Array(257, 255, 258), PackedInt32Array(258, 259, 257), PackedInt32Array(254, 251, 257), PackedInt32Array(257, 251, 260), PackedInt32Array(257, 260, 256), PackedInt32Array(263, 262, 261), PackedInt32Array(261, 265, 264), PackedInt32Array(267, 266, 268), PackedInt32Array(268, 266, 263), PackedInt32Array(268, 263, 261), PackedInt32Array(268, 261, 264), PackedInt32Array(269, 263, 270), PackedInt32Array(270, 263, 266), PackedInt32Array(272, 271, 273), PackedInt32Array(273, 271, 274), PackedInt32Array(274, 271, 275), PackedInt32Array(275, 271, 277), PackedInt32Array(275, 277, 276), PackedInt32Array(279, 278, 280), PackedInt32Array(280, 278, 281), PackedInt32Array(284, 283, 282), PackedInt32Array(206, 207, 285), PackedInt32Array(282, 280, 281), PackedInt32Array(206, 285, 287), PackedInt32Array(287, 285, 286), PackedInt32Array(288, 218, 219), PackedInt32Array(218, 288, 289), PackedInt32Array(289, 288, 286), PackedInt32Array(214, 215, 290), PackedInt32Array(290, 215, 291), PackedInt32Array(281, 293, 292), PackedInt32Array(285, 296, 292), PackedInt32Array(292, 299, 281), PackedInt32Array(281, 298, 284), PackedInt32Array(284, 300, 294), PackedInt32Array(294, 299, 295), PackedInt32Array(295, 296, 285), PackedInt32Array(296, 299, 292), PackedInt32Array(299, 298, 281), PackedInt32Array(298, 300, 284), PackedInt32Array(300, 299, 294), PackedInt32Array(299, 296, 295), PackedInt32Array(299, 297, 298), PackedInt32Array(298, 297, 300), PackedInt32Array(300, 297, 299), PackedInt32Array(221, 222, 301), PackedInt32Array(290, 291, 285), PackedInt32Array(285, 291, 295), PackedInt32Array(301, 222, 295), PackedInt32Array(295, 222, 294), PackedInt32Array(284, 282, 281), PackedInt32Array(289, 286, 292), PackedInt32Array(292, 286, 285), PackedInt32Array(303, 302, 197), PackedInt32Array(197, 302, 202), PackedInt32Array(164, 172, 304), PackedInt32Array(164, 304, 305), PackedInt32Array(305, 304, 202), PackedInt32Array(305, 202, 307), PackedInt32Array(305, 307, 306), PackedInt32Array(202, 302, 307), PackedInt32Array(309, 308, 210), PackedInt32Array(210, 308, 209), PackedInt32Array(310, 305, 306), PackedInt32Array(312, 311, 313), PackedInt32Array(313, 311, 314), PackedInt32Array(308, 309, 306), PackedInt32Array(306, 309, 315), PackedInt32Array(311, 316, 314), PackedInt32Array(314, 316, 306), PackedInt32Array(306, 316, 310), PackedInt32Array(310, 316, 317), PackedInt32Array(306, 315, 314), PackedInt32Array(314, 319, 313), PackedInt32Array(313, 319, 318), PackedInt32Array(320, 267, 268), PackedInt32Array(322, 321, 323), PackedInt32Array(323, 321, 326), PackedInt32Array(323, 326, 325), PackedInt32Array(323, 325, 324), PackedInt32Array(325, 328, 327), PackedInt32Array(330, 329, 320), PackedInt32Array(326, 331, 325), PackedInt32Array(320, 268, 330), PackedInt32Array(330, 268, 332), PackedInt32Array(332, 268, 324), PackedInt32Array(333, 324, 327), PackedInt32Array(327, 324, 325), PackedInt32Array(334, 330, 335), PackedInt32Array(335, 330, 332), PackedInt32Array(324, 333, 332), PackedInt32Array(321, 336, 326), PackedInt32Array(338, 337, 339), PackedInt32Array(339, 337, 340), PackedInt32Array(342, 341, 318), PackedInt32Array(345, 344, 343), PackedInt32Array(347, 346, 343), PackedInt32Array(343, 346, 342), PackedInt32Array(342, 346, 348), PackedInt32Array(343, 318, 345), PackedInt32Array(345, 318, 350), PackedInt32Array(345, 350, 225), PackedInt32Array(345, 225, 349), PackedInt32Array(318, 319, 350), PackedInt32Array(225, 217, 349), PackedInt32Array(337, 345, 340), PackedInt32Array(340, 345, 349), PackedInt32Array(343, 342, 318), PackedInt32Array(354, 353, 351), PackedInt32Array(351, 353, 352), PackedInt32Array(357, 356, 355), PackedInt32Array(358, 360, 359), PackedInt32Array(361, 363, 362), PackedInt32Array(365, 364, 317), PackedInt32Array(367, 366, 368), PackedInt32Array(368, 366, 369), PackedInt32Array(316, 370, 368), PackedInt32Array(371, 360, 363), PackedInt32Array(171, 372, 170), PackedInt32Array(372, 171, 358), PackedInt32Array(358, 171, 373), PackedInt32Array(365, 317, 361), PackedInt32Array(361, 317, 369), PackedInt32Array(361, 369, 374), PackedInt32Array(375, 380, 374), PackedInt32Array(374, 378, 361), PackedInt32Array(361, 377, 363), PackedInt32Array(363, 377, 371), PackedInt32Array(371, 380, 355), PackedInt32Array(355, 380, 357), PackedInt32Array(357, 380, 375), PackedInt32Array(380, 378, 374), PackedInt32Array(378, 377, 361), PackedInt32Array(377, 379, 371), PackedInt32Array(371, 379, 380), PackedInt32Array(380, 376, 378), PackedInt32Array(378, 376, 377), PackedInt32Array(377, 376, 379), PackedInt32Array(379, 376, 380), PackedInt32Array(368, 369, 316), PackedInt32Array(316, 369, 317), PackedInt32Array(373, 363, 358), PackedInt32Array(358, 363, 360), PackedInt32Array(357, 375, 382), PackedInt32Array(382, 375, 374), PackedInt32Array(382, 374, 381), PackedInt32Array(384, 383, 385), PackedInt32Array(385, 383, 386), PackedInt32Array(388, 387, 389), PackedInt32Array(391, 390, 392), PackedInt32Array(392, 390, 393), PackedInt32Array(396, 395, 394), PackedInt32Array(394, 387, 386), PackedInt32Array(386, 387, 385), PackedInt32Array(386, 396, 394), PackedInt32Array(394, 393, 387), PackedInt32Array(387, 393, 390), PackedInt32Array(387, 390, 397), PackedInt32Array(387, 397, 389), PackedInt32Array(401, 400, 398), PackedInt32Array(398, 400, 399), PackedInt32Array(402, 404, 403), PackedInt32Array(406, 405, 407), PackedInt32Array(407, 405, 409), PackedInt32Array(407, 409, 408), PackedInt32Array(411, 410, 404), PackedInt32Array(402, 416, 404), PackedInt32Array(404, 416, 411), PackedInt32Array(411, 417, 412), PackedInt32Array(412, 417, 413), PackedInt32Array(413, 417, 414), PackedInt32Array(414, 417, 415), PackedInt32Array(415, 417, 416), PackedInt32Array(416, 417, 411), PackedInt32Array(409, 405, 418), PackedInt32Array(418, 405, 413), PackedInt32Array(411, 292, 410), PackedInt32Array(410, 292, 293), PackedInt32Array(414, 418, 413), PackedInt32Array(413, 339, 412), PackedInt32Array(412, 339, 340), PackedInt32Array(192, 419, 191), PackedInt32Array(191, 419, 420), PackedInt32Array(422, 421, 355), PackedInt32Array(355, 421, 371), PackedInt32Array(419, 371, 420), PackedInt32Array(420, 371, 421), PackedInt32Array(355, 423, 422), PackedInt32Array(427, 426, 424), PackedInt32Array(424, 426, 425), PackedInt32Array(431, 430, 428), PackedInt32Array(428, 430, 429), PackedInt32Array(402, 403, 432), PackedInt32Array(432, 278, 279), PackedInt32Array(279, 433, 432), PackedInt32Array(432, 433, 402), PackedInt32Array(283, 284, 434), PackedInt32Array(434, 284, 435), PackedInt32Array(433, 434, 402), PackedInt32Array(402, 434, 437), PackedInt32Array(437, 434, 438), PackedInt32Array(438, 434, 439), PackedInt32Array(439, 434, 435), PackedInt32Array(439, 435, 436), PackedInt32Array(441, 440, 442), PackedInt32Array(442, 440, 443), PackedInt32Array(444, 443, 445), PackedInt32Array(445, 443, 446), PackedInt32Array(437, 448, 402), PackedInt32Array(402, 448, 447), PackedInt32Array(447, 443, 440), PackedInt32Array(443, 448, 446), PackedInt32Array(446, 448, 436), PackedInt32Array(436, 448, 439), PackedInt32Array(439, 448, 438), PackedInt32Array(438, 448, 437), PackedInt32Array(448, 443, 447), PackedInt32Array(366, 367, 449), PackedInt32Array(449, 367, 450), PackedInt32Array(451, 453, 452), PackedInt32Array(454, 451, 455), PackedInt32Array(457, 456, 450), PackedInt32Array(450, 456, 458), PackedInt32Array(460, 459, 454), PackedInt32Array(454, 459, 461), PackedInt32Array(453, 451, 454), PackedInt32Array(348, 346, 456), PackedInt32Array(456, 346, 458), PackedInt32Array(449, 450, 461), PackedInt32Array(461, 450, 458), PackedInt32Array(461, 458, 454), PackedInt32Array(454, 458, 453), PackedInt32Array(414, 415, 462), PackedInt32Array(462, 415, 416), PackedInt32Array(462, 416, 402), PackedInt32Array(462, 402, 463), PackedInt32Array(463, 402, 464), PackedInt32Array(402, 447, 464), PackedInt32Array(466, 465, 467), PackedInt32Array(467, 465, 408), PackedInt32Array(467, 408, 468), PackedInt32Array(465, 407, 408), PackedInt32Array(467, 468, 469), PackedInt32Array(469, 468, 470), PackedInt32Array(470, 468, 471), PackedInt32Array(470, 453, 469), PackedInt32Array(469, 453, 458), PackedInt32Array(473, 472, 459), PackedInt32Array(459, 472, 461), PackedInt32Array(476, 475, 474), PackedInt32Array(477, 476, 478), PackedInt32Array(478, 476, 479), PackedInt32Array(479, 476, 480), PackedInt32Array(480, 476, 481), PackedInt32Array(483, 482, 474), PackedInt32Array(474, 482, 476), PackedInt32Array(476, 482, 481), PackedInt32Array(485, 484, 486), PackedInt32Array(486, 484, 481), PackedInt32Array(484, 487, 481), PackedInt32Array(485, 488, 484), PackedInt32Array(487, 480, 481), PackedInt32Array(490, 489, 491), PackedInt32Array(491, 489, 492), PackedInt32Array(494, 496, 495), PackedInt32Array(495, 496, 493), PackedInt32Array(495, 493, 492), PackedInt32Array(497, 494, 498), PackedInt32Array(498, 494, 499), PackedInt32Array(489, 500, 492), PackedInt32Array(492, 500, 495), PackedInt32Array(495, 500, 501), PackedInt32Array(495, 501, 494), PackedInt32Array(494, 503, 499), PackedInt32Array(499, 503, 502), PackedInt32Array(502, 503, 473), PackedInt32Array(473, 504, 502), PackedInt32Array(505, 508, 506), PackedInt32Array(506, 508, 507), PackedInt32Array(357, 382, 500), PackedInt32Array(500, 382, 501), PackedInt32Array(501, 382, 494), PackedInt32Array(494, 382, 503), PackedInt32Array(503, 382, 473), PackedInt32Array(473, 382, 381), PackedInt32Array(473, 381, 472), PackedInt32Array(505, 499, 508), PackedInt32Array(508, 499, 502), PackedInt32Array(512, 516, 513), PackedInt32Array(513, 516, 509), PackedInt32Array(509, 515, 510), PackedInt32Array(510, 515, 511), PackedInt32Array(511, 516, 471), PackedInt32Array(471, 516, 512), PackedInt32Array(516, 514, 509), PackedInt32Array(509, 514, 515), PackedInt32Array(515, 516, 511), PackedInt32Array(516, 515, 514), PackedInt32Array(468, 511, 471), PackedInt32Array(462, 463, 510), PackedInt32Array(510, 463, 509), PackedInt32Array(519, 518, 517), PackedInt32Array(521, 520, 522), PackedInt32Array(522, 520, 523), PackedInt32Array(523, 520, 526), PackedInt32Array(526, 520, 525), PackedInt32Array(526, 525, 524), PackedInt32Array(529, 528, 527), PackedInt32Array(497, 498, 530), PackedInt32Array(530, 498, 531), PackedInt32Array(533, 532, 534), PackedInt32Array(534, 532, 535), PackedInt32Array(534, 535, 536), PackedInt32Array(536, 535, 537), PackedInt32Array(537, 535, 530), PackedInt32Array(537, 530, 531), PackedInt32Array(535, 538, 530), PackedInt32Array(540, 539, 507), PackedInt32Array(507, 539, 506), PackedInt32Array(506, 539, 531), PackedInt32Array(539, 529, 527), PackedInt32Array(542, 541, 543), PackedInt32Array(543, 541, 536), PackedInt32Array(539, 527, 544), PackedInt32Array(441, 442, 545), PackedInt32Array(545, 442, 546), PackedInt32Array(547, 546, 548), PackedInt32Array(513, 545, 512), PackedInt32Array(512, 545, 544), PackedInt32Array(537, 553, 547), PackedInt32Array(547, 554, 546), PackedInt32Array(546, 551, 545), PackedInt32Array(545, 550, 544), PackedInt32Array(544, 550, 531), PackedInt32Array(531, 550, 537), PackedInt32Array(553, 554, 547), PackedInt32Array(537, 550, 553), PackedInt32Array(554, 551, 546), PackedInt32Array(551, 550, 545), PackedInt32Array(553, 552, 554), PackedInt32Array(550, 549, 553), PackedInt32Array(554, 552, 551), PackedInt32Array(551, 549, 550), PackedInt32Array(553, 549, 552), PackedInt32Array(552, 549, 551), PackedInt32Array(531, 539, 544), PackedInt32Array(536, 541, 534), PackedInt32Array(558, 557, 555), PackedInt32Array(555, 557, 556), PackedInt32Array(355, 356, 559), PackedInt32Array(491, 560, 490), PackedInt32Array(490, 560, 559), PackedInt32Array(562, 561, 564), PackedInt32Array(564, 561, 563), PackedInt32Array(566, 565, 567), PackedInt32Array(567, 565, 423), PackedInt32Array(567, 423, 560), PackedInt32Array(560, 423, 355), PackedInt32Array(568, 569, 561), PackedInt32Array(561, 569, 563), PackedInt32Array(355, 559, 560), PackedInt32Array(568, 571, 570), PackedInt32Array(568, 570, 569), PackedInt32Array(569, 570, 563), PackedInt32Array(563, 570, 572), PackedInt32Array(566, 573, 565), PackedInt32Array(563, 572, 566), PackedInt32Array(566, 572, 573), PackedInt32Array(533, 574, 532), PackedInt32Array(532, 574, 575), PackedInt32Array(577, 562, 576), PackedInt32Array(579, 575, 578), PackedInt32Array(574, 580, 575), PackedInt32Array(582, 581, 583), PackedInt32Array(583, 581, 579), PackedInt32Array(585, 584, 577), PackedInt32Array(580, 542, 543), PackedInt32Array(576, 586, 577), PackedInt32Array(577, 586, 585), PackedInt32Array(576, 567, 586), PackedInt32Array(586, 567, 585), PackedInt32Array(585, 567, 587), PackedInt32Array(580, 543, 575), PackedInt32Array(575, 543, 578), PackedInt32Array(560, 591, 567), PackedInt32Array(567, 591, 588), PackedInt32Array(588, 579, 578), PackedInt32Array(579, 590, 583), PackedInt32Array(583, 589, 560), PackedInt32Array(560, 589, 591), PackedInt32Array(591, 579, 588), PackedInt32Array(590, 589, 583), PackedInt32Array(579, 591, 590), PackedInt32Array(589, 590, 591), PackedInt32Array(588, 587, 567), PackedInt32Array(494, 592, 496), PackedInt32Array(496, 592, 593), PackedInt32Array(496, 593, 581), PackedInt32Array(496, 581, 493), PackedInt32Array(493, 581, 582), PackedInt32Array(595, 597, 596), PackedInt32Array(596, 597, 594), PackedInt32Array(594, 593, 592), PackedInt32Array(593, 597, 581), PackedInt32Array(581, 597, 595), PackedInt32Array(597, 593, 594), PackedInt32Array(581, 595, 579), PackedInt32Array(599, 598, 600), PackedInt32Array(600, 598, 601), PackedInt32Array(603, 602, 598), PackedInt32Array(598, 602, 601), PackedInt32Array(606, 605, 604), PackedInt32Array(444, 445, 607), PackedInt32Array(607, 445, 608), PackedInt32Array(609, 548, 546), PackedInt32Array(604, 610, 606), PackedInt32Array(607, 608, 609), PackedInt32Array(609, 608, 610), PackedInt32Array(609, 610, 604), PackedInt32Array(609, 604, 548), PackedInt32Array(594, 538, 596), PackedInt32Array(596, 538, 535), PackedInt32Array(612, 611, 613), PackedInt32Array(613, 611, 614), PackedInt32Array(614, 611, 615), PackedInt32Array(617, 616, 618), PackedInt32Array(618, 616, 619), PackedInt32Array(621, 620, 616), PackedInt32Array(616, 620, 619), PackedInt32Array(624, 623, 622), PackedInt32Array(626, 625, 624), PackedInt32Array(628, 627, 622), PackedInt32Array(622, 627, 624), PackedInt32Array(626, 624, 629), PackedInt32Array(629, 624, 630), PackedInt32Array(630, 624, 627), PackedInt32Array(584, 585, 631), PackedInt32Array(631, 585, 632), PackedInt32Array(631, 632, 570), PackedInt32Array(570, 571, 631)]
agent_height = 1.75
agent_radius = 0.375
agent_max_climb = 0.5
edge_max_length = 12.0
filter_low_hanging_obstacles = true
filter_ledge_spans = true
filter_walkable_low_height_spans = true

[sub_resource type="StandardMaterial3D" id="StandardMaterial3D_cin4e"]
albedo_texture = ExtResource("15_df8b0")
metallic = 0.2
metallic_texture = ExtResource("16_003du")
roughness = 0.8
uv1_scale = Vector3(10, 10, 1)

[sub_resource type="StandardMaterial3D" id="StandardMaterial3D_01ds2"]
transparency = 1
albedo_color = Color(1, 1, 0, 0.3)
emission_enabled = true
emission = Color(1, 1, 0, 1)

[node name="level01" type="Node3D"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 0, 0, 1.94383)
script = ExtResource("1_hk2b6")
minimap_scene = ExtResource("2_j32d5")
player_avatar_scene = ExtResource("3_0f42i")
item_boxes_scene = ExtResource("4_swvig")
skill_box_ui_scene = ExtResource("5_dbohx")

[node name="WorldEnvironment" type="WorldEnvironment" parent="."]
environment = SubResource("Environment_j4i7h")

[node name="DirectionalLight3D" type="DirectionalLight3D" parent="."]
transform = Transform3D(0.999999, 0, 0, 0, 0.5, 0.866026, 0, -0.866025, 0.5, 0, 0, 0)
light_color = Color(1, 0.868175, 0.323518, 1)
light_energy = 1.5
shadow_enabled = true
shadow_opacity = 0.7
shadow_blur = 2.0

[node name="ModularLevelGenerator" type="Node3D" parent="."]
script = ExtResource("6_6ly8b")
level_config = ExtResource("7_r5102")

[node name="FogOfWar" parent="." instance=ExtResource("9_003du")]
player_vision_radius = 8.0
enabled = false

[node name="CameraPivot" type="Node3D" parent="."]
unique_name_in_owner = true
script = ExtResource("7_camera")

[node name="SpringArm3D" type="SpringArm3D" parent="CameraPivot"]
unique_name_in_owner = true
shape = SubResource("SphereShape3D_camera")

[node name="Camera3D" type="Camera3D" parent="CameraPivot/SpringArm3D"]
unique_name_in_owner = true
transform = Transform3D(1, 3.19744e-14, 2.13163e-14, 0, 1, 0, 0, 0, 1, 0, 0, 0)
v_offset = -10.0
current = true
fov = 54.8
far = 90.3

[node name="Player" parent="." instance=ExtResource("11_82jtv")]

[node name="CameraRefactorTest" type="Node" parent="."]
script = ExtResource("8_test")

[node name="Chest" parent="." instance=ExtResource("14_1ks1q")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 4.57862, 0, 6.58062)
interaction_distance = 3.0
item_resource = ExtResource("17_j32d5")

[node name="Chest2" parent="." instance=ExtResource("14_1ks1q")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 9.81867, 0, 6.58062)
interaction_distance = 3.0
item_resource = ExtResource("18_j32d5")

[node name="EnemyBoy01" parent="." instance=ExtResource("19_0f42i")]
transform = Transform3D(0.8, 0, 0, 0, 0.8, 0, 0, 0, 0.8, -10.0014, 0, -8.65824)

[node name="Enemy02" parent="." instance=ExtResource("21_dbohx")]
transform = Transform3D(2.1, 0, 0, 0, 2.1, 0, 0, 0, 2.1, 7.86917, 0, -7.768)

[node name="NavigationRegion3D" type="NavigationRegion3D" parent="."]
navigation_mesh = SubResource("NavigationMesh_xcdtp")

[node name="Floor" type="Node3D" parent="NavigationRegion3D"]

[node name="@CSGBox3D@77225" type="CSGBox3D" parent="NavigationRegion3D/Floor"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 0, -0.05, 0)
use_collision = true
size = Vector3(100, 0.1, 100)
material = SubResource("StandardMaterial3D_cin4e")

[node name="FloorTiles" type="Node3D" parent="NavigationRegion3D/Floor"]

[node name="Floor01_0" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 42.0142, 0.1, 27.8492)

[node name="Floor01_1" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -43.6087, 0.1, 15.1836)

[node name="Floor01_2" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 26.1332, 0.1, 41.9934)

[node name="Floor01_3" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 34.9691, 0.1, 7.87586)

[node name="Floor01_4" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -41.9545, 0.1, 27.4085)

[node name="Floor01_5" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 9.19888, 0.1, 31.7469)

[node name="Floor01_6" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -17.8979, 0.1, 40.1232)

[node name="Floor01_7" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -25.7344, 0.1, 26.9941)

[node name="Floor01_8" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 12.5462, 0.1, 2.36067)

[node name="Floor01_9" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -34.3325, 0.1, 7.91648)

[node name="Floor01_10" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -9.37372, 0.1, 0.154055)

[node name="Floor01_11" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 29.353, 0.1, 1.34194)

[node name="Floor01_12" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -13.4332, 0.1, 9.08382)

[node name="Floor01_13" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -3.95343, 0.1, 19.9164)

[node name="Floor01_14" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 38.3519, 0.1, -34.6236)

[node name="Floor01_15" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 10.9663, 0.1, -24.5509)

[node name="Floor01_16" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 17.3749, 0.1, -32.5032)

[node name="Floor01_17" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 37.3276, 0.1, 17.0806)

[node name="Floor01_18" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -21.9289, 0.1, -36.9634)

[node name="Floor01_19" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 41.3877, 0.1, 36.9133)

[node name="Floor01_20" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -41.1067, 0.1, -41.4236)

[node name="Floor01_21" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 15.556, 0.1, 44.4872)

[node name="Floor01_22" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 2.78333, 0.1, 1.07374)

[node name="Floor01_23" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -30.3068, 0.1, -19.9348)

[node name="Floor01_24" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 33.3587, 0.1, -13.0436)

[node name="Floor01_25" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 22.0676, 0.1, -19.5506)

[node name="Floor01_26" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -25.3892, 0.1, 14.3429)

[node name="Floor01_27" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -6.96152, 0.1, -35.4016)

[node name="Floor01_28" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -1.788, 0.1, -20.5136)

[node name="Floor01_29" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 13.6704, 0.1, -16.4492)

[node name="Floor01_30" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 30.5595, 0.1, 27.6526)

[node name="Floor01_31" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 29.061, 0.1, -36.2207)

[node name="Floor01_32" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 25.3487, 0.1, 19.0651)

[node name="Floor01_33" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 43.5374, 0.1, 10.7429)

[node name="Floor01_34" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 3.66083, 0.1, -34.657)

[node name="Floor01_35" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 18.0134, 0.1, 29.1557)

[node name="Floor01_36" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -33.128, 0.1, 42.5806)

[node name="Floor01_37" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -32.2602, 0.1, -38.9158)

[node name="Floor01_38" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -14.5861, 0.1, -7.51753)

[node name="Floor01_39" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 1.24265, 0.1, 29.864)

[node name="Floor01_40" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -4.54014, 0.1, 39.0301)

[node name="Floor01_41" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 0.560977, 0.1, -9.13352)

[node name="Floor01_42" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 39.0512, 0.1, -44.1323)

[node name="Floor01_43" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 40.2579, 0.1, -8.79998)

[node name="Floor01_44" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 14.4042, 0.1, -41.2759)

[node name="Floor01_45" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 3.77384, 0.1, 43.4885)

[node name="Floor01_46" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -10.7727, 0.1, -21.9661)

[node name="Floor01_47" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 21.2919, 0.1, -9.38066)

[node name="Floor01_48" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -31.2275, 0.1, -7.05829)

[node name="Floor01_49" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -42.9496, 0.1, -17.9086)

[node name="Floor01_50" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 44.1822, 0.1, -1.52495)

[node name="Floor01_51" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -44.8599, 0.1, -26.2171)

[node name="Floor01_52" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -36.109, 0.1, 0.0843343)

[node name="Floor01_53" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 44.1037, 0.1, -28.7176)

[node name="Floor01_54" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -21.3397, 0.1, -24.5268)

[node name="Floor01_55" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -14.4792, 0.1, -40.6559)

[node name="Floor01_56" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 2.29412, 0.1, 13.2996)

[node name="Floor01_57" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -24.0223, 0.1, -44.88)

[node name="Floor01_58" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 34.5592, 0.1, 43.326)

[node name="Floor01_59" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -23.4036, 0.1, 4.32859)

[node name="Floor01_60" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 14.9806, 0.1, 12.1582)

[node name="Floor01_61" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -44.7602, 0.1, -8.30337)

[node name="Floor01_62" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 43.1373, 0.1, -19.7158)

[node name="Floor01_63" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -14.4361, 0.1, 18.7061)

[node name="Floor01_64" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -7.43338, 0.1, -44.5917)

[node name="Floor01_65" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -1.71689, 0.1, -28.5515)

[node name="Floor01_66" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 27.5657, 0.1, -44.33)

[node name="Floor01_67" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -33.4134, 0.1, 16.7544)

[node name="Floor01_68" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -15.822, 0.1, 30.9991)

[node name="Floor01_69" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -28.8018, 0.1, -30.0702)

[node name="Floor01_70" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 8.8951, 0.1, 21.4809)

[node name="Floor01_71" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -33.8391, 0.1, 29.6793)

[node name="Floor01_72" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -14.5442, 0.1, -32.2149)

[node name="Floor01_73" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -6.85133, 0.1, 30.3117)

[node name="Floor01_74" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 20.5356, 0.1, 3.61005)

[node name="Floor01_75" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -37.2653, 0.1, -29.2702)

[node name="Floor01_76" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 34.0694, 0.1, -25.8225)

[node name="Floor01_77" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -44.5904, 0.1, 6.18359)

[node name="Floor01_78" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 6.38607, 0.1, -43.3278)

[node name="Floor01_79" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -44.7719, 0.1, 44.9664)

[node name="Floor01_80" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 8.076, 0.1, -6.26389)

[node name="Floor01_81" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -24.3338, 0.1, 34.8857)

[node name="wall_0" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -50, 0, -50)

[node name="wall_1" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -50, 0, 50)

[node name="wall_2" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -48, 0, -50)

[node name="wall_3" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -48, 0, 50)

[node name="wall_4" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -46, 0, -50)

[node name="wall_5" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -46, 0, 50)

[node name="wall_6" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -44, 0, -50)

[node name="wall_7" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -44, 0, 50)

[node name="wall_8" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -42, 0, -50)

[node name="wall_9" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -42, 0, 50)

[node name="wall_10" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -40, 0, -50)

[node name="wall_11" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -40, 0, 50)

[node name="wall_12" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -38, 0, -50)

[node name="wall_13" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -38, 0, 50)

[node name="wall_14" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -36, 0, -50)

[node name="wall_15" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -36, 0, 50)

[node name="wall_16" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -34, 0, -50)

[node name="wall_17" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -34, 0, 50)

[node name="wall_18" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -32, 0, -50)

[node name="wall_19" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -32, 0, 50)

[node name="wall_20" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -30, 0, -50)

[node name="wall_21" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -30, 0, 50)

[node name="wall_22" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -28, 0, -50)

[node name="wall_23" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -28, 0, 50)

[node name="wall_24" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -26, 0, -50)

[node name="wall_25" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -26, 0, 50)

[node name="wall_26" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -24, 0, -50)

[node name="wall_27" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -24, 0, 50)

[node name="wall_28" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -22, 0, -50)

[node name="wall_29" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -22, 0, 50)

[node name="wall_30" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -20, 0, -50)

[node name="wall_31" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -20, 0, 50)

[node name="wall_32" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -18, 0, -50)

[node name="wall_33" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -18, 0, 50)

[node name="wall_34" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -16, 0, -50)

[node name="wall_35" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -16, 0, 50)

[node name="wall_36" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -14, 0, -50)

[node name="wall_37" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -14, 0, 50)

[node name="wall_38" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -12, 0, -50)

[node name="wall_39" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -12, 0, 50)

[node name="wall_40" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -10, 0, -50)

[node name="wall_41" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -10, 0, 50)

[node name="wall_42" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -8, 0, -50)

[node name="wall_43" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -8, 0, 50)

[node name="wall_44" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -6, 0, -50)

[node name="wall_45" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -6, 0, 50)

[node name="wall_46" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -4, 0, -50)

[node name="wall_47" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -4, 0, 50)

[node name="wall_48" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -2, 0, -50)

[node name="wall_49" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -2, 0, 50)

[node name="wall_50" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 0, 0, -50)

[node name="wall_51" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 0, 0, 50)

[node name="wall_52" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 2, 0, -50)

[node name="wall_53" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 2, 0, 50)

[node name="wall_54" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 4, 0, -50)

[node name="wall_55" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 4, 0, 50)

[node name="wall_56" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 6, 0, -50)

[node name="wall_57" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 6, 0, 50)

[node name="wall_58" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 8, 0, -50)

[node name="wall_59" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 8, 0, 50)

[node name="wall_60" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 10, 0, -50)

[node name="wall_61" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 10, 0, 50)

[node name="wall_62" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 12, 0, -50)

[node name="wall_63" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 12, 0, 50)

[node name="wall_64" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 14, 0, -50)

[node name="wall_65" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 14, 0, 50)

[node name="wall_66" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 16, 0, -50)

[node name="wall_67" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 16, 0, 50)

[node name="wall_68" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 18, 0, -50)

[node name="wall_69" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 18, 0, 50)

[node name="wall_70" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 20, 0, -50)

[node name="wall_71" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 20, 0, 50)

[node name="wall_72" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 22, 0, -50)

[node name="wall_73" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 22, 0, 50)

[node name="wall_74" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 24, 0, -50)

[node name="wall_75" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 24, 0, 50)

[node name="wall_76" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 26, 0, -50)

[node name="wall_77" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 26, 0, 50)

[node name="wall_78" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 28, 0, -50)

[node name="wall_79" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 28, 0, 50)

[node name="wall_80" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 30, 0, -50)

[node name="wall_81" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 30, 0, 50)

[node name="wall_82" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 32, 0, -50)

[node name="wall_83" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 32, 0, 50)

[node name="wall_84" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 34, 0, -50)

[node name="wall_85" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 34, 0, 50)

[node name="wall_86" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 36, 0, -50)

[node name="wall_87" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 36, 0, 50)

[node name="wall_88" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 38, 0, -50)

[node name="wall_89" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 38, 0, 50)

[node name="wall_90" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 40, 0, -50)

[node name="wall_91" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 40, 0, 50)

[node name="wall_92" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 42, 0, -50)

[node name="wall_93" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 42, 0, 50)

[node name="wall_94" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 44, 0, -50)

[node name="wall_95" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 44, 0, 50)

[node name="wall_96" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 46, 0, -50)

[node name="wall_97" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 46, 0, 50)

[node name="wall_98" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 48, 0, -50)

[node name="wall_99" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 48, 0, 50)

[node name="wall_100" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 50, 0, -50)

[node name="wall_101" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 50, 0, 50)

[node name="wall_102" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, -50, 0, -50)

[node name="wall_103" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, 50, 0, -50)

[node name="wall_104" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, -50, 0, -48)

[node name="wall_105" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, 50, 0, -48)

[node name="wall_106" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, -50, 0, -46)

[node name="wall_107" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, 50, 0, -46)

[node name="wall_108" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, -50, 0, -44)

[node name="wall_109" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, 50, 0, -44)

[node name="wall_110" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, -50, 0, -42)

[node name="wall_111" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, 50, 0, -42)

[node name="wall_112" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, -50, 0, -40)

[node name="wall_113" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, 50, 0, -40)

[node name="wall_114" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, -50, 0, -38)

[node name="wall_115" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, 50, 0, -38)

[node name="wall_116" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, -50, 0, -36)

[node name="wall_117" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, 50, 0, -36)

[node name="wall_118" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, -50, 0, -34)

[node name="wall_119" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, 50, 0, -34)

[node name="wall_120" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, -50, 0, -32)

[node name="wall_121" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, 50, 0, -32)

[node name="wall_122" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, -50, 0, -30)

[node name="wall_123" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, 50, 0, -30)

[node name="wall_124" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, -50, 0, -28)

[node name="wall_125" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, 50, 0, -28)

[node name="wall_126" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, -50, 0, -26)

[node name="wall_127" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, 50, 0, -26)

[node name="wall_128" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, -50, 0, -24)

[node name="wall_129" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, 50, 0, -24)

[node name="wall_130" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, -50, 0, -22)

[node name="wall_131" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, 50, 0, -22)

[node name="wall_132" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, -50, 0, -20)

[node name="wall_133" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, 50, 0, -20)

[node name="wall_134" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, -50, 0, -18)

[node name="wall_135" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, 50, 0, -18)

[node name="wall_136" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, -50, 0, -16)

[node name="wall_137" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, 50, 0, -16)

[node name="wall_138" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, -50, 0, -14)

[node name="wall_139" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, 50, 0, -14)

[node name="wall_140" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, -50, 0, -12)

[node name="wall_141" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, 50, 0, -12)

[node name="wall_142" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, -50, 0, -10)

[node name="wall_143" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, 50, 0, -10)

[node name="wall_144" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, -50, 0, -8)

[node name="wall_145" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, 50, 0, -8)

[node name="wall_146" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, -50, 0, -6)

[node name="wall_147" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, 50, 0, -6)

[node name="wall_148" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, -50, 0, -4)

[node name="wall_149" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, 50, 0, -4)

[node name="wall_150" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, -50, 0, -2)

[node name="wall_151" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, 50, 0, -2)

[node name="wall_152" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, -50, 0, 0)

[node name="wall_153" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, 50, 0, 0)

[node name="wall_154" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, -50, 0, 2)

[node name="wall_155" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, 50, 0, 2)

[node name="wall_156" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, -50, 0, 4)

[node name="wall_157" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, 50, 0, 4)

[node name="wall_158" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, -50, 0, 6)

[node name="wall_159" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, 50, 0, 6)

[node name="wall_160" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, -50, 0, 8)

[node name="wall_161" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, 50, 0, 8)

[node name="wall_162" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, -50, 0, 10)

[node name="wall_163" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, 50, 0, 10)

[node name="wall_164" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, -50, 0, 12)

[node name="wall_165" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, 50, 0, 12)

[node name="wall_166" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, -50, 0, 14)

[node name="wall_167" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, 50, 0, 14)

[node name="wall_168" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, -50, 0, 16)

[node name="wall_169" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, 50, 0, 16)

[node name="wall_170" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, -50, 0, 18)

[node name="wall_171" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, 50, 0, 18)

[node name="wall_172" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, -50, 0, 20)

[node name="wall_173" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, 50, 0, 20)

[node name="wall_174" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, -50, 0, 22)

[node name="wall_175" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, 50, 0, 22)

[node name="wall_176" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, -50, 0, 24)

[node name="wall_177" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, 50, 0, 24)

[node name="wall_178" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, -50, 0, 26)

[node name="wall_179" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, 50, 0, 26)

[node name="wall_180" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, -50, 0, 28)

[node name="wall_181" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, 50, 0, 28)

[node name="wall_182" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, -50, 0, 30)

[node name="wall_183" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, 50, 0, 30)

[node name="wall_184" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, -50, 0, 32)

[node name="wall_185" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, 50, 0, 32)

[node name="wall_186" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, -50, 0, 34)

[node name="wall_187" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, 50, 0, 34)

[node name="wall_188" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, -50, 0, 36)

[node name="wall_189" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, 50, 0, 36)

[node name="wall_190" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, -50, 0, 38)

[node name="wall_191" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, 50, 0, 38)

[node name="wall_192" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, -50, 0, 40)

[node name="wall_193" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, 50, 0, 40)

[node name="wall_194" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, -50, 0, 42)

[node name="wall_195" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, 50, 0, 42)

[node name="wall_196" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, -50, 0, 44)

[node name="wall_197" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, 50, 0, 44)

[node name="wall_198" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, -50, 0, 46)

[node name="wall_199" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, 50, 0, 46)

[node name="wall_200" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, -50, 0, 48)

[node name="wall_201" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, 50, 0, 48)

[node name="wall_202" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, -50, 0, 50)

[node name="wall_203" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, 50, 0, 50)

[node name="tree_0" type="Node3D" parent="NavigationRegion3D" instance=ExtResource("15_003du")]
transform = Transform3D(2.5, -0.00145593, -0.00167976, 0.00145725, 2.5, 0.00196474, 0.00167861, -0.00196571, 2.5, -16.9437, 0, -16.1185)
script = ExtResource("18_oh2bt")
highlight_material = SubResource("StandardMaterial3D_01ds2")
progress_bar_scene = ExtResource("19_fcnrs")

[node name="PatrolPoint_0" type="Node3D" parent="NavigationRegion3D"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -12.7229, 1, -16.9063)
script = ExtResource("15_6ly8b")

[node name="tree_1" type="Node3D" parent="NavigationRegion3D" instance=ExtResource("15_003du")]
transform = Transform3D(2.5, -0.00145593, -0.00167976, 0.00145725, 2.5, 0.00196474, 0.00167861, -0.00196571, 2.5, 3.45531, 0, -4.20964)
script = ExtResource("18_oh2bt")
highlight_material = SubResource("StandardMaterial3D_01ds2")
preset_burnt_out_type = 1
progress_bar_scene = ExtResource("19_fcnrs")

[node name="PatrolPoint_1" type="Node3D" parent="NavigationRegion3D"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -0.211129, 1, -0.273514)
script = ExtResource("15_6ly8b")
point_id = 1

[node name="tree_2" type="Node3D" parent="NavigationRegion3D" instance=ExtResource("15_003du")]
transform = Transform3D(2.5, -0.00145593, -0.00167976, 0.00145725, 2.5, 0.00196474, 0.00167861, -0.00196571, 2.5, 5.87103, 0, 17.1997)
script = ExtResource("18_oh2bt")
highlight_material = SubResource("StandardMaterial3D_01ds2")
progress_bar_scene = ExtResource("19_fcnrs")

[node name="PatrolPoint_2" type="Node3D" parent="NavigationRegion3D"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 10.6822, 1, 16.0051)
script = ExtResource("15_6ly8b")
point_id = 2

[node name="tree_3" type="Node3D" parent="NavigationRegion3D" instance=ExtResource("15_003du")]
transform = Transform3D(2.5, -0.00145593, -0.00167976, 0.00145725, 2.5, 0.00196474, 0.00167861, -0.00196571, 2.5, 16.4507, 0, -4.3727)
script = ExtResource("18_oh2bt")
highlight_material = SubResource("StandardMaterial3D_01ds2")
progress_bar_scene = ExtResource("19_fcnrs")

[node name="PatrolPoint_3" type="Node3D" parent="NavigationRegion3D"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 21.8856, 1, 0.283772)
script = ExtResource("15_6ly8b")
point_id = 3

[node name="chest_0" type="Area3D" parent="NavigationRegion3D" instance=ExtResource("14_1ks1q")]
transform = Transform3D(0.0779106, 0, 0.99696, 0, 1, 0, -0.99696, 0, 0.0779106, 28.8111, 0, 15.3071)
collision_layer = 8
collision_mask = 16
script = ExtResource("21_1ks1q")
interaction_distance = 1.5
item_resource = ExtResource("22_xcdtp")
unlock_sound = ExtResource("23_cin4e")

[node name="rock_0" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("21_xcdtp")]
transform = Transform3D(0.827458, 0, 0.350675, 0, 0.898699, 0, -0.350675, 0, 0.827458, 27.0593, 0.01, -27.6819)
collision_layer = 2
collision_mask = 0

[node name="rock_1" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("21_xcdtp")]
transform = Transform3D(1.19506, 0, -0.0191928, 0, 1.19521, 0, 0.0191928, 0, 1.19506, 5.19798, 0.01, -20.4779)
collision_layer = 2
collision_mask = 0

[node name="rock_2" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("21_xcdtp")]
transform = Transform3D(-0.262373, 0, 0.789428, 0, 0.831887, 0, -0.789428, 0, -0.262373, -22.3029, 0.01, 22.69)
collision_layer = 2
collision_mask = 0

[node name="rock_3" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("21_xcdtp")]
transform = Transform3D(-0.225788, 0, -1.07261, 0, 1.09611, 0, 1.07261, 0, -0.225788, 43.3323, 0.01, -13.066)
collision_layer = 2
collision_mask = 0

[node name="rock_4" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("21_xcdtp")]
transform = Transform3D(0.429067, 0, 0.917379, 0, 1.01276, 0, -0.917379, 0, 0.429067, 20.3112, 0.01, -25.0578)
collision_layer = 2
collision_mask = 0

[node name="rock_5" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("21_xcdtp")]
transform = Transform3D(-0.0866199, 0, 1.19274, 0, 1.19588, 0, -1.19274, 0, -0.0866199, 2.24632, 0.01, -24.1722)
collision_layer = 2
collision_mask = 0

[node name="rock_6" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("21_xcdtp")]
transform = Transform3D(0.21696, 0, 1.07189, 0, 1.09363, 0, -1.07189, 0, 0.21696, -23.4512, 0.01, -29.3046)
collision_layer = 2
collision_mask = 0

[node name="rock_7" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("21_xcdtp")]
transform = Transform3D(-0.632225, 0, 0.722233, 0, 0.959859, 0, -0.722233, 0, -0.632225, -18.9974, 0.01, 1.79022)
collision_layer = 2
collision_mask = 0

[node name="rock_8" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("21_xcdtp")]
transform = Transform3D(-0.798128, 0, 0.773124, 0, 1.11118, 0, -0.773124, 0, -0.798128, -8.93346, 0.01, -9.47347)
collision_layer = 2
collision_mask = 0

[node name="rock_9" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("21_xcdtp")]
transform = Transform3D(1.11509, 0, -0.309268, 0, 1.15718, 0, 0.309268, 0, 1.11509, 22.238, 0.01, 38.5805)
collision_layer = 2
collision_mask = 0

[node name="rock_10" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("21_xcdtp")]
transform = Transform3D(-0.225486, 0, -0.877693, 0, 0.906195, 0, 0.877693, 0, -0.225486, 41.8073, 0.01, 3.12826)
collision_layer = 2
collision_mask = 0

[node name="rock_11" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("21_xcdtp")]
transform = Transform3D(-0.309333, 0, 1.14209, 0, 1.18324, 0, -1.14209, 0, -0.309333, 33.1077, 0.01, 21.9524)
collision_layer = 2
collision_mask = 0

[node name="rock_12" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("21_xcdtp")]
transform = Transform3D(0.00273695, 0, 0.93006, 0, 0.930064, 0, -0.93006, 0, 0.00273695, 22.9833, 0.01, 10.6668)
collision_layer = 2
collision_mask = 0

[node name="rock_13" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("21_xcdtp")]
transform = Transform3D(0.0880611, 0, 0.964273, 0, 0.968286, 0, -0.964273, 0, 0.0880611, -34.193, 0.01, -33.7636)
collision_layer = 2
collision_mask = 0

[node name="rock_14" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("21_xcdtp")]
transform = Transform3D(0.810735, 0, 0.805187, 0, 1.14264, 0, -0.805187, 0, 0.810735, 25.4219, 0.01, 31.851)
collision_layer = 2
collision_mask = 0

[node name="barrel_0" type="StaticBody3D" parent="NavigationRegion3D" groups=["destructible"] instance=ExtResource("22_cin4e")]
transform = Transform3D(-0.752223, 0, -0.409077, 0, 0.856261, 0, 0.409077, 0, -0.752223, 39.2374, 0, -26.5886)
collision_layer = 3
collision_mask = 3

[node name="barrel_1" type="StaticBody3D" parent="NavigationRegion3D" groups=["destructible"] instance=ExtResource("22_cin4e")]
transform = Transform3D(-0.950692, 0, 0.0081441, 0, 0.950727, 0, -0.0081441, 0, -0.950692, 25.6266, 0, -12.9787)
collision_layer = 3
collision_mask = 3

[node name="decoration_0" type="Node3D" parent="NavigationRegion3D" instance=ExtResource("23_6j2fk")]
transform = Transform3D(0.739131, 0, 1.13901, 0, 1.35782, 0, -1.13901, 0, 0.739131, -23.9732, 0, -11.9983)

[node name="decoration_1" type="Node3D" parent="NavigationRegion3D" instance=ExtResource("23_6j2fk")]
transform = Transform3D(-0.780437, 0, -1.07146, 0, 1.32556, 0, 1.07146, 0, -0.780437, -40.5077, 0, 41.8472)

[node name="decoration_2" type="Node3D" parent="NavigationRegion3D" instance=ExtResource("23_6j2fk")]
transform = Transform3D(-0.711464, 0, 1.15829, 0, 1.35934, 0, -1.15829, 0, -0.711464, -20.8034, 0, 18.0061)

[node name="decoration_3" type="Node3D" parent="NavigationRegion3D" instance=ExtResource("23_6j2fk")]
transform = Transform3D(-0.310829, 0, 0.927314, 0, 0.978021, 0, -0.927314, 0, -0.310829, -10.5361, 0, 26.2006)

[node name="decoration_4" type="Node3D" parent="NavigationRegion3D" instance=ExtResource("23_6j2fk")]
transform = Transform3D(-0.353941, 0, 1.05559, 0, 1.11335, 0, -1.05559, 0, -0.353941, -43.5242, 0, -33.7095)

[node name="decoration_5" type="Node3D" parent="NavigationRegion3D" instance=ExtResource("23_6j2fk")]
transform = Transform3D(0.654326, 0, 0.323952, 0, 0.730129, 0, -0.323952, 0, 0.654326, -11.9768, 0, -16.1819)

[node name="decoration_6" type="Node3D" parent="NavigationRegion3D" instance=ExtResource("23_6j2fk")]
transform = Transform3D(0.630041, 0, -0.239267, 0, 0.673944, 0, 0.239267, 0, 0.630041, -38.638, 0, -23.1996)

[node name="decoration_7" type="Node3D" parent="NavigationRegion3D" instance=ExtResource("23_6j2fk")]
transform = Transform3D(-0.1888, 0, 0.730974, 0, 0.754963, 0, -0.730974, 0, -0.1888, -2.97634, 0, 10.4905)

[node name="decoration_8" type="Node3D" parent="NavigationRegion3D" instance=ExtResource("23_6j2fk")]
transform = Transform3D(0.277672, 0, -0.747122, 0, 0.797053, 0, 0.747122, 0, 0.277672, 13.1274, 0, 18.1822)

[node name="decoration_9" type="Node3D" parent="NavigationRegion3D" instance=ExtResource("23_6j2fk")]
transform = Transform3D(-0.950531, 0, -0.268328, 0, 0.987678, 0, 0.268328, 0, -0.950531, 13.9662, 0, 39.544)

[node name="decoration_10" type="Node3D" parent="NavigationRegion3D" instance=ExtResource("23_6j2fk")]
transform = Transform3D(-0.688326, 0, 1.08143, 0, 1.2819, 0, -1.08143, 0, -0.688326, -12.6063, 0, 41.1216)

[node name="decoration_11" type="Node3D" parent="NavigationRegion3D" instance=ExtResource("23_6j2fk")]
transform = Transform3D(1.13028, 0, 0.378512, 0, 1.19197, 0, -0.378512, 0, 1.13028, 24.0585, 0, -0.951723)

[node name="decoration_12" type="Node3D" parent="NavigationRegion3D" instance=ExtResource("23_6j2fk")]
transform = Transform3D(0.359657, 0, -0.834278, 0, 0.908501, 0, 0.834278, 0, 0.359657, 1.21164, 0, -40.9531)

[node name="decoration_13" type="Node3D" parent="NavigationRegion3D" instance=ExtResource("23_6j2fk")]
transform = Transform3D(1.36351, 0, 0.204116, 0, 1.3787, 0, -0.204116, 0, 1.36351, 36.7958, 0, -4.87518)

[node name="decoration_14" type="Node3D" parent="NavigationRegion3D" instance=ExtResource("23_6j2fk")]
transform = Transform3D(-0.301385, 0, -0.533045, 0, 0.612348, 0, 0.533045, 0, -0.301385, -40.3948, 0, 20.3527)

[node name="decoration_15" type="Node3D" parent="NavigationRegion3D" instance=ExtResource("23_6j2fk")]
transform = Transform3D(0.75987, 0, 1.04824, 0, 1.29469, 0, -1.04824, 0, 0.75987, 29.9246, 0, -22.3603)

[node name="decoration_16" type="Node3D" parent="NavigationRegion3D" instance=ExtResource("23_6j2fk")]
transform = Transform3D(-1.05669, 0, -0.723404, 0, 1.28059, 0, 0.723404, 0, -1.05669, -3.83231, 0, 24.9191)

[node name="decoration_17" type="Node3D" parent="NavigationRegion3D" instance=ExtResource("23_6j2fk")]
transform = Transform3D(-0.608418, 0, 1.17742, 0, 1.32533, 0, -1.17742, 0, -0.608418, -11.5827, 0, -27.1814)

[node name="decoration_18" type="Node3D" parent="NavigationRegion3D" instance=ExtResource("23_6j2fk")]
transform = Transform3D(0.0895736, 0, -0.785083, 0, 0.790177, 0, 0.785083, 0, 0.0895736, 38.8074, 0, 0.637021)

[node name="decoration_19" type="Node3D" parent="NavigationRegion3D" instance=ExtResource("23_6j2fk")]
transform = Transform3D(0.658858, 0, 0.743709, 0, 0.993578, 0, -0.743709, 0, 0.658858, 12.2931, 0, -33.6485)

[node name="PatrolPointManager" type="Node" parent="."]
script = ExtResource("24_j2uky")
