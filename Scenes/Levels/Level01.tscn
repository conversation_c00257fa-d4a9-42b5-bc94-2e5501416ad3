[gd_scene load_steps=32 format=3 uid="uid://c0rj4dwj18nv8"]

[ext_resource type="Script" uid="uid://bo0h8t35yls55" path="res://Scripts/Levels/Level01.gd" id="1_hk2b6"]
[ext_resource type="PackedScene" uid="uid://d2xyw4d37ymv4" path="res://Scenes/Minimap.tscn" id="2_j32d5"]
[ext_resource type="PackedScene" uid="uid://ctxvyh1qr52ue" path="res://Scenes/PlayerAvatar.tscn" id="3_0f42i"]
[ext_resource type="PackedScene" uid="uid://do1sypgxbecbd" path="res://Scenes/ItemBoxes.tscn" id="4_swvig"]
[ext_resource type="PackedScene" uid="uid://b4l4phqqbvprm" path="res://Scenes/SkillBoxUI.tscn" id="5_dbohx"]
[ext_resource type="Script" uid="uid://f54r1khulcvh" path="res://Scripts/ModularLevelGenerator.gd" id="6_6ly8b"]
[ext_resource type="Script" uid="uid://c30rtq521bf7l" path="res://Scripts/CameraController.gd" id="7_camera"]
[ext_resource type="Resource" uid="uid://cvji54t3pjwfe" path="res://Resources/LevelConfigs/AllModulesConfig.tres" id="7_r5102"]
[ext_resource type="PackedScene" uid="uid://d2b5h1mlxxy7d" path="res://Scenes/FogOfWar.tscn" id="9_003du"]
[ext_resource type="PackedScene" uid="uid://b88l8pk1ebe1x" path="res://Scenes/player.tscn" id="11_82jtv"]
[ext_resource type="PackedScene" uid="uid://c3h8fj2xsp5oy" path="res://Scenes/Prefabs/Chest.tscn" id="14_1ks1q"]
[ext_resource type="PackedScene" uid="uid://dwusy8dd8usvo" path="res://Scenes/Prefabs/Wall.tscn" id="14_df8b0"]
[ext_resource type="PackedScene" uid="uid://crtnthqkksmri" path="res://Scenes/Prefabs/Tree.tscn" id="15_003du"]
[ext_resource type="Script" uid="uid://c3tr23vwvnmwf" path="res://Scripts/PatrolPoint.gd" id="15_6ly8b"]
[ext_resource type="Texture2D" uid="uid://c2ny0yi07rvcf" path="res://Environment/Floor/Floor01_Rocks_BaseColor.png" id="15_df8b0"]
[ext_resource type="Texture2D" uid="uid://dhfikoo16s5n0" path="res://Environment/Floor/Rocks_Metallic.png" id="16_003du"]
[ext_resource type="PackedScene" uid="uid://ddttv643pel23" path="res://Environment/Floor/Floor01_Custom.tscn" id="17_2bvpm"]
[ext_resource type="Resource" path="res://Resources/Items/Trap.tres" id="17_j32d5"]
[ext_resource type="Resource" path="res://Resources/Items/Torch.tres" id="18_j32d5"]
[ext_resource type="PackedScene" uid="uid://rvgn0irsuwao" path="res://Scenes/EnemyBoy01.tscn" id="19_0f42i"]
[ext_resource type="PackedScene" uid="uid://ervckea7fk57" path="res://Scenes/Enemy02.tscn" id="21_dbohx"]
[ext_resource type="PackedScene" uid="uid://b5dqjsb63wbhl" path="res://Scenes/Prefabs/Rock.tscn" id="21_xcdtp"]
[ext_resource type="PackedScene" uid="uid://bdq3b4e0mlgo4" path="res://Scenes/Prefabs/Barrel.tscn" id="22_cin4e"]
[ext_resource type="PackedScene" uid="uid://c6k7j3t3flhst" path="res://Scenes/Prefabs/Decoration.tscn" id="23_6j2fk"]
[ext_resource type="Script" uid="uid://de18ote8otj6u" path="res://Scripts/PatrolPointManager.gd" id="24_j2uky"]

[sub_resource type="ProceduralSkyMaterial" id="ProceduralSkyMaterial_gbvua"]
sky_top_color = Color(0.2, 0.4, 0.8, 1)
sky_horizon_color = Color(0.5, 0.7, 0.9, 1)
ground_bottom_color = Color(0.3, 0.5, 0.7, 1)
ground_horizon_color = Color(0.5, 0.7, 0.9, 1)

[sub_resource type="Sky" id="Sky_m0r78"]
sky_material = SubResource("ProceduralSkyMaterial_gbvua")

[sub_resource type="Environment" id="Environment_j4i7h"]
background_mode = 2
sky = SubResource("Sky_m0r78")
ambient_light_source = 3
ambient_light_color = Color(0.85, 0.85, 0.9, 1)
ambient_light_energy = 0.4
tonemap_mode = 2
ssao_enabled = true
glow_enabled = true
fog_enabled = true
fog_light_color = Color(0.85, 0.9, 1, 1)
fog_density = 0.001

[sub_resource type="SphereShape3D" id="SphereShape3D_camera"]
radius = 0.2

[sub_resource type="NavigationMesh" id="NavigationMesh_1ks1q"]
vertices = PackedVector3Array(-33.5681, 0.340398, -52.4658, -33.3181, 0.340398, -52.9658, -33.3181, 0.340398, -54.2158, -38.3181, 0.340398, -52.9658, -39.5681, 0.340398, -53.2158, -40.3181, 0.590398, -52.7158, -34.3181, 0.340398, -55.4658, -36.0681, 0.590398, -55.7158, -37.5681, 0.340398, -54.7158, 1.68188, 0.340398, -52.7158, 1.18188, 0.340398, -54.7158, -0.818123, 0.590398, -55.4658, -2.56812, 0.340398, -54.4658, -3.31812, 0.340398, -52.7158, -4.81812, 0.590398, -52.7158, -25.5681, 0.340398, -54.7158, -27.0681, 0.590398, -55.2158, -28.8181, 0.340398, -54.7158, -30.0681, 0.340398, -52.7158, -25.0681, 0.340398, -52.4658, 26.4319, 0.590398, -54.7158, 26.1819, 0.590398, -55.2158, 24.6819, 0.340398, -54.7158, 28.4319, 0.340398, -52.4658, 27.9319, 0.340398, -54.4658, 23.4319, 0.340398, -52.7158, -48.8181, 4.3404, -51.7158, -48.8181, 4.3404, -53.9658, -51.0681, 4.3404, -53.9658, -51.0681, 4.3404, -48.9658, -48.5681, 4.3404, -48.7158, 34.6819, 4.3404, -48.9658, 46.6819, 4.3404, -48.9658, 46.6819, 4.3404, -51.2158, 34.6819, 4.3404, -51.2158, -48.3181, 4.3404, -51.2158, -37.0681, 4.3404, -48.9658, -36.5681, 4.3404, -51.2158, -0.818123, 4.3404, -51.2158, -1.06812, 4.3404, -48.9658, 10.6819, 4.3404, -48.9658, 10.9319, 4.3404, -51.2158, -25.0681, 4.3404, -48.9658, -24.5681, 4.3404, -51.2158, 22.9319, 4.3404, -51.2158, 22.6819, 4.3404, -48.9658, -13.0681, 4.3404, -48.9658, -12.8181, 4.3404, -51.2158, 48.4319, 4.3404, -48.9658, 48.4319, 4.3404, -51.2158, 51.1819, 4.3404, -48.4658, 51.6819, 4.3404, -51.2158, 51.1819, 4.3404, -51.7158, 48.9319, 4.3404, -51.7158, 51.6819, 4.3404, -48.9658, 48.9319, 4.3404, -48.4658, 48.9319, 4.3404, -46.7158, 51.1819, 4.3404, -46.7158, 51.1819, 4.3404, -53.9658, 48.9319, 4.3404, -53.9658, 53.9319, 4.3404, -48.9658, 53.9319, 4.3404, -51.2158, -46.0681, 0.340398, -52.7158, -42.5681, 0.340398, -52.4658, -44.0681, 0.590398, -53.4658, -10.8181, 0.340398, -52.7158, -7.81812, 0.340398, -52.4658, -9.06812, 0.590398, -52.7158, -9.31812, 0.590398, -53.2158, -51.0681, 4.3404, -42.9658, -48.8181, 4.3404, -42.7158, -48.8181, 4.3404, 36.0342, -51.0681, 4.3404, 31.0342, -51.0681, 4.3404, 37.2842, -51.0681, 4.3404, 43.5342, -51.0681, 4.3404, 49.7842, -50.3181, 4.3404, 49.7842, -48.8181, 4.3404, 48.2842, -48.8181, 4.3404, 42.0342, -51.0681, 4.3404, -36.7158, -48.8181, 4.3404, -36.7158, -19.3181, 4.3404, 51.0342, -13.0681, 4.3404, 51.0342, -12.0681, 4.3404, 48.7842, -24.0681, 4.3404, 48.7842, -48.8181, 4.3404, -30.7158, -51.0681, 4.3404, -30.7158, -25.3181, 4.3404, 51.0342, 42.6819, 4.3404, 51.0342, 48.9319, 4.3404, 51.0342, 48.6819, 4.3404, 48.5342, 42.6819, 4.3404, 48.7842, -36.3181, 4.3404, 48.7842, -48.3181, 4.3404, 48.7842, -43.8181, 4.3404, 51.0342, -37.5681, 4.3404, 51.0342, 36.6819, 4.3404, 48.7842, 36.4319, 4.3404, 51.0342, 30.4319, 4.3404, 48.7842, 30.1819, 4.3404, 51.0342, 24.4319, 4.3404, 48.7842, 24.1819, 4.3404, 51.0342, -31.5681, 4.3404, 51.0342, -30.3181, 4.3404, 48.7842, 18.1819, 4.3404, 48.7842, 17.9319, 4.3404, 51.0342, -0.568123, 4.3404, 51.0342, 5.43188, 4.3404, 51.0342, 0.181877, 4.3404, 48.7842, 12.1819, 4.3404, 48.7842, 11.6819, 4.3404, 51.0342, -48.8181, 4.3404, 24.0342, -51.0681, 4.3404, 25.0342, -49.8181, 4.3404, 51.0342, -48.8181, 4.3404, -24.4658, -51.0681, 4.3404, -24.4658, -51.0681, 4.3404, -18.4658, -48.8181, 4.3404, -12.4658, -48.8181, 4.3404, 17.7842, -51.0681, 4.3404, 18.7842, -51.0681, 4.3404, -12.2158, -48.8181, 4.3404, -6.46578, -51.0681, 4.3404, -5.96578, -6.81812, 4.3404, 51.0342, -6.06812, 4.3404, 48.7842, -48.8181, 4.3404, -0.215782, -51.0681, 4.3404, 0.284218, -51.0681, 4.3404, 6.28422, -48.8181, 4.3404, 11.7842, -51.0681, 4.3404, 12.5342, -15.5681, 0.590398, -43.2158, -15.8181, 0.590398, -44.2158, -16.0681, 0.340398, -44.4658, -17.5681, 0.590398, -44.7158, -14.8181, 0.340398, -7.46578, -14.5681, 0.590398, -6.46578, -13.3181, 0.590398, -6.71578, -7.56812, 0.340398, -13.2158, -11.0681, 0.340398, -32.9658, -11.5681, 0.590398, -34.2158, -13.0681, 0.590398, -33.7158, -17.5681, 0.340398, -45.7158, -16.8181, 0.840398, -45.9658, -16.8181, 0.340398, -47.7158, -24.5681, 0.340398, -47.7158, -7.56812, 0.340398, -14.2158, -6.81812, 0.340398, -14.7158, -6.31812, 0.590398, -16.2158, -15.5681, 0.590398, -7.46578, -18.0681, 0.340398, -6.71578, -17.0681, 0.840398, -6.46578, -19.3181, 0.590398, -3.96578, -18.3181, 0.840398, -4.21578, -22.5681, 0.340398, -1.96578, -19.3181, 0.340398, -2.96578, -23.8181, 0.340398, -1.96578, -47.5681, 0.340398, -0.715782, -24.3181, 0.340398, -0.715782, -47.5681, 0.590398, -12.4658, -32.3181, 0.340398, -47.7158, -40.0681, 0.590398, -47.7158, -47.5681, 0.590398, -35.9658, -47.5681, 0.340398, -24.2158, -13.3181, 0.590398, -34.9658, -14.5681, 0.340398, -43.2158, -47.5681, 0.340398, -47.7158, -18.5779, 0.840398, -39.982, -17.0579, 0.340398, -38.4601, -18.5829, 0.340398, -41.465, -23.078, 0.340398, -42.9419, -10.0681, 0.340398, -33.2158, -27.5799, 0.840398, -18.9682, -29.0649, 0.340398, -18.9792, -26.0454, 0.340398, -21.9467, -29.083, 0.840398, -42.9749, -29.073, 0.340398, -41.4589, -26.0444, 0.340398, -15.9677, -27.548, 0.340398, -42.9639, -15.8181, 0.590398, -45.4658, -14.3181, 0.590398, -44.7158, -15.0681, 0.840398, -45.0908, -15.0681, 0.840398, -44.4658, -12.0681, 0.590398, -35.4658, -10.8181, 0.590398, -34.9658, 6.93188, 0.590398, -33.7158, 7.93188, 0.340398, -33.7158, 7.93188, 0.340398, -47.7158, -10.0681, 0.590398, -34.9658, -9.56812, 0.340398, -33.7158, -8.81812, 0.340398, -33.4658, -14.0681, 0.340398, -43.4658, -10.8181, 0.590398, -47.7158, 6.68188, 0.590398, -33.2158, 1.68188, 0.590398, -47.7158, -4.56812, 0.340398, -47.7158, -9.55271, 0.840398, -39.9672, -8.0677, 0.340398, -39.9562, -9.5876, 0.340398, -38.4683, -13.0681, 0.590398, -35.4658, 9.43188, 0.590398, -33.2158, 8.93188, 0.590398, -31.7158, 20.6819, 0.590398, -27.7158, 20.9319, 0.590398, -27.7158, 20.9319, 0.340398, -47.7158, 14.4319, 0.590398, -47.7158, 47.6819, 0.590398, -37.2158, 47.6819, 0.340398, -47.7158, 40.9319, 0.590398, -47.7158, 34.1819, 0.340398, -47.7158, 21.6819, 0.590398, -26.7158, 27.4319, 0.340398, -47.7158, 47.6819, 0.340398, -26.7158, 42.9236, 0.840398, -29.4636, 41.4385, 0.340398, -29.4746, 36.942, 0.840398, -44.4699, 35.4521, 0.340398, -42.954, 44.4484, 0.340398, -30.9784, 38.4221, 0.340398, -42.982, -52.3181, 0.340398, -46.7158, -53.3181, 0.340398, -45.9658, -53.5681, 0.340398, -44.4658, -53.5681, 0.340398, -41.2158, -52.3181, 0.340398, -40.7158, 53.9319, 4.3404, 51.0342, 53.9319, 4.3404, 48.7842, 51.6819, 4.3404, 48.7842, 48.9319, 4.3404, -10.9658, 48.9319, 4.3404, 1.03422, 51.1819, 4.3404, 0.784218, 51.1819, 4.3404, -11.2158, 51.1819, 4.3404, 48.2842, 48.9319, 4.3404, 37.0342, 51.1819, 4.3404, 36.2842, 51.1819, 4.3404, -22.9658, 51.1819, 4.3404, -34.9658, 48.9319, 4.3404, -34.9658, 48.9319, 4.3404, -22.9658, 48.9319, 4.3404, 25.0342, 51.1819, 4.3404, 24.5342, 48.9319, 4.3404, 13.0342, 51.1819, 4.3404, 12.5342, 55.1819, 0.340398, -43.9658, 55.6819, 0.340398, -44.2158, 55.6819, 0.340398, -46.2158, 53.1819, 0.340398, -46.2158, 52.4319, 0.340398, -46.7158, 52.4319, 0.590398, -39.9658, 54.6819, 0.340398, -40.2158, -53.8181, 0.340398, -34.7158, -54.3181, 0.340398, -34.7158, -54.0681, 0.340398, -33.4658, -52.3181, 0.340398, -36.7158, -55.3181, 0.340398, -32.7158, -55.3181, 0.340398, -27.7158, -52.3181, 0.590398, -26.7158, -4.56812, 0.340398, -17.7158, -3.06812, 0.590398, -17.7158, -2.81812, 0.590398, -16.9658, -2.31812, 0.590398, -17.4658, -1.31812, 0.340398, -17.2158, 6.43188, 0.340398, -31.7158, -0.562664, 0.840398, -23.4733, -0.552711, 0.340398, -24.9672, -2.08773, 0.340398, -24.9782, 0.932301, 0.340398, -24.9562, -2.06758, 0.340398, -18.9464, -0.818123, 0.590398, -16.7158, 0.431877, 0.340398, -17.4658, 6.93188, 0.340398, -31.2158, 19.6819, 0.590398, -26.7158, 8.18188, 0.340398, -30.9658, 20.6819, 0.590398, -25.2158, 20.6819, 0.840398, -25.9658, 19.9319, 0.840398, -25.9658, 20.1819, 0.590398, -24.9658, 20.1819, 0.340398, -24.2158, 19.1819, 0.340398, 1.53422, 19.6819, 0.340398, 2.03422, 20.9319, 0.590398, 2.03422, 20.9319, 0.840398, -23.9658, 20.9319, 0.340398, -18.188, 20.9319, 0.840398, -21.0769, 47.6819, 0.340398, -25.4658, 21.1819, 0.840398, -25.9658, 21.4319, 0.590398, 4.03422, 21.6819, 0.590398, 4.28422, 21.9319, 0.590398, 3.78422, 21.4319, 0.340398, 2.28422, 21.4319, 1.0904, -24.9658, 22.8902, 0.340398, -24.9936, 21.4319, 0.840398, -24.2158, 47.6819, 0.340398, -10.2158, 47.6819, 0.590398, -17.9658, 22.8902, 0.340398, -23.8686, 33.9135, 0.840398, -8.4795, 33.9533, 0.340398, -9.95529, 30.9336, 0.340398, -6.94765, 32.4186, 0.840398, -6.98667, 35.4485, 0.340398, -8.46851, 30.9538, 0.340398, -5.46581, 23.4319, 0.340398, 3.78422, 47.6819, 0.340398, -2.71578, 23.9319, 0.590398, 5.03422, 47.6819, 0.340398, 5.03422, -1.56812, 0.590398, -14.9658, -1.56812, 0.340398, -14.2158, -1.31812, 0.590398, -13.9658, 0.181877, 0.590398, -13.9658, -0.818123, 0.590398, -15.2158, 16.4319, 0.340398, 2.28422, 17.4319, 0.590398, 2.28422, 17.6819, 0.340398, 1.53422, 0.431877, 0.340398, -12.2158, -52.3181, 0.340398, -21.9658, -52.8181, 0.340398, -21.9658, -52.5681, 0.340398, -20.7158, -53.8181, 0.340398, -19.9658, -53.5681, 0.340398, -18.7158, -54.0681, 0.340398, -16.7158, -54.0681, 0.340398, -14.9658, -52.3181, 0.340398, -14.4658, -5.06812, 5.0904, -15.4658, -5.06812, 5.0904, -14.2158, -3.81812, 5.0904, -14.2158, -3.81812, 5.0904, -15.4658, -1.81812, 0.840398, -12.4658, -3.56812, 0.340398, -12.4658, -1.06812, 0.590398, -11.7158, -12.5681, 0.340398, -4.71578, -12.5681, 0.590398, 4.78422, 14.9319, 0.590398, 5.28422, 15.6819, 0.340398, 3.03422, 0.458191, 0.340398, 5.02106, 1.90556, 0.840398, 5.04737, 3.35293, 0.340398, 5.07369, 5.41803, 0.840398, 2.01398, 3.94765, 0.340398, -0.98761, 9.91827, 0.340398, 3.53372, 3.93302, 0.340398, 2.05299, 2.44321, 0.840398, 3.52879, -15.3181, 0.340398, 2.53422, -14.5681, 0.340398, 3.03422, -13.0681, 0.590398, 3.03422, -15.5681, 0.590398, -2.21578, 15.6819, 0.340398, 4.28422, -4.81812, 0.840398, -12.9658, -6.31812, 0.340398, -12.4658, -12.8181, 0.590398, -6.46578, 54.6819, 0.340398, -8.71578, 55.1819, 0.340398, -8.96578, 55.1819, 0.340398, -10.7158, 52.4319, 0.340398, -10.9658, 52.4319, 0.340398, -4.71578, 54.1819, 0.340398, -4.71578, -16.8181, 5.0904, -5.21578, -16.8181, 5.0904, -3.96578, -15.5681, 5.0904, -3.96578, -15.5681, 5.0904, -5.21578, -53.0681, 0.340398, -1.71578, -54.0681, 0.340398, -1.21578, -53.8181, 0.340398, 0.0342178, -54.8181, 0.340398, 0.784218, -55.0681, 0.340398, 2.03422, -55.0681, 0.340398, 5.53422, -52.3181, 0.340398, 6.28422, -52.3181, 0.340398, -3.21578, -18.0681, 0.340398, 3.28422, -16.3181, 0.590398, 3.28422, -18.0681, 0.340398, -2.21578, -22.0681, 0.590398, -1.46578, -22.0681, 0.340398, -0.215782, -22.8181, 0.340398, 0.284218, -18.3181, 0.340398, 3.78422, -33.8181, 0.340398, 18.2842, -32.0681, 0.590398, 18.0342, -30.8181, 0.340398, 16.2842, -47.5681, 0.340398, 19.2842, -34.3181, 0.590398, 19.2842, -47.5681, 0.590398, 9.28422, -24.3181, 0.340398, -0.215782, -30.2765, 0.840398, 14.9092, -29.7348, 0.340398, 13.5342, -19.3181, 0.590398, 6.03422, -18.0681, 0.590398, 5.28422, -17.8181, 0.590398, 11.5342, -17.8181, 0.590398, 7.78422, -19.3181, 0.340398, 7.53422, -19.3181, 0.590398, 12.0342, -24.0681, 0.340398, 0.284218, -19.3181, 0.340398, 13.0342, 55.4319, 0.340398, 3.53422, 56.1819, 0.340398, 2.28422, 55.9319, 0.340398, 0.784218, 53.4319, 0.340398, 0.784218, 52.4319, 0.340398, 0.0342178, 52.4319, 0.590398, 7.03422, 55.1819, 0.340398, 6.78422, 17.4319, 5.0904, 3.78422, 17.4319, 5.0904, 5.03422, 18.6819, 5.0904, 5.03422, 18.6819, 5.0904, 3.78422, 21.6819, 0.590398, 5.53422, 19.6819, 0.590398, 6.28422, 27.9319, 0.340398, 47.5342, 37.6819, 0.340398, 47.5342, 47.6819, 0.340398, 36.7842, 47.6819, 0.340398, 26.2842, 22.1819, 0.340398, 6.03422, 17.4319, 0.840398, 6.28422, 17.1819, 0.590398, 6.78422, 23.4319, 0.590398, 6.03422, 9.43188, 0.340398, 18.7842, 9.43188, 0.340398, 21.0342, 47.6819, 0.590398, 15.5342, 15.4319, 0.340398, 6.53422, 47.6819, 0.340398, 47.5342, 18.1819, 0.340398, 47.5342, 23.4069, 0.840398, 8.00922, 26.4319, 0.340398, 9.51415, 18.9521, 0.340398, 12.546, 8.43188, 0.590398, 21.2842, 8.43188, 0.590398, 47.5342, -16.8181, 5.0904, 4.78422, -16.8181, 5.0904, 6.03422, -15.5681, 5.0904, 6.03422, -15.5681, 5.0904, 4.78422, 9.18188, 0.340398, 18.2842, 14.9319, 0.340398, 6.28422, 7.43188, 0.590398, 18.0342, -14.3181, 0.340398, 7.03422, -17.0681, 0.590398, 11.7842, -15.3181, 0.590398, 7.78422, -17.0681, 0.590398, 13.2842, -17.8181, 0.340398, 13.7842, 6.93188, 0.590398, 18.5342, -31.5681, 0.340398, 18.5342, -20.8181, 0.590398, 16.5342, -18.5681, 0.340398, 13.7842, -25.5681, 0.590398, 26.7842, -25.0681, 0.340398, 28.7842, -18.3181, 0.340398, 47.5342, -9.31812, 0.340398, 47.5342, 6.68188, 0.590398, 20.7842, -0.568123, 0.340398, 47.5342, -5.07081, 0.840398, 33.5187, -6.55582, 0.340398, 33.5578, -2.08616, 0.340398, 30.5501, -5.07548, 0.340398, 36.5154, -27.0681, 0.590398, 29.5342, -27.0681, 0.340398, 47.5342, 52.9319, 0.590398, 17.0342, 52.9319, 0.340398, 16.2842, 52.4319, 0.340398, 16.2842, 56.4319, 0.340398, 23.0342, 56.9319, 0.340398, 22.5342, 56.9319, 0.340398, 20.5342, 54.4319, 0.340398, 20.7842, 53.1819, 0.340398, 19.7842, 53.4319, 0.340398, 17.2842, 55.9319, 0.340398, 33.5342, 56.6819, 0.340398, 32.2842, 56.4319, 0.340398, 30.7842, 53.4319, 0.340398, 30.5342, 52.6819, 0.340398, 28.7842, 52.9319, 0.590398, 27.5342, 52.4319, 0.340398, 26.7842, 52.4319, 0.340398, 37.5342, 52.6819, 0.340398, 29.7842, 53.6819, 0.340398, 26.7842, 52.9319, 0.590398, 37.0342, 55.6819, 0.340398, 36.7842, 55.9319, 0.340398, 26.7842, -31.0681, 0.590398, 20.2842, -28.0681, 0.590398, 26.7842, -27.3181, 0.590398, 26.2842, -25.8181, 0.590398, 26.2842, -39.0681, 0.590398, 31.2842, -38.8181, 0.590398, 31.7842, -38.3181, 0.840398, 31.5342, -38.3181, 0.590398, 30.5342, -37.5681, 0.590398, 30.2842, -34.0681, 0.590398, 20.5342, -47.5681, 0.340398, 25.7842, -39.8181, 0.840398, 31.2842, -47.5681, 0.340398, 32.2842, -40.5681, 0.590398, 32.2842, -28.5681, 0.590398, 27.0342, -32.8181, 0.340398, 21.2842, -37.0681, 0.340398, 30.7842, -27.8181, 0.340398, 29.2842, -28.5681, 0.590398, 27.7842, -33.5681, 0.590398, 21.2842, -52.8181, 0.340398, 22.0342, -52.8181, 0.340398, 25.2842, -52.3181, 0.340398, 25.2842, -52.3181, 0.340398, 21.5342, -39.3181, 0.590398, 32.7842, -37.0681, 0.590398, 32.0342, -40.3181, 0.590398, 32.7842, -47.5681, 0.340398, 39.7842, -37.3181, 0.340398, 47.5342, -47.5681, 0.340398, 47.5342, -52.3181, 0.340398, 35.7842, -52.8181, 0.340398, 35.7842, -53.0681, 0.340398, 37.2842, -53.0681, 0.340398, 40.5342, -52.3181, 0.340398, 40.7842)
polygons = [PackedInt32Array(2, 1, 0), PackedInt32Array(5, 4, 3), PackedInt32Array(2, 0, 6), PackedInt32Array(6, 0, 7), PackedInt32Array(7, 0, 8), PackedInt32Array(8, 0, 3), PackedInt32Array(3, 0, 5), PackedInt32Array(12, 11, 13), PackedInt32Array(13, 11, 10), PackedInt32Array(13, 10, 9), PackedInt32Array(9, 14, 13), PackedInt32Array(16, 15, 17), PackedInt32Array(17, 15, 19), PackedInt32Array(17, 19, 18), PackedInt32Array(22, 21, 20), PackedInt32Array(20, 24, 22), PackedInt32Array(22, 24, 23), PackedInt32Array(22, 23, 25), PackedInt32Array(28, 27, 26), PackedInt32Array(30, 29, 26), PackedInt32Array(26, 29, 28), PackedInt32Array(34, 33, 31), PackedInt32Array(31, 33, 32), PackedInt32Array(30, 26, 35), PackedInt32Array(30, 35, 36), PackedInt32Array(36, 35, 37), PackedInt32Array(41, 40, 38), PackedInt32Array(38, 40, 39), PackedInt32Array(43, 42, 37), PackedInt32Array(37, 42, 36), PackedInt32Array(34, 31, 44), PackedInt32Array(44, 31, 45), PackedInt32Array(47, 46, 43), PackedInt32Array(43, 46, 42), PackedInt32Array(44, 45, 41), PackedInt32Array(41, 45, 40), PackedInt32Array(38, 39, 47), PackedInt32Array(47, 39, 46), PackedInt32Array(49, 48, 33), PackedInt32Array(33, 48, 32), PackedInt32Array(49, 53, 48), PackedInt32Array(48, 53, 52), PackedInt32Array(48, 52, 51), PackedInt32Array(48, 51, 50), PackedInt32Array(51, 54, 50), PackedInt32Array(50, 57, 55), PackedInt32Array(55, 57, 56), PackedInt32Array(53, 59, 52), PackedInt32Array(52, 59, 58), PackedInt32Array(50, 55, 48), PackedInt32Array(51, 61, 54), PackedInt32Array(54, 61, 60), PackedInt32Array(64, 63, 62), PackedInt32Array(67, 66, 65), PackedInt32Array(68, 67, 65), PackedInt32Array(70, 69, 30), PackedInt32Array(30, 69, 29), PackedInt32Array(73, 72, 71), PackedInt32Array(76, 75, 77), PackedInt32Array(77, 75, 74), PackedInt32Array(77, 74, 78), PackedInt32Array(80, 79, 70), PackedInt32Array(70, 79, 69), PackedInt32Array(82, 81, 83), PackedInt32Array(83, 81, 84), PackedInt32Array(86, 79, 85), PackedInt32Array(85, 79, 80), PackedInt32Array(84, 81, 87), PackedInt32Array(91, 90, 88), PackedInt32Array(88, 90, 89), PackedInt32Array(95, 94, 92), PackedInt32Array(92, 94, 93), PackedInt32Array(91, 88, 96), PackedInt32Array(96, 88, 97), PackedInt32Array(76, 77, 93), PackedInt32Array(99, 98, 97), PackedInt32Array(97, 98, 96), PackedInt32Array(71, 78, 73), PackedInt32Array(73, 78, 74), PackedInt32Array(101, 100, 99), PackedInt32Array(99, 100, 98), PackedInt32Array(103, 102, 92), PackedInt32Array(92, 102, 95), PackedInt32Array(105, 104, 101), PackedInt32Array(101, 104, 100), PackedInt32Array(108, 107, 106), PackedInt32Array(104, 105, 109), PackedInt32Array(109, 105, 110), PackedInt32Array(112, 111, 72), PackedInt32Array(72, 111, 71), PackedInt32Array(76, 93, 113), PackedInt32Array(113, 93, 94), PackedInt32Array(115, 86, 114), PackedInt32Array(114, 86, 85), PackedInt32Array(102, 103, 87), PackedInt32Array(87, 103, 84), PackedInt32Array(115, 114, 116), PackedInt32Array(116, 114, 117), PackedInt32Array(112, 119, 111), PackedInt32Array(111, 119, 118), PackedInt32Array(120, 116, 117), PackedInt32Array(117, 121, 120), PackedInt32Array(120, 121, 122), PackedInt32Array(124, 123, 83), PackedInt32Array(83, 123, 82), PackedInt32Array(126, 122, 125), PackedInt32Array(125, 122, 121), PackedInt32Array(123, 124, 106), PackedInt32Array(106, 124, 108), PackedInt32Array(126, 125, 127), PackedInt32Array(127, 125, 128), PackedInt32Array(110, 107, 109), PackedInt32Array(109, 107, 108), PackedInt32Array(129, 127, 128), PackedInt32Array(128, 118, 129), PackedInt32Array(129, 118, 119), PackedInt32Array(131, 130, 132), PackedInt32Array(132, 130, 133), PackedInt32Array(135, 134, 136), PackedInt32Array(136, 134, 137), PackedInt32Array(140, 139, 138), PackedInt32Array(142, 141, 143), PackedInt32Array(143, 141, 144), PackedInt32Array(145, 147, 146), PackedInt32Array(145, 148, 147), PackedInt32Array(147, 148, 149), PackedInt32Array(147, 149, 138), PackedInt32Array(138, 149, 140), PackedInt32Array(149, 148, 150), PackedInt32Array(149, 152, 151), PackedInt32Array(151, 154, 153), PackedInt32Array(153, 155, 151), PackedInt32Array(151, 155, 149), PackedInt32Array(157, 156, 155), PackedInt32Array(155, 156, 158), PackedInt32Array(160, 159, 161), PackedInt32Array(161, 159, 162), PackedInt32Array(141, 133, 144), PackedInt32Array(130, 164, 163), PackedInt32Array(161, 165, 160), PackedInt32Array(144, 169, 159), PackedInt32Array(159, 169, 140), PackedInt32Array(140, 167, 163), PackedInt32Array(163, 167, 130), PackedInt32Array(130, 168, 133), PackedInt32Array(133, 169, 144), PackedInt32Array(169, 167, 140), PackedInt32Array(167, 168, 130), PackedInt32Array(168, 169, 133), PackedInt32Array(169, 166, 167), PackedInt32Array(167, 166, 168), PackedInt32Array(168, 166, 169), PackedInt32Array(134, 148, 137), PackedInt32Array(137, 148, 145), PackedInt32Array(138, 170, 147), PackedInt32Array(159, 175, 162), PackedInt32Array(162, 172, 158), PackedInt32Array(158, 176, 155), PackedInt32Array(155, 176, 149), PackedInt32Array(149, 173, 140), PackedInt32Array(140, 177, 159), PackedInt32Array(175, 173, 162), PackedInt32Array(159, 174, 175), PackedInt32Array(172, 176, 158), PackedInt32Array(162, 173, 172), PackedInt32Array(176, 173, 149), PackedInt32Array(173, 175, 140), PackedInt32Array(177, 174, 159), PackedInt32Array(140, 175, 177), PackedInt32Array(174, 177, 175), PackedInt32Array(172, 171, 176), PackedInt32Array(173, 171, 172), PackedInt32Array(176, 171, 173), PackedInt32Array(131, 132, 181), PackedInt32Array(181, 132, 178), PackedInt32Array(181, 178, 180), PackedInt32Array(181, 180, 179), PackedInt32Array(183, 139, 182), PackedInt32Array(186, 185, 184), PackedInt32Array(189, 188, 187), PackedInt32Array(183, 182, 187), PackedInt32Array(187, 182, 190), PackedInt32Array(178, 142, 143), PackedInt32Array(178, 143, 180), PackedInt32Array(180, 143, 179), PackedInt32Array(179, 143, 191), PackedInt32Array(193, 196, 194), PackedInt32Array(194, 195, 190), PackedInt32Array(190, 197, 187), PackedInt32Array(187, 197, 189), PackedInt32Array(189, 196, 192), PackedInt32Array(192, 196, 193), PackedInt32Array(196, 195, 194), PackedInt32Array(195, 197, 190), PackedInt32Array(197, 196, 189), PackedInt32Array(196, 197, 195), PackedInt32Array(179, 191, 190), PackedInt32Array(190, 191, 194), PackedInt32Array(198, 163, 164), PackedInt32Array(164, 190, 198), PackedInt32Array(198, 190, 182), PackedInt32Array(184, 192, 186), PackedInt32Array(186, 192, 193), PackedInt32Array(201, 200, 202), PackedInt32Array(202, 200, 199), PackedInt32Array(202, 199, 204), PackedInt32Array(202, 204, 203), PackedInt32Array(185, 186, 199), PackedInt32Array(199, 186, 204), PackedInt32Array(207, 206, 205), PackedInt32Array(208, 207, 205), PackedInt32Array(202, 203, 209), PackedInt32Array(209, 203, 210), PackedInt32Array(209, 213, 211), PackedInt32Array(211, 216, 205), PackedInt32Array(205, 214, 208), PackedInt32Array(208, 215, 210), PackedInt32Array(210, 215, 209), PackedInt32Array(213, 212, 211), PackedInt32Array(209, 215, 213), PackedInt32Array(216, 217, 205), PackedInt32Array(211, 212, 216), PackedInt32Array(214, 215, 208), PackedInt32Array(205, 217, 214), PackedInt32Array(213, 216, 212), PackedInt32Array(215, 217, 213), PackedInt32Array(216, 213, 217), PackedInt32Array(214, 217, 215), PackedInt32Array(219, 218, 220), PackedInt32Array(220, 218, 221), PackedInt32Array(221, 218, 222), PackedInt32Array(225, 224, 223), PackedInt32Array(90, 225, 89), PackedInt32Array(89, 225, 223), PackedInt32Array(229, 228, 226), PackedInt32Array(226, 228, 227), PackedInt32Array(90, 230, 225), PackedInt32Array(90, 231, 230), PackedInt32Array(230, 231, 232), PackedInt32Array(236, 235, 233), PackedInt32Array(233, 235, 234), PackedInt32Array(238, 232, 237), PackedInt32Array(237, 232, 231), PackedInt32Array(235, 56, 234), PackedInt32Array(234, 56, 57), PackedInt32Array(240, 238, 239), PackedInt32Array(239, 238, 237), PackedInt32Array(233, 229, 236), PackedInt32Array(236, 229, 226), PackedInt32Array(228, 240, 227), PackedInt32Array(227, 240, 239), PackedInt32Array(242, 241, 243), PackedInt32Array(243, 241, 244), PackedInt32Array(244, 241, 245), PackedInt32Array(245, 241, 247), PackedInt32Array(245, 247, 246), PackedInt32Array(250, 249, 248), PackedInt32Array(250, 248, 251), PackedInt32Array(253, 252, 250), PackedInt32Array(253, 250, 254), PackedInt32Array(254, 250, 251), PackedInt32Array(188, 189, 170), PackedInt32Array(170, 189, 255), PackedInt32Array(170, 255, 147), PackedInt32Array(258, 257, 256), PackedInt32Array(192, 260, 189), PackedInt32Array(189, 263, 256), PackedInt32Array(256, 265, 258), PackedInt32Array(258, 265, 259), PackedInt32Array(259, 264, 260), PackedInt32Array(260, 262, 189), PackedInt32Array(263, 265, 256), PackedInt32Array(189, 262, 263), PackedInt32Array(265, 261, 259), PackedInt32Array(264, 262, 260), PackedInt32Array(259, 261, 264), PackedInt32Array(263, 261, 265), PackedInt32Array(262, 261, 263), PackedInt32Array(264, 261, 262), PackedInt32Array(256, 255, 189), PackedInt32Array(266, 259, 267), PackedInt32Array(267, 259, 268), PackedInt32Array(268, 259, 260), PackedInt32Array(270, 200, 269), PackedInt32Array(269, 200, 201), PackedInt32Array(271, 274, 272), PackedInt32Array(272, 274, 273), PackedInt32Array(273, 275, 269), PackedInt32Array(269, 275, 270), PackedInt32Array(270, 275, 268), PackedInt32Array(268, 275, 267), PackedInt32Array(273, 274, 275), PackedInt32Array(277, 276, 278), PackedInt32Array(278, 276, 280), PackedInt32Array(280, 276, 281), PackedInt32Array(281, 276, 279), PackedInt32Array(279, 276, 275), PackedInt32Array(275, 276, 267), PackedInt32Array(211, 282, 209), PackedInt32Array(209, 282, 283), PackedInt32Array(285, 284, 286), PackedInt32Array(286, 284, 287), PackedInt32Array(272, 283, 271), PackedInt32Array(271, 283, 288), PackedInt32Array(288, 283, 289), PackedInt32Array(289, 283, 282), PackedInt32Array(291, 298, 292), PackedInt32Array(292, 295, 293), PackedInt32Array(293, 279, 290), PackedInt32Array(279, 293, 281), PackedInt32Array(281, 293, 280), PackedInt32Array(280, 296, 278), PackedInt32Array(278, 299, 287), PackedInt32Array(287, 299, 291), PackedInt32Array(298, 295, 292), PackedInt32Array(291, 299, 298), PackedInt32Array(295, 280, 293), PackedInt32Array(296, 299, 278), PackedInt32Array(280, 295, 296), PackedInt32Array(298, 294, 295), PackedInt32Array(299, 297, 298), PackedInt32Array(296, 297, 299), PackedInt32Array(295, 297, 296), PackedInt32Array(294, 297, 295), PackedInt32Array(298, 297, 294), PackedInt32Array(288, 289, 290), PackedInt32Array(290, 289, 293), PackedInt32Array(293, 289, 282), PackedInt32Array(293, 282, 292), PackedInt32Array(286, 287, 300), PackedInt32Array(300, 287, 301), PackedInt32Array(301, 287, 291), PackedInt32Array(302, 300, 303), PackedInt32Array(303, 300, 301), PackedInt32Array(305, 304, 306), PackedInt32Array(306, 304, 308), PackedInt32Array(306, 308, 307), PackedInt32Array(266, 267, 308), PackedInt32Array(308, 267, 307), PackedInt32Array(310, 309, 311), PackedInt32Array(311, 309, 312), PackedInt32Array(307, 267, 312), PackedInt32Array(312, 267, 311), PackedInt32Array(311, 267, 276), PackedInt32Array(315, 314, 313), PackedInt32Array(317, 316, 315), PackedInt32Array(319, 318, 320), PackedInt32Array(320, 318, 317), PackedInt32Array(320, 317, 315), PackedInt32Array(315, 313, 320), PackedInt32Array(324, 323, 321), PackedInt32Array(321, 323, 322), PackedInt32Array(306, 325, 305), PackedInt32Array(305, 325, 326), PackedInt32Array(330, 337, 331), PackedInt32Array(331, 336, 312), PackedInt32Array(312, 336, 327), PackedInt32Array(327, 336, 328), PackedInt32Array(328, 332, 329), PackedInt32Array(332, 339, 333), PackedInt32Array(333, 339, 334), PackedInt32Array(334, 337, 330), PackedInt32Array(337, 336, 331), PackedInt32Array(336, 332, 328), PackedInt32Array(332, 336, 339), PackedInt32Array(339, 338, 334), PackedInt32Array(334, 335, 337), PackedInt32Array(337, 335, 336), PackedInt32Array(336, 338, 339), PackedInt32Array(338, 335, 334), PackedInt32Array(335, 338, 336), PackedInt32Array(341, 340, 342), PackedInt32Array(342, 340, 343), PackedInt32Array(342, 343, 328), PackedInt32Array(331, 344, 330), PackedInt32Array(327, 326, 325), PackedInt32Array(346, 345, 326), PackedInt32Array(326, 327, 346), PackedInt32Array(346, 327, 347), PackedInt32Array(347, 327, 328), PackedInt32Array(329, 342, 328), PackedInt32Array(331, 312, 309), PackedInt32Array(136, 137, 347), PackedInt32Array(347, 137, 346), PackedInt32Array(349, 348, 350), PackedInt32Array(350, 348, 351), PackedInt32Array(353, 352, 348), PackedInt32Array(348, 352, 351), PackedInt32Array(357, 356, 354), PackedInt32Array(354, 356, 355), PackedInt32Array(360, 359, 358), PackedInt32Array(361, 360, 362), PackedInt32Array(362, 360, 363), PackedInt32Array(363, 360, 364), PackedInt32Array(358, 365, 360), PackedInt32Array(360, 365, 364), PackedInt32Array(367, 366, 340), PackedInt32Array(340, 366, 343), PackedInt32Array(343, 366, 368), PackedInt32Array(369, 153, 154), PackedInt32Array(369, 154, 370), PackedInt32Array(370, 154, 368), PackedInt32Array(370, 368, 371), PackedInt32Array(371, 368, 366), PackedInt32Array(371, 366, 372), PackedInt32Array(375, 374, 373), PackedInt32Array(377, 376, 373), PackedInt32Array(373, 376, 378), PackedInt32Array(375, 373, 380), PackedInt32Array(380, 373, 381), PackedInt32Array(381, 373, 378), PackedInt32Array(381, 378, 156), PackedInt32Array(381, 156, 379), PackedInt32Array(379, 156, 157), PackedInt32Array(383, 382, 372), PackedInt32Array(372, 382, 371), PackedInt32Array(386, 385, 384), PackedInt32Array(384, 387, 386), PackedInt32Array(386, 387, 388), PackedInt32Array(375, 380, 389), PackedInt32Array(389, 380, 381), PackedInt32Array(389, 381, 387), PackedInt32Array(387, 381, 388), PackedInt32Array(388, 381, 379), PackedInt32Array(371, 382, 388), PackedInt32Array(388, 382, 386), PackedInt32Array(391, 390, 392), PackedInt32Array(392, 390, 393), PackedInt32Array(393, 390, 394), PackedInt32Array(394, 390, 396), PackedInt32Array(394, 396, 395), PackedInt32Array(400, 399, 397), PackedInt32Array(397, 399, 398), PackedInt32Array(285, 401, 284), PackedInt32Array(284, 401, 402), PackedInt32Array(404, 403, 405), PackedInt32Array(405, 403, 406), PackedInt32Array(402, 401, 407), PackedInt32Array(409, 408, 402), PackedInt32Array(407, 410, 402), PackedInt32Array(402, 410, 409), PackedInt32Array(409, 410, 411), PackedInt32Array(411, 410, 412), PackedInt32Array(302, 303, 410), PackedInt32Array(410, 303, 413), PackedInt32Array(409, 411, 414), PackedInt32Array(405, 415, 404), PackedInt32Array(416, 412, 403), PackedInt32Array(403, 419, 406), PackedInt32Array(406, 418, 413), PackedInt32Array(413, 418, 410), PackedInt32Array(410, 419, 412), PackedInt32Array(412, 419, 403), PackedInt32Array(419, 418, 406), PackedInt32Array(418, 417, 410), PackedInt32Array(410, 417, 419), PackedInt32Array(419, 417, 418), PackedInt32Array(420, 412, 421), PackedInt32Array(421, 412, 416), PackedInt32Array(425, 424, 422), PackedInt32Array(422, 424, 423), PackedInt32Array(427, 414, 426), PackedInt32Array(426, 414, 411), PackedInt32Array(330, 427, 334), PackedInt32Array(334, 427, 333), PackedInt32Array(333, 427, 332), PackedInt32Array(332, 427, 426), PackedInt32Array(332, 426, 428), PackedInt32Array(332, 428, 329), PackedInt32Array(329, 428, 429), PackedInt32Array(384, 385, 430), PackedInt32Array(430, 385, 431), PackedInt32Array(434, 433, 432), PackedInt32Array(431, 429, 430), PackedInt32Array(430, 429, 432), PackedInt32Array(432, 429, 434), PackedInt32Array(434, 429, 428), PackedInt32Array(374, 375, 435), PackedInt32Array(435, 375, 436), PackedInt32Array(436, 375, 389), PackedInt32Array(436, 389, 437), PackedInt32Array(437, 433, 436), PackedInt32Array(436, 433, 438), PackedInt32Array(438, 433, 439), PackedInt32Array(441, 440, 439), PackedInt32Array(441, 447, 443), PackedInt32Array(443, 446, 442), PackedInt32Array(442, 433, 434), PackedInt32Array(433, 445, 439), PackedInt32Array(439, 445, 441), PackedInt32Array(447, 446, 443), PackedInt32Array(441, 445, 447), PackedInt32Array(446, 433, 442), PackedInt32Array(433, 446, 445), PackedInt32Array(447, 444, 446), PackedInt32Array(445, 444, 447), PackedInt32Array(446, 444, 445), PackedInt32Array(448, 439, 449), PackedInt32Array(449, 439, 440), PackedInt32Array(420, 421, 442), PackedInt32Array(442, 421, 443), PackedInt32Array(452, 451, 450), PackedInt32Array(454, 453, 455), PackedInt32Array(455, 453, 456), PackedInt32Array(450, 458, 457), PackedInt32Array(460, 459, 461), PackedInt32Array(461, 459, 462), PackedInt32Array(465, 464, 463), PackedInt32Array(450, 457, 452), PackedInt32Array(452, 457, 465), PackedInt32Array(463, 467, 465), PackedInt32Array(465, 467, 466), PackedInt32Array(464, 465, 468), PackedInt32Array(468, 465, 453), PackedInt32Array(453, 465, 456), PackedInt32Array(456, 465, 457), PackedInt32Array(459, 470, 469), PackedInt32Array(453, 471, 468), PackedInt32Array(462, 459, 467), PackedInt32Array(467, 459, 469), PackedInt32Array(467, 469, 466), PackedInt32Array(474, 473, 472), PackedInt32Array(474, 472, 475), PackedInt32Array(475, 472, 435), PackedInt32Array(475, 435, 436), PackedInt32Array(436, 438, 475), PackedInt32Array(477, 476, 478), PackedInt32Array(478, 476, 479), PackedInt32Array(481, 480, 377), PackedInt32Array(377, 480, 482), PackedInt32Array(377, 482, 376), PackedInt32Array(479, 476, 483), PackedInt32Array(483, 485, 484), PackedInt32Array(479, 483, 484), PackedInt32Array(479, 484, 480), PackedInt32Array(480, 484, 482), PackedInt32Array(473, 486, 472), PackedInt32Array(472, 486, 487), PackedInt32Array(490, 489, 491), PackedInt32Array(491, 489, 488), PackedInt32Array(491, 488, 480), PackedInt32Array(487, 486, 491), PackedInt32Array(491, 486, 490), PackedInt32Array(491, 480, 481), PackedInt32Array(493, 492, 494), PackedInt32Array(494, 492, 495), PackedInt32Array(477, 478, 496), PackedInt32Array(496, 478, 497), PackedInt32Array(499, 498, 496), PackedInt32Array(485, 498, 484), PackedInt32Array(484, 498, 499), PackedInt32Array(489, 448, 488), PackedInt32Array(488, 448, 497), PackedInt32Array(497, 448, 496), PackedInt32Array(496, 448, 499), PackedInt32Array(499, 448, 500), PackedInt32Array(500, 448, 449), PackedInt32Array(500, 501, 499), PackedInt32Array(503, 502, 504), PackedInt32Array(504, 502, 505), PackedInt32Array(505, 502, 506)]
agent_height = 1.75
agent_radius = 0.375
agent_max_climb = 0.5
edge_max_length = 12.0
filter_low_hanging_obstacles = true
filter_ledge_spans = true
filter_walkable_low_height_spans = true

[sub_resource type="StandardMaterial3D" id="StandardMaterial3D_xcdtp"]
albedo_texture = ExtResource("15_df8b0")
metallic = 0.2
metallic_texture = ExtResource("16_003du")
roughness = 0.8
uv1_scale = Vector3(10, 10, 1)

[node name="level01" type="Node3D"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 0, 0, 1.94383)
script = ExtResource("1_hk2b6")
minimap_scene = ExtResource("2_j32d5")
player_avatar_scene = ExtResource("3_0f42i")
item_boxes_scene = ExtResource("4_swvig")
skill_box_ui_scene = ExtResource("5_dbohx")

[node name="WorldEnvironment" type="WorldEnvironment" parent="."]
environment = SubResource("Environment_j4i7h")

[node name="DirectionalLight3D" type="DirectionalLight3D" parent="."]
transform = Transform3D(0.999999, 0, 0, 0, 0.5, 0.866026, 0, -0.866025, 0.5, 0, 0, 0)
light_color = Color(1, 0.868175, 0.323518, 1)
light_energy = 1.5
shadow_enabled = true
shadow_opacity = 0.7
shadow_blur = 2.0

[node name="ModularLevelGenerator" type="Node3D" parent="."]
script = ExtResource("6_6ly8b")
level_config = ExtResource("7_r5102")

[node name="FogOfWar" parent="." instance=ExtResource("9_003du")]
player_vision_radius = 8.0
enabled = false

[node name="CameraPivot" type="Node3D" parent="."]
unique_name_in_owner = true
script = ExtResource("7_camera")

[node name="SpringArm3D" type="SpringArm3D" parent="CameraPivot"]
unique_name_in_owner = true
shape = SubResource("SphereShape3D_camera")

[node name="Camera3D" type="Camera3D" parent="CameraPivot/SpringArm3D"]
unique_name_in_owner = true
transform = Transform3D(-4.37114e-08, 0, -1, 0, 1, 0, 1, 0, -4.37114e-08, -42.2537, 29.6518, 0)
v_offset = -10.0
current = true
fov = 85.6
far = 90.3

[node name="Player" parent="." instance=ExtResource("11_82jtv")]

[node name="Chest" parent="." instance=ExtResource("14_1ks1q")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 4.57862, 0, 6.58062)
interaction_distance = 3.0
item_resource = ExtResource("17_j32d5")

[node name="Chest2" parent="." instance=ExtResource("14_1ks1q")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 9.81867, 0, 6.58062)
interaction_distance = 3.0
item_resource = ExtResource("18_j32d5")

[node name="EnemyBoy01" parent="." instance=ExtResource("19_0f42i")]
transform = Transform3D(0.8, 0, 0, 0, 0.8, 0, 0, 0, 0.8, -10.0014, 0, -8.65824)

[node name="Enemy02" parent="." instance=ExtResource("21_dbohx")]
transform = Transform3D(2.1, 0, 0, 0, 2.1, 0, 0, 0, 2.1, 7.86917, 0, -7.768)

[node name="NavigationRegion3D" type="NavigationRegion3D" parent="."]
navigation_mesh = SubResource("NavigationMesh_1ks1q")

[node name="Floor" type="Node3D" parent="NavigationRegion3D"]

[node name="@CSGBox3D@79353" type="CSGBox3D" parent="NavigationRegion3D/Floor"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 0, -0.05, 0)
use_collision = true
size = Vector3(100, 0.1, 100)
material = SubResource("StandardMaterial3D_xcdtp")

[node name="FloorTiles" type="Node3D" parent="NavigationRegion3D/Floor"]

[node name="Floor01_0" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -28.8723, 0.1, 37.4977)

[node name="Floor01_1" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 8.24348, 0.1, 28.7727)

[node name="Floor01_2" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -19.772, 0.1, 13.3454)

[node name="Floor01_3" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 0.568942, 0.1, -4.23568)

[node name="Floor01_4" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 29.9388, 0.1, 12.1889)

[node name="Floor01_5" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 22.8887, 0.1, -31.7267)

[node name="Floor01_6" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -41.4543, 0.1, 11.7415)

[node name="Floor01_7" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 43.7287, 0.1, 37.2549)

[node name="Floor01_8" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -1.31674, 0.1, 40.8384)

[node name="Floor01_9" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 4.89719, 0.1, 11.4519)

[node name="Floor01_10" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 37.3503, 0.1, 44.8098)

[node name="Floor01_11" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 19.1704, 0.1, -43.7589)

[node name="Floor01_12" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -12.182, 0.1, -18.8551)

[node name="Floor01_13" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -44.3942, 0.1, -30.9969)

[node name="Floor01_14" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 42.3646, 0.1, -4.33155)

[node name="Floor01_15" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -36.6816, 0.1, 43.4262)

[node name="Floor01_16" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -13.6239, 0.1, 37.3417)

[node name="Floor01_17" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 24.4037, 0.1, 23.6824)

[node name="Floor01_18" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -34.2914, 0.1, -43.8434)

[node name="Floor01_19" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 37.993, 0.1, 21.9797)

[node name="Floor01_20" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -11.7711, 0.1, 13.3509)

[node name="Floor01_21" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 10.9168, 0.1, -3.55078)

[node name="Floor01_22" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -41.7364, 0.1, 22.078)

[node name="Floor01_23" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -2.95727, 0.1, 22.5011)

[node name="Floor01_24" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 29.1857, 0.1, -0.333988)

[node name="Floor01_25" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 25.8997, 0.1, -16.8851)

[node name="Floor01_26" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -27.4725, 0.1, -29.7911)

[node name="Floor01_27" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 9.53418, 0.1, -13.0562)

[node name="Floor01_28" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -42.8986, 0.1, -18.2565)

[node name="Floor01_29" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -36.3254, 0.1, -1.95393)

[node name="Floor01_30" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 42.9241, 0.1, -39.8057)

[node name="Floor01_31" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 28.8251, 0.1, 40.5788)

[node name="Floor01_32" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -33.941, 0.1, -18.1622)

[node name="Floor01_33" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -21.284, 0.1, -14.3062)

[node name="Floor01_34" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -27.9065, 0.1, 1.35118)

[node name="Floor01_35" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -11.8555, 0.1, 28.6139)

[node name="Floor01_36" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -9.2156, 0.1, -0.885637)

[node name="Floor01_37" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 22.2635, 0.1, 34.8633)

[node name="Floor01_38" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -29.7515, 0.1, -11.079)

[node name="Floor01_39" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 32.3121, 0.1, 31.2415)

[node name="Floor01_40" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 43.23, 0.1, 7.18344)

[node name="Floor01_41" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -42.576, 0.1, -44.3877)

[node name="Floor01_42" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -34.6915, 0.1, 26.4163)

[node name="Floor01_43" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -25.1945, 0.1, 19.3495)

[node name="Floor01_44" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 15.6874, 0.1, 22.0596)

[node name="Floor01_45" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 17.283, 0.1, -9.73387)

[node name="Floor01_46" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 33.6679, 0.1, -38.8513)

[node name="Floor01_47" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -11.4006, 0.1, -26.876)

[node name="Floor01_48" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 4.6136, 0.1, -22.0107)

[node name="Floor01_49" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 1.42227, 0.1, -34.8424)

[node name="Floor01_50" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 39.3509, 0.1, -20.2807)

[node name="Floor01_51" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 13.5622, 0.1, -20.6076)

[node name="Floor01_52" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -33.9315, 0.1, 7.05932)

[node name="Floor01_53" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -19.7669, 0.1, 30.849)

[node name="Floor01_54" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -18.6374, 0.1, -33.3644)

[node name="Floor01_55" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -42.0296, 0.1, 37.3812)

[node name="Floor01_56" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 16.238, 0.1, 43.077)

[node name="Floor01_57" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 38.9822, 0.1, -30.6672)

[node name="Floor01_58" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -7.55976, 0.1, -44.0581)

[node name="Floor01_59" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -20.4, 0.1, 43.8035)

[node name="Floor01_60" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 13.574, 0.1, 7.10505)

[node name="Floor01_61" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -34.9049, 0.1, -26.5364)

[node name="Floor01_62" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -38.7457, 0.1, -10.0467)

[node name="Floor01_63" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 44.1874, 0.1, 27.1025)

[node name="Floor01_64" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -23.7296, 0.1, -41.1166)

[node name="Floor01_65" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -44.1181, 0.1, 2.26987)

[node name="Floor01_66" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 12.0294, 0.1, -29.6986)

[node name="Floor01_67" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 38.9327, 0.1, -12.2269)

[node name="Floor01_68" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 30.6742, 0.1, -23.6489)

[node name="Floor01_69" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -8.3166, 0.1, -8.9315)

[node name="wall_0" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -50, 0, -50)

[node name="wall_1" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -50, 0, 50)

[node name="wall_2" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -48, 0, -50)

[node name="wall_3" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -48, 0, 50)

[node name="wall_4" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -46, 0, -50)

[node name="wall_5" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -46, 0, 50)

[node name="wall_6" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -44, 0, -50)

[node name="wall_7" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -44, 0, 50)

[node name="wall_8" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -42, 0, -50)

[node name="wall_9" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -42, 0, 50)

[node name="wall_10" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -40, 0, -50)

[node name="wall_11" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -40, 0, 50)

[node name="wall_12" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -38, 0, -50)

[node name="wall_13" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -38, 0, 50)

[node name="wall_14" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -36, 0, -50)

[node name="wall_15" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -36, 0, 50)

[node name="wall_16" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -34, 0, -50)

[node name="wall_17" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -34, 0, 50)

[node name="wall_18" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -32, 0, -50)

[node name="wall_19" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -32, 0, 50)

[node name="wall_20" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -30, 0, -50)

[node name="wall_21" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -30, 0, 50)

[node name="wall_22" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -28, 0, -50)

[node name="wall_23" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -28, 0, 50)

[node name="wall_24" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -26, 0, -50)

[node name="wall_25" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -26, 0, 50)

[node name="wall_26" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -24, 0, -50)

[node name="wall_27" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -24, 0, 50)

[node name="wall_28" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -22, 0, -50)

[node name="wall_29" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -22, 0, 50)

[node name="wall_30" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -20, 0, -50)

[node name="wall_31" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -20, 0, 50)

[node name="wall_32" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -18, 0, -50)

[node name="wall_33" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -18, 0, 50)

[node name="wall_34" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -16, 0, -50)

[node name="wall_35" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -16, 0, 50)

[node name="wall_36" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -14, 0, -50)

[node name="wall_37" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -14, 0, 50)

[node name="wall_38" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -12, 0, -50)

[node name="wall_39" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -12, 0, 50)

[node name="wall_40" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -10, 0, -50)

[node name="wall_41" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -10, 0, 50)

[node name="wall_42" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -8, 0, -50)

[node name="wall_43" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -8, 0, 50)

[node name="wall_44" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -6, 0, -50)

[node name="wall_45" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -6, 0, 50)

[node name="wall_46" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -4, 0, -50)

[node name="wall_47" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -4, 0, 50)

[node name="wall_48" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -2, 0, -50)

[node name="wall_49" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -2, 0, 50)

[node name="wall_50" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 0, 0, -50)

[node name="wall_51" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 0, 0, 50)

[node name="wall_52" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 2, 0, -50)

[node name="wall_53" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 2, 0, 50)

[node name="wall_54" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 4, 0, -50)

[node name="wall_55" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 4, 0, 50)

[node name="wall_56" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 6, 0, -50)

[node name="wall_57" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 6, 0, 50)

[node name="wall_58" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 8, 0, -50)

[node name="wall_59" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 8, 0, 50)

[node name="wall_60" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 10, 0, -50)

[node name="wall_61" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 10, 0, 50)

[node name="wall_62" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 12, 0, -50)

[node name="wall_63" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 12, 0, 50)

[node name="wall_64" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 14, 0, -50)

[node name="wall_65" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 14, 0, 50)

[node name="wall_66" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 16, 0, -50)

[node name="wall_67" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 16, 0, 50)

[node name="wall_68" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 18, 0, -50)

[node name="wall_69" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 18, 0, 50)

[node name="wall_70" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 20, 0, -50)

[node name="wall_71" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 20, 0, 50)

[node name="wall_72" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 22, 0, -50)

[node name="wall_73" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 22, 0, 50)

[node name="wall_74" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 24, 0, -50)

[node name="wall_75" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 24, 0, 50)

[node name="wall_76" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 26, 0, -50)

[node name="wall_77" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 26, 0, 50)

[node name="wall_78" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 28, 0, -50)

[node name="wall_79" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 28, 0, 50)

[node name="wall_80" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 30, 0, -50)

[node name="wall_81" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 30, 0, 50)

[node name="wall_82" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 32, 0, -50)

[node name="wall_83" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 32, 0, 50)

[node name="wall_84" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 34, 0, -50)

[node name="wall_85" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 34, 0, 50)

[node name="wall_86" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 36, 0, -50)

[node name="wall_87" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 36, 0, 50)

[node name="wall_88" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 38, 0, -50)

[node name="wall_89" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 38, 0, 50)

[node name="wall_90" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 40, 0, -50)

[node name="wall_91" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 40, 0, 50)

[node name="wall_92" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 42, 0, -50)

[node name="wall_93" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 42, 0, 50)

[node name="wall_94" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 44, 0, -50)

[node name="wall_95" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 44, 0, 50)

[node name="wall_96" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 46, 0, -50)

[node name="wall_97" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 46, 0, 50)

[node name="wall_98" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 48, 0, -50)

[node name="wall_99" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 48, 0, 50)

[node name="wall_100" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 50, 0, -50)

[node name="wall_101" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 50, 0, 50)

[node name="wall_102" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, -50, 0, -50)

[node name="wall_103" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, 50, 0, -50)

[node name="wall_104" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, -50, 0, -48)

[node name="wall_105" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, 50, 0, -48)

[node name="wall_106" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, -50, 0, -46)

[node name="wall_107" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, 50, 0, -46)

[node name="wall_108" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, -50, 0, -44)

[node name="wall_109" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, 50, 0, -44)

[node name="wall_110" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, -50, 0, -42)

[node name="wall_111" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, 50, 0, -42)

[node name="wall_112" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, -50, 0, -40)

[node name="wall_113" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, 50, 0, -40)

[node name="wall_114" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, -50, 0, -38)

[node name="wall_115" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, 50, 0, -38)

[node name="wall_116" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, -50, 0, -36)

[node name="wall_117" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, 50, 0, -36)

[node name="wall_118" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, -50, 0, -34)

[node name="wall_119" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, 50, 0, -34)

[node name="wall_120" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, -50, 0, -32)

[node name="wall_121" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, 50, 0, -32)

[node name="wall_122" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, -50, 0, -30)

[node name="wall_123" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, 50, 0, -30)

[node name="wall_124" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, -50, 0, -28)

[node name="wall_125" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, 50, 0, -28)

[node name="wall_126" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, -50, 0, -26)

[node name="wall_127" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, 50, 0, -26)

[node name="wall_128" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, -50, 0, -24)

[node name="wall_129" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, 50, 0, -24)

[node name="wall_130" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, -50, 0, -22)

[node name="wall_131" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, 50, 0, -22)

[node name="wall_132" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, -50, 0, -20)

[node name="wall_133" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, 50, 0, -20)

[node name="wall_134" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, -50, 0, -18)

[node name="wall_135" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, 50, 0, -18)

[node name="wall_136" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, -50, 0, -16)

[node name="wall_137" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, 50, 0, -16)

[node name="wall_138" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, -50, 0, -14)

[node name="wall_139" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, 50, 0, -14)

[node name="wall_140" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, -50, 0, -12)

[node name="wall_141" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, 50, 0, -12)

[node name="wall_142" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, -50, 0, -10)

[node name="wall_143" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, 50, 0, -10)

[node name="wall_144" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, -50, 0, -8)

[node name="wall_145" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, 50, 0, -8)

[node name="wall_146" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, -50, 0, -6)

[node name="wall_147" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, 50, 0, -6)

[node name="wall_148" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, -50, 0, -4)

[node name="wall_149" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, 50, 0, -4)

[node name="wall_150" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, -50, 0, -2)

[node name="wall_151" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, 50, 0, -2)

[node name="wall_152" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, -50, 0, 0)

[node name="wall_153" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, 50, 0, 0)

[node name="wall_154" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, -50, 0, 2)

[node name="wall_155" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, 50, 0, 2)

[node name="wall_156" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, -50, 0, 4)

[node name="wall_157" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, 50, 0, 4)

[node name="wall_158" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, -50, 0, 6)

[node name="wall_159" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, 50, 0, 6)

[node name="wall_160" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, -50, 0, 8)

[node name="wall_161" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, 50, 0, 8)

[node name="wall_162" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, -50, 0, 10)

[node name="wall_163" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, 50, 0, 10)

[node name="wall_164" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, -50, 0, 12)

[node name="wall_165" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, 50, 0, 12)

[node name="wall_166" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, -50, 0, 14)

[node name="wall_167" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, 50, 0, 14)

[node name="wall_168" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, -50, 0, 16)

[node name="wall_169" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, 50, 0, 16)

[node name="wall_170" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, -50, 0, 18)

[node name="wall_171" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, 50, 0, 18)

[node name="wall_172" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, -50, 0, 20)

[node name="wall_173" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, 50, 0, 20)

[node name="wall_174" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, -50, 0, 22)

[node name="wall_175" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, 50, 0, 22)

[node name="wall_176" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, -50, 0, 24)

[node name="wall_177" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, 50, 0, 24)

[node name="wall_178" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, -50, 0, 26)

[node name="wall_179" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, 50, 0, 26)

[node name="wall_180" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, -50, 0, 28)

[node name="wall_181" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, 50, 0, 28)

[node name="wall_182" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, -50, 0, 30)

[node name="wall_183" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, 50, 0, 30)

[node name="wall_184" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, -50, 0, 32)

[node name="wall_185" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, 50, 0, 32)

[node name="wall_186" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, -50, 0, 34)

[node name="wall_187" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, 50, 0, 34)

[node name="wall_188" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, -50, 0, 36)

[node name="wall_189" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, 50, 0, 36)

[node name="wall_190" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, -50, 0, 38)

[node name="wall_191" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, 50, 0, 38)

[node name="wall_192" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, -50, 0, 40)

[node name="wall_193" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, 50, 0, 40)

[node name="wall_194" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, -50, 0, 42)

[node name="wall_195" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, 50, 0, 42)

[node name="wall_196" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, -50, 0, 44)

[node name="wall_197" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, 50, 0, 44)

[node name="wall_198" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, -50, 0, 46)

[node name="wall_199" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, 50, 0, 46)

[node name="wall_200" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, -50, 0, 48)

[node name="wall_201" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, 50, 0, 48)

[node name="wall_202" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, -50, 0, 50)

[node name="wall_203" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, 50, 0, 50)

[node name="tree_0" parent="NavigationRegion3D" instance=ExtResource("15_003du")]
transform = Transform3D(2.5, -0.00145593, -0.00167976, 0.00145725, 2.5, 0.00196474, 0.00167861, -0.00196571, 2.5, -16.1411, 0, 5.38274)

[node name="PatrolPoint_0" type="Node3D" parent="NavigationRegion3D"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -18.156, 1, 12.586)
script = ExtResource("15_6ly8b")

[node name="tree_1" parent="NavigationRegion3D" instance=ExtResource("15_003du")]
transform = Transform3D(2.5, -0.00145593, -0.00167976, 0.00145725, 2.5, 0.00196474, 0.00167861, -0.00196571, 2.5, -16.1191, 0, -4.56893)

[node name="PatrolPoint_1" type="Node3D" parent="NavigationRegion3D"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -23.2746, 1, -0.780309)
script = ExtResource("15_6ly8b")
point_id = 1

[node name="tree_2" parent="NavigationRegion3D" instance=ExtResource("15_003du")]
transform = Transform3D(2.5, -0.00145593, -0.00167976, 0.00145725, 2.5, 0.00196474, 0.00167861, -0.00196571, 2.5, -4.42803, 0, -14.9565)

[node name="PatrolPoint_2" type="Node3D" parent="NavigationRegion3D"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -0.588588, 1, -12.9177)
script = ExtResource("15_6ly8b")
point_id = 2

[node name="tree_3" parent="NavigationRegion3D" instance=ExtResource("15_003du")]
transform = Transform3D(2.5, -0.00145593, -0.00167976, 0.00145725, 2.5, 0.00196474, 0.00167861, -0.00196571, 2.5, 18.0477, 0, 4.29445)
preset_burnt_out_type = 1

[node name="PatrolPoint_3" type="Node3D" parent="NavigationRegion3D"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 22.7667, 1, 4.81789)
script = ExtResource("15_6ly8b")
point_id = 3

[node name="chest_0" parent="NavigationRegion3D" instance=ExtResource("14_1ks1q")]
transform = Transform3D(-0.109273, 0, -0.994012, 0, 1, 0, 0.994012, 0, -0.109273, 8.09608, 0, 19.6668)

[node name="rock_0" parent="NavigationRegion3D" instance=ExtResource("21_xcdtp")]
transform = Transform3D(0.717528, 0, 0.621004, 0, 0.948942, 0, -0.621004, 0, 0.717528, -23.5177, 0.01, 38.985)

[node name="rock_1" parent="NavigationRegion3D" instance=ExtResource("21_xcdtp")]
transform = Transform3D(-0.286026, 0, 0.965881, 0, 1.00734, 0, -0.965881, 0, -0.286026, 37.2111, 0.01, -43.8872)

[node name="rock_2" parent="NavigationRegion3D" instance=ExtResource("21_xcdtp")]
transform = Transform3D(-0.313141, 0, -0.951972, 0, 1.00215, 0, 0.951972, 0, -0.313141, -0.797423, 0.01, -23.057)

[node name="rock_3" parent="NavigationRegion3D" instance=ExtResource("21_xcdtp")]
transform = Transform3D(0.896556, 0, 0.469603, 0, 1.0121, 0, -0.469603, 0, 0.896556, -30.3317, 0.01, 15.3044)

[node name="rock_4" parent="NavigationRegion3D" instance=ExtResource("21_xcdtp")]
transform = Transform3D(-0.309144, 0, -0.985293, 0, 1.03265, 0, 0.985293, 0, -0.309144, 24.0964, 0.01, 8.13151)

[node name="rock_5" parent="NavigationRegion3D" instance=ExtResource("21_xcdtp")]
transform = Transform3D(-0.0389964, 0, -1.02344, 0, 1.02418, 0, 1.02344, 0, -0.0389964, -2.934, 0.01, 10.3588)

[node name="rock_6" parent="NavigationRegion3D" instance=ExtResource("21_xcdtp")]
transform = Transform3D(0.587238, 0, 0.805097, 0, 0.996509, 0, -0.805097, 0, 0.587238, 1.90259, 0.01, 5.25729)

[node name="rock_7" parent="NavigationRegion3D" instance=ExtResource("21_xcdtp")]
transform = Transform3D(0.436054, 0, -0.893659, 0, 0.994369, 0, 0.893659, 0, 0.436054, 5.60388, 0.01, 2.47468)

[node name="rock_8" parent="NavigationRegion3D" instance=ExtResource("21_xcdtp")]
transform = Transform3D(0.814127, 0, -0.497358, 0, 0.954027, 0, 0.497358, 0, 0.814127, 16.3272, 0.01, -32.9706)

[node name="rock_9" parent="NavigationRegion3D" instance=ExtResource("21_xcdtp")]
transform = Transform3D(0.107271, 0, 1.1831, 0, 1.18795, 0, -1.1831, 0, 0.107271, 7.79519, 0.01, -32.4607)

[node name="rock_10" parent="NavigationRegion3D" instance=ExtResource("21_xcdtp")]
transform = Transform3D(0.932361, 0, -0.561193, 0, 1.08823, 0, 0.561193, 0, 0.932361, 13.5308, 0.01, -37.4191)

[node name="rock_11" parent="NavigationRegion3D" instance=ExtResource("21_xcdtp")]
transform = Transform3D(0.763854, 0, -0.337178, 0, 0.834962, 0, 0.337178, 0, 0.763854, -1.62378, 0.01, -12.6526)

[node name="rock_12" parent="NavigationRegion3D" instance=ExtResource("21_xcdtp")]
transform = Transform3D(0.793896, 0, -0.427526, 0, 0.901693, 0, 0.427526, 0, 0.793896, 40.7176, 0.01, 31.0697)

[node name="rock_13" parent="NavigationRegion3D" instance=ExtResource("21_xcdtp")]
transform = Transform3D(-0.293084, 0, 0.899648, 0, 0.946184, 0, -0.899648, 0, -0.293084, -29.1185, 0.01, -42.8035)

[node name="rock_14" parent="NavigationRegion3D" instance=ExtResource("21_xcdtp")]
transform = Transform3D(0.619115, 0, -0.782457, 0, 0.997769, 0, 0.782457, 0, 0.619115, -9.82654, 0.01, -39.4828)

[node name="barrel_0" parent="NavigationRegion3D" instance=ExtResource("22_cin4e")]
transform = Transform3D(-0.814916, 0, -0.25264, 0, 0.853179, 0, 0.25264, 0, -0.814916, -32.7246, 0, 19.6339)

[node name="barrel_1" parent="NavigationRegion3D" instance=ExtResource("22_cin4e")]
transform = Transform3D(-0.33453, 0, 0.826237, 0, 0.891391, 0, -0.826237, 0, -0.33453, -26.824, 0, 27.8777)

[node name="decoration_0" parent="NavigationRegion3D" instance=ExtResource("23_6j2fk")]
transform = Transform3D(-0.640489, 0, -0.686118, 0, 0.938607, 0, 0.686118, 0, -0.640489, 44.185, 0, -29.7552)

[node name="decoration_1" parent="NavigationRegion3D" instance=ExtResource("23_6j2fk")]
transform = Transform3D(-1.03843, 0, -0.746602, 0, 1.27897, 0, 0.746602, 0, -1.03843, -38.711, 0, 31.6986)

[node name="decoration_2" parent="NavigationRegion3D" instance=ExtResource("23_6j2fk")]
transform = Transform3D(-0.705904, 0, -1.02768, 0, 1.24676, 0, 1.02768, 0, -0.705904, -4.23677, 0, 33.3962)

[node name="decoration_3" parent="NavigationRegion3D" instance=ExtResource("23_6j2fk")]
transform = Transform3D(-0.871547, 0, -0.191298, 0, 0.892295, 0, 0.191298, 0, -0.871547, 3.88379, 0, -10.6794)

[node name="decoration_4" parent="NavigationRegion3D" instance=ExtResource("23_6j2fk")]
transform = Transform3D(-0.295369, 0, 0.8907, 0, 0.938397, 0, -0.8907, 0, -0.295369, 43.3117, 0, 1.46481)

[node name="decoration_5" parent="NavigationRegion3D" instance=ExtResource("23_6j2fk")]
transform = Transform3D(-1.01228, 0, 0.623713, 0, 1.18901, 0, -0.623713, 0, -1.01228, 32.9918, 0, -7.99186)

[node name="decoration_6" parent="NavigationRegion3D" instance=ExtResource("23_6j2fk")]
transform = Transform3D(-1.2277, 0, 0.470538, 0, 1.31478, 0, -0.470538, 0, -1.2277, -15.7601, 0, -44.5431)

[node name="decoration_7" parent="NavigationRegion3D" instance=ExtResource("23_6j2fk")]
transform = Transform3D(-0.651139, 0, 0.953492, 0, 1.15461, 0, -0.953492, 0, -0.651139, -18.4551, 0, -39.0467)

[node name="decoration_8" parent="NavigationRegion3D" instance=ExtResource("23_6j2fk")]
transform = Transform3D(0.765916, 0, -1.10962, 0, 1.34829, 0, 1.10962, 0, 0.765916, 21.0977, 0, -25.6994)

[node name="decoration_9" parent="NavigationRegion3D" instance=ExtResource("23_6j2fk")]
transform = Transform3D(0.729875, 0, -0.0682511, 0, 0.733059, 0, 0.0682511, 0, 0.729875, 36.2862, 0, -24.6035)

[node name="decoration_10" parent="NavigationRegion3D" instance=ExtResource("23_6j2fk")]
transform = Transform3D(-0.497438, 0, 0.946635, 0, 1.06938, 0, -0.946635, 0, -0.497438, -34.8978, 0, -35.1672)

[node name="decoration_11" parent="NavigationRegion3D" instance=ExtResource("23_6j2fk")]
transform = Transform3D(0.158713, 0, -1.09682, 0, 1.10825, 0, 1.09682, 0, 0.158713, 17.5333, 0, 13.6759)

[node name="decoration_12" parent="NavigationRegion3D" instance=ExtResource("23_6j2fk")]
transform = Transform3D(-0.0654079, 0, 1.21267, 0, 1.21444, 0, -1.21267, 0, -0.0654079, 2.67053, 0, 2.82346)

[node name="decoration_13" parent="NavigationRegion3D" instance=ExtResource("23_6j2fk")]
transform = Transform3D(1.37538, 0, -0.0921303, 0, 1.37846, 0, 0.0921303, 0, 1.37538, -11.5841, 0, -34.441)

[node name="decoration_14" parent="NavigationRegion3D" instance=ExtResource("23_6j2fk")]
transform = Transform3D(0.775709, 0, 0.577832, 0, 0.967271, 0, -0.577832, 0, 0.775709, 22.5559, 0, 40.4191)

[node name="decoration_15" parent="NavigationRegion3D" instance=ExtResource("23_6j2fk")]
transform = Transform3D(0.419047, 0, -0.991508, 0, 1.07642, 0, 0.991508, 0, 0.419047, 14.0607, 0, 37.3537)

[node name="decoration_16" parent="NavigationRegion3D" instance=ExtResource("23_6j2fk")]
transform = Transform3D(0.475541, 0, -1.10929, 0, 1.20692, 0, 1.10929, 0, 0.475541, 21.2857, 0, -20.1493)

[node name="decoration_17" parent="NavigationRegion3D" instance=ExtResource("23_6j2fk")]
transform = Transform3D(-0.561695, 0, -0.989359, 0, 1.13769, 0, 0.989359, 0, -0.561695, 4.0697, 0, -43.9201)

[node name="decoration_18" parent="NavigationRegion3D" instance=ExtResource("23_6j2fk")]
transform = Transform3D(-0.712226, 0, 0.22341, 0, 0.746443, 0, -0.22341, 0, -0.712226, -20.2085, 0, -1.82559)

[node name="decoration_19" parent="NavigationRegion3D" instance=ExtResource("23_6j2fk")]
transform = Transform3D(0.0541961, 0, 1.00669, 0, 1.00815, 0, -1.00669, 0, 0.0541961, -28.0397, 0, -17.8128)

[node name="PatrolPointManager" type="Node" parent="."]
script = ExtResource("24_j2uky")
