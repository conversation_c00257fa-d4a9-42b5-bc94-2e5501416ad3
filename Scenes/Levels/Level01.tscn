[gd_scene load_steps=38 format=3 uid="uid://c0rj4dwj18nv8"]

[ext_resource type="Script" uid="uid://bo0h8t35yls55" path="res://Scripts/Levels/Level01.gd" id="1_hk2b6"]
[ext_resource type="PackedScene" uid="uid://d2xyw4d37ymv4" path="res://Scenes/Minimap.tscn" id="2_j32d5"]
[ext_resource type="PackedScene" uid="uid://ctxvyh1qr52ue" path="res://Scenes/PlayerAvatar.tscn" id="3_0f42i"]
[ext_resource type="PackedScene" uid="uid://do1sypgxbecbd" path="res://Scenes/ItemBoxes.tscn" id="4_swvig"]
[ext_resource type="PackedScene" uid="uid://b4l4phqqbvprm" path="res://Scenes/SkillBoxUI.tscn" id="5_dbohx"]
[ext_resource type="Script" uid="uid://f54r1khulcvh" path="res://Scripts/ModularLevelGenerator.gd" id="6_6ly8b"]
[ext_resource type="Script" uid="uid://c30rtq521bf7l" path="res://Scripts/CameraController.gd" id="7_camera"]
[ext_resource type="Resource" uid="uid://cvji54t3pjwfe" path="res://Resources/LevelConfigs/AllModulesConfig.tres" id="7_r5102"]
[ext_resource type="PackedScene" uid="uid://d2b5h1mlxxy7d" path="res://Scenes/FogOfWar.tscn" id="9_003du"]
[ext_resource type="PackedScene" uid="uid://b88l8pk1ebe1x" path="res://Scenes/player.tscn" id="11_82jtv"]
[ext_resource type="PackedScene" uid="uid://c3h8fj2xsp5oy" path="res://Scenes/Prefabs/Chest.tscn" id="14_1ks1q"]
[ext_resource type="PackedScene" uid="uid://dwusy8dd8usvo" path="res://Scenes/Prefabs/Wall.tscn" id="14_df8b0"]
[ext_resource type="PackedScene" uid="uid://crtnthqkksmri" path="res://Scenes/Prefabs/Tree.tscn" id="15_003du"]
[ext_resource type="Script" uid="uid://c3tr23vwvnmwf" path="res://Scripts/PatrolPoint.gd" id="15_6ly8b"]
[ext_resource type="Texture2D" uid="uid://c2ny0yi07rvcf" path="res://Environment/Floor/Floor01_Rocks_BaseColor.png" id="15_df8b0"]
[ext_resource type="Texture2D" uid="uid://dhfikoo16s5n0" path="res://Environment/Floor/Rocks_Metallic.png" id="16_003du"]
[ext_resource type="PackedScene" uid="uid://ddttv643pel23" path="res://Environment/Floor/Floor01_Custom.tscn" id="17_2bvpm"]
[ext_resource type="Resource" path="res://Resources/Items/Trap.tres" id="17_j32d5"]
[ext_resource type="Resource" path="res://Resources/Items/Torch.tres" id="18_j32d5"]
[ext_resource type="Script" uid="uid://pfva6p6rllgp" path="res://Scripts/Tree.gd" id="18_oh2bt"]
[ext_resource type="PackedScene" uid="uid://rvgn0irsuwao" path="res://Scenes/EnemyBoy01.tscn" id="19_0f42i"]
[ext_resource type="PackedScene" uid="uid://brxqv3iv3op6u" path="res://Scenes/ProgressBar3D.tscn" id="19_fcnrs"]
[ext_resource type="Script" uid="uid://dhw1dgex7wmiy" path="res://Scripts/ChestInteractable.gd" id="21_1ks1q"]
[ext_resource type="PackedScene" uid="uid://ervckea7fk57" path="res://Scenes/Enemy02.tscn" id="21_dbohx"]
[ext_resource type="PackedScene" uid="uid://b5dqjsb63wbhl" path="res://Scenes/Prefabs/Rock.tscn" id="21_xcdtp"]
[ext_resource type="PackedScene" uid="uid://bdq3b4e0mlgo4" path="res://Scenes/Prefabs/Barrel.tscn" id="22_cin4e"]
[ext_resource type="Resource" uid="uid://bjih53ivkm0qi" path="res://Resources/GasItem.tres" id="22_xcdtp"]
[ext_resource type="PackedScene" uid="uid://c6k7j3t3flhst" path="res://Scenes/Prefabs/Decoration.tscn" id="23_6j2fk"]
[ext_resource type="AudioStream" uid="uid://dgskkp7epyn0x" path="res://ChestOpening.mp3" id="23_cin4e"]
[ext_resource type="Script" uid="uid://de18ote8otj6u" path="res://Scripts/PatrolPointManager.gd" id="24_j2uky"]

[sub_resource type="ProceduralSkyMaterial" id="ProceduralSkyMaterial_gbvua"]
sky_top_color = Color(0.2, 0.4, 0.8, 1)
sky_horizon_color = Color(0.5, 0.7, 0.9, 1)
ground_bottom_color = Color(0.3, 0.5, 0.7, 1)
ground_horizon_color = Color(0.5, 0.7, 0.9, 1)

[sub_resource type="Sky" id="Sky_m0r78"]
sky_material = SubResource("ProceduralSkyMaterial_gbvua")

[sub_resource type="Environment" id="Environment_j4i7h"]
background_mode = 2
sky = SubResource("Sky_m0r78")
ambient_light_source = 3
ambient_light_color = Color(0.85, 0.85, 0.9, 1)
ambient_light_energy = 0.4
tonemap_mode = 2
ssao_enabled = true
glow_enabled = true
fog_enabled = true
fog_light_color = Color(0.85, 0.9, 1, 1)
fog_density = 0.001

[sub_resource type="SphereShape3D" id="SphereShape3D_camera"]
radius = 0.2

[sub_resource type="NavigationMesh" id="NavigationMesh_1ks1q"]
agent_height = 1.75
agent_radius = 0.375
agent_max_climb = 0.5
edge_max_length = 12.0
filter_low_hanging_obstacles = true
filter_ledge_spans = true
filter_walkable_low_height_spans = true

[sub_resource type="StandardMaterial3D" id="StandardMaterial3D_xcdtp"]
albedo_texture = ExtResource("15_df8b0")
metallic = 0.2
metallic_texture = ExtResource("16_003du")
roughness = 0.8
uv1_scale = Vector3(10, 10, 1)

[sub_resource type="StandardMaterial3D" id="StandardMaterial3D_01ds2"]
transparency = 1
albedo_color = Color(1, 1, 0, 0.3)
emission_enabled = true
emission = Color(1, 1, 0, 1)

[node name="level01" type="Node3D"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 0, 0, 1.94383)
script = ExtResource("1_hk2b6")
minimap_scene = ExtResource("2_j32d5")
player_avatar_scene = ExtResource("3_0f42i")
item_boxes_scene = ExtResource("4_swvig")
skill_box_ui_scene = ExtResource("5_dbohx")

[node name="WorldEnvironment" type="WorldEnvironment" parent="."]
environment = SubResource("Environment_j4i7h")

[node name="DirectionalLight3D" type="DirectionalLight3D" parent="."]
transform = Transform3D(0.999999, 0, 0, 0, 0.5, 0.866026, 0, -0.866025, 0.5, 0, 0, 0)
light_color = Color(1, 0.868175, 0.323518, 1)
light_energy = 1.5
shadow_enabled = true
shadow_opacity = 0.7
shadow_blur = 2.0

[node name="ModularLevelGenerator" type="Node3D" parent="."]
script = ExtResource("6_6ly8b")
level_config = ExtResource("7_r5102")

[node name="FogOfWar" parent="." instance=ExtResource("9_003du")]
player_vision_radius = 8.0
enabled = false

[node name="CameraPivot" type="Node3D" parent="."]
unique_name_in_owner = true
script = ExtResource("7_camera")

[node name="SpringArm3D" type="SpringArm3D" parent="CameraPivot"]
unique_name_in_owner = true
shape = SubResource("SphereShape3D_camera")

[node name="Camera3D" type="Camera3D" parent="CameraPivot/SpringArm3D"]
unique_name_in_owner = true
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 0, 24.0502, 37.8871)
v_offset = -10.0
current = true
fov = 54.8
far = 90.3

[node name="Player" parent="." instance=ExtResource("11_82jtv")]

[node name="Chest" parent="." instance=ExtResource("14_1ks1q")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 4.57862, 0, 6.58062)
interaction_distance = 3.0
item_resource = ExtResource("17_j32d5")

[node name="Chest2" parent="." instance=ExtResource("14_1ks1q")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 9.81867, 0, 6.58062)
interaction_distance = 3.0
item_resource = ExtResource("18_j32d5")

[node name="EnemyBoy01" parent="." instance=ExtResource("19_0f42i")]
transform = Transform3D(0.8, 0, 0, 0, 0.8, 0, 0, 0, 0.8, -10.0014, 0, -8.65824)

[node name="Enemy02" parent="." instance=ExtResource("21_dbohx")]
transform = Transform3D(2.1, 0, 0, 0, 2.1, 0, 0, 0, 2.1, 7.86917, 0, -7.768)

[node name="NavigationRegion3D" type="NavigationRegion3D" parent="."]
navigation_mesh = SubResource("NavigationMesh_1ks1q")

[node name="Floor" type="Node3D" parent="NavigationRegion3D"]

[node name="@CSGBox3D@25539" type="CSGBox3D" parent="NavigationRegion3D/Floor"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 0, -0.05, 0)
use_collision = true
size = Vector3(100, 0.1, 100)
material = SubResource("StandardMaterial3D_xcdtp")

[node name="FloorTiles" type="Node3D" parent="NavigationRegion3D/Floor"]

[node name="Floor01_0" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -4.19112, 0.1, 31.5575)

[node name="Floor01_1" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -35.2475, 0.1, 19.6947)

[node name="Floor01_2" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 0.555233, 0.1, -18.1588)

[node name="Floor01_3" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -28.3491, 0.1, -34.1993)

[node name="Floor01_4" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -14.2274, 0.1, 0.475262)

[node name="Floor01_5" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -26.1553, 0.1, 14.5942)

[node name="Floor01_6" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -21.1358, 0.1, -14.7119)

[node name="Floor01_7" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 3.22, 0.1, -6.28318)

[node name="Floor01_8" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 34.1903, 0.1, 14.7692)

[node name="Floor01_9" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -11.641, 0.1, 17.5215)

[node name="Floor01_10" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 4.91474, 0.1, -35.3841)

[node name="Floor01_11" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -0.667273, 0.1, 5.8359)

[node name="Floor01_12" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 30.1682, 0.1, 1.49434)

[node name="Floor01_13" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -30.3196, 0.1, -43.0024)

[node name="Floor01_14" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 35.1064, 0.1, -44.0508)

[node name="Floor01_15" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 10.6978, 0.1, -11.0227)

[node name="Floor01_16" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 25.0095, 0.1, 23.6507)

[node name="Floor01_17" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -33.3917, 0.1, 11.1414)

[node name="Floor01_18" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 16.283, 0.1, 7.66878)

[node name="Floor01_19" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 25.2194, 0.1, -32.2188)

[node name="Floor01_20" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -39.7929, 0.1, -42.9972)

[node name="Floor01_21" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 21.9636, 0.1, -21.4594)

[node name="Floor01_22" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 6.38818, 0.1, -26.4116)

[node name="Floor01_23" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 7.91501, 0.1, 38.2925)

[node name="Floor01_24" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 21.694, 0.1, 31.9695)

[node name="Floor01_25" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -30.6902, 0.1, -14.7333)

[node name="Floor01_26" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -18.6074, 0.1, 40.4555)

[node name="Floor01_27" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 28.6228, 0.1, 42.1535)

[node name="Floor01_28" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 12.1209, 0.1, 28.6643)

[node name="Floor01_29" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -10.2388, 0.1, -18.9038)

[node name="Floor01_30" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -40.2857, 0.1, -9.09307)

[node name="Floor01_31" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 32.0786, 0.1, -13.4792)

[node name="Floor01_32" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 5.07542, 0.1, 13.9926)

[node name="Floor01_33" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -9.32296, 0.1, -32.7684)

[node name="Floor01_34" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 26.4695, 0.1, 11.8257)

[node name="Floor01_35" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -26.3075, 0.1, 26.1978)

[node name="Floor01_36" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 42.9553, 0.1, 26.4646)

[node name="Floor01_37" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -14.3582, 0.1, 30.9521)

[node name="Floor01_38" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -20.529, 0.1, -29.6686)

[node name="Floor01_39" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -4.14342, 0.1, -42.2326)

[node name="Floor01_40" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -38.7264, 0.1, 28.8987)

[node name="Floor01_41" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 15.8997, 0.1, -42.2602)

[node name="Floor01_42" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -27.3789, 0.1, -0.118529)

[node name="Floor01_43" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 40.2219, 0.1, 8.74087)

[node name="Floor01_44" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 34.1718, 0.1, 36.3865)

[node name="Floor01_45" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 43.3897, 0.1, -40.6455)

[node name="Floor01_46" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -19.5852, 0.1, 9.38359)

[node name="Floor01_47" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -11.6325, 0.1, -7.48115)

[node name="Floor01_48" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -39.9064, 0.1, 3.7422)

[node name="Floor01_49" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 0.862553, 0.1, 24.3325)

[node name="Floor01_50" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 42.1386, 0.1, -22.7615)

[node name="Floor01_51" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 10.7324, 0.1, 20.0666)

[node name="Floor01_52" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -31.3113, 0.1, 44.7978)

[node name="Floor01_53" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -12.4627, 0.1, -43.6384)

[node name="Floor01_54" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 25.4674, 0.1, -43.4671)

[node name="Floor01_55" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -29.1263, 0.1, 36.1072)

[node name="Floor01_56" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 33.9383, 0.1, -30.728)

[node name="Floor01_57" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 8.87789, 0.1, 1.4263)

[node name="Floor01_58" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -44.2032, 0.1, 10.9654)

[node name="Floor01_59" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -8.58251, 0.1, 8.99714)

[node name="Floor01_60" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 34.2182, 0.1, 24.6851)

[node name="Floor01_61" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 43.9971, 0.1, 38.4958)

[node name="Floor01_62" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -44.2984, 0.1, -35.6995)

[node name="Floor01_63" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 19.7488, 0.1, -5.07123)

[node name="Floor01_64" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -20.0545, 0.1, -38.8739)

[node name="Floor01_65" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 44.0151, 0.1, 1.66458)

[node name="Floor01_66" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 42.5844, 0.1, -32.4462)

[node name="Floor01_67" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -44.9135, 0.1, -22.2693)

[node name="Floor01_68" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -43.3428, 0.1, 20.0187)

[node name="Floor01_69" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -44.0689, 0.1, 38.3825)

[node name="Floor01_70" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 20.4612, 0.1, 44.7931)

[node name="Floor01_71" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -1.50118, 0.1, -28.3398)

[node name="Floor01_72" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -9.34338, 0.1, 44.5537)

[node name="Floor01_73" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 13.8196, 0.1, -23.3036)

[node name="Floor01_74" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -25.4283, 0.1, -23.3435)

[node name="wall_0" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -50, 0, -50)

[node name="wall_1" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -50, 0, 50)

[node name="wall_2" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -48, 0, -50)

[node name="wall_3" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -48, 0, 50)

[node name="wall_4" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -46, 0, -50)

[node name="wall_5" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -46, 0, 50)

[node name="wall_6" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -44, 0, -50)

[node name="wall_7" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -44, 0, 50)

[node name="wall_8" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -42, 0, -50)

[node name="wall_9" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -42, 0, 50)

[node name="wall_10" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -40, 0, -50)

[node name="wall_11" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -40, 0, 50)

[node name="wall_12" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -38, 0, -50)

[node name="wall_13" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -38, 0, 50)

[node name="wall_14" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -36, 0, -50)

[node name="wall_15" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -36, 0, 50)

[node name="wall_16" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -34, 0, -50)

[node name="wall_17" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -34, 0, 50)

[node name="wall_18" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -32, 0, -50)

[node name="wall_19" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -32, 0, 50)

[node name="wall_20" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -30, 0, -50)

[node name="wall_21" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -30, 0, 50)

[node name="wall_22" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -28, 0, -50)

[node name="wall_23" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -28, 0, 50)

[node name="wall_24" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -26, 0, -50)

[node name="wall_25" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -26, 0, 50)

[node name="wall_26" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -24, 0, -50)

[node name="wall_27" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -24, 0, 50)

[node name="wall_28" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -22, 0, -50)

[node name="wall_29" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -22, 0, 50)

[node name="wall_30" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -20, 0, -50)

[node name="wall_31" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -20, 0, 50)

[node name="wall_32" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -18, 0, -50)

[node name="wall_33" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -18, 0, 50)

[node name="wall_34" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -16, 0, -50)

[node name="wall_35" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -16, 0, 50)

[node name="wall_36" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -14, 0, -50)

[node name="wall_37" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -14, 0, 50)

[node name="wall_38" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -12, 0, -50)

[node name="wall_39" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -12, 0, 50)

[node name="wall_40" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -10, 0, -50)

[node name="wall_41" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -10, 0, 50)

[node name="wall_42" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -8, 0, -50)

[node name="wall_43" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -8, 0, 50)

[node name="wall_44" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -6, 0, -50)

[node name="wall_45" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -6, 0, 50)

[node name="wall_46" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -4, 0, -50)

[node name="wall_47" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -4, 0, 50)

[node name="wall_48" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -2, 0, -50)

[node name="wall_49" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -2, 0, 50)

[node name="wall_50" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 0, 0, -50)

[node name="wall_51" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 0, 0, 50)

[node name="wall_52" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 2, 0, -50)

[node name="wall_53" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 2, 0, 50)

[node name="wall_54" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 4, 0, -50)

[node name="wall_55" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 4, 0, 50)

[node name="wall_56" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 6, 0, -50)

[node name="wall_57" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 6, 0, 50)

[node name="wall_58" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 8, 0, -50)

[node name="wall_59" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 8, 0, 50)

[node name="wall_60" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 10, 0, -50)

[node name="wall_61" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 10, 0, 50)

[node name="wall_62" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 12, 0, -50)

[node name="wall_63" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 12, 0, 50)

[node name="wall_64" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 14, 0, -50)

[node name="wall_65" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 14, 0, 50)

[node name="wall_66" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 16, 0, -50)

[node name="wall_67" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 16, 0, 50)

[node name="wall_68" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 18, 0, -50)

[node name="wall_69" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 18, 0, 50)

[node name="wall_70" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 20, 0, -50)

[node name="wall_71" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 20, 0, 50)

[node name="wall_72" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 22, 0, -50)

[node name="wall_73" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 22, 0, 50)

[node name="wall_74" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 24, 0, -50)

[node name="wall_75" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 24, 0, 50)

[node name="wall_76" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 26, 0, -50)

[node name="wall_77" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 26, 0, 50)

[node name="wall_78" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 28, 0, -50)

[node name="wall_79" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 28, 0, 50)

[node name="wall_80" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 30, 0, -50)

[node name="wall_81" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 30, 0, 50)

[node name="wall_82" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 32, 0, -50)

[node name="wall_83" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 32, 0, 50)

[node name="wall_84" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 34, 0, -50)

[node name="wall_85" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 34, 0, 50)

[node name="wall_86" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 36, 0, -50)

[node name="wall_87" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 36, 0, 50)

[node name="wall_88" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 38, 0, -50)

[node name="wall_89" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 38, 0, 50)

[node name="wall_90" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 40, 0, -50)

[node name="wall_91" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 40, 0, 50)

[node name="wall_92" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 42, 0, -50)

[node name="wall_93" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 42, 0, 50)

[node name="wall_94" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 44, 0, -50)

[node name="wall_95" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 44, 0, 50)

[node name="wall_96" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 46, 0, -50)

[node name="wall_97" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 46, 0, 50)

[node name="wall_98" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 48, 0, -50)

[node name="wall_99" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 48, 0, 50)

[node name="wall_100" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 50, 0, -50)

[node name="wall_101" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 50, 0, 50)

[node name="wall_102" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, -50, 0, -50)

[node name="wall_103" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, 50, 0, -50)

[node name="wall_104" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, -50, 0, -48)

[node name="wall_105" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, 50, 0, -48)

[node name="wall_106" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, -50, 0, -46)

[node name="wall_107" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, 50, 0, -46)

[node name="wall_108" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, -50, 0, -44)

[node name="wall_109" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, 50, 0, -44)

[node name="wall_110" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, -50, 0, -42)

[node name="wall_111" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, 50, 0, -42)

[node name="wall_112" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, -50, 0, -40)

[node name="wall_113" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, 50, 0, -40)

[node name="wall_114" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, -50, 0, -38)

[node name="wall_115" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, 50, 0, -38)

[node name="wall_116" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, -50, 0, -36)

[node name="wall_117" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, 50, 0, -36)

[node name="wall_118" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, -50, 0, -34)

[node name="wall_119" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, 50, 0, -34)

[node name="wall_120" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, -50, 0, -32)

[node name="wall_121" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, 50, 0, -32)

[node name="wall_122" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, -50, 0, -30)

[node name="wall_123" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, 50, 0, -30)

[node name="wall_124" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, -50, 0, -28)

[node name="wall_125" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, 50, 0, -28)

[node name="wall_126" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, -50, 0, -26)

[node name="wall_127" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, 50, 0, -26)

[node name="wall_128" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, -50, 0, -24)

[node name="wall_129" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, 50, 0, -24)

[node name="wall_130" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, -50, 0, -22)

[node name="wall_131" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, 50, 0, -22)

[node name="wall_132" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, -50, 0, -20)

[node name="wall_133" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, 50, 0, -20)

[node name="wall_134" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, -50, 0, -18)

[node name="wall_135" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, 50, 0, -18)

[node name="wall_136" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, -50, 0, -16)

[node name="wall_137" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, 50, 0, -16)

[node name="wall_138" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, -50, 0, -14)

[node name="wall_139" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, 50, 0, -14)

[node name="wall_140" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, -50, 0, -12)

[node name="wall_141" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, 50, 0, -12)

[node name="wall_142" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, -50, 0, -10)

[node name="wall_143" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, 50, 0, -10)

[node name="wall_144" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, -50, 0, -8)

[node name="wall_145" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, 50, 0, -8)

[node name="wall_146" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, -50, 0, -6)

[node name="wall_147" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, 50, 0, -6)

[node name="wall_148" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, -50, 0, -4)

[node name="wall_149" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, 50, 0, -4)

[node name="wall_150" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, -50, 0, -2)

[node name="wall_151" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, 50, 0, -2)

[node name="wall_152" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, -50, 0, 0)

[node name="wall_153" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, 50, 0, 0)

[node name="wall_154" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, -50, 0, 2)

[node name="wall_155" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, 50, 0, 2)

[node name="wall_156" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, -50, 0, 4)

[node name="wall_157" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, 50, 0, 4)

[node name="wall_158" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, -50, 0, 6)

[node name="wall_159" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, 50, 0, 6)

[node name="wall_160" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, -50, 0, 8)

[node name="wall_161" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, 50, 0, 8)

[node name="wall_162" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, -50, 0, 10)

[node name="wall_163" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, 50, 0, 10)

[node name="wall_164" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, -50, 0, 12)

[node name="wall_165" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, 50, 0, 12)

[node name="wall_166" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, -50, 0, 14)

[node name="wall_167" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, 50, 0, 14)

[node name="wall_168" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, -50, 0, 16)

[node name="wall_169" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, 50, 0, 16)

[node name="wall_170" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, -50, 0, 18)

[node name="wall_171" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, 50, 0, 18)

[node name="wall_172" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, -50, 0, 20)

[node name="wall_173" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, 50, 0, 20)

[node name="wall_174" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, -50, 0, 22)

[node name="wall_175" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, 50, 0, 22)

[node name="wall_176" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, -50, 0, 24)

[node name="wall_177" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, 50, 0, 24)

[node name="wall_178" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, -50, 0, 26)

[node name="wall_179" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, 50, 0, 26)

[node name="wall_180" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, -50, 0, 28)

[node name="wall_181" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, 50, 0, 28)

[node name="wall_182" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, -50, 0, 30)

[node name="wall_183" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, 50, 0, 30)

[node name="wall_184" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, -50, 0, 32)

[node name="wall_185" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, 50, 0, 32)

[node name="wall_186" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, -50, 0, 34)

[node name="wall_187" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, 50, 0, 34)

[node name="wall_188" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, -50, 0, 36)

[node name="wall_189" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, 50, 0, 36)

[node name="wall_190" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, -50, 0, 38)

[node name="wall_191" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, 50, 0, 38)

[node name="wall_192" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, -50, 0, 40)

[node name="wall_193" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, 50, 0, 40)

[node name="wall_194" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, -50, 0, 42)

[node name="wall_195" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, 50, 0, 42)

[node name="wall_196" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, -50, 0, 44)

[node name="wall_197" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, 50, 0, 44)

[node name="wall_198" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, -50, 0, 46)

[node name="wall_199" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, 50, 0, 46)

[node name="wall_200" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, -50, 0, 48)

[node name="wall_201" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, 50, 0, 48)

[node name="wall_202" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, -50, 0, 50)

[node name="wall_203" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, 50, 0, 50)

[node name="tree_0" type="Node3D" parent="NavigationRegion3D" instance=ExtResource("15_003du")]
transform = Transform3D(2.5, -0.00145593, -0.00167976, 0.00145725, 2.5, 0.00196474, 0.00167861, -0.00196571, 2.5, -18.0983, 0, -8.69245)
script = ExtResource("18_oh2bt")
highlight_material = SubResource("StandardMaterial3D_01ds2")
progress_bar_scene = ExtResource("19_fcnrs")

[node name="PatrolPoint_0" type="Node3D" parent="NavigationRegion3D"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -18.9897, 1, -9.48398)
script = ExtResource("15_6ly8b")

[node name="tree_1" type="Node3D" parent="NavigationRegion3D" instance=ExtResource("15_003du")]
transform = Transform3D(2.5, -0.00145593, -0.00167976, 0.00145725, 2.5, 0.00196474, 0.00167861, -0.00196571, 2.5, -6.23265, 0, 0.222434)
script = ExtResource("18_oh2bt")
highlight_material = SubResource("StandardMaterial3D_01ds2")
preset_burnt_out_type = 1
progress_bar_scene = ExtResource("19_fcnrs")

[node name="PatrolPoint_1" type="Node3D" parent="NavigationRegion3D"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -13.1637, 1, 0.588261)
script = ExtResource("15_6ly8b")
point_id = 1

[node name="tree_2" type="Node3D" parent="NavigationRegion3D" instance=ExtResource("15_003du")]
transform = Transform3D(2.5, -0.00145593, -0.00167976, 0.00145725, 2.5, 0.00196474, 0.00167861, -0.00196571, 2.5, 15.4044, 0, 17.1587)
script = ExtResource("18_oh2bt")
highlight_material = SubResource("StandardMaterial3D_01ds2")
progress_bar_scene = ExtResource("19_fcnrs")

[node name="PatrolPoint_2" type="Node3D" parent="NavigationRegion3D"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 10.9194, 1, 19.3909)
script = ExtResource("15_6ly8b")
point_id = 2

[node name="tree_3" type="Node3D" parent="NavigationRegion3D" instance=ExtResource("15_003du")]
transform = Transform3D(2.5, -0.00145593, -0.00167976, 0.00145725, 2.5, 0.00196474, 0.00167861, -0.00196571, 2.5, -16.526, 0, 19.5054)
script = ExtResource("18_oh2bt")
highlight_material = SubResource("StandardMaterial3D_01ds2")
progress_bar_scene = ExtResource("19_fcnrs")

[node name="PatrolPoint_3" type="Node3D" parent="NavigationRegion3D"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -15.6078, 1, 26.9068)
script = ExtResource("15_6ly8b")
point_id = 3

[node name="chest_0" type="Area3D" parent="NavigationRegion3D" instance=ExtResource("14_1ks1q")]
transform = Transform3D(-0.0120305, 0, 0.999928, 0, 1, 0, -0.999928, 0, -0.0120305, -10.5647, 0, 39.4676)
collision_layer = 8
collision_mask = 16
script = ExtResource("21_1ks1q")
interaction_distance = 1.5
item_resource = ExtResource("22_xcdtp")
unlock_sound = ExtResource("23_cin4e")

[node name="rock_0" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("21_xcdtp")]
transform = Transform3D(-0.104328, 0, 1.02776, 0, 1.03304, 0, -1.02776, 0, -0.104328, -16.4428, 0.01, 24.3689)
collision_layer = 2
collision_mask = 0

[node name="rock_1" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("21_xcdtp")]
transform = Transform3D(0.50949, 0, 0.755533, 0, 0.911269, 0, -0.755533, 0, 0.50949, 42.1993, 0.01, 43.6715)
collision_layer = 2
collision_mask = 0

[node name="rock_2" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("21_xcdtp")]
transform = Transform3D(-0.875736, 0, -0.635485, 0, 1.08201, 0, 0.635485, 0, -0.875736, -35.983, 0.01, 38.7687)
collision_layer = 2
collision_mask = 0

[node name="rock_3" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("21_xcdtp")]
transform = Transform3D(0.769212, 0, 0.448131, 0, 0.890229, 0, -0.448131, 0, 0.769212, 15.4137, 0.01, -30.3441)
collision_layer = 2
collision_mask = 0

[node name="rock_4" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("21_xcdtp")]
transform = Transform3D(0.797958, 0, 0.480303, 0, 0.931358, 0, -0.480303, 0, 0.797958, 4.91075, 0.01, 30.1771)
collision_layer = 2
collision_mask = 0

[node name="rock_5" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("21_xcdtp")]
transform = Transform3D(0.839683, 0, 0.243803, 0, 0.874361, 0, -0.243803, 0, 0.839683, -28.7248, 0.01, -7.12944)
collision_layer = 2
collision_mask = 0

[node name="rock_6" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("21_xcdtp")]
transform = Transform3D(0.306417, 0, 0.754248, 0, 0.814114, 0, -0.754248, 0, 0.306417, -15.7397, 0.01, -34.8589)
collision_layer = 2
collision_mask = 0

[node name="rock_7" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("21_xcdtp")]
transform = Transform3D(0.491032, 0, -0.889821, 0, 1.01631, 0, 0.889821, 0, 0.491032, 5.82822, 0.01, -44.3821)
collision_layer = 2
collision_mask = 0

[node name="rock_8" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("21_xcdtp")]
transform = Transform3D(0.775652, 0, 0.880943, 0, 1.17375, 0, -0.880943, 0, 0.775652, 15.1488, 0.01, 43.3659)
collision_layer = 2
collision_mask = 0

[node name="rock_9" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("21_xcdtp")]
transform = Transform3D(-0.981713, 0, 0.117902, 0, 0.988768, 0, -0.117902, 0, -0.981713, -42.115, 0.01, -17.505)
collision_layer = 2
collision_mask = 0

[node name="rock_10" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("21_xcdtp")]
transform = Transform3D(0.799305, 0, -0.501483, 0, 0.943596, 0, 0.501483, 0, 0.799305, -39.6353, 0.01, -32.9801)
collision_layer = 2
collision_mask = 0

[node name="rock_11" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("21_xcdtp")]
transform = Transform3D(-0.385524, 0, 1.04695, 0, 1.11568, 0, -1.04695, 0, -0.385524, 37.3742, 0.01, 29.9887)
collision_layer = 2
collision_mask = 0

[node name="rock_12" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("21_xcdtp")]
transform = Transform3D(0.626838, 0, 0.729425, 0, 0.961762, 0, -0.729425, 0, 0.626838, 39.7502, 0.01, -2.61027)
collision_layer = 2
collision_mask = 0

[node name="rock_13" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("21_xcdtp")]
transform = Transform3D(1.06429, 0, 0.365487, 0, 1.1253, 0, -0.365487, 0, 1.06429, -24.5236, 0.01, -43.0861)
collision_layer = 2
collision_mask = 0

[node name="rock_14" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("21_xcdtp")]
transform = Transform3D(-0.932318, 0, -0.177314, 0, 0.94903, 0, 0.177314, 0, -0.932318, 6.32524, 0.01, -41.3814)
collision_layer = 2
collision_mask = 0

[node name="barrel_0" type="StaticBody3D" parent="NavigationRegion3D" groups=["destructible"] instance=ExtResource("22_cin4e")]
transform = Transform3D(-0.201087, 0, 0.848823, 0, 0.872316, 0, -0.848823, 0, -0.201087, -24.1788, 0, 35.3597)
collision_layer = 3
collision_mask = 3

[node name="barrel_1" type="StaticBody3D" parent="NavigationRegion3D" groups=["destructible"] instance=ExtResource("22_cin4e")]
transform = Transform3D(-0.303175, 0, -1.08588, 0, 1.12741, 0, 1.08588, 0, -0.303175, 43.8023, 0, 18.6827)
collision_layer = 3
collision_mask = 3

[node name="decoration_0" type="Node3D" parent="NavigationRegion3D" instance=ExtResource("23_6j2fk")]
transform = Transform3D(0.534082, 0, 0.510361, 0, 0.738723, 0, -0.510361, 0, 0.534082, -32.6741, 0, 5.18283)

[node name="decoration_1" type="Node3D" parent="NavigationRegion3D" instance=ExtResource("23_6j2fk")]
transform = Transform3D(0.816418, 0, 0.131673, 0, 0.826968, 0, -0.131673, 0, 0.816418, 27.1589, 0, 32.3213)

[node name="decoration_2" type="Node3D" parent="NavigationRegion3D" instance=ExtResource("23_6j2fk")]
transform = Transform3D(-0.226815, 0, -0.775084, 0, 0.807589, 0, 0.775084, 0, -0.226815, -4.3977, 0, 15.5373)

[node name="decoration_3" type="Node3D" parent="NavigationRegion3D" instance=ExtResource("23_6j2fk")]
transform = Transform3D(0.354916, 0, -0.733781, 0, 0.815107, 0, 0.733781, 0, 0.354916, -16.773, 0, -6.48721)

[node name="decoration_4" type="Node3D" parent="NavigationRegion3D" instance=ExtResource("23_6j2fk")]
transform = Transform3D(-0.847192, 0, -0.150119, 0, 0.860389, 0, 0.150119, 0, -0.847192, -19.0412, 0, -23.0993)

[node name="decoration_5" type="Node3D" parent="NavigationRegion3D" instance=ExtResource("23_6j2fk")]
transform = Transform3D(0.930221, 0, -0.768822, 0, 1.20681, 0, 0.768822, 0, 0.930221, -42.93, 0, 32.2858)

[node name="decoration_6" type="Node3D" parent="NavigationRegion3D" instance=ExtResource("23_6j2fk")]
transform = Transform3D(0.579235, 0, -1.19875, 0, 1.33136, 0, 1.19875, 0, 0.579235, -31.6167, 0, -27.6152)

[node name="decoration_7" type="Node3D" parent="NavigationRegion3D" instance=ExtResource("23_6j2fk")]
transform = Transform3D(-1.24196, 0, -0.343848, 0, 1.28868, 0, 0.343848, 0, -1.24196, 42.3866, 0, -10.6688)

[node name="decoration_8" type="Node3D" parent="NavigationRegion3D" instance=ExtResource("23_6j2fk")]
transform = Transform3D(-0.847743, 0, -0.0619197, 0, 0.850002, 0, 0.0619197, 0, -0.847743, -40.4364, 0, 44.2276)

[node name="decoration_9" type="Node3D" parent="NavigationRegion3D" instance=ExtResource("23_6j2fk")]
transform = Transform3D(-0.900566, 0, -0.0163137, 0, 0.900714, 0, 0.0163137, 0, -0.900566, -4.31188, 0, -13.6773)

[node name="decoration_10" type="Node3D" parent="NavigationRegion3D" instance=ExtResource("23_6j2fk")]
transform = Transform3D(1.14557, 0, -0.126089, 0, 1.15248, 0, 0.126089, 0, 1.14557, -37.134, 0, -27.8664)

[node name="decoration_11" type="Node3D" parent="NavigationRegion3D" instance=ExtResource("23_6j2fk")]
transform = Transform3D(-0.77692, 0, 1.12872, 0, 1.37026, 0, -1.12872, 0, -0.77692, -22.097, 0, 44.1212)

[node name="decoration_12" type="Node3D" parent="NavigationRegion3D" instance=ExtResource("23_6j2fk")]
transform = Transform3D(0.380826, 0, 1.13983, 0, 1.20177, 0, -1.13983, 0, 0.380826, 37.6436, 0, -27.0046)

[node name="decoration_13" type="Node3D" parent="NavigationRegion3D" instance=ExtResource("23_6j2fk")]
transform = Transform3D(-1.21076, 0, -0.0434358, 0, 1.21154, 0, 0.0434358, 0, -1.21076, 26.9121, 0, -20.4543)

[node name="decoration_14" type="Node3D" parent="NavigationRegion3D" instance=ExtResource("23_6j2fk")]
transform = Transform3D(0.706727, 0, -0.624525, 0, 0.94313, 0, 0.624525, 0, 0.706727, -34.4232, 0, -21.6147)

[node name="decoration_15" type="Node3D" parent="NavigationRegion3D" instance=ExtResource("23_6j2fk")]
transform = Transform3D(0.744766, 0, -0.35657, 0, 0.825723, 0, 0.35657, 0, 0.744766, 27.9744, 0, 36.8233)

[node name="decoration_16" type="Node3D" parent="NavigationRegion3D" instance=ExtResource("23_6j2fk")]
transform = Transform3D(0.710874, 0, 1.17354, 0, 1.37206, 0, -1.17354, 0, 0.710874, -1.45675, 0, 41.2913)

[node name="decoration_17" type="Node3D" parent="NavigationRegion3D" instance=ExtResource("23_6j2fk")]
transform = Transform3D(0.586784, 0, 0.236651, 0, 0.632708, 0, -0.236651, 0, 0.586784, -0.912963, 0, 37.873)

[node name="decoration_18" type="Node3D" parent="NavigationRegion3D" instance=ExtResource("23_6j2fk")]
transform = Transform3D(0.208544, 0, -0.579292, 0, 0.615686, 0, 0.579292, 0, 0.208544, -8.69707, 0, 3.73772)

[node name="decoration_19" type="Node3D" parent="NavigationRegion3D" instance=ExtResource("23_6j2fk")]
transform = Transform3D(0.330808, 0, -0.971382, 0, 1.02617, 0, 0.971382, 0, 0.330808, -38.8628, 0, 40.7982)

[node name="PatrolPointManager" type="Node" parent="."]
script = ExtResource("24_j2uky")
