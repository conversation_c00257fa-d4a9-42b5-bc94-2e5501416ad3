[gd_scene load_steps=33 format=3 uid="uid://c0rj4dwj18nv8"]

[ext_resource type="Script" uid="uid://bo0h8t35yls55" path="res://Scripts/Levels/Level01.gd" id="1_hk2b6"]
[ext_resource type="PackedScene" uid="uid://d2xyw4d37ymv4" path="res://Scenes/Minimap.tscn" id="2_j32d5"]
[ext_resource type="PackedScene" uid="uid://ctxvyh1qr52ue" path="res://Scenes/PlayerAvatar.tscn" id="3_0f42i"]
[ext_resource type="PackedScene" uid="uid://do1sypgxbecbd" path="res://Scenes/ItemBoxes.tscn" id="4_swvig"]
[ext_resource type="PackedScene" uid="uid://b4l4phqqbvprm" path="res://Scenes/SkillBoxUI.tscn" id="5_dbohx"]
[ext_resource type="Script" uid="uid://f54r1khulcvh" path="res://Scripts/ModularLevelGenerator.gd" id="6_6ly8b"]
[ext_resource type="Resource" uid="uid://cvji54t3pjwfe" path="res://Resources/LevelConfigs/AllModulesConfig.tres" id="7_r5102"]
[ext_resource type="PackedScene" uid="uid://d2b5h1mlxxy7d" path="res://Scenes/FogOfWar.tscn" id="9_003du"]
[ext_resource type="PackedScene" uid="uid://b88l8pk1ebe1x" path="res://Scenes/player.tscn" id="11_82jtv"]
[ext_resource type="PackedScene" uid="uid://c3h8fj2xsp5oy" path="res://Scenes/Prefabs/Chest.tscn" id="14_1ks1q"]
[ext_resource type="PackedScene" uid="uid://dwusy8dd8usvo" path="res://Scenes/Prefabs/Wall.tscn" id="14_df8b0"]
[ext_resource type="PackedScene" uid="uid://crtnthqkksmri" path="res://Scenes/Prefabs/Tree.tscn" id="15_003du"]
[ext_resource type="Script" uid="uid://c3tr23vwvnmwf" path="res://Scripts/PatrolPoint.gd" id="15_6ly8b"]
[ext_resource type="Texture2D" uid="uid://c2ny0yi07rvcf" path="res://Environment/Floor/Floor01_Rocks_BaseColor.png" id="15_df8b0"]
[ext_resource type="Texture2D" uid="uid://dhfikoo16s5n0" path="res://Environment/Floor/Rocks_Metallic.png" id="16_003du"]
[ext_resource type="PackedScene" uid="uid://ddttv643pel23" path="res://Environment/Floor/Floor01_Custom.tscn" id="17_2bvpm"]
[ext_resource type="Resource" path="res://Resources/Items/Trap.tres" id="17_j32d5"]
[ext_resource type="Resource" path="res://Resources/Items/Torch.tres" id="18_j32d5"]
[ext_resource type="PackedScene" uid="uid://rvgn0irsuwao" path="res://Scenes/EnemyBoy01.tscn" id="19_0f42i"]
[ext_resource type="PackedScene" uid="uid://ervckea7fk57" path="res://Scenes/Enemy02.tscn" id="21_dbohx"]
[ext_resource type="PackedScene" uid="uid://b5dqjsb63wbhl" path="res://Scenes/Prefabs/Rock.tscn" id="21_xcdtp"]
[ext_resource type="PackedScene" uid="uid://bdq3b4e0mlgo4" path="res://Scenes/Prefabs/Barrel.tscn" id="22_cin4e"]
[ext_resource type="PackedScene" uid="uid://c6k7j3t3flhst" path="res://Scenes/Prefabs/Decoration.tscn" id="23_6j2fk"]
[ext_resource type="Script" uid="uid://de18ote8otj6u" path="res://Scripts/PatrolPointManager.gd" id="24_j2uky"]
[ext_resource type="Script" uid="uid://dt3st7cac7lok" path="res://Scripts/UI/UIManager.gd" id="25_ui_manager"]
[ext_resource type="Script" uid="uid://3mbdmmqaljis" path="res://Scripts/CameraFollower.gd" id="26_camera_follower"]

[sub_resource type="ProceduralSkyMaterial" id="ProceduralSkyMaterial_gbvua"]
sky_top_color = Color(0.2, 0.4, 0.8, 1)
sky_horizon_color = Color(0.5, 0.7, 0.9, 1)
ground_bottom_color = Color(0.3, 0.5, 0.7, 1)
ground_horizon_color = Color(0.5, 0.7, 0.9, 1)

[sub_resource type="Sky" id="Sky_m0r78"]
sky_material = SubResource("ProceduralSkyMaterial_gbvua")

[sub_resource type="Environment" id="Environment_j4i7h"]
background_mode = 2
sky = SubResource("Sky_m0r78")
ambient_light_source = 3
ambient_light_color = Color(0.9, 0.9, 0.95, 1)
ambient_light_energy = 0.5
tonemap_mode = 2
ssao_enabled = true
ssao_radius = 2.0
ssao_intensity = 1.5
glow_enabled = true
glow_strength = 1.2

[sub_resource type="NavigationMesh" id="NavigationMesh_xcdtp"]
vertices = PackedVector3Array(19.4868, 0.37304, -52.1736, 20.2368, 0.37304, -52.1736, 20.2368, 0.37304, -52.9236, 17.7368, 0.37304, -52.6736, 17.2368, 0.37304, -55.1736, 20.9868, 0.37304, -53.4236, 29.2368, 0.37304, -54.6736, 23.7368, 0.37304, -107.174, 22.9868, 0.37304, -108.174, 21.4868, 0.37304, -108.674, 19.2368, 0.37304, -106.674, 18.4868, 0.37304, -105.924, 23.4868, 0.37304, -104.674, 19.2368, 0.37304, -107.924, 17.2368, 0.37304, -106.174, 15.7368, 0.37304, -104.174, 24.4868, 0.37304, -103.674, 27.2368, 0.37304, -101.674, 26.9868, 0.37304, -103.924, 27.7368, 0.37304, -101.174, 15.9868, 0.37304, -55.4236, 28.9868, 0.37304, -101.424, 47.9868, 0.37304, -105.674, 47.2368, 0.37304, -105.174, 47.2368, 0.37304, -103.924, 54.4868, 0.37304, -103.674, 53.9868, 0.37304, -104.174, 49.2368, 0.37304, -105.424, 54.2368, 0.37304, -106.674, 53.4868, 0.37304, -107.674, 52.2368, 0.37304, -108.174, 49.7368, 0.37304, -105.924, 46.2368, 0.37304, -53.6736, 46.2368, 0.37304, -52.6736, 52.9868, 0.37304, -53.6736, 57.4868, 0.37304, -100.674, 57.7368, 0.37304, -103.424, 55.2368, 0.37304, -103.174, 49.7368, 0.37304, -107.174, 45.7368, 0.37304, -53.9236, 57.9868, 0.37304, -100.174, 45.9868, 0.37304, -103.424, -40.0132, 0.37304, -53.4236, -39.2632, 0.37304, -53.4236, -39.0132, 0.37304, -53.9236, -42.2632, 0.37304, -102.424, -42.2632, 0.37304, -53.9236, -34.7632, 0.37304, -105.924, -34.7632, 0.37304, -106.674, -36.2632, 0.37304, -107.174, -34.5132, 0.37304, -103.174, -34.2632, 0.37304, -105.674, -38.7632, 0.37304, -104.924, -41.2632, 0.37304, -104.174, -41.2632, 0.37304, -102.924, -33.0132, 0.37304, -102.174, -33.0132, 0.37304, -53.6736, -38.2632, 0.37304, -106.674, 41.9868, 0.37304, -106.674, 40.2368, 0.37304, -105.674, 40.2368, 0.37304, -104.674, 43.9868, 0.37304, -106.174, 39.4868, 0.37304, -103.924, 37.9868, 0.37304, -103.924, 36.9868, 0.37304, -102.674, 33.2368, 0.37304, -52.6736, 35.9868, 0.37304, -51.9236, 36.4868, 0.37304, -52.6736, 32.7368, 0.37304, -54.6736, 35.9868, 0.37304, -102.674, -64.0132, 0.37304, -105.924, -64.2632, 0.37304, -106.424, -66.2632, 0.37304, -105.424, -66.2632, 0.37304, -104.174, -66.7632, 0.37304, -103.674, -68.7632, 0.37304, -103.424, -68.7632, 0.37304, -102.174, -61.5132, 0.37304, -101.674, -62.0132, 0.37304, -102.174, -61.7632, 0.37304, -104.674, -62.7632, 0.37304, -105.924, -58.7632, 0.37304, -102.174, -59.0132, 0.37304, -102.674, -69.5132, 0.37304, -101.674, -68.2632, 0.37304, -65.4236, -56.0132, 0.37304, -54.6736, -56.0132, 0.37304, -100.174, -57.7632, 0.37304, -102.174, 14.2368, 0.37304, -105.924, 12.2368, 0.37304, -106.424, 11.9868, 0.37304, -105.924, 6.98684, 0.37304, -104.674, 5.98684, 0.37304, -104.674, 5.23684, 0.37304, -103.174, 10.2368, 0.37304, -105.424, 9.98684, 0.37304, -104.674, 14.9868, 0.37304, -104.424, 7.23684, 0.37304, -105.174, 8.23684, 0.37304, -53.4236, -1.76316, 0.37304, -103.424, -2.76316, 0.37304, -105.174, -4.01316, 0.37304, -105.424, -6.01316, 0.37304, -104.924, -6.26316, 0.37304, -103.424, 1.73684, 0.37304, -100.674, 0.236839, 0.37304, -102.424, -0.763161, 0.37304, -102.424, -6.76316, 0.37304, -102.924, -8.26316, 0.37304, -102.924, -9.51316, 0.37304, -100.924, -6.26316, 0.37304, -54.6736, 0.986839, 0.37304, -53.1736, -9.01316, 0.37304, -54.4236, -1.26316, 0.37304, -52.6736, -0.263161, 0.37304, -53.4236, -17.0132, 0.37304, -104.924, -18.7632, 0.37304, -105.174, -19.0132, 0.37304, -104.674, -21.0132, 0.37304, -102.424, -22.2632, 0.37304, -102.674, -23.0132, 0.37304, -102.174, -23.5132, 0.37304, -100.924, -20.5132, 0.37304, -104.424, -20.5132, 0.37304, -102.924, -16.0132, 0.37304, -102.674, -16.0132, 0.37304, -103.674, -15.2632, 0.37304, -102.174, -23.0132, 0.37304, -53.6736, -15.2632, 0.37304, -54.1736, -43.5132, 0.37304, -103.674, -43.5132, 0.37304, -104.424, -45.0132, 0.37304, -104.924, -53.0132, 0.37304, -102.174, -55.0132, 0.37304, -101.924, -55.0132, 0.37304, -100.924, -50.7632, 0.37304, -104.924, -52.5132, 0.37304, -103.924, -52.5132, 0.37304, -102.674, -47.7632, 0.37304, -103.924, -50.2632, 0.37304, -53.1736, -46.2632, 0.37304, -52.9236, -46.0132, 0.37304, -54.1736, -53.7874, 0.37304, -88.6011, -53.4809, 0.87304, -85.5204, -53.3277, 0.37304, -83.9801, -51.5293, 0.37304, -82.8269, -52.4003, 0.87304, -87.3511, -52.6906, 0.37304, -88.8591, -51.0374, 0.37304, -88.4394, 79.2368, 0.37304, -101.924, 78.2368, 0.37304, -102.174, 77.7368, 0.37304, -101.674, 72.7368, 0.37304, -102.424, 71.4868, 0.37304, -102.174, 71.4868, 0.37304, -101.174, 74.9868, 0.37304, -102.674, 98.2368, 0.37304, -104.424, 97.4868, 0.37304, -103.924, 97.4868, 0.37304, -102.674, 101.737, 0.37304, -100.674, 101.987, 0.37304, -103.174, 100.987, 0.37304, -104.424, 81.2368, 0.37304, -102.174, 79.4868, 0.37304, -102.424, 77.7368, 0.37304, -100.424, 82.2368, 0.37304, -100.674, 72.9868, 0.37304, -102.924, 93.2368, 0.37304, -100.674, 92.2368, 0.37304, -102.174, 91.4868, 0.37304, -102.174, 88.7368, 0.37304, -104.424, 88.2368, 0.37304, -104.924, 86.4868, 0.37304, -103.924, 86.4868, 0.37304, -102.674, 101.987, 0.37304, -66.6736, 103.987, 0.37304, -68.1736, 103.237, 0.37304, -68.6736, 101.487, 0.37304, -67.4236, 103.237, 0.37304, -75.4236, 103.737, 0.37304, -84.4236, 103.737, 0.37304, -92.6736, 106.737, 0.37304, -89.1736, 107.487, 0.37304, -90.6736, 107.237, 0.37304, -91.9236, 104.737, 0.37304, -91.9236, 103.737, 0.37304, -83.1736, 85.9868, 0.37304, -102.174, 83.9868, 0.37304, -101.924, 83.2368, 0.37304, -100.674, 100.237, 0.37304, -67.4236, 103.987, 0.37304, -73.6736, 103.237, 0.37304, -74.1736, 106.737, 0.37304, -76.4236, 107.487, 0.37304, -82.4236, 104.987, 0.37304, -82.1736, 103.987, 0.37304, -76.1736, 102.987, 0.37304, -99.9236, 94.4868, 0.37304, -100.424, 104.737, 0.37304, -94.9236, 90.2368, 0.37304, -104.424, 99.7368, 0.37304, -66.1736, 94.9868, 0.37304, -101.674, 76.2368, 0.37304, -99.9236, 70.7368, 0.37304, -100.424, 67.2368, 0.37304, -99.6736, 77.2368, 0.37304, -99.9236, 104.487, 0.37304, -85.6736, 106.487, 0.37304, -85.9236, 66.7368, 0.37304, -65.6736, 105.487, 0.37304, -99.9236, 35.2368, 0.37304, -103.174, 35.2368, 0.37304, -103.924, 33.7368, 0.37304, -103.924, 33.2368, 0.37304, -104.424, 31.4868, 0.37304, -103.424, 31.4868, 0.37304, -102.424, 30.9868, 0.37304, -55.1736, 30.7368, 0.37304, -101.674, 32.4868, 0.37304, -55.1736, 66.7368, 0.37304, -102.674, 66.2368, 0.37304, -103.424, 65.7368, 0.37304, -103.924, 63.7368, 0.37304, -104.174, 62.2368, 0.37304, -103.424, 62.2368, 0.37304, -102.174, 59.7368, 0.37304, -99.9236, 58.7368, 0.37304, -99.4236, 58.2368, 0.37304, -59.4236, 61.7368, 0.37304, -101.674, 59.7368, 0.37304, -101.174, 66.4868, 0.37304, -100.174, -95.7632, 0.37304, -100.674, -96.0132, 0.37304, -101.174, -97.5132, 0.37304, -100.674, -71.5132, 0.37304, -103.674, -73.2632, 0.37304, -103.924, -73.5132, 0.37304, -103.424, -71.0132, 0.37304, -102.674, -103.013, 0.37304, -96.1736, -104.263, 0.37304, -95.6736, -104.013, 0.37304, -94.1736, -85.0132, 0.37304, -99.9236, -85.0132, 0.37304, -101.924, -85.5132, 0.37304, -102.174, -85.5132, 0.37304, -102.924, -87.5132, 0.37304, -103.424, -93.7632, 0.37304, -99.4236, -94.5132, 0.37304, -100.674, -105.013, 0.37304, -93.6736, -105.263, 0.37304, -92.1736, -105.263, 0.37304, -88.9236, -101.513, 0.37304, -87.4236, -99.5132, 0.37304, -83.6736, -100.763, 0.37304, -82.9236, -100.763, 0.37304, -81.6736, -106.513, 0.37304, -76.9236, -107.513, 0.37304, -76.4236, -107.763, 0.37304, -74.9236, -107.763, 0.37304, -71.4236, -104.263, 0.37304, -70.1736, -105.513, 0.37304, -79.1736, -106.763, 0.37304, -78.4236, -100.513, 0.37304, -68.4236, -99.2632, 0.37304, -67.4236, -101.263, 0.37304, -81.1736, -75.2632, 0.37304, -103.174, -75.7632, 0.37304, -102.174, -99.7632, 0.37304, -98.4236, -90.0132, 0.37304, -100.674, -92.0132, 0.37304, -100.424, -92.5132, 0.37304, -99.4236, -100.763, 0.37304, -85.9236, -99.7632, 0.37304, -85.1736, -77.5132, 0.37304, -101.174, -78.7632, 0.37304, -101.174, -80.0132, 0.37304, -99.4236, -99.2632, 0.37304, -99.4236, -89.2632, 0.37304, -102.424, -99.2632, 0.37304, -66.4236, -102.263, 0.37304, -97.9236, -103.013, 0.37304, -68.1736, -84.5132, 0.37304, -99.4236, -105.013, 0.37304, -80.4236, -10.5132, 0.37304, -101.174, -11.2632, 0.37304, -102.674, -12.7632, 0.37304, -103.174, -13.0132, 0.37304, -54.4236, -12.5132, 0.37304, -53.4236, 3.48684, 0.37304, -102.674, 2.73684, 0.37304, -102.174, 2.73684, 0.37304, -101.174, 4.73684, 0.37304, -102.424, 5.23684, 0.37304, -51.9236, 5.48684, 0.37304, -52.6736, -24.7632, 0.37304, -101.174, -25.5132, 0.37304, -102.424, -27.0132, 0.37304, -102.924, -28.7632, 0.37304, -102.174, -29.0132, 0.37304, -53.9236, -24.7632, 0.37304, -53.1736, -53.0132, 0.37304, -52.1736, -50.7632, 0.37304, -52.1736, -50.7632, 0.37304, -52.9236, -54.0031, 0.37304, -79.4377, -54.0132, 0.37304, -51.4236, 53.7368, 0.37304, -52.9236, 100.237, 0.37304, -45.9236, 101.737, 0.37304, -45.6736, 101.737, 0.37304, -47.1736, 109.487, 0.37304, -60.6736, 109.987, 0.37304, -61.1736, 109.987, 0.37304, -63.1736, 107.487, 0.37304, -63.1736, 102.237, 0.37304, -47.6736, 105.487, 0.37304, -51.6736, 105.737, 0.37304, -56.4236, 106.487, 0.37304, -66.6736, 105.737, 0.37304, -67.6736, 101.487, 0.37304, -65.4236, 106.237, 0.37304, -64.1736, 106.737, 0.37304, -57.1736, 100.237, 0.37304, -65.4236, 54.2368, 0.37304, -48.6736, 106.737, 0.37304, -63.6736, 109.237, 0.37304, -57.4236, 105.987, 0.37304, -51.9236, 104.987, 0.37304, -47.9236, -102.513, 0.37304, -63.6736, -103.513, 0.37304, -63.1736, -103.263, 0.37304, -62.1736, -104.513, 0.37304, -61.4236, -104.263, 0.37304, -60.1736, -101.763, 0.37304, -55.6736, -100.263, 0.37304, -54.4236, -99.7632, 0.37304, -65.6736, -102.013, 0.37304, -65.1736, -104.763, 0.37304, -59.6736, -104.763, 0.37304, -56.4236, -54.0132, 0.37304, -51.1736, -99.2632, 0.37304, -52.6736, -99.2632, 0.37304, -50.6736, 5.98684, 3.62304, -50.4236, 3.23684, 3.62304, -51.1736, 2.73684, 3.62304, -48.4236, 7.48684, 3.62304, -46.6736, 16.7368, 3.62304, -48.6736, 20.2368, 3.62304, -47.6736, 20.2368, 3.62304, -50.6736, 16.9868, 3.62304, -51.4236, 6.48684, 3.62304, -51.6736, 14.9868, 3.62304, -48.6736, 16.4868, 3.62304, -51.9236, 16.2368, 3.62304, -53.9236, 21.4868, 3.62304, -47.4236, 22.2368, 3.62304, -46.6736, 23.7368, 3.62304, -46.6736, 21.4868, 3.62304, -51.1736, 32.2368, 3.62304, -51.6736, 31.4868, 3.62304, -53.6736, 30.2368, 3.62304, -53.6736, 30.9868, 3.62304, -48.1736, 21.4868, 3.62304, -52.1736, 33.7368, 3.62304, -47.9236, 34.2368, 3.62304, -51.1736, -6.01316, 3.62304, -53.1736, -6.76316, 3.62304, -53.1736, -7.51316, 3.62304, -51.9236, 1.48684, 3.62304, -48.6736, 0.486839, 3.62304, -51.9236, -0.0131607, 3.62304, -51.1736, -8.26316, 3.62304, -51.9236, -8.76316, 3.62304, -52.9236, -12.5132, 3.62304, -52.1736, -12.7632, 3.62304, -47.4236, -10.2632, 3.62304, -48.1736, -8.01316, 3.62304, -48.1736, -0.0131607, 3.62304, -45.1736, 0.986839, 3.62304, -48.1736, -1.01316, 3.62304, -51.1736, -45.7632, 3.62304, -51.4236, -46.5132, 3.62304, -51.4236, -46.7632, 3.62304, -48.1736, -45.5132, 3.62304, -47.4236, -40.7632, 3.62304, -46.9236, -40.2632, 3.62304, -52.1736, -45.0132, 3.62304, -52.9236, -13.5132, 3.62304, -52.1736, -14.0132, 3.62304, -52.9236, -16.7632, 3.62304, -52.9236, -18.5132, 3.62304, -47.4236, -23.5132, 3.62304, -51.9236, -25.5132, 3.62304, -52.1736, -25.7632, 3.62304, -47.1736, -20.5132, 3.62304, -46.6736, -20.2632, 3.62304, -47.1736, -22.7632, 3.62304, -52.4236, -36.0132, 3.62304, -52.4236, -38.2632, 3.62304, -52.6736, -38.5132, 3.62304, -52.1736, -36.7632, 3.62304, -46.1736, -35.7632, 3.62304, -46.9236, -29.5132, 3.62304, -52.6736, 38.2368, 3.87304, -51.6736, 37.4868, 3.87304, -51.4236, 36.9868, 3.87304, -50.4236, 38.2368, 4.12304, -48.1736, 43.2368, 4.12304, -48.9236, 43.2368, 3.87304, -52.4236, 44.9868, 4.12304, -48.9236, 45.4868, 4.12304, -48.4236, 45.9868, 3.87304, -51.1736, 45.7368, 4.12304, -49.7986, 44.9868, 3.87304, -52.4236, 36.9868, 3.62304, -46.9236, 46.9868, 3.62304, -48.1736, 51.4868, 3.62304, -48.6736, 52.9868, 3.62304, -49.1736, 52.4868, 3.62304, -52.1736, 47.7368, 3.62304, -46.9236, 51.4868, 3.62304, -47.4236, -49.5132, 3.62304, -51.9236, -50.2632, 3.62304, -50.9236, -48.0132, 3.62304, -47.6736, -52.5132, 3.62304, -50.9236, -51.5132, 3.87304, -42.1736, -47.5132, 4.12304, -42.4236, -47.6382, 3.62304, -43.7361, -12.2632, 0.37304, -50.4236, -12.2632, 0.37304, -49.9236, -9.01316, 0.37304, -50.4236, -9.26316, 0.37304, -51.4236, 13.7368, 0.37304, -50.9236, 13.7368, 0.37304, -51.4236, 12.9868, 0.37304, -51.4236, 9.98684, 0.37304, -50.4236, 9.98684, 0.37304, -49.9236, 10.9868, 0.37304, -49.9236, 29.2368, 0.37304, -50.9236, 29.2368, 0.37304, -51.4236, 28.7368, 0.37304, -51.4236, 25.2368, 0.37304, -50.4236, 25.4868, 0.37304, -49.6736, 28.9868, 0.37304, -50.4236, -99.2632, 0.37304, -48.9236, -99.7632, 0.37304, -48.6736, -99.5132, 0.37304, -47.1736, -53.5132, 0.37304, -47.1736, -19.0132, 0.37304, -50.4236, -19.0132, 0.37304, -49.9236, -15.7632, 0.37304, -49.9236, -15.7632, 0.37304, -50.9236, -3.26316, 0.37304, -49.4236, -1.76316, 0.37304, -48.6736, -2.01316, 0.37304, -49.6736, -7.01316, 0.37304, -50.9236, -6.51316, 0.37304, -49.9236, 39.2368, 0.37304, -49.4236, 43.9868, 0.37304, -50.1736, 43.7368, 0.37304, -50.9236, 39.2368, 0.37304, -50.4236, -28.5132, 0.37304, -49.6736, -28.5132, 0.37304, -50.4236, -29.0132, 0.37304, -50.6736, -30.0132, 0.37304, -50.4236, -30.0132, 0.37304, -49.9236, -34.2632, 0.37304, -50.4236, -34.2632, 0.37304, -49.6736, -31.7632, 0.37304, -49.9236, -31.7632, 0.37304, -50.4236, -40.5132, 0.37304, -49.4236, -39.5132, 0.37304, -49.4236, -39.5132, 0.37304, -50.1736, -40.7632, 0.37304, -50.1736, -37.5132, 0.37304, -50.1736, -37.5132, 0.37304, -49.6736, -36.5132, 0.37304, -49.6736, -36.5132, 0.37304, -50.1736, -26.2632, 0.37304, -49.6736, -24.0132, 0.37304, -49.4236, -24.0132, 0.37304, -50.1736, -26.2632, 0.37304, -50.1736, -22.0132, 0.37304, -49.4236, -21.2632, 0.37304, -49.4236, -21.2632, 0.37304, -50.1736, -22.2632, 0.37304, -50.1736, 35.4868, 0.37304, -49.4236, 36.4868, 0.37304, -48.9236, 36.2368, 0.37304, -49.4236, 52.9868, 0.62304, -47.9236, 53.9868, 0.37304, -43.4236, 99.4868, 0.37304, -43.9236, 99.4868, 0.37304, -45.4236, -24.5132, 0.37304, -47.4236, -23.7632, 0.37304, -46.9236, -23.7632, 0.37304, -47.4236, 44.7368, 0.37304, -46.6736, 44.4868, 0.37304, -47.4236, 42.9868, 0.37304, -47.4236, 14.4868, 0.87304, -11.9236, 15.2368, 0.62304, -11.6736, 16.4868, 0.62304, -12.6736, 7.48684, 0.37304, -19.4236, 6.98684, 0.37304, -19.1736, 6.48684, 0.37304, -17.4236, 13.344, 0.37304, -12.7094, 46.2368, 0.37304, -40.9236, 37.9868, 0.37304, -45.6736, 36.2368, 0.37304, -45.6736, 45.4868, 0.37304, -27.9236, 47.4868, 0.37304, -33.6736, 46.2368, 0.37304, -43.6736, 46.9868, 0.37304, -43.9236, 46.4868, 0.37304, -46.6736, 31.4868, 0.37304, -46.9236, 21.4868, 0.37304, -45.1736, 38.2368, 0.37304, -46.6736, 7.23684, 0.37304, -20.9236, 18.7368, 0.37304, -11.9236, 20.9868, 0.37304, -46.1736, 15.4868, 0.37304, -47.4236, 7.48684, 0.37304, -45.1736, 13.9868, 0.37304, -32.4105, 14.7368, 0.87304, -33.6868, 15.4868, 0.37304, -34.9631, 13.4973, 0.37304, -35.9482, 45.4868, 0.37304, -19.1736, 45.4868, 0.37304, -26.9236, 11.9703, 0.87304, -19.4393, 16.468, 0.37304, -32.9268, 12, 0.37304, -20.931, 13.5055, 0.37304, -17.9086, 46.7368, 0.37304, -26.4236, 51.4868, 3.62304, -42.6736, 52.4868, 3.62304, -43.1736, 52.4868, 3.62304, -44.1736, 48.2368, 3.62304, -43.1736, 47.4868, 3.62304, -42.6736, 47.4868, 3.62304, -41.4236, 48.2368, 3.62304, -37.1736, 51.9868, 3.62304, -37.6736, -52.5132, 0.37304, -40.1736, -52.5132, 0.37304, -40.9236, -53.0132, 0.37304, -41.1736, -53.7632, 0.37304, -37.1736, -100.763, 0.37304, -43.4236, -101.263, 0.37304, -43.1736, -101.013, 0.37304, -42.1736, -100.513, 0.37304, -46.4236, -102.013, 0.37304, -41.1736, -91.868, 0.37304, -41.2059, -93.3922, 0.87304, -41.3672, -94.9164, 0.37304, -41.5285, -50.2632, 0.37304, -44.6736, -49.7632, 0.37304, -44.4236, -50.0132, 0.37304, -46.6736, -50.5132, 0.37304, -47.1736, 2.48684, 0.87304, -21.1736, 3.23684, 0.62304, -20.9236, 3.48684, 0.37304, -21.4236, 5.23684, 0.37304, -21.9236, 5.73684, 0.37304, -21.1736, 6.98684, 0.37304, -21.4236, -0.263161, 0.37304, -43.9236, -0.263161, 0.37304, -22.4236, 1.57018, 0.37304, -21.5903, 6.48684, 0.37304, -45.9236, 1.98684, 0.37304, -47.1736, 1.23684, 0.37304, -44.4236, -6.76316, 0.37304, -46.1736, -9.76316, 0.62304, -46.9236, -13.7632, 0.37304, -45.9236, -13.7632, 0.37304, -25.1736, -13.7632, 0.37304, -24.9236, -46.5132, 0.37304, -46.4236, -46.2632, 0.37304, -43.1736, -45.0132, 0.37304, -42.9236, -37.2632, 0.37304, -44.9236, -44.7632, 0.37304, -41.6736, -46.7632, 0.37304, -36.4236, -45.2632, 0.37304, -35.6736, -45.2632, 0.37304, -34.1736, -35.2632, 0.37304, -44.9236, -24.7632, 0.37304, -45.9236, -35.0132, 0.37304, -45.9236, -20.0132, 0.37304, -45.4236, -47.5132, 0.62304, -27.4236, -47.5132, 0.37304, -24.6736, -19.0132, 0.37304, -24.6736, -18.7632, 0.37304, -25.1736, -15.2632, 0.37304, -46.4236, -19.5132, 0.37304, -45.9236, -15.7632, 0.37304, -25.9236, -14.7632, 0.37304, -25.9236, -13.7632, 0.37304, -22.4236, 54.2368, 0.37304, -31.1736, 54.2368, 0.37304, -30.4236, 55.7368, 0.37304, -30.1736, 100.237, 0.37304, -43.1736, 55.9868, 0.37304, -36.6736, 106.737, 0.37304, -21.1736, 107.237, 0.37304, -21.4236, 107.237, 0.37304, -23.1736, 106.487, 0.37304, -23.6736, 103.987, 0.37304, -45.4236, 102.237, 0.37304, -45.1736, 102.487, 0.37304, -43.9236, 103.487, 0.37304, -16.9236, 106.487, 0.37304, -24.6736, 100.737, 0.37304, -15.1736, 106.487, 0.37304, -45.1736, 104.237, 0.37304, -45.9236, 110.237, 0.37304, -28.9236, 110.737, 0.37304, -29.4236, 110.737, 0.37304, -31.4236, 108.237, 0.37304, -31.4236, 109.737, 0.37304, -38.1736, 110.487, 0.37304, -39.6736, 110.237, 0.37304, -40.9236, 107.737, 0.37304, -40.9236, 101.987, 0.37304, -43.1736, 106.487, 0.37304, -41.9236, 106.237, 0.37304, -16.9236, 102.737, 0.37304, -15.1736, 106.987, 0.37304, -32.4236, 54.2368, 0.37304, -21.6736, 107.737, 0.37304, -34.9236, 109.487, 0.37304, -34.9236, 107.987, 0.37304, -25.4236, 109.737, 0.37304, -25.4236, 107.237, 0.37304, -34.4236, 53.9868, 0.37304, -42.1736, 52.9868, 0.37304, -41.9236, 53.2368, 0.62304, -38.1736, -46.5132, 4.12304, -41.1736, -46.5132, 4.12304, -41.9236, -51.0132, 3.87304, -40.9236, -52.7632, 3.87304, -36.1736, -51.0132, 3.87304, -35.4236, -48.2632, 4.12304, -35.9236, -106.013, 0.37304, -38.9236, -107.263, 0.37304, -38.4236, -107.013, 0.37304, -36.9236, -108.013, 0.37304, -36.4236, -108.263, 0.37304, -34.9236, -108.263, 0.37304, -31.4236, -105.513, 0.37304, -30.6736, -105.513, 0.37304, -40.4236, -104.013, 0.37304, -29.4236, -54.0132, 0.37304, -35.1736, -53.7632, 0.37304, -28.6736, -52.0132, 0.37304, -34.4236, -104.013, 0.37304, -28.6736, -48.5132, 0.37304, -40.4236, -48.5132, 0.37304, -41.1736, -49.5132, 0.37304, -41.1736, -50.7632, 0.37304, -37.6736, -50.2632, 0.37304, -37.1736, -49.2632, 0.37304, -37.4236, 49.2368, 0.37304, -41.1736, 49.7368, 0.37304, -38.9236, 49.7368, 0.37304, -41.1736, 52.9868, 3.62304, -36.4236, 48.7368, 3.62304, -33.1736, 47.7368, 3.62304, -30.1736, 52.7368, 3.62304, -30.1736, 54.4868, 3.62304, -36.1736, -52.5132, 3.87304, -28.9236, -52.5132, 3.87304, -28.1736, -51.5132, 3.87304, -27.9236, -47.0132, 4.12304, -33.9236, -47.0132, 4.12304, -34.9236, -50.7632, 3.87304, -34.1736, -49.0132, 4.12304, -27.9236, 51.2368, 0.37304, -33.4236, 51.2368, 0.37304, -33.9236, 50.7368, 0.37304, -33.9236, 50.2368, 0.37304, -32.6736, 50.4868, 0.37304, -31.1736, -48.7632, 0.37304, -32.9236, -48.7632, 0.37304, -33.4236, -49.7632, 0.37304, -33.1736, -49.2632, 0.37304, -32.4236, -51.0132, 0.37304, -28.9236, -50.7632, 0.37304, -28.1736, -50.0132, 0.37304, -28.1736, 54.2368, 3.62304, -28.4236, 54.2368, 3.62304, -29.1736, 53.2368, 3.62304, -29.4236, 46.9868, 3.62304, -27.6736, 47.9868, 3.62304, -26.9236, 48.9868, 3.62304, -18.9236, 48.7368, 3.62304, -17.1736, 51.9868, 3.62304, -16.6736, 48.2368, 3.62304, -19.6736, 47.9868, 3.62304, -25.1736, 46.7368, 3.62304, -19.9236, -104.763, 0.37304, -27.4236, -105.263, 0.37304, -27.4236, -105.013, 0.37304, -26.1736, -106.263, 0.37304, -25.4236, -106.513, 0.37304, -20.6736, -103.513, 0.37304, -19.6736, -53.0132, 0.37304, -16.6736, -52.2632, 0.37304, -20.9236, -100.513, 0.37304, -16.6736, -51.2632, 4.12304, -26.9236, -51.7632, 3.62304, -26.6736, -51.7632, 3.62304, -24.9236, -49.0132, 3.62304, -24.9236, -48.0132, 3.87304, -19.1736, -48.7632, 3.87304, -19.9236, -51.0132, 3.87304, -18.9236, -52.2632, 3.87304, -13.4236, -51.0132, 3.87304, -13.4236, -50.5132, 3.87304, -12.6736, -48.0132, 3.87304, -12.9236, -47.0132, 3.87304, -19.1736, -48.2632, 3.62304, -21.6736, 53.2368, 0.37304, -26.6736, 53.2368, 0.37304, -24.6736, 53.7368, 0.37304, -25.9236, 53.7368, 0.62304, -27.4236, 49.9868, 0.37304, -25.4236, 49.4868, 0.37304, -22.6736, 49.4868, 0.37304, -21.9236, 50.2368, 0.37304, -21.9236, 50.7368, 0.37304, -24.6736, 50.7368, 0.37304, -25.1736, -47.0132, 0.37304, -20.4236, -45.7632, 0.37304, -20.1736, -18.5132, 0.37304, -21.4236, -17.5132, 3.62304, -24.1736, -17.5132, 3.62304, -22.6736, -15.0132, 3.62304, -22.6736, -15.0132, 3.62304, -24.4236, 1.98684, 0.37304, -16.1736, 2.23684, 0.37304, -16.6736, 0.986839, 0.37304, -17.1736, 0.736839, 0.37304, -15.9236, -14.0132, 0.37304, -21.4236, -13.7632, 0.37304, -15.1736, 0.986839, 0.37304, -18.4236, 1.73684, 0.37304, -18.9236, 1.73684, 0.37304, -20.4236, -16.0132, 0.37304, -15.6736, -15.0132, 0.37304, -14.9236, -16.0132, 0.37304, -21.1736, 0.736839, 0.37304, -14.6736, -13.5132, 0.37304, -14.1736, 53.2368, 0.37304, -15.1736, 55.9868, 0.37304, -14.4236, 56.4868, 0.37304, -12.9236, 99.9868, 0.37304, -14.6736, 99.4868, 0.37304, -12.1736, -20.0132, 0.37304, -11.4236, -19.0132, 0.37304, -12.6736, -19.0132, 0.37304, -14.6736, -45.5132, 0.37304, -18.6736, -46.7632, 0.37304, -13.4236, -45.5132, 0.37304, -12.9236, -18.0132, 0.87304, -15.007, -45.5132, 0.37304, -11.6736, -28.4927, 0.87304, -14.9421, -27.0077, 0.37304, -14.9311, -27.0178, 0.37304, -16.4471, -33.0078, 0.37304, -16.441, 3.48684, 5.12304, -19.6736, 3.48684, 5.12304, -18.4236, 4.73684, 5.12304, -18.4236, 4.73684, 5.12304, -19.6736, -49.2632, 0.62304, -18.1736, -48.7632, 0.37304, -18.1736, -48.7632, 0.37304, -18.9236, -49.7632, 0.37304, -19.1736, -50.7632, 0.37304, -14.6736, -49.5132, 0.37304, -14.1736, -49.5132, 0.62304, -17.4236, 46.7368, 0.37304, -13.6736, 47.4868, 0.37304, -18.4236, 45.7368, 0.37304, -18.6736, 19.2368, 0.37304, -11.6736, 19.2368, 0.37304, -10.9236, 46.9868, 0.37304, -10.9236, 2.98684, 0.37304, -15.6736, 5.48684, 0.37304, -16.6736, 12.7368, 0.37304, -9.67364, 13.9868, 0.62304, -9.92364, 13.7368, 0.37304, -10.9236, 2.98684, 0.37304, -14.4236, 2.23684, 0.37304, -13.9236, 12.4868, 0.37304, -9.17364, 51.9868, 3.62304, -14.4236, 47.9868, 3.62304, -13.1736, 48.7368, 3.62304, -12.4236, 48.7368, 3.62304, -11.6736, 52.9868, 3.62304, -7.67364, 54.9868, 3.62304, -13.1736, 47.9868, 3.62304, -4.42364, 52.9868, 3.62304, -4.42364, 46.4868, 3.62304, -5.67364, -53.2632, 0.37304, -15.6736, -100.763, 0.37304, -15.9236, -52.0132, 0.37304, -11.1736, -52.0132, 0.37304, -12.1736, -53.7632, 0.37304, -12.6736, -101.263, 0.37304, -14.6736, -102.513, 0.37304, -13.9236, -102.263, 0.37304, -12.4236, -103.263, 0.37304, -11.9236, -103.513, 0.37304, -6.17364, -53.5132, 0.37304, -5.17364, -104.013, 0.37304, -5.42364, -8.01316, 0.37304, -4.17364, -7.51316, 0.37304, -3.92364, -7.01316, 0.37304, -4.92364, -8.26316, 0.37304, -5.92364, -14.7632, 0.37304, -10.6736, -14.5132, 0.37304, -10.1736, -13.5132, 0.37304, -10.1736, -13.2632, 0.37304, -13.1736, -4.76316, 0.37304, -5.67364, -3.76316, 0.37304, -5.67364, -12.0132, 0.37304, -8.92364, -3.51316, 0.37304, -4.92364, -2.01316, 0.37304, -5.17364, -5.76316, 0.62304, -4.92364, -1.76316, 0.37304, -4.67364, 1.23684, 0.37304, -13.9236, -12.0132, 0.37304, -7.67364, -12.7632, 0.37304, -6.67364, -10.0132, 0.37304, -5.42364, -9.76316, 0.37304, -5.92364, 12.2368, 0.37304, 0.0763626, 13.2368, 0.37304, 0.326363, 13.4868, 0.37304, -7.42364, 12.4868, 0.37304, -7.92364, -2.26316, 0.37304, -2.17364, 11.4868, 0.37304, 0.826363, -17.2632, 5.12304, -13.4236, -17.2632, 5.12304, -12.1736, -16.0132, 5.12304, -12.1736, -16.0132, 5.12304, -13.4236, -47.0132, 3.87304, -11.1736, -47.0132, 3.87304, -12.1736, -52.2632, 3.87304, -5.92364, -48.2632, 3.87304, -7.42364, -51.7632, 3.87304, -5.42364, -48.2632, 3.87304, -6.17364, -52.0132, 3.62304, 3.07636, -48.7632, 3.62304, 3.32636, -47.7632, 3.62304, 2.57636, -47.7632, 3.87304, -5.92364, 106.487, 0.37304, -11.1736, 106.487, 0.37304, -11.9236, 104.487, 0.37304, -12.4236, 102.737, 0.37304, -11.4236, 102.737, 0.37304, -10.4236, 104.987, 0.37304, 8.82636, 106.237, 0.37304, 1.57636, 106.237, 0.37304, -0.423637, 106.737, 0.37304, -8.42364, 106.987, 0.37304, -10.9236, 101.987, 0.37304, -9.67364, 109.987, 0.37304, -4.67364, 110.737, 0.37304, -5.92364, 110.487, 0.37304, -7.42364, 107.987, 0.37304, -7.42364, 108.487, 0.37304, 5.07636, 109.237, 0.37304, 3.82636, 108.987, 0.37304, 2.07636, 106.737, 0.37304, 2.07636, 99.9868, 0.37304, -9.67364, 99.4868, 0.37304, -10.1736, 54.4868, 0.37304, -7.67364, 53.9868, 0.37304, 8.82636, 107.487, 0.37304, -1.42364, 109.737, 0.37304, -1.42364, 105.487, 0.37304, 8.32636, 108.237, 0.37304, 8.07636, 53.4868, 0.37304, 3.32636, -50.5132, 0.37304, -7.67364, -50.5132, 0.37304, -6.92364, -49.7632, 0.37304, -6.92364, -48.7632, 0.37304, -10.6736, -48.7632, 0.37304, -11.4236, -49.5132, 0.37304, -11.6736, -8.26316, 0.37304, -3.67364, -7.26316, 0.62304, -3.17364, -28.7632, 0.37304, 46.5764, -22.5132, 0.37304, 46.8264, -15.0132, 0.37304, 45.3264, -8.26316, 0.37304, -2.17364, -9.76316, 0.37304, -3.67364, -15.7632, 0.37304, -8.92364, -18.7632, 0.37304, -10.4236, -46.7632, 0.37304, -7.67364, -46.5132, 0.37304, 3.57636, -45.7632, 0.37304, 8.32636, -8.26316, 0.37304, -1.17364, -13.2632, 0.37304, -6.17364, -10.2632, 0.37304, -4.42364, -45.7632, 0.37304, 23.0764, -47.2632, 0.37304, 32.0764, -46.0132, 0.37304, 40.0764, -37.2632, 0.37304, 46.0764, -45.7632, 0.37304, 9.32636, -14.5132, 0.37304, -6.17364, -16.0132, 0.37304, -7.42364, -19.7632, 0.37304, -10.6736, -47.2632, 0.37304, 3.82636, -46.7632, 0.37304, 17.8264, -47.5132, 0.37304, 18.0764, -23.0132, 0.37304, 48.3264, -16.0132, 0.37304, 47.5764, -47.0132, 0.37304, 39.5764, -37.0132, 0.37304, 47.3264, -46.7632, 0.37304, 9.82636, -47.0132, 0.37304, 47.0764, -13.0132, 0.37304, 45.5764, -10.5317, 0.87304, 0.0527725, -12.0114, 0.37304, 1.59449, -13.5029, 0.87304, 21.0637, -15.0379, 0.37304, 21.0527, -13.5326, 0.37304, 22.5554, -13.5231, 0.37304, 19.572, -10.537, 0.37304, -1.39993, 53.4868, 0.37304, -10.1736, 53.7368, 0.37304, -9.17364, 54.2368, 0.37304, -10.6736, 53.9868, 0.37304, -11.4236, 50.7368, 0.37304, -9.67364, 51.4868, 0.37304, -10.9236, 50.4868, 0.37304, -11.1736, 49.7368, 0.37304, -8.17364, 50.4868, 0.37304, -7.67364, 19.4868, 0.37304, -9.92364, 18.9868, 0.37304, -9.67364, 18.9868, 0.37304, -8.67364, 45.2368, 0.37304, -5.92364, 17.7368, 0.37304, -7.42364, 15.4868, 5.12304, -10.4236, 15.4868, 5.12304, -9.17364, 16.7368, 5.12304, -9.17364, 16.7368, 5.12304, -10.4236, -14.5132, 2.62304, -8.42364, -14.2632, 2.62304, -7.67364, -13.5132, 2.62304, -7.92364, -13.7632, 2.62304, -8.67364, 45.9868, 0.37304, 2.32636, 46.7368, 0.37304, -3.92364, 44.9868, 0.37304, -4.67364, 14.4868, 0.37304, 1.57636, 14.4868, 0.37304, 2.32636, -105.263, 0.37304, -4.42364, -105.013, 0.37304, -3.17364, -102.763, 0.37304, 3.57636, -101.513, 0.37304, 5.57636, -99.5132, 0.37304, 5.57636, -106.013, 0.37304, -2.42364, -106.263, 0.37304, 2.32636, -54.7632, 0.37304, 3.07636, -53.2632, 0.37304, 2.32636, -53.0132, 0.37304, -4.67364, -54.5132, 0.37304, 4.82636, 47.2368, 3.62304, 2.32636, 47.7368, 3.62304, 6.07636, 52.2368, 3.62304, 6.07636, -5.76316, 5.12304, -3.42364, -5.76316, 5.12304, -2.17364, -4.51316, 5.12304, -2.17364, -4.51316, 5.12304, -3.42364, 49.7368, 0.37304, -0.923637, 50.2368, 0.37304, -1.17364, 50.4868, 0.37304, -2.42364, 49.7368, 0.37304, -2.92364, 10.4868, 0.37304, 1.82636, -3.26316, 0.37304, -0.673637, -50.2632, 0.37304, -1.92364, -50.2632, 0.37304, 1.32636, -49.7632, 0.37304, 1.32636, -8.76316, 0.37304, 46.8264, -8.51316, 0.37304, 45.8264, -7.26316, 0.37304, 45.3264, -8.01316, 0.37304, -0.673637, -9.033, 0.87304, 31.5942, -9.00303, 0.37304, 30.0722, -9.01298, 0.37304, 33.0662, -6.76316, 0.37304, -0.423637, -2.01316, 0.37304, 47.3264, -1.26316, 0.37304, 47.0764, -1.01316, 0.37304, 45.5764, 4.73684, 0.37304, 46.5764, 10.4868, 0.37304, 2.32636, -2.99284, 0.87304, 6.09801, -3.02773, 0.37304, 7.5969, -2.99296, 0.37304, 4.58814, -1.50782, 0.37304, 6.059, 11.9868, 2.37304, 2.07636, 12.2368, 2.37304, 2.57636, 12.9868, 2.62304, 2.32636, 12.7368, 2.62304, 1.57636, 11.9868, 0.37304, 4.07636, 12.7368, 0.37304, 47.5764, 12.4868, 0.37304, 4.07636, 45.2368, 0.37304, 7.82636, 46.4868, 0.37304, 7.07636, 13.7368, 0.37304, 3.32636, -53.2632, 3.62304, 3.82636, -53.2632, 3.62304, 4.57636, -51.5132, 3.62304, 9.82636, -49.7632, 3.62304, 9.82636, -49.2632, 3.62304, 9.07636, -47.2632, 3.62304, 8.57636, 13.7368, 0.37304, 47.8264, 13.9868, 0.37304, 47.3264, 35.7368, 0.37304, 47.5764, 37.7368, 0.37304, 47.5764, 38.2368, 0.62304, 46.3264, 30.9868, 0.37304, 46.0764, 31.2368, 0.37304, 47.0764, 47.4868, 0.37304, 29.8264, 47.9868, 0.37304, 25.5764, 47.4868, 0.37304, 25.3264, 46.4868, 0.37304, 46.3264, 47.7368, 0.37304, 41.8264, 46.4868, 0.37304, 41.5764, 44.7368, 0.37304, 46.0764, 22.7368, 0.37304, 46.5764, 28.4868, 0.37304, 46.0764, 45.9868, 0.37304, 30.0764, 46.9868, 0.37304, 19.3264, 39.7368, 0.37304, 46.3264, 44.2368, 0.37304, 48.0764, 25.4868, 0.37304, 46.8264, 47.4868, 0.37304, 20.0764, 31.4626, 0.87304, 10.5606, 31.4978, 0.37304, 12.0913, 21.0117, 0.87304, 37.5731, 21.0066, 0.37304, 36.0803, 22.4968, 0.37304, 37.5841, 28.5073, 0.37304, 9.05788, 32.9976, 0.37304, 10.5716, 19.4619, 0.37304, 39.083, -51.7632, 0.37304, 3.82636, -51.2632, 0.37304, 5.32636, -50.7632, 0.37304, 4.57636, -50.7632, 0.37304, 3.82636, 49.2368, 0.37304, 5.57636, 49.9868, 0.37304, 6.57636, 49.9868, 0.37304, 4.07636, 49.2368, 0.37304, 4.07636, -52.7632, 0.37304, 10.8264, -52.7632, 0.37304, 9.57636, -55.0132, 0.37304, 18.3264, -53.2632, 0.37304, 17.3264, -99.2632, 0.37304, 11.8264, -67.532, 0.87304, 10.5732, -69.0171, 0.37304, 10.5622, -65.997, 0.37304, 10.5842, -99.5132, 0.37304, 18.0764, 47.7368, 3.62304, 7.82636, 46.7368, 3.62304, 8.32636, 46.7368, 3.62304, 9.57636, 51.7368, 3.62304, 11.0764, 51.7368, 3.62304, 9.82636, 52.7368, 3.62304, 9.32636, 51.4868, 3.62304, 18.3264, 52.9868, 3.62304, 17.5764, 48.2368, 3.62304, 18.8264, 48.7368, 3.62304, 19.5764, 51.4868, 3.62304, 24.3264, 48.7368, 3.62304, 24.5764, 49.2368, 3.62304, 24.8264, 48.9868, 3.62304, 28.5764, 51.4868, 3.62304, 29.3264, 52.2368, 3.62304, 24.8264, -50.0132, 0.37304, 7.57636, -50.0132, 0.37304, 8.57636, -49.2632, 0.37304, 8.82636, 104.237, 0.37304, 10.5764, 104.737, 0.37304, 10.3264, 53.9868, 0.37304, 10.3264, 104.737, 0.37304, 14.5764, 104.737, 0.37304, 11.8264, 104.237, 0.37304, 11.5764, 53.2368, 0.37304, 10.5764, 53.9868, 0.37304, 15.0764, -51.2632, 3.87304, 17.5764, -49.0132, 3.87304, 17.8264, -49.0132, 3.87304, 17.3264, -49.2632, 3.62304, 10.8264, -52.0132, 3.62304, 16.5764, -48.0132, 3.62304, 16.8264, -47.7632, 3.62304, 11.0764, 49.7368, 0.37304, 16.3264, 49.9868, 0.37304, 16.8264, 50.7368, 0.37304, 16.8264, 49.7368, 0.37304, 14.3264, 49.4868, 0.37304, 10.8264, 48.7368, 0.37304, 11.0764, -49.5132, 0.37304, 11.8264, -49.5132, 0.37304, 11.3264, -50.2632, 0.37304, 11.3264, -50.0132, 0.37304, 12.8264, -50.2632, 0.37304, 16.0764, -49.7632, 0.37304, 16.0764, 107.737, 0.37304, 17.5764, 108.237, 0.37304, 17.3264, 108.237, 0.37304, 15.3264, 105.737, 0.37304, 15.3264, 102.987, 0.37304, 24.8264, 104.487, 0.37304, 21.5764, 54.2368, 0.37304, 18.5764, 107.237, 0.37304, 21.3264, 52.7368, 0.62304, 19.0764, 52.7368, 0.37304, 23.5764, 53.4868, 0.37304, 24.0764, 53.4868, 0.37304, 24.8264, -51.7632, 3.87304, 18.3264, -53.0132, 4.12304, 18.5764, -53.2632, 4.12304, 19.3264, -51.2632, 4.12304, 24.0764, -51.0132, 4.12304, 25.8264, -47.5132, 3.62304, 25.8264, -47.0132, 3.87304, 23.3264, -99.7632, 0.37304, 20.0764, -100.763, 0.37304, 20.5764, -100.513, 0.37304, 21.5764, -104.263, 0.37304, 24.3264, -105.513, 0.37304, 25.0764, -105.263, 0.37304, 26.3264, -101.513, 0.37304, 22.5764, -103.763, 0.37304, 22.8264, -104.013, 0.37304, 32.5764, -103.263, 0.37304, 33.5764, -106.263, 0.37304, 27.0764, -106.513, 0.37304, 31.8264, -53.0132, 0.37304, 33.5764, -53.2632, 0.37304, 30.8264, -52.2632, 0.37304, 25.8264, -50.7632, 0.37304, 22.3264, -49.2632, 0.37304, 22.8264, -50.7632, 0.37304, 18.3264, -52.0132, 0.37304, 18.8264, 102.737, 0.37304, 26.5764, 103.237, 0.37304, 26.3264, 52.9868, 0.37304, 27.8264, 53.2368, 0.37304, 29.5764, 102.237, 0.37304, 29.5764, 50.2368, 0.37304, 25.5764, 50.2368, 0.37304, 26.5764, 50.7368, 0.37304, 25.8264, -52.0132, 3.62304, 31.3264, -48.7632, 3.62304, 33.0764, -51.2632, 3.62304, 40.0764, -48.2632, 3.62304, 40.0764, 48.7368, 3.62304, 30.5764, 52.4868, 3.62304, 32.5764, 47.9868, 3.62304, 46.0764, 50.9868, 3.62304, 47.0764, 51.4868, 3.62304, 47.0764, 52.9868, 3.62304, 42.3264, 52.2368, 3.62304, 41.3264, 49.2368, 3.62304, 42.3264, 49.2368, 3.62304, 40.8264, 52.2368, 3.62304, 40.5764, 52.7368, 3.62304, 40.3264, 47.2368, 3.62304, 31.0764, 47.7368, 3.62304, 40.3264, 102.737, 0.37304, 33.0764, 102.737, 0.37304, 30.3264, 53.7368, 0.37304, 33.3264, 99.9868, 0.37304, 41.5764, 101.737, 0.37304, 41.8264, 102.487, 0.37304, 39.8264, 103.737, 0.37304, 33.8264, 105.237, 0.37304, 39.8264, 106.237, 0.37304, 33.5764, 54.4868, 0.37304, 41.0764, 54.4868, 0.37304, 41.8264, 99.4868, 0.37304, 42.5764, 53.9868, 0.37304, 40.8264, -104.013, 0.37304, 36.0764, -105.263, 0.37304, 36.5764, -105.013, 0.37304, 38.0764, -106.013, 0.37304, 38.5764, -106.513, 0.37304, 44.8264, -53.2632, 0.37304, 45.5764, -52.5132, 0.37304, 42.0764, 49.7368, 0.37304, 39.3264, 50.4868, 0.37304, 39.5764, 49.9868, 0.37304, 33.5764, 49.2368, 0.37304, 33.5764, -49.0132, 3.87304, 48.5764, -49.7632, 3.87304, 48.5764, -49.5132, 3.87304, 52.0764, -47.2632, 3.62304, 40.8264, -52.0132, 3.62304, 46.5764, -49.0132, 3.62304, 46.8264, -45.2632, 3.87304, 51.8264, -45.2632, 3.87304, 48.3264, -47.7632, 3.87304, 48.3264, -48.5132, 3.62304, 47.3264, -48.2632, 3.87304, 47.8264, -50.0132, 0.37304, 41.3264, -50.0132, 0.37304, 44.5764, -49.2632, 0.37304, 41.8264, -49.2632, 0.37304, 41.3264, 50.7368, 0.37304, 41.5764, 50.7368, 0.37304, 42.3264, 51.2368, 0.37304, 42.0764, 52.4868, 0.37304, 47.8264, 54.2368, 0.37304, 48.0764, 54.9868, 0.37304, 49.0764, 99.4868, 0.37304, 49.0764, -107.763, 0.37304, 45.5764, -107.513, 0.37304, 46.8264, -106.013, 0.37304, 53.3264, -104.763, 0.37304, 54.3264, -101.763, 0.37304, 62.5764, -101.263, 0.37304, 63.8264, -99.7632, 0.37304, 63.8264, -103.013, 0.37304, 61.5764, -104.763, 0.37304, 55.3264, -105.763, 0.37304, 55.8264, -106.013, 0.37304, 60.5764, -108.763, 0.37304, 47.5764, -108.763, 0.37304, 52.5764, -50.7632, 0.37304, 52.8264, -51.0132, 0.37304, 48.0764, -53.2632, 0.37304, 47.3264, -63.5132, 0.37304, 65.0764, -52.2632, 0.37304, 55.5764, -99.2632, 0.37304, 65.0764, -8.26316, 3.62304, 54.5764, -7.26316, 3.62304, 54.5764, -6.51316, 3.62304, 52.5764, -16.2632, 3.62304, 49.0764, -16.7632, 3.62304, 52.3264, -14.5132, 3.62304, 52.3264, -14.7632, 3.62304, 48.5764, -8.01316, 3.62304, 48.3264, -9.26316, 3.62304, 48.3264, -2.76316, 3.62304, 48.3264, -7.26316, 3.62304, 46.8264, -3.76316, 3.62304, 53.0764, -14.2632, 3.62304, 46.5764, 0.236839, 3.62304, 54.5764, 1.23684, 3.62304, 54.5764, 1.98684, 3.62304, 52.8264, -0.763161, 3.62304, 48.8264, 9.73684, 3.62304, 51.5764, 11.2368, 3.62304, 51.5764, 12.2368, 3.62304, 49.0764, 5.73684, 3.62304, 48.3264, 0.236839, 3.62304, 46.8264, 8.98684, 3.62304, 54.3264, 9.23684, 3.62304, 52.0764, 31.2368, 3.87304, 51.5764, 32.2368, 3.62304, 48.3264, 30.7368, 3.87304, 48.3264, 23.7368, 3.87304, 48.3264, 22.7368, 3.87304, 51.5764, 23.2368, 3.87304, 52.5764, 29.7368, 3.87304, 47.3264, -44.5132, 3.87304, 51.8264, -36.2632, 3.62304, 51.8264, -36.0132, 3.62304, 48.5764, -37.7632, 3.62304, 48.5764, -44.0132, 3.87304, 52.5764, -38.2632, 3.62304, 47.5764, 42.7368, 3.62304, 53.3264, 43.2368, 3.62304, 53.3264, 43.4868, 3.62304, 52.3264, 44.7368, 3.62304, 51.5764, 44.2368, 3.62304, 49.3264, 39.7368, 3.62304, 52.0764, 39.2368, 3.62304, 47.5764, 38.2368, 3.62304, 48.8264, 36.2368, 3.62304, 52.3264, 45.4868, 3.62304, 48.5764, 52.4868, 3.62304, 53.5764, 53.4868, 3.62304, 49.3264, 45.4868, 3.62304, 47.5764, -21.7632, 3.62304, 48.0764, -21.7632, 3.62304, 49.3264, -22.5132, 3.62304, 51.5764, -23.2632, 3.62304, 49.8264, -28.5132, 3.62304, 51.3264, -29.2632, 3.62304, 47.8264, -23.5132, 3.62304, 53.0764, 102.237, 0.37304, 50.3264, 100.987, 0.37304, 50.3264, 100.487, 0.37304, 50.8264, 107.237, 0.37304, 49.3264, 106.237, 0.37304, 48.0764, 105.237, 0.37304, 47.8264, 103.237, 0.37304, 48.3264, 106.987, 0.37304, 51.8264, 101.237, 0.37304, 62.5764, 103.237, 0.37304, 62.8264, 103.487, 0.37304, 61.0764, 103.987, 0.37304, 60.5764, 106.487, 0.37304, 60.5764, 106.487, 0.37304, 59.3264, 106.987, 0.37304, 58.8264, 107.487, 0.37304, 52.3264, 109.737, 0.37304, 58.8264, 110.737, 0.37304, 52.5764, 20.4868, 3.62304, 48.0764, 17.9868, 3.62304, 51.5764, 15.2368, 3.62304, 52.3264, 24.4868, 0.37304, 49.5764, 24.7368, 0.37304, 50.5764, 28.7368, 0.37304, 49.8264, 28.7368, 0.37304, 48.8264, -41.0132, 0.37304, 49.5764, -39.0132, 0.37304, 50.0764, -39.0132, 0.37304, 49.0764, -10.7632, 0.37304, 50.8264, -10.0132, 0.37304, 50.8264, -9.76316, 0.37304, 50.3264, -13.5132, 0.37304, 49.0764, -14.0132, 0.37304, 50.0764, -13.2632, 0.37304, 50.3264, -14.0132, 0.37304, 49.3264, 99.4868, 0.37304, 50.3264, 62.9868, 0.37304, 63.0764, 99.4868, 0.37304, 64.3264, 53.7368, 0.37304, 54.3264, 52.9868, 0.37304, 54.8264, -49.2632, 0.37304, 50.5764, -45.5132, 0.37304, 50.3264, -45.7632, 0.37304, 49.3264, -49.2632, 0.37304, 49.5764, 19.4868, 0.37304, 49.5764, 21.4868, 0.37304, 49.8264, 21.2368, 0.37304, 49.3264, 45.9868, 0.37304, 49.3264, 46.2368, 0.37304, 50.0764, 47.9868, 0.37304, 50.0764, -43.7632, 0.37304, 50.3264, -42.7632, 0.37304, 50.3264, -42.7632, 0.37304, 49.8264, -44.0132, 0.37304, 49.8264, -4.01316, 0.37304, 50.5764, -2.26316, 0.37304, 50.8264, -2.26316, 0.37304, 50.3264, -4.51316, 0.37304, 49.8264, 3.98684, 0.37304, 50.5764, 5.98684, 0.37304, 50.8264, 5.98684, 0.37304, 50.0764, 3.98684, 0.37304, 49.8264, -18.0132, 0.37304, 50.0764, -16.7632, 0.37304, 50.5764, -16.7632, 0.37304, 50.0764, 35.2368, 0.37304, 50.0764, 36.7368, 0.37304, 50.5764, 36.7368, 0.37304, 50.0764, 51.4868, 0.37304, 50.3264, 50.9868, 0.37304, 50.8264, 51.9868, 0.37304, 51.5764, 52.4868, 0.37304, 51.0764, 52.4868, 0.37304, 50.5764, 41.4868, 0.37304, 50.3264, 43.4868, 0.37304, 51.3264, 43.4868, 0.37304, 50.5764, -31.2632, 0.37304, 99.8264, -29.2632, 0.37304, 100.326, -28.7632, 0.37304, 52.8264, -31.0132, 0.37304, 52.8264, 23.9868, 0.37304, 101.576, 26.4868, 0.37304, 100.826, 26.4868, 0.37304, 99.8264, 22.7368, 0.37304, 100.826, 22.2368, 0.37304, 53.8264, 21.4868, 0.37304, 52.5764, 15.2368, 0.37304, 53.5764, 15.4868, 0.37304, 99.3264, 18.9868, 0.37304, 99.3264, 19.9868, 0.37304, 101.076, 27.7368, 0.37304, 99.3264, 26.9868, 0.37304, 53.3264, -50.5132, 0.37304, 53.3264, -48.7632, 0.37304, 53.3264, -53.2632, 0.37304, 99.5764, -50.7632, 0.37304, 100.576, -48.7632, 0.37304, 100.076, -34.5132, 0.37304, 100.076, -32.7632, 0.37304, 100.326, -32.2632, 0.37304, 99.5764, -34.5132, 0.37304, 53.0764, -22.2632, 0.37304, 103.076, -21.2632, 0.37304, 103.076, -20.7632, 0.37304, 101.826, -25.0132, 0.37304, 102.576, -22.5132, 0.37304, 103.576, -27.7632, 0.37304, 101.326, -27.2632, 0.37304, 102.826, -24.0132, 0.37304, 54.3264, -22.5132, 0.37304, 54.3264, -18.5132, 0.37304, 53.3264, -21.7632, 0.37304, 52.8264, -22.0132, 0.37304, 54.0764, -20.0132, 0.37304, 100.826, -18.7632, 0.37304, 100.826, 10.4868, 0.37304, 52.8264, 9.98684, 0.37304, 55.3264, 8.73684, 0.37304, 99.3264, 8.73684, 0.37304, 55.5764, 38.7368, 0.37304, 53.8264, 30.9868, 0.37304, 52.8264, 37.7368, 0.37304, 99.3264, 38.2368, 0.37304, 100.076, -44.7632, 0.37304, 53.8264, -45.2632, 0.37304, 53.0764, -46.2632, 0.37304, 100.576, -45.3438, 0.37304, 71.9231, -45.247, 0.87304, 68.907, -45.1986, 0.37304, 67.3989, -46.503, 0.87304, 70.5722, -46.518, 0.37304, 72.0832, -46.5231, 0.37304, 67.6002, -45.2632, 0.37304, 101.326, -41.7632, 0.37304, 102.826, -41.5132, 0.37304, 103.326, -40.0132, 0.37304, 103.326, -39.5132, 0.37304, 103.826, -37.7632, 0.37304, 102.826, -37.7632, 0.37304, 101.576, -37.2632, 0.37304, 101.076, -35.7632, 0.37304, 101.076, -44.2632, 0.37304, 103.076, 4.48684, 0.37304, 53.0764, 6.73684, 0.37304, 53.5764, 5.98684, 0.37304, 53.0764, 49.4868, 0.37304, 99.3264, 50.2368, 0.37304, 100.576, 52.2368, 0.37304, 100.326, 52.2368, 0.37304, 54.8264, 44.7368, 0.37304, 53.0764, 44.4868, 0.37304, 54.3264, 43.4868, 0.37304, 54.8264, 45.4868, 0.37304, 99.3264, 44.9868, 0.37304, 100.076, -9.01316, 0.37304, 101.326, -7.76316, 0.37304, 101.576, -6.76316, 0.37304, 100.576, -11.5132, 0.37304, 104.076, -9.76316, 0.37304, 103.076, -9.76316, 0.37304, 102.076, -14.0132, 0.37304, 103.076, -17.2632, 0.37304, 101.576, -16.2632, 0.37304, 103.326, -10.5132, 0.37304, 55.3264, -14.2632, 0.37304, 53.8264, -15.0328, 0.87304, 67.5639, -15.0326, 0.37304, 70.5738, -15.0078, 0.37304, 66.059, -16.513, 0.37304, 61.5662, -6.01316, 0.37304, 100.576, -6.76316, 0.37304, 56.0764, -11.2632, 0.37304, 53.3264, -8.76316, 0.37304, 54.0764, -9.26316, 0.37304, 53.5764, 38.4868, 0.37304, 100.826, 40.7368, 0.37304, 100.576, 42.7368, 0.37304, 101.576, 44.4868, 0.37304, 101.076, 39.9868, 0.37304, 53.5764, -4.76316, 0.37304, 100.576, -4.01316, 0.37304, 99.3264, 1.23684, 0.37304, 56.0764, -5.76316, 0.37304, 53.8264, -6.26316, 0.37304, 55.8264, 1.23684, 0.37304, 99.3264, 2.48684, 0.37304, 54.3264, 2.23684, 0.37304, 55.8264, 54.2368, 0.37304, 100.826, 54.4868, 0.37304, 101.326, 56.2368, 0.37304, 101.076, 63.2368, 0.37304, 104.576, 65.7368, 0.37304, 103.576, 65.7368, 0.37304, 102.576, 61.2368, 0.37304, 103.576, 57.9868, 0.37304, 102.076, 59.2368, 0.37304, 103.826, 66.4868, 0.37304, 102.076, -62.7632, 0.37304, 101.326, -62.5132, 0.37304, 101.826, -61.2632, 0.37304, 102.076, -60.5132, 0.37304, 101.576, -58.5132, 0.37304, 99.5764, -59.5132, 0.37304, 101.826, -66.0132, 0.37304, 100.076, -65.5132, 0.37304, 101.576, -66.7632, 0.37304, 99.5764, 72.2368, 0.37304, 102.576, 73.7368, 0.37304, 102.326, 73.7368, 0.37304, 100.826, 69.2368, 0.37304, 102.076, 74.2368, 0.37304, 100.326, 75.4868, 0.37304, 100.576, 76.7368, 0.37304, 99.5764, 106.487, 0.37304, 73.0764, 105.737, 0.37304, 72.0764, 104.237, 0.37304, 71.5764, 101.987, 0.37304, 72.5764, 101.987, 0.37304, 73.8264, 106.237, 0.37304, 75.5764, 67.7368, 0.37304, 102.576, 71.9868, 0.37304, 103.076, 107.487, 0.37304, 76.5764, 106.737, 0.37304, 76.0764, 101.487, 0.37304, 74.3264, 99.9868, 0.37304, 74.3264, 103.237, 0.37304, 84.5764, 106.237, 0.37304, 82.5764, 100.487, 0.37304, 86.5764, 102.487, 0.37304, 86.5764, 99.4868, 0.37304, 73.8264, 105.487, 0.37304, 84.5764, 79.2368, 0.37304, 99.3264, 89.2368, 0.37304, 99.3264, 99.4868, 0.37304, 87.8264, 99.4868, 0.37304, 99.3264, 108.987, 0.37304, 82.5764, 109.987, 0.37304, 76.5764, -92.5132, 0.37304, 101.826, -92.2632, 0.37304, 102.326, -91.0132, 0.37304, 102.576, -88.7632, 0.37304, 100.576, -88.2632, 0.37304, 100.076, -100.263, 0.37304, 73.0764, -101.513, 0.37304, 73.5764, -101.263, 0.37304, 75.0764, -105.513, 0.37304, 82.5764, -106.763, 0.37304, 83.3264, -106.513, 0.37304, 84.5764, -103.763, 0.37304, 81.0764, -105.013, 0.37304, 81.0764, -107.513, 0.37304, 85.3264, -107.763, 0.37304, 86.5764, -107.763, 0.37304, 90.0764, -104.263, 0.37304, 91.3264, -100.263, 0.37304, 98.5764, -99.5132, 0.37304, 99.3264, -97.7632, 0.37304, 99.3264, -87.0132, 0.37304, 100.076, -85.7632, 0.37304, 99.3264, -99.7632, 0.37304, 94.0764, -88.7632, 0.37304, 101.826, -76.2632, 0.37304, 99.3264, -102.513, 0.37304, 80.3264, -100.763, 0.37304, 93.0764, -103.013, 0.37304, 93.3264, -96.5132, 0.37304, 100.076, -95.2632, 0.37304, 102.076, -99.2632, 0.37304, 71.0764, -102.263, 0.37304, 75.5764)
polygons = [PackedInt32Array(1, 0, 2), PackedInt32Array(2, 0, 3), PackedInt32Array(6, 5, 4), PackedInt32Array(8, 7, 9), PackedInt32Array(9, 7, 12), PackedInt32Array(9, 12, 10), PackedInt32Array(10, 12, 11), PackedInt32Array(10, 13, 9), PackedInt32Array(14, 11, 15), PackedInt32Array(15, 11, 12), PackedInt32Array(15, 12, 16), PackedInt32Array(2, 3, 5), PackedInt32Array(5, 3, 4), PackedInt32Array(16, 18, 17), PackedInt32Array(16, 19, 15), PackedInt32Array(15, 19, 20), PackedInt32Array(20, 19, 4), PackedInt32Array(4, 19, 6), PackedInt32Array(19, 16, 17), PackedInt32Array(19, 21, 6), PackedInt32Array(23, 22, 24), PackedInt32Array(24, 22, 27), PackedInt32Array(24, 27, 26), PackedInt32Array(24, 26, 25), PackedInt32Array(29, 28, 30), PackedInt32Array(30, 28, 26), PackedInt32Array(30, 26, 31), PackedInt32Array(31, 26, 27), PackedInt32Array(34, 33, 32), PackedInt32Array(37, 36, 35), PackedInt32Array(31, 38, 30), PackedInt32Array(32, 39, 34), PackedInt32Array(34, 39, 40), PackedInt32Array(40, 39, 35), PackedInt32Array(35, 39, 41), PackedInt32Array(37, 35, 25), PackedInt32Array(25, 35, 24), PackedInt32Array(24, 35, 41), PackedInt32Array(43, 42, 44), PackedInt32Array(44, 42, 46), PackedInt32Array(44, 46, 45), PackedInt32Array(47, 49, 48), PackedInt32Array(47, 51, 50), PackedInt32Array(53, 52, 54), PackedInt32Array(54, 52, 50), PackedInt32Array(54, 50, 55), PackedInt32Array(54, 55, 45), PackedInt32Array(45, 55, 44), PackedInt32Array(44, 55, 56), PackedInt32Array(57, 49, 52), PackedInt32Array(52, 49, 47), PackedInt32Array(52, 47, 50), PackedInt32Array(59, 58, 60), PackedInt32Array(60, 58, 61), PackedInt32Array(64, 63, 62), PackedInt32Array(66, 65, 67), PackedInt32Array(67, 65, 68), PackedInt32Array(68, 69, 64), PackedInt32Array(62, 41, 64), PackedInt32Array(64, 41, 68), PackedInt32Array(68, 41, 67), PackedInt32Array(67, 41, 39), PackedInt32Array(60, 61, 62), PackedInt32Array(62, 61, 41), PackedInt32Array(71, 70, 72), PackedInt32Array(72, 70, 73), PackedInt32Array(75, 74, 76), PackedInt32Array(76, 74, 78), PackedInt32Array(76, 78, 77), PackedInt32Array(80, 79, 70), PackedInt32Array(70, 79, 78), PackedInt32Array(70, 78, 73), PackedInt32Array(73, 78, 74), PackedInt32Array(77, 82, 81), PackedInt32Array(76, 77, 83), PackedInt32Array(83, 77, 86), PackedInt32Array(83, 86, 84), PackedInt32Array(84, 86, 85), PackedInt32Array(87, 86, 81), PackedInt32Array(81, 86, 77), PackedInt32Array(90, 89, 88), PackedInt32Array(93, 92, 91), PackedInt32Array(94, 90, 95), PackedInt32Array(95, 90, 88), PackedInt32Array(95, 88, 96), PackedInt32Array(91, 97, 95), PackedInt32Array(96, 15, 95), PackedInt32Array(95, 15, 93), PackedInt32Array(93, 15, 20), PackedInt32Array(93, 20, 98), PackedInt32Array(93, 91, 95), PackedInt32Array(101, 100, 99), PackedInt32Array(103, 102, 101), PackedInt32Array(106, 105, 104), PackedInt32Array(109, 108, 107), PackedInt32Array(103, 101, 107), PackedInt32Array(107, 101, 99), PackedInt32Array(107, 99, 106), PackedInt32Array(106, 104, 107), PackedInt32Array(107, 104, 109), PackedInt32Array(109, 104, 110), PackedInt32Array(110, 104, 111), PackedInt32Array(109, 110, 112), PackedInt32Array(114, 113, 110), PackedInt32Array(111, 114, 110), PackedInt32Array(117, 116, 115), PackedInt32Array(119, 118, 120), PackedInt32Array(120, 118, 121), PackedInt32Array(125, 124, 115), PackedInt32Array(115, 124, 117), PackedInt32Array(117, 124, 122), PackedInt32Array(122, 124, 123), PackedInt32Array(124, 126, 118), PackedInt32Array(118, 126, 121), PackedInt32Array(121, 126, 128), PackedInt32Array(121, 128, 127), PackedInt32Array(118, 123, 124), PackedInt32Array(129, 131, 130), PackedInt32Array(134, 133, 132), PackedInt32Array(136, 135, 137), PackedInt32Array(137, 135, 138), PackedInt32Array(129, 45, 131), PackedInt32Array(131, 45, 138), PackedInt32Array(140, 139, 141), PackedInt32Array(141, 139, 145), PackedInt32Array(145, 139, 144), PackedInt32Array(145, 144, 143), PackedInt32Array(145, 143, 146), PackedInt32Array(146, 143, 142), PackedInt32Array(146, 142, 147), PackedInt32Array(147, 142, 134), PackedInt32Array(46, 145, 45), PackedInt32Array(45, 148, 138), PackedInt32Array(138, 148, 132), PackedInt32Array(132, 147, 134), PackedInt32Array(147, 148, 146), PackedInt32Array(146, 148, 145), PackedInt32Array(145, 46, 141), PackedInt32Array(145, 148, 45), PackedInt32Array(148, 147, 132), PackedInt32Array(132, 137, 138), PackedInt32Array(149, 151, 150), PackedInt32Array(153, 152, 154), PackedInt32Array(154, 152, 155), PackedInt32Array(157, 156, 158), PackedInt32Array(158, 156, 161), PackedInt32Array(158, 161, 160), PackedInt32Array(158, 160, 159), PackedInt32Array(149, 163, 162), PackedInt32Array(151, 149, 164), PackedInt32Array(164, 149, 162), PackedInt32Array(164, 162, 165), PackedInt32Array(152, 166, 155), PackedInt32Array(169, 168, 167), PackedInt32Array(171, 170, 172), PackedInt32Array(172, 170, 173), PackedInt32Array(177, 176, 174), PackedInt32Array(174, 176, 175), PackedInt32Array(180, 179, 178), PackedInt32Array(182, 181, 183), PackedInt32Array(183, 181, 184), PackedInt32Array(179, 185, 178), PackedInt32Array(187, 186, 188), PackedInt32Array(188, 186, 169), PackedInt32Array(188, 169, 167), PackedInt32Array(177, 189, 176), PackedInt32Array(176, 189, 190), PackedInt32Array(190, 189, 191), PackedInt32Array(193, 192, 194), PackedInt32Array(194, 192, 195), PackedInt32Array(159, 196, 158), PackedInt32Array(158, 196, 197), PackedInt32Array(197, 196, 198), PackedInt32Array(197, 198, 180), PackedInt32Array(173, 170, 186), PackedInt32Array(186, 170, 199), PackedInt32Array(186, 199, 169), PackedInt32Array(178, 189, 200), PackedInt32Array(158, 197, 201), PackedInt32Array(195, 178, 194), PackedInt32Array(194, 178, 185), PackedInt32Array(204, 203, 202), PackedInt32Array(167, 197, 188), PackedInt32Array(188, 197, 205), PackedInt32Array(184, 181, 180), PackedInt32Array(180, 181, 207), PackedInt32Array(180, 207, 206), PackedInt32Array(178, 191, 189), PackedInt32Array(178, 200, 180), PackedInt32Array(180, 200, 197), PackedInt32Array(197, 200, 205), PackedInt32Array(205, 200, 208), PackedInt32Array(180, 206, 179), PackedInt32Array(154, 155, 203), PackedInt32Array(203, 155, 202), PackedInt32Array(164, 165, 205), PackedInt32Array(205, 165, 188), PackedInt32Array(196, 209, 198), PackedInt32Array(202, 205, 204), PackedInt32Array(204, 205, 208), PackedInt32Array(212, 211, 210), PackedInt32Array(213, 212, 214), PackedInt32Array(214, 212, 215), PackedInt32Array(212, 210, 69), PackedInt32Array(216, 6, 21), PackedInt32Array(215, 212, 217), PackedInt32Array(217, 212, 69), PackedInt32Array(217, 69, 218), PackedInt32Array(218, 69, 68), PackedInt32Array(216, 21, 218), PackedInt32Array(218, 21, 217), PackedInt32Array(220, 219, 221), PackedInt32Array(221, 219, 222), PackedInt32Array(222, 219, 223), PackedInt32Array(223, 219, 224), PackedInt32Array(225, 204, 226), PackedInt32Array(226, 204, 208), PackedInt32Array(226, 208, 227), PackedInt32Array(229, 228, 225), PackedInt32Array(225, 228, 230), PackedInt32Array(225, 230, 204), PackedInt32Array(224, 219, 228), PackedInt32Array(228, 219, 230), PackedInt32Array(233, 232, 231), PackedInt32Array(235, 234, 236), PackedInt32Array(236, 234, 237), PackedInt32Array(240, 239, 238), PackedInt32Array(243, 242, 241), PackedInt32Array(245, 244, 243), PackedInt32Array(231, 247, 246), PackedInt32Array(248, 240, 249), PackedInt32Array(249, 240, 250), PackedInt32Array(250, 240, 251), PackedInt32Array(254, 253, 252), PackedInt32Array(256, 255, 257), PackedInt32Array(257, 255, 258), PackedInt32Array(258, 255, 259), PackedInt32Array(261, 260, 255), PackedInt32Array(255, 260, 264), PackedInt32Array(255, 264, 262), PackedInt32Array(262, 264, 263), PackedInt32Array(265, 236, 266), PackedInt32Array(240, 238, 267), PackedInt32Array(270, 269, 268), PackedInt32Array(272, 271, 251), PackedInt32Array(275, 274, 273), PackedInt32Array(276, 233, 267), PackedInt32Array(267, 233, 231), PackedInt32Array(267, 231, 246), PackedInt32Array(277, 245, 268), PackedInt32Array(268, 245, 243), PackedInt32Array(268, 243, 241), PackedInt32Array(84, 278, 263), PackedInt32Array(267, 238, 279), PackedInt32Array(280, 259, 262), PackedInt32Array(262, 259, 255), PackedInt32Array(252, 281, 275), PackedInt32Array(237, 83, 236), PackedInt32Array(236, 83, 266), PackedInt32Array(266, 83, 273), PackedInt32Array(273, 83, 275), PackedInt32Array(263, 264, 254), PackedInt32Array(260, 282, 264), PackedInt32Array(254, 252, 263), PackedInt32Array(263, 252, 275), PackedInt32Array(263, 275, 83), PackedInt32Array(263, 83, 84), PackedInt32Array(246, 270, 267), PackedInt32Array(267, 270, 240), PackedInt32Array(240, 270, 251), PackedInt32Array(251, 270, 272), PackedInt32Array(241, 281, 268), PackedInt32Array(268, 281, 270), PackedInt32Array(270, 281, 272), PackedInt32Array(272, 281, 252), PackedInt32Array(284, 283, 285), PackedInt32Array(285, 283, 126), PackedInt32Array(287, 286, 112), PackedInt32Array(112, 286, 109), PackedInt32Array(283, 109, 126), PackedInt32Array(126, 109, 286), PackedInt32Array(126, 286, 128), PackedInt32Array(289, 288, 290), PackedInt32Array(290, 288, 291), PackedInt32Array(290, 291, 98), PackedInt32Array(291, 93, 98), PackedInt32Array(293, 292, 111), PackedInt32Array(293, 111, 98), PackedInt32Array(98, 111, 104), PackedInt32Array(98, 104, 290), PackedInt32Array(295, 294, 296), PackedInt32Array(296, 294, 297), PackedInt32Array(294, 121, 55), PackedInt32Array(55, 121, 298), PackedInt32Array(55, 298, 56), PackedInt32Array(299, 298, 127), PackedInt32Array(127, 298, 121), PackedInt32Array(55, 297, 294), PackedInt32Array(302, 301, 300), PackedInt32Array(86, 303, 85), PackedInt32Array(85, 302, 300), PackedInt32Array(302, 85, 139), PackedInt32Array(139, 303, 144), PackedInt32Array(144, 303, 143), PackedInt32Array(143, 303, 142), PackedInt32Array(142, 86, 134), PackedInt32Array(303, 139, 85), PackedInt32Array(86, 142, 303), PackedInt32Array(300, 304, 85), PackedInt32Array(305, 34, 227), PackedInt32Array(227, 34, 226), PackedInt32Array(226, 34, 40), PackedInt32Array(308, 307, 306), PackedInt32Array(310, 309, 311), PackedInt32Array(311, 309, 312), PackedInt32Array(308, 306, 313), PackedInt32Array(313, 306, 314), PackedInt32Array(314, 306, 315), PackedInt32Array(317, 316, 175), PackedInt32Array(175, 316, 174), PackedInt32Array(174, 316, 318), PackedInt32Array(318, 316, 319), PackedInt32Array(320, 315, 309), PackedInt32Array(309, 315, 319), PackedInt32Array(319, 315, 318), PackedInt32Array(318, 315, 321), PackedInt32Array(227, 322, 305), PackedInt32Array(323, 312, 309), PackedInt32Array(309, 324, 320), PackedInt32Array(200, 321, 315), PackedInt32Array(315, 325, 314), PackedInt32Array(319, 323, 309), PackedInt32Array(227, 208, 322), PackedInt32Array(322, 208, 200), PackedInt32Array(322, 200, 315), PackedInt32Array(322, 315, 306), PackedInt32Array(313, 314, 326), PackedInt32Array(329, 328, 327), PackedInt32Array(330, 329, 331), PackedInt32Array(331, 329, 332), PackedInt32Array(332, 329, 333), PackedInt32Array(327, 335, 334), PackedInt32Array(336, 331, 337), PackedInt32Array(337, 331, 332), PackedInt32Array(304, 338, 85), PackedInt32Array(85, 338, 84), PackedInt32Array(84, 338, 339), PackedInt32Array(339, 338, 340), PackedInt32Array(327, 334, 329), PackedInt32Array(329, 334, 333), PackedInt32Array(333, 334, 339), PackedInt32Array(334, 278, 339), PackedInt32Array(339, 278, 84), PackedInt32Array(342, 341, 343), PackedInt32Array(343, 341, 344), PackedInt32Array(348, 347, 345), PackedInt32Array(345, 347, 346), PackedInt32Array(341, 349, 344), PackedInt32Array(344, 349, 350), PackedInt32Array(350, 349, 351), PackedInt32Array(351, 349, 352), PackedInt32Array(348, 345, 351), PackedInt32Array(351, 345, 350), PackedInt32Array(354, 353, 355), PackedInt32Array(355, 353, 356), PackedInt32Array(356, 353, 347), PackedInt32Array(359, 358, 357), PackedInt32Array(353, 346, 347), PackedInt32Array(356, 361, 355), PackedInt32Array(355, 361, 360), PackedInt32Array(360, 361, 359), PackedInt32Array(360, 359, 357), PackedInt32Array(363, 362, 357), PackedInt32Array(357, 362, 360), PackedInt32Array(366, 365, 364), PackedInt32Array(368, 342, 369), PackedInt32Array(369, 342, 367), PackedInt32Array(367, 342, 343), PackedInt32Array(371, 370, 372), PackedInt32Array(372, 370, 374), PackedInt32Array(372, 374, 373), PackedInt32Array(366, 364, 375), PackedInt32Array(375, 364, 378), PackedInt32Array(375, 378, 377), PackedInt32Array(375, 377, 376), PackedInt32Array(367, 377, 369), PackedInt32Array(369, 377, 378), PackedInt32Array(366, 375, 370), PackedInt32Array(370, 375, 374), PackedInt32Array(381, 380, 379), PackedInt32Array(381, 379, 382), PackedInt32Array(382, 379, 385), PackedInt32Array(382, 385, 384), PackedInt32Array(382, 384, 383), PackedInt32Array(387, 386, 388), PackedInt32Array(388, 386, 373), PackedInt32Array(388, 373, 389), PackedInt32Array(386, 372, 373), PackedInt32Array(392, 391, 390), PackedInt32Array(394, 393, 392), PackedInt32Array(390, 395, 392), PackedInt32Array(392, 395, 394), PackedInt32Array(394, 395, 389), PackedInt32Array(389, 395, 388), PackedInt32Array(397, 396, 398), PackedInt32Array(398, 396, 400), PackedInt32Array(398, 400, 399), PackedInt32Array(398, 399, 383), PackedInt32Array(383, 384, 398), PackedInt32Array(391, 392, 401), PackedInt32Array(401, 392, 396), PackedInt32Array(396, 392, 400), PackedInt32Array(403, 402, 404), PackedInt32Array(404, 402, 405), PackedInt32Array(405, 402, 406), PackedInt32Array(406, 402, 407), PackedInt32Array(409, 408, 411), PackedInt32Array(411, 408, 410), PackedInt32Array(412, 410, 407), PackedInt32Array(407, 410, 408), PackedInt32Array(407, 408, 406), PackedInt32Array(405, 413, 404), PackedInt32Array(404, 413, 362), PackedInt32Array(404, 362, 363), PackedInt32Array(409, 411, 414), PackedInt32Array(414, 411, 410), PackedInt32Array(417, 416, 415), PackedInt32Array(415, 419, 418), PackedInt32Array(414, 410, 418), PackedInt32Array(418, 410, 415), PackedInt32Array(415, 410, 417), PackedInt32Array(420, 380, 421), PackedInt32Array(421, 380, 381), PackedInt32Array(421, 381, 422), PackedInt32Array(425, 424, 426), PackedInt32Array(426, 424, 422), PackedInt32Array(422, 424, 421), PackedInt32Array(421, 424, 423), PackedInt32Array(427, 430, 428), PackedInt32Array(428, 430, 429), PackedInt32Array(432, 431, 433), PackedInt32Array(433, 431, 436), PackedInt32Array(433, 436, 434), PackedInt32Array(434, 436, 435), PackedInt32Array(438, 437, 439), PackedInt32Array(439, 437, 442), PackedInt32Array(439, 442, 441), PackedInt32Array(439, 441, 440), PackedInt32Array(444, 443, 445), PackedInt32Array(445, 443, 338), PackedInt32Array(445, 338, 446), PackedInt32Array(443, 340, 338), PackedInt32Array(448, 447, 449), PackedInt32Array(449, 447, 450), PackedInt32Array(453, 452, 451), PackedInt32Array(455, 454, 451), PackedInt32Array(451, 454, 453), PackedInt32Array(458, 457, 459), PackedInt32Array(459, 457, 456), PackedInt32Array(461, 460, 462), PackedInt32Array(462, 460, 463), PackedInt32Array(463, 460, 464), PackedInt32Array(468, 467, 465), PackedInt32Array(465, 467, 466), PackedInt32Array(470, 469, 471), PackedInt32Array(471, 469, 472), PackedInt32Array(476, 475, 473), PackedInt32Array(473, 475, 474), PackedInt32Array(480, 479, 477), PackedInt32Array(477, 479, 478), PackedInt32Array(482, 481, 483), PackedInt32Array(483, 481, 484), PackedInt32Array(487, 486, 485), PackedInt32Array(488, 322, 489), PackedInt32Array(489, 322, 491), PackedInt32Array(489, 491, 490), PackedInt32Array(322, 306, 491), PackedInt32Array(494, 493, 492), PackedInt32Array(496, 495, 497), PackedInt32Array(498, 504, 499), PackedInt32Array(499, 504, 500), PackedInt32Array(500, 504, 503), PackedInt32Array(500, 503, 502), PackedInt32Array(500, 502, 501), PackedInt32Array(506, 505, 507), PackedInt32Array(507, 505, 509), PackedInt32Array(507, 509, 508), PackedInt32Array(511, 510, 512), PackedInt32Array(512, 510, 495), PackedInt32Array(514, 513, 507), PackedInt32Array(495, 510, 497), PackedInt32Array(497, 510, 505), PackedInt32Array(497, 505, 515), PackedInt32Array(515, 505, 506), PackedInt32Array(501, 516, 500), PackedInt32Array(500, 516, 517), PackedInt32Array(520, 524, 516), PackedInt32Array(516, 524, 521), PackedInt32Array(521, 524, 522), PackedInt32Array(522, 524, 523), PackedInt32Array(523, 524, 514), PackedInt32Array(514, 519, 518), PackedInt32Array(519, 524, 520), PackedInt32Array(524, 519, 514), PackedInt32Array(516, 530, 517), PackedInt32Array(517, 526, 525), PackedInt32Array(526, 528, 507), PackedInt32Array(507, 528, 514), PackedInt32Array(514, 528, 523), PackedInt32Array(523, 528, 522), PackedInt32Array(522, 528, 521), PackedInt32Array(521, 529, 516), PackedInt32Array(530, 528, 517), PackedInt32Array(516, 527, 530), PackedInt32Array(517, 528, 526), PackedInt32Array(528, 529, 521), PackedInt32Array(529, 527, 516), PackedInt32Array(530, 529, 528), PackedInt32Array(527, 529, 530), PackedInt32Array(526, 531, 525), PackedInt32Array(507, 508, 526), PackedInt32Array(533, 532, 534), PackedInt32Array(534, 532, 535), PackedInt32Array(534, 535, 419), PackedInt32Array(419, 535, 418), PackedInt32Array(536, 535, 537), PackedInt32Array(537, 535, 532), PackedInt32Array(537, 532, 538), PackedInt32Array(538, 532, 539), PackedInt32Array(541, 540, 542), PackedInt32Array(542, 540, 543), PackedInt32Array(546, 545, 544), PackedInt32Array(544, 547, 445), PackedInt32Array(546, 551, 548), PackedInt32Array(548, 551, 550), PackedInt32Array(548, 550, 549), PackedInt32Array(548, 549, 543), PackedInt32Array(544, 445, 546), PackedInt32Array(546, 445, 551), PackedInt32Array(551, 445, 550), PackedInt32Array(550, 445, 549), PackedInt32Array(549, 445, 446), PackedInt32Array(549, 446, 542), PackedInt32Array(549, 542, 543), PackedInt32Array(553, 552, 554), PackedInt32Array(554, 552, 555), PackedInt32Array(556, 558, 557), PackedInt32Array(561, 560, 559), PackedInt32Array(556, 564, 558), PackedInt32Array(558, 564, 559), PackedInt32Array(559, 564, 563), PackedInt32Array(559, 563, 562), PackedInt32Array(567, 566, 565), PackedInt32Array(561, 520, 516), PackedInt32Array(565, 520, 567), PackedInt32Array(567, 520, 562), PackedInt32Array(562, 520, 559), PackedInt32Array(559, 520, 561), PackedInt32Array(569, 568, 570), PackedInt32Array(570, 568, 571), PackedInt32Array(568, 572, 571), PackedInt32Array(574, 573, 575), PackedInt32Array(575, 573, 576), PackedInt32Array(579, 578, 577), PackedInt32Array(579, 577, 580), PackedInt32Array(580, 577, 576), PackedInt32Array(580, 576, 581), PackedInt32Array(583, 582, 581), PackedInt32Array(581, 582, 584), PackedInt32Array(577, 575, 576), PackedInt32Array(586, 585, 580), PackedInt32Array(588, 587, 584), PackedInt32Array(584, 587, 581), PackedInt32Array(581, 587, 580), PackedInt32Array(580, 587, 586), PackedInt32Array(590, 589, 584), PackedInt32Array(584, 589, 570), PackedInt32Array(584, 570, 591), PackedInt32Array(591, 570, 592), PackedInt32Array(570, 571, 592), PackedInt32Array(591, 588, 584), PackedInt32Array(593, 572, 563), PackedInt32Array(563, 572, 562), PackedInt32Array(562, 572, 568), PackedInt32Array(595, 594, 596), PackedInt32Array(596, 594, 598), PackedInt32Array(596, 598, 597), PackedInt32Array(600, 599, 601), PackedInt32Array(601, 599, 602), PackedInt32Array(605, 604, 603), PackedInt32Array(602, 606, 607), PackedInt32Array(607, 606, 608), PackedInt32Array(603, 610, 609), PackedInt32Array(612, 611, 613), PackedInt32Array(613, 611, 614), PackedInt32Array(616, 615, 617), PackedInt32Array(617, 615, 618), PackedInt32Array(605, 603, 619), PackedInt32Array(619, 603, 609), PackedInt32Array(619, 609, 620), PackedInt32Array(621, 606, 599), PackedInt32Array(599, 606, 602), PackedInt32Array(606, 622, 608), PackedInt32Array(607, 608, 623), PackedInt32Array(623, 608, 597), PackedInt32Array(597, 608, 596), PackedInt32Array(596, 608, 624), PackedInt32Array(615, 626, 625), PackedInt32Array(611, 628, 627), PackedInt32Array(619, 620, 597), PackedInt32Array(597, 620, 629), PackedInt32Array(597, 629, 623), PackedInt32Array(625, 629, 615), PackedInt32Array(615, 629, 618), PackedInt32Array(618, 629, 620), PackedInt32Array(614, 611, 623), PackedInt32Array(623, 611, 627), PackedInt32Array(623, 627, 607), PackedInt32Array(631, 630, 632), PackedInt32Array(632, 630, 598), PackedInt32Array(630, 489, 598), PackedInt32Array(598, 489, 490), PackedInt32Array(598, 490, 597), PackedInt32Array(633, 425, 634), PackedInt32Array(635, 424, 425), PackedInt32Array(637, 636, 638), PackedInt32Array(638, 636, 635), PackedInt32Array(638, 635, 633), PackedInt32Array(633, 635, 425), PackedInt32Array(641, 640, 639), PackedInt32Array(642, 641, 643), PackedInt32Array(643, 641, 644), PackedInt32Array(644, 641, 645), PackedInt32Array(639, 646, 548), PackedInt32Array(639, 548, 641), PackedInt32Array(641, 548, 645), PackedInt32Array(645, 548, 647), PackedInt32Array(548, 543, 648), PackedInt32Array(650, 649, 648), PackedInt32Array(648, 649, 548), PackedInt32Array(548, 649, 647), PackedInt32Array(647, 649, 651), PackedInt32Array(653, 652, 654), PackedInt32Array(654, 652, 657), PackedInt32Array(654, 657, 656), PackedInt32Array(654, 656, 655), PackedInt32Array(660, 659, 658), PackedInt32Array(538, 539, 661), PackedInt32Array(664, 663, 662), PackedInt32Array(661, 665, 538), PackedInt32Array(538, 665, 662), PackedInt32Array(662, 665, 664), PackedInt32Array(668, 667, 666), PackedInt32Array(638, 670, 669), PackedInt32Array(671, 637, 638), PackedInt32Array(668, 666, 672), PackedInt32Array(672, 666, 671), PackedInt32Array(672, 671, 669), PackedInt32Array(669, 671, 638), PackedInt32Array(674, 673, 675), PackedInt32Array(675, 673, 676), PackedInt32Array(676, 673, 677), PackedInt32Array(678, 681, 679), PackedInt32Array(679, 681, 680), PackedInt32Array(683, 682, 684), PackedInt32Array(684, 682, 681), PackedInt32Array(681, 682, 680), PackedInt32Array(687, 686, 685), PackedInt32Array(688, 663, 689), PackedInt32Array(689, 663, 664), PackedInt32Array(689, 664, 687), PackedInt32Array(692, 691, 690), PackedInt32Array(692, 690, 693), PackedInt32Array(693, 695, 694), PackedInt32Array(687, 685, 689), PackedInt32Array(689, 685, 694), PackedInt32Array(694, 685, 693), PackedInt32Array(693, 685, 692), PackedInt32Array(698, 697, 696), PackedInt32Array(698, 696, 651), PackedInt32Array(700, 699, 698), PackedInt32Array(700, 698, 701), PackedInt32Array(701, 698, 651), PackedInt32Array(703, 702, 649), PackedInt32Array(649, 702, 704), PackedInt32Array(649, 704, 701), PackedInt32Array(649, 701, 651), PackedInt32Array(706, 705, 707), PackedInt32Array(707, 705, 708), PackedInt32Array(708, 705, 672), PackedInt32Array(705, 668, 672), PackedInt32Array(709, 711, 710), PackedInt32Array(713, 712, 711), PackedInt32Array(714, 713, 715), PackedInt32Array(715, 713, 711), PackedInt32Array(715, 711, 709), PackedInt32Array(715, 709, 716), PackedInt32Array(710, 711, 717), PackedInt32Array(717, 711, 708), PackedInt32Array(708, 711, 707), PackedInt32Array(721, 720, 718), PackedInt32Array(718, 720, 719), PackedInt32Array(727, 726, 722), PackedInt32Array(722, 726, 723), PackedInt32Array(723, 726, 724), PackedInt32Array(724, 726, 725), PackedInt32Array(728, 586, 729), PackedInt32Array(729, 586, 587), PackedInt32Array(729, 587, 730), PackedInt32Array(732, 731, 733), PackedInt32Array(733, 731, 734), PackedInt32Array(736, 735, 737), PackedInt32Array(737, 735, 738), PackedInt32Array(740, 739, 593), PackedInt32Array(742, 741, 743), PackedInt32Array(743, 741, 563), PackedInt32Array(745, 744, 740), PackedInt32Array(740, 744, 746), PackedInt32Array(740, 746, 739), PackedInt32Array(737, 738, 741), PackedInt32Array(741, 738, 563), PackedInt32Array(556, 743, 564), PackedInt32Array(564, 743, 563), PackedInt32Array(738, 747, 563), PackedInt32Array(563, 747, 748), PackedInt32Array(563, 748, 740), PackedInt32Array(563, 740, 593), PackedInt32Array(750, 749, 624), PackedInt32Array(750, 624, 751), PackedInt32Array(751, 624, 752), PackedInt32Array(752, 624, 608), PackedInt32Array(752, 753, 751), PackedInt32Array(756, 755, 754), PackedInt32Array(758, 757, 759), PackedInt32Array(759, 757, 754), PackedInt32Array(744, 760, 746), PackedInt32Array(746, 760, 756), PackedInt32Array(746, 756, 730), PackedInt32Array(754, 761, 759), PackedInt32Array(729, 765, 757), PackedInt32Array(757, 762, 754), PackedInt32Array(754, 763, 756), PackedInt32Array(756, 764, 730), PackedInt32Array(730, 765, 729), PackedInt32Array(765, 762, 757), PackedInt32Array(762, 763, 754), PackedInt32Array(763, 764, 756), PackedInt32Array(764, 765, 730), PackedInt32Array(765, 764, 762), PackedInt32Array(762, 764, 763), PackedInt32Array(769, 768, 766), PackedInt32Array(766, 768, 767), PackedInt32Array(771, 770, 772), PackedInt32Array(772, 770, 773), PackedInt32Array(775, 774, 776), PackedInt32Array(776, 774, 773), PackedInt32Array(773, 770, 776), PackedInt32Array(779, 778, 777), PackedInt32Array(779, 777, 525), PackedInt32Array(525, 777, 782), PackedInt32Array(525, 782, 780), PackedInt32Array(780, 782, 781), PackedInt32Array(780, 517, 525), PackedInt32Array(735, 736, 783), PackedInt32Array(783, 736, 784), PackedInt32Array(787, 786, 785), PackedInt32Array(788, 790, 789), PackedInt32Array(498, 787, 504), PackedInt32Array(504, 787, 785), PackedInt32Array(504, 785, 503), PackedInt32Array(788, 783, 784), PackedInt32Array(784, 503, 788), PackedInt32Array(788, 503, 785), PackedInt32Array(788, 785, 790), PackedInt32Array(793, 792, 791), PackedInt32Array(791, 792, 692), PackedInt32Array(692, 792, 691), PackedInt32Array(793, 791, 794), PackedInt32Array(794, 791, 796), PackedInt32Array(794, 796, 795), PackedInt32Array(797, 799, 798), PackedInt32Array(798, 799, 795), PackedInt32Array(795, 799, 794), PackedInt32Array(801, 704, 800), PackedInt32Array(800, 704, 702), PackedInt32Array(803, 802, 804), PackedInt32Array(807, 806, 805), PackedInt32Array(807, 809, 808), PackedInt32Array(802, 810, 804), PackedInt32Array(804, 810, 801), PackedInt32Array(801, 810, 805), PackedInt32Array(805, 810, 809), PackedInt32Array(809, 807, 805), PackedInt32Array(809, 810, 811), PackedInt32Array(801, 800, 804), PackedInt32Array(813, 812, 814), PackedInt32Array(814, 812, 815), PackedInt32Array(817, 816, 818), PackedInt32Array(818, 816, 819), PackedInt32Array(820, 815, 821), PackedInt32Array(821, 815, 822), PackedInt32Array(824, 823, 821), PackedInt32Array(825, 814, 820), PackedInt32Array(820, 814, 815), PackedInt32Array(827, 826, 824), PackedInt32Array(831, 830, 828), PackedInt32Array(828, 830, 829), PackedInt32Array(827, 824, 747), PackedInt32Array(747, 824, 821), PackedInt32Array(747, 821, 822), PackedInt32Array(747, 822, 748), PackedInt32Array(822, 819, 748), PackedInt32Array(828, 822, 831), PackedInt32Array(831, 822, 815), PackedInt32Array(822, 818, 819), PackedInt32Array(833, 832, 834), PackedInt32Array(834, 832, 835), PackedInt32Array(827, 789, 826), PackedInt32Array(826, 789, 836), PackedInt32Array(836, 789, 790), PackedInt32Array(836, 790, 837), PackedInt32Array(832, 837, 835), PackedInt32Array(835, 837, 790), PackedInt32Array(841, 840, 838), PackedInt32Array(838, 840, 839), PackedInt32Array(843, 842, 715), PackedInt32Array(715, 842, 714), PackedInt32Array(714, 842, 845), PackedInt32Array(714, 845, 844), PackedInt32Array(846, 844, 847), PackedInt32Array(847, 844, 845), PackedInt32Array(847, 851, 846), PackedInt32Array(846, 851, 850), PackedInt32Array(846, 850, 849), PackedInt32Array(846, 849, 848), PackedInt32Array(853, 852, 854), PackedInt32Array(854, 852, 855), PackedInt32Array(855, 852, 856), PackedInt32Array(859, 858, 857), PackedInt32Array(861, 860, 852), PackedInt32Array(852, 860, 856), PackedInt32Array(856, 860, 862), PackedInt32Array(864, 863, 865), PackedInt32Array(865, 863, 866), PackedInt32Array(868, 867, 869), PackedInt32Array(869, 867, 870), PackedInt32Array(872, 871, 859), PackedInt32Array(859, 857, 872), PackedInt32Array(872, 857, 751), PackedInt32Array(751, 857, 873), PackedInt32Array(873, 857, 874), PackedInt32Array(875, 859, 863), PackedInt32Array(863, 859, 860), PackedInt32Array(860, 859, 862), PackedInt32Array(862, 859, 871), PackedInt32Array(863, 876, 875), PackedInt32Array(870, 867, 858), PackedInt32Array(858, 867, 877), PackedInt32Array(858, 877, 857), PackedInt32Array(867, 878, 877), PackedInt32Array(874, 879, 873), PackedInt32Array(751, 753, 872), PackedInt32Array(860, 866, 863), PackedInt32Array(881, 880, 882), PackedInt32Array(882, 880, 883), PackedInt32Array(883, 880, 884), PackedInt32Array(884, 880, 885), PackedInt32Array(812, 813, 886), PackedInt32Array(886, 813, 887), PackedInt32Array(890, 889, 888), PackedInt32Array(887, 891, 886), PackedInt32Array(886, 891, 892), PackedInt32Array(817, 893, 816), PackedInt32Array(816, 893, 894), PackedInt32Array(895, 761, 896), PackedInt32Array(896, 761, 897), PackedInt32Array(892, 891, 898), PackedInt32Array(829, 830, 899), PackedInt32Array(899, 830, 900), PackedInt32Array(902, 901, 903), PackedInt32Array(903, 901, 904), PackedInt32Array(904, 901, 888), PackedInt32Array(888, 901, 890), PackedInt32Array(901, 905, 761), PackedInt32Array(892, 898, 900), PackedInt32Array(900, 898, 899), PackedInt32Array(899, 898, 906), PackedInt32Array(894, 893, 908), PackedInt32Array(908, 893, 907), PackedInt32Array(908, 907, 761), PackedInt32Array(906, 761, 907), PackedInt32Array(897, 909, 896), PackedInt32Array(905, 897, 761), PackedInt32Array(901, 911, 910), PackedInt32Array(889, 912, 888), PackedInt32Array(890, 913, 889), PackedInt32Array(908, 761, 754), PackedInt32Array(902, 903, 914), PackedInt32Array(904, 888, 915), PackedInt32Array(916, 905, 910), PackedInt32Array(910, 905, 901), PackedInt32Array(904, 917, 903), PackedInt32Array(890, 923, 918), PackedInt32Array(918, 923, 898), PackedInt32Array(898, 925, 906), PackedInt32Array(906, 920, 761), PackedInt32Array(761, 920, 901), PackedInt32Array(901, 922, 890), PackedInt32Array(890, 922, 923), PackedInt32Array(923, 921, 898), PackedInt32Array(925, 920, 906), PackedInt32Array(898, 919, 925), PackedInt32Array(920, 922, 901), PackedInt32Array(922, 921, 923), PackedInt32Array(921, 924, 898), PackedInt32Array(925, 919, 920), PackedInt32Array(898, 920, 919), PackedInt32Array(920, 924, 922), PackedInt32Array(922, 924, 921), PackedInt32Array(924, 920, 898), PackedInt32Array(929, 928, 926), PackedInt32Array(926, 928, 927), PackedInt32Array(932, 931, 930), PackedInt32Array(934, 933, 930), PackedInt32Array(930, 933, 932), PackedInt32Array(936, 935, 937), PackedInt32Array(937, 935, 938), PackedInt32Array(938, 939, 937), PackedInt32Array(935, 781, 938), PackedInt32Array(938, 781, 782), PackedInt32Array(943, 942, 940), PackedInt32Array(940, 942, 941), PackedInt32Array(947, 946, 944), PackedInt32Array(944, 946, 945), PackedInt32Array(949, 948, 950), PackedInt32Array(950, 948, 939), PackedInt32Array(939, 948, 951), PackedInt32Array(951, 948, 952), PackedInt32Array(833, 834, 951), PackedInt32Array(951, 834, 939), PackedInt32Array(939, 938, 950), PackedInt32Array(954, 953, 811), PackedInt32Array(957, 956, 955), PackedInt32Array(958, 954, 959), PackedInt32Array(959, 954, 955), PackedInt32Array(954, 811, 955), PackedInt32Array(955, 811, 957), PackedInt32Array(957, 811, 960), PackedInt32Array(960, 811, 810), PackedInt32Array(962, 961, 810), PackedInt32Array(810, 961, 960), PackedInt32Array(960, 963, 957), PackedInt32Array(965, 964, 966), PackedInt32Array(966, 964, 797), PackedInt32Array(966, 797, 798), PackedInt32Array(970, 969, 967), PackedInt32Array(967, 969, 968), PackedInt32Array(972, 971, 973), PackedInt32Array(973, 971, 974), PackedInt32Array(837, 975, 836), PackedInt32Array(836, 975, 976), PackedInt32Array(979, 978, 977), PackedInt32Array(981, 980, 918), PackedInt32Array(983, 985, 898), PackedInt32Array(898, 985, 918), PackedInt32Array(918, 986, 981), PackedInt32Array(981, 986, 982), PackedInt32Array(982, 985, 983), PackedInt32Array(985, 984, 918), PackedInt32Array(918, 984, 986), PackedInt32Array(986, 985, 982), PackedInt32Array(985, 986, 984), PackedInt32Array(983, 987, 982), PackedInt32Array(989, 988, 990), PackedInt32Array(990, 988, 982), PackedInt32Array(990, 992, 991), PackedInt32Array(982, 994, 990), PackedInt32Array(990, 994, 992), PackedInt32Array(992, 996, 975), PackedInt32Array(975, 996, 976), PackedInt32Array(976, 995, 987), PackedInt32Array(987, 994, 982), PackedInt32Array(994, 996, 992), PackedInt32Array(996, 995, 976), PackedInt32Array(995, 993, 987), PackedInt32Array(987, 993, 994), PackedInt32Array(994, 993, 996), PackedInt32Array(996, 993, 995), PackedInt32Array(998, 997, 999), PackedInt32Array(999, 997, 1000), PackedInt32Array(991, 992, 1001), PackedInt32Array(1001, 1003, 991), PackedInt32Array(991, 1003, 1002), PackedInt32Array(1005, 1004, 948), PackedInt32Array(948, 1004, 952), PackedInt32Array(952, 1004, 1006), PackedInt32Array(1008, 1007, 848), PackedInt32Array(1010, 1009, 1011), PackedInt32Array(1011, 1009, 1008), PackedInt32Array(1011, 1008, 848), PackedInt32Array(1011, 848, 849), PackedInt32Array(1011, 849, 1012), PackedInt32Array(1014, 1013, 1002), PackedInt32Array(1016, 1015, 1017), PackedInt32Array(1017, 1015, 1019), PackedInt32Array(1017, 1019, 1018), PackedInt32Array(1022, 1021, 1020), PackedInt32Array(1024, 1023, 1025), PackedInt32Array(1025, 1023, 1026), PackedInt32Array(1027, 1014, 1028), PackedInt32Array(1028, 1014, 1025), PackedInt32Array(1025, 1014, 1029), PackedInt32Array(1025, 1018, 1028), PackedInt32Array(1020, 1029, 1022), PackedInt32Array(1022, 1029, 1030), PackedInt32Array(1030, 1029, 1004), PackedInt32Array(1026, 1032, 1031), PackedInt32Array(1028, 1033, 1027), PackedInt32Array(1030, 1034, 1022), PackedInt32Array(1031, 1017, 1026), PackedInt32Array(1026, 1017, 1025), PackedInt32Array(1025, 1017, 1018), PackedInt32Array(1014, 1039, 1029), PackedInt32Array(1029, 1041, 1004), PackedInt32Array(1004, 1040, 1006), PackedInt32Array(1006, 1040, 1003), PackedInt32Array(1003, 1042, 1002), PackedInt32Array(1002, 1042, 1014), PackedInt32Array(1039, 1038, 1029), PackedInt32Array(1014, 1042, 1039), PackedInt32Array(1041, 1040, 1004), PackedInt32Array(1029, 1036, 1041), PackedInt32Array(1040, 1038, 1003), PackedInt32Array(1003, 1038, 1042), PackedInt32Array(1038, 1036, 1029), PackedInt32Array(1039, 1037, 1038), PackedInt32Array(1042, 1037, 1039), PackedInt32Array(1041, 1035, 1040), PackedInt32Array(1036, 1035, 1041), PackedInt32Array(1040, 1036, 1038), PackedInt32Array(1038, 1037, 1042), PackedInt32Array(1035, 1036, 1040), PackedInt32Array(1046, 1045, 1043), PackedInt32Array(1043, 1045, 1044), PackedInt32Array(1050, 1049, 1047), PackedInt32Array(1047, 1049, 1048), PackedInt32Array(963, 1052, 1051), PackedInt32Array(957, 1057, 1055), PackedInt32Array(1055, 1057, 1053), PackedInt32Array(1053, 1051, 1054), PackedInt32Array(1051, 1058, 963), PackedInt32Array(963, 1057, 957), PackedInt32Array(1057, 1056, 1053), PackedInt32Array(1053, 1058, 1051), PackedInt32Array(1058, 1056, 963), PackedInt32Array(963, 1056, 1057), PackedInt32Array(1056, 1058, 1053), PackedInt32Array(1053, 1059, 1055), PackedInt32Array(1061, 1060, 1062), PackedInt32Array(1062, 1060, 1064), PackedInt32Array(1062, 1064, 1063), PackedInt32Array(1065, 1064, 966), PackedInt32Array(966, 1064, 1060), PackedInt32Array(966, 1060, 965), PackedInt32Array(1069, 1068, 1066), PackedInt32Array(1066, 1068, 1067), PackedInt32Array(1067, 1068, 1063), PackedInt32Array(1063, 1068, 1062), PackedInt32Array(1072, 1071, 1070), PackedInt32Array(1070, 1071, 1069), PackedInt32Array(1070, 1069, 1066), PackedInt32Array(1070, 1075, 1072), PackedInt32Array(1072, 1075, 1073), PackedInt32Array(1073, 1075, 1074), PackedInt32Array(1078, 1077, 1076), PackedInt32Array(1080, 1079, 857), PackedInt32Array(857, 1079, 1081), PackedInt32Array(857, 1081, 874), PackedInt32Array(1083, 1082, 1084), PackedInt32Array(1084, 1082, 1086), PackedInt32Array(1084, 1086, 1081), PackedInt32Array(1081, 1086, 1085), PackedInt32Array(1081, 1079, 1084), PackedInt32Array(1089, 1088, 1087), PackedInt32Array(1009, 1010, 1090), PackedInt32Array(1087, 1091, 1089), PackedInt32Array(1089, 1091, 1092), PackedInt32Array(1092, 1091, 1090), PackedInt32Array(1090, 1091, 1009), PackedInt32Array(1090, 1093, 1092), PackedInt32Array(1095, 1094, 1096), PackedInt32Array(1096, 1094, 1097), PackedInt32Array(1099, 1098, 1097), PackedInt32Array(1097, 1098, 1096), PackedInt32Array(1101, 1100, 1102), PackedInt32Array(1102, 1100, 1103), PackedInt32Array(1105, 1104, 1103), PackedInt32Array(1103, 1104, 1102), PackedInt32Array(1107, 1106, 1108), PackedInt32Array(1108, 1106, 1109), PackedInt32Array(1111, 1110, 1082), PackedInt32Array(1082, 1110, 1112), PackedInt32Array(1082, 1112, 1086), PackedInt32Array(1109, 1106, 1082), PackedInt32Array(1082, 1106, 1113), PackedInt32Array(1082, 1113, 1111), PackedInt32Array(1115, 1114, 1116), PackedInt32Array(1116, 1114, 1112), PackedInt32Array(1116, 1112, 1117), PackedInt32Array(1117, 1112, 1110), PackedInt32Array(1118, 1120, 1119), PackedInt32Array(1118, 1087, 1088), PackedInt32Array(1123, 1122, 1121), PackedInt32Array(1118, 1088, 1120), PackedInt32Array(1120, 1088, 1121), PackedInt32Array(1121, 1088, 1124), PackedInt32Array(1121, 1124, 1123), PackedInt32Array(1127, 1126, 1125), PackedInt32Array(1130, 1129, 1128), PackedInt32Array(1128, 1132, 1131), PackedInt32Array(1128, 1131, 1130), PackedInt32Array(1130, 1131, 1133), PackedInt32Array(1133, 1131, 1134), PackedInt32Array(1135, 1130, 1136), PackedInt32Array(1136, 1130, 1133), PackedInt32Array(1127, 1125, 1134), PackedInt32Array(1134, 1125, 1053), PackedInt32Array(1134, 1053, 1138), PackedInt32Array(1134, 1138, 1137), PackedInt32Array(1053, 1139, 1138), PackedInt32Array(1125, 1059, 1053), PackedInt32Array(1134, 1131, 1127), PackedInt32Array(1143, 1142, 1140), PackedInt32Array(1140, 1142, 1141), PackedInt32Array(1110, 1145, 1144), PackedInt32Array(1146, 1117, 1147), PackedInt32Array(1147, 1117, 1148), PackedInt32Array(1148, 1117, 1144), PackedInt32Array(1144, 1117, 1110), PackedInt32Array(1151, 1150, 1149), PackedInt32Array(1122, 1123, 1152), PackedInt32Array(1152, 1123, 1153), PackedInt32Array(1155, 1154, 1153), PackedInt32Array(1153, 1154, 1152), PackedInt32Array(1073, 1074, 1156), PackedInt32Array(1156, 1074, 1157), PackedInt32Array(1159, 1158, 1160), PackedInt32Array(1160, 1158, 1163), PackedInt32Array(1160, 1163, 1161), PackedInt32Array(1161, 1163, 1162), PackedInt32Array(1165, 1164, 1166), PackedInt32Array(1165, 1162, 1164), PackedInt32Array(1164, 1162, 1163), PackedInt32Array(1164, 1168, 1166), PackedInt32Array(1166, 1168, 1157), PackedInt32Array(1157, 1168, 1156), PackedInt32Array(1156, 1168, 1167), PackedInt32Array(1170, 1169, 1148), PackedInt32Array(1148, 1169, 1171), PackedInt32Array(1148, 1171, 1147), PackedInt32Array(1173, 1172, 1174), PackedInt32Array(1174, 1172, 1175), PackedInt32Array(1175, 1172, 1169), PackedInt32Array(1177, 1176, 1175), PackedInt32Array(1175, 1176, 1174), PackedInt32Array(1180, 1179, 1178), PackedInt32Array(1181, 1171, 1178), PackedInt32Array(1178, 1171, 1180), PackedInt32Array(1180, 1171, 1172), PackedInt32Array(1172, 1171, 1169), PackedInt32Array(1184, 1183, 1182), PackedInt32Array(1185, 1184, 1186), PackedInt32Array(1182, 1186, 1184), PackedInt32Array(1182, 1134, 1186), PackedInt32Array(1186, 1134, 1187), PackedInt32Array(1187, 1134, 1188), PackedInt32Array(1188, 1134, 1137), PackedInt32Array(1192, 1191, 1189), PackedInt32Array(1189, 1191, 1190), PackedInt32Array(1195, 1194, 1193), PackedInt32Array(1155, 1196, 1154), PackedInt32Array(1154, 1196, 1198), PackedInt32Array(1154, 1198, 1197), PackedInt32Array(1201, 1200, 1193), PackedInt32Array(1193, 1200, 1199), PackedInt32Array(1193, 1199, 1195), PackedInt32Array(1196, 1203, 1202), PackedInt32Array(1196, 1202, 1198), PackedInt32Array(1193, 1203, 1201), PackedInt32Array(1203, 1193, 1202), PackedInt32Array(1207, 1206, 1204), PackedInt32Array(1204, 1206, 1205), PackedInt32Array(1210, 1209, 1208), PackedInt32Array(1212, 1211, 1179), PackedInt32Array(1212, 1179, 1213), PackedInt32Array(1213, 1179, 1180), PackedInt32Array(1213, 1180, 1214), PackedInt32Array(1215, 1186, 1216), PackedInt32Array(1216, 1186, 1217), PackedInt32Array(1217, 1186, 1218), PackedInt32Array(1221, 1220, 1219), PackedInt32Array(1219, 1222, 1221), PackedInt32Array(1221, 1222, 1218), PackedInt32Array(1221, 1218, 1186), PackedInt32Array(1224, 1223, 1225), PackedInt32Array(1225, 1223, 1222), PackedInt32Array(1226, 1216, 1227), PackedInt32Array(1227, 1216, 1217), PackedInt32Array(1229, 1228, 1230), PackedInt32Array(1230, 1228, 1232), PackedInt32Array(1230, 1232, 1231), PackedInt32Array(1222, 1223, 1218), PackedInt32Array(1221, 1186, 1233), PackedInt32Array(1233, 1186, 1231), PackedInt32Array(1231, 1186, 1230), PackedInt32Array(1230, 1186, 1187), PackedInt32Array(1236, 1235, 1234), PackedInt32Array(1240, 1239, 1237), PackedInt32Array(1237, 1239, 1238), PackedInt32Array(1242, 1241, 1236), PackedInt32Array(1244, 1243, 1241), PackedInt32Array(1241, 1243, 1236), PackedInt32Array(1236, 1243, 1245), PackedInt32Array(1240, 1246, 1239), PackedInt32Array(1239, 1246, 1242), PackedInt32Array(1239, 1242, 1234), PackedInt32Array(1234, 1242, 1236), PackedInt32Array(1248, 1247, 1249), PackedInt32Array(1249, 1247, 1245), PackedInt32Array(1249, 1245, 1250), PackedInt32Array(1250, 1245, 1243), PackedInt32Array(1252, 1251, 1253), PackedInt32Array(1253, 1251, 1254), PackedInt32Array(1250, 1255, 1249), PackedInt32Array(1249, 1255, 1254), PackedInt32Array(1249, 1254, 1257), PackedInt32Array(1249, 1257, 1256), PackedInt32Array(1254, 1251, 1257), PackedInt32Array(1260, 1259, 1258), PackedInt32Array(1262, 1261, 1263), PackedInt32Array(1263, 1261, 1264), PackedInt32Array(1263, 1264, 1260), PackedInt32Array(1263, 1260, 1258), PackedInt32Array(1265, 1199, 1200), PackedInt32Array(1268, 1267, 1266), PackedInt32Array(1265, 1200, 1269), PackedInt32Array(1269, 1200, 1270), PackedInt32Array(1269, 1270, 1268), PackedInt32Array(1269, 1268, 1266), PackedInt32Array(1273, 1272, 1271), PackedInt32Array(1273, 1275, 1274), PackedInt32Array(1271, 1276, 1273), PackedInt32Array(1273, 1276, 1275), PackedInt32Array(1275, 1276, 1278), PackedInt32Array(1275, 1278, 1277), PackedInt32Array(1276, 1279, 1278), PackedInt32Array(1278, 1279, 1259), PackedInt32Array(1259, 1279, 1258), PackedInt32Array(1275, 1280, 1274), PackedInt32Array(1274, 1280, 1281), PackedInt32Array(1281, 1280, 1282), PackedInt32Array(1280, 1283, 1282), PackedInt32Array(1285, 1284, 1237), PackedInt32Array(1287, 1285, 1286), PackedInt32Array(1286, 1285, 1238), PackedInt32Array(1238, 1285, 1237), PackedInt32Array(1267, 1289, 1266), PackedInt32Array(1266, 1289, 1288), PackedInt32Array(1286, 1290, 1287), PackedInt32Array(1287, 1290, 1288), PackedInt32Array(1287, 1288, 1289), PackedInt32Array(1293, 1292, 1291), PackedInt32Array(1295, 1294, 1296), PackedInt32Array(1296, 1294, 1297), PackedInt32Array(1297, 1294, 1291), PackedInt32Array(1291, 1294, 1298), PackedInt32Array(1301, 1300, 1299), PackedInt32Array(1304, 1303, 1302), PackedInt32Array(1302, 1301, 1299), PackedInt32Array(1304, 1302, 1305), PackedInt32Array(1305, 1302, 1306), PackedInt32Array(1298, 1306, 1291), PackedInt32Array(1291, 1306, 1293), PackedInt32Array(1293, 1306, 1302), PackedInt32Array(1293, 1302, 1299), PackedInt32Array(1305, 1306, 1307), PackedInt32Array(1307, 1306, 1308), PackedInt32Array(1261, 1262, 1309), PackedInt32Array(1309, 1262, 1310), PackedInt32Array(1252, 1253, 1311), PackedInt32Array(1311, 1253, 1310), PackedInt32Array(1310, 1253, 1309), PackedInt32Array(1313, 1312, 1314), PackedInt32Array(1314, 1312, 1315), PackedInt32Array(1318, 1317, 1316), PackedInt32Array(1320, 1319, 1321), PackedInt32Array(1321, 1319, 1324), PackedInt32Array(1321, 1324, 1323), PackedInt32Array(1321, 1323, 1322), PackedInt32Array(1323, 1325, 1322), PackedInt32Array(1293, 1299, 1326), PackedInt32Array(1326, 1299, 1328), PackedInt32Array(1326, 1328, 1327), PackedInt32Array(1327, 1330, 1329), PackedInt32Array(1329, 1213, 1327), PackedInt32Array(1327, 1213, 1326), PackedInt32Array(1326, 1213, 1214), PackedInt32Array(1334, 1333, 1331), PackedInt32Array(1331, 1333, 1332), PackedInt32Array(1337, 1336, 1335), PackedInt32Array(1339, 1338, 1340), PackedInt32Array(1342, 1341, 1343), PackedInt32Array(1343, 1341, 1344), PackedInt32Array(1346, 1345, 1347), PackedInt32Array(1347, 1345, 1348), PackedInt32Array(1352, 1351, 1349), PackedInt32Array(1349, 1351, 1350), PackedInt32Array(1355, 1354, 1353), PackedInt32Array(1358, 1357, 1356), PackedInt32Array(1340, 1359, 1360), PackedInt32Array(1362, 1361, 1363), PackedInt32Array(1363, 1361, 1359), PackedInt32Array(1359, 1361, 1360), PackedInt32Array(1366, 1365, 1364), PackedInt32Array(1370, 1369, 1367), PackedInt32Array(1367, 1369, 1368), PackedInt32Array(1372, 1371, 1373), PackedInt32Array(1373, 1371, 1374), PackedInt32Array(1376, 1375, 1377), PackedInt32Array(1377, 1375, 1379), PackedInt32Array(1377, 1379, 1378), PackedInt32Array(1374, 1380, 1379), PackedInt32Array(1373, 1374, 1381), PackedInt32Array(1381, 1374, 1379), PackedInt32Array(1381, 1379, 1375), PackedInt32Array(1381, 1375, 1382), PackedInt32Array(1383, 1232, 1228), PackedInt32Array(1383, 1384, 1232), PackedInt32Array(1232, 1384, 1385), PackedInt32Array(1385, 1384, 1386), PackedInt32Array(1386, 1384, 1387), PackedInt32Array(1389, 1388, 1390), PackedInt32Array(1390, 1388, 1391), PackedInt32Array(1390, 1391, 1367), PackedInt32Array(1367, 1391, 1370), PackedInt32Array(1393, 1392, 1394), PackedInt32Array(1394, 1392, 1395), PackedInt32Array(1392, 1396, 1395), PackedInt32Array(1395, 1398, 1397), PackedInt32Array(1368, 1369, 1399), PackedInt32Array(1397, 1368, 1395), PackedInt32Array(1395, 1368, 1394), PackedInt32Array(1394, 1368, 1399), PackedInt32Array(1394, 1399, 1400), PackedInt32Array(1403, 1402, 1401), PackedInt32Array(1403, 1401, 1400), PackedInt32Array(1400, 1401, 1404), PackedInt32Array(1404, 1401, 1405), PackedInt32Array(1404, 1394, 1400), PackedInt32Array(1406, 1377, 1407), PackedInt32Array(1407, 1377, 1378), PackedInt32Array(1407, 1378, 1408), PackedInt32Array(1408, 1409, 1407), PackedInt32Array(1411, 1410, 1382), PackedInt32Array(1382, 1410, 1412), PackedInt32Array(1382, 1412, 1381), PackedInt32Array(1412, 1410, 1413), PackedInt32Array(1387, 1421, 1416), PackedInt32Array(1416, 1421, 1417), PackedInt32Array(1417, 1420, 1418), PackedInt32Array(1418, 1422, 1419), PackedInt32Array(1419, 1422, 1414), PackedInt32Array(1414, 1384, 1415), PackedInt32Array(1384, 1421, 1387), PackedInt32Array(1421, 1420, 1417), PackedInt32Array(1420, 1422, 1418), PackedInt32Array(1422, 1384, 1414), PackedInt32Array(1384, 1422, 1421), PackedInt32Array(1421, 1422, 1420), PackedInt32Array(1416, 1417, 1423), PackedInt32Array(1423, 1417, 1418), PackedInt32Array(1423, 1418, 1419), PackedInt32Array(1423, 1419, 1414), PackedInt32Array(1426, 1425, 1424), PackedInt32Array(1427, 1426, 1428), PackedInt32Array(1428, 1426, 1429), PackedInt32Array(1430, 1423, 1388), PackedInt32Array(1388, 1423, 1414), PackedInt32Array(1388, 1414, 1391), PackedInt32Array(1431, 1430, 1388), PackedInt32Array(1429, 1426, 1430), PackedInt32Array(1430, 1426, 1424), PackedInt32Array(1430, 1424, 1423), PackedInt32Array(1424, 1432, 1423), PackedInt32Array(1435, 1434, 1433), PackedInt32Array(1437, 1436, 1438), PackedInt32Array(1438, 1436, 1439), PackedInt32Array(1441, 1440, 1439), PackedInt32Array(1441, 1439, 1442), PackedInt32Array(1442, 1439, 1436), PackedInt32Array(1442, 1436, 1443), PackedInt32Array(1443, 1444, 1442), PackedInt32Array(1447, 1446, 1445), PackedInt32Array(1449, 1448, 1450), PackedInt32Array(1450, 1448, 1451), PackedInt32Array(1451, 1453, 1452), PackedInt32Array(1445, 1457, 1447), PackedInt32Array(1447, 1457, 1454), PackedInt32Array(1454, 1459, 1455), PackedInt32Array(1455, 1459, 1401), PackedInt32Array(1401, 1459, 1405), PackedInt32Array(1405, 1457, 1445), PackedInt32Array(1457, 1456, 1454), PackedInt32Array(1454, 1458, 1459), PackedInt32Array(1459, 1457, 1405), PackedInt32Array(1456, 1458, 1454), PackedInt32Array(1457, 1459, 1456), PackedInt32Array(1458, 1456, 1459), PackedInt32Array(1452, 1405, 1451), PackedInt32Array(1451, 1405, 1450), PackedInt32Array(1450, 1405, 1445), PackedInt32Array(1460, 1447, 1461), PackedInt32Array(1461, 1447, 1454), PackedInt32Array(1462, 1464, 1463), PackedInt32Array(1466, 1465, 1413), PackedInt32Array(1468, 1467, 1444), PackedInt32Array(1444, 1467, 1466), PackedInt32Array(1469, 1442, 1410), PackedInt32Array(1410, 1442, 1413), PackedInt32Array(1413, 1442, 1466), PackedInt32Array(1466, 1442, 1444), PackedInt32Array(1470, 1460, 1471), PackedInt32Array(1471, 1460, 1461), PackedInt32Array(1472, 1474, 1473), PackedInt32Array(1474, 1472, 1461), PackedInt32Array(1461, 1472, 1471), PackedInt32Array(1471, 1472, 1475), PackedInt32Array(1477, 1476, 1409), PackedInt32Array(1477, 1409, 1472), PackedInt32Array(1472, 1409, 1475), PackedInt32Array(1475, 1409, 1408), PackedInt32Array(1478, 1480, 1479), PackedInt32Array(1482, 1481, 1483), PackedInt32Array(1483, 1481, 1484), PackedInt32Array(1484, 1486, 1485), PackedInt32Array(1480, 1478, 1438), PackedInt32Array(1330, 1327, 1439), PackedInt32Array(1439, 1327, 1438), PackedInt32Array(1438, 1327, 1480), PackedInt32Array(1480, 1327, 1487), PackedInt32Array(1483, 1484, 1487), PackedInt32Array(1487, 1484, 1485), PackedInt32Array(1487, 1485, 1480), PackedInt32Array(1489, 1488, 1490), PackedInt32Array(1490, 1488, 1491), PackedInt32Array(1492, 1231, 1385), PackedInt32Array(1385, 1231, 1232), PackedInt32Array(1491, 1492, 1493), PackedInt32Array(1495, 1494, 1488), PackedInt32Array(1494, 1496, 1488), PackedInt32Array(1488, 1496, 1491), PackedInt32Array(1491, 1496, 1492), PackedInt32Array(1492, 1496, 1231), PackedInt32Array(1498, 1497, 1499), PackedInt32Array(1499, 1497, 1500), PackedInt32Array(1501, 1503, 1502), PackedInt32Array(1505, 1504, 1506), PackedInt32Array(1506, 1504, 1509), PackedInt32Array(1506, 1509, 1507), PackedInt32Array(1507, 1509, 1508), PackedInt32Array(1500, 1510, 1487), PackedInt32Array(1497, 1511, 1500), PackedInt32Array(1513, 1512, 1514), PackedInt32Array(1514, 1512, 1515), PackedInt32Array(1515, 1512, 1517), PackedInt32Array(1515, 1517, 1516), PackedInt32Array(1519, 1518, 1516), PackedInt32Array(1516, 1518, 1515), PackedInt32Array(1515, 1518, 1520), PackedInt32Array(1521, 1516, 1517), PackedInt32Array(1523, 1522, 1524), PackedInt32Array(1518, 1524, 1520), PackedInt32Array(1520, 1524, 1522), PackedInt32Array(1520, 1522, 1503), PackedInt32Array(1524, 1525, 1523), PackedInt32Array(1508, 1509, 1514), PackedInt32Array(1514, 1509, 1513), PackedInt32Array(1499, 1500, 1501), PackedInt32Array(1501, 1500, 1487), PackedInt32Array(1501, 1487, 1503), PackedInt32Array(1503, 1487, 1520), PackedInt32Array(1520, 1487, 1328), PackedInt32Array(1328, 1487, 1327), PackedInt32Array(1527, 1526, 1512), PackedInt32Array(1512, 1526, 1517), PackedInt32Array(1529, 1528, 1530), PackedInt32Array(1530, 1528, 1531), PackedInt32Array(1531, 1528, 1532), PackedInt32Array(1535, 1534, 1533), PackedInt32Array(1538, 1537, 1536), PackedInt32Array(1536, 1540, 1539), PackedInt32Array(1541, 1538, 1542), PackedInt32Array(1542, 1538, 1543), PackedInt32Array(1543, 1538, 1544), PackedInt32Array(1545, 1547, 1546), PackedInt32Array(1548, 1532, 1549), PackedInt32Array(1549, 1532, 1547), PackedInt32Array(1549, 1547, 1545), PackedInt32Array(1549, 1545, 1550), PackedInt32Array(1531, 1551, 1530), PackedInt32Array(1552, 1549, 1550), PackedInt32Array(1539, 1553, 1536), PackedInt32Array(1536, 1553, 1538), PackedInt32Array(1538, 1553, 1554), PackedInt32Array(1554, 1553, 1550), PackedInt32Array(1555, 1544, 1554), PackedInt32Array(1554, 1544, 1538), PackedInt32Array(1528, 1557, 1556), PackedInt32Array(1558, 1553, 1533), PackedInt32Array(1535, 1553, 1559), PackedInt32Array(1558, 1233, 1231), PackedInt32Array(1553, 1535, 1533), PackedInt32Array(1556, 1547, 1528), PackedInt32Array(1528, 1547, 1532), PackedInt32Array(1553, 1558, 1550), PackedInt32Array(1550, 1558, 1552), PackedInt32Array(1552, 1558, 1496), PackedInt32Array(1496, 1558, 1231)]
agent_height = 1.75
agent_radius = 0.375
agent_max_climb = 0.5
edge_max_length = 12.0
filter_low_hanging_obstacles = true
filter_ledge_spans = true
filter_walkable_low_height_spans = true

[sub_resource type="StandardMaterial3D" id="StandardMaterial3D_cin4e"]
albedo_texture = ExtResource("15_df8b0")
metallic = 0.2
metallic_texture = ExtResource("16_003du")
roughness = 0.8
uv1_scale = Vector3(10, 10, 1)

[sub_resource type="StandardMaterial3D" id="StandardMaterial3D_01ds2"]
transparency = 1
albedo_color = Color(1, 1, 0, 0.3)
emission_enabled = true
emission = Color(1, 1, 0, 1)

[node name="level01" type="Node3D"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 0, 0, 1.94383)
script = ExtResource("1_hk2b6")
minimap_scene = ExtResource("2_j32d5")
player_avatar_scene = ExtResource("3_0f42i")
item_boxes_scene = ExtResource("4_swvig")
skill_box_ui_scene = ExtResource("5_dbohx")

[node name="WorldEnvironment" type="WorldEnvironment" parent="."]
environment = SubResource("Environment_j4i7h")

[node name="DirectionalLight3D" type="DirectionalLight3D" parent="."]
transform = Transform3D(0.707107, -0.5, 0.5, 0, 0.707107, 0.707107, -0.707107, -0.5, 0.5, 0, 0, 0)
light_color = Color(1, 0.95, 0.8, 1)
light_energy = 1.2
shadow_enabled = true
shadow_opacity = 0.6
shadow_blur = 1.5

[node name="ModularLevelGenerator" type="Node3D" parent="."]
script = ExtResource("6_6ly8b")
level_config = ExtResource("7_r5102")
level_size = Vector2(200, 200)

[node name="FogOfWar" parent="." instance=ExtResource("9_003du")]
player_vision_radius = 8.0
enabled = false

[node name="Camera3D" type="Camera3D" parent="."]
unique_name_in_owner = true
transform = Transform3D(1, 0, 0, 0, 0.866025, 0.5, 0, -0.5, 0.866025, 0, 30, 50)
v_offset = 20.0
projection = 1
current = true
size = 44.805
near = 0.022
script = ExtResource("26_camera_follower")
target_path = NodePath("../Player")
follow_speed = 10.0
position_offset = Vector3(0, 10, 55)

[node name="UIManager" type="Node" parent="."]
script = ExtResource("25_ui_manager")
border_width = 100.0

[node name="Player" parent="." instance=ExtResource("11_82jtv")]

[node name="Chest" parent="." instance=ExtResource("14_1ks1q")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, 4.57862, 0, -14.4123)
interaction_distance = 3.0
item_resource = ExtResource("17_j32d5")

[node name="Chest2" parent="." instance=ExtResource("14_1ks1q")]
transform = Transform3D(1.5, 0, 0, 0, 1.5, 0, 0, 0, 1.5, 20, 0.165, -3.779)
interaction_distance = 3.0
item_resource = ExtResource("18_j32d5")

[node name="EnemyBoy01" parent="." instance=ExtResource("19_0f42i")]
transform = Transform3D(0.8, 0, 0, 0, 0.751754, 0.273616, 0, -0.273616, 0.751754, -10.0014, 0, -8.65824)

[node name="Enemy02" parent="." instance=ExtResource("21_dbohx")]
transform = Transform3D(2.1, 0, 0, 0, 1.97335, 0.718242, 0, -0.718242, 1.97335, 7.86917, 0, -7.768)

[node name="NavigationRegion3D" type="NavigationRegion3D" parent="."]
navigation_mesh = SubResource("NavigationMesh_xcdtp")

[node name="Floor" type="Node3D" parent="NavigationRegion3D"]

[node name="@CSGBox3D@166740" type="CSGBox3D" parent="NavigationRegion3D/Floor"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 0, -0.05, 0)
use_collision = true
size = Vector3(200, 0.1, 200)
material = SubResource("StandardMaterial3D_cin4e")

[node name="FloorTiles" type="Node3D" parent="NavigationRegion3D/Floor"]

[node name="Floor01_0" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 62.2459, 0.1, 24.7924)

[node name="Floor01_1" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 32.2873, 0.1, -65.4812)

[node name="Floor01_2" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -45.5368, 0.1, 4.5277)

[node name="Floor01_3" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -7.21767, 0.1, -70.9398)

[node name="Floor01_4" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 4.4179, 0.1, -1.15786)

[node name="Floor01_5" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -73.9478, 0.1, -72.9406)

[node name="Floor01_6" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -83.2323, 0.1, -63.5457)

[node name="Floor01_7" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -8.82557, 0.1, -59.1056)

[node name="Floor01_8" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -62.9981, 0.1, -0.196998)

[node name="Floor01_9" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -31.9753, 0.1, -23.4919)

[node name="Floor01_10" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 93.21, 0.1, -47.5144)

[node name="Floor01_11" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 84.9513, 0.1, -0.823295)

[node name="Floor01_12" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 73.4314, 0.1, 95.9211)

[node name="Floor01_13" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 19.0152, 0.1, 84.3414)

[node name="Floor01_14" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 77.8768, 0.1, 83.1401)

[node name="Floor01_15" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 66.576, 0.1, -9.14418)

[node name="Floor01_16" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -11.1734, 0.1, -94.1324)

[node name="Floor01_17" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -48.8494, 0.1, 93.3963)

[node name="Floor01_18" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 97.2448, 0.1, -56.7725)

[node name="Floor01_19" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 72.4325, 0.1, -71.3308)

[node name="Floor01_20" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 55.0381, 0.1, -0.301969)

[node name="Floor01_21" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 4.38456, 0.1, 89.6682)

[node name="Floor01_22" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -43.2783, 0.1, 78.0001)

[node name="Floor01_23" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -77.6207, 0.1, 26.297)

[node name="Floor01_24" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -79.0122, 0.1, 62.6003)

[node name="Floor01_25" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -70.5308, 0.1, -12.4611)

[node name="Floor01_26" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 57.4375, 0.1, -92.9255)

[node name="Floor01_27" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 11.4077, 0.1, 17.3967)

[node name="Floor01_28" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -52.8076, 0.1, 54.4491)

[node name="Floor01_29" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 9.43441, 0.1, -43.5348)

[node name="Floor01_30" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -80.6115, 0.1, -12.2082)

[node name="Floor01_31" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 13.4533, 0.1, 60.6913)

[node name="Floor01_32" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -34.5102, 0.1, -33.3251)

[node name="Floor01_33" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -86.773, 0.1, 84.3588)

[node name="Floor01_34" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 11.5638, 0.1, -28.5565)

[node name="Floor01_35" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 45.0033, 0.1, -70.3903)

[node name="Floor01_36" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 84.8598, 0.1, -82.5622)

[node name="Floor01_37" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 0.505102, 0.1, -93.8174)

[node name="Floor01_38" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -68.4735, 0.1, -38.307)

[node name="Floor01_39" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 60.0875, 0.1, -59.9479)

[node name="Floor01_40" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 41.3609, 0.1, -5.85835)

[node name="Floor01_41" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -77.3428, 0.1, -56.3694)

[node name="Floor01_42" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 85.0221, 0.1, 71.0112)

[node name="Floor01_43" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -71.0893, 0.1, -94.955)

[node name="Floor01_44" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -52.7283, 0.1, 85.9297)

[node name="Floor01_45" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -63.8339, 0.1, 47.9685)

[node name="Floor01_46" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 81.7707, 0.1, -40.8358)

[node name="Floor01_47" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 82.84, 0.1, -15.113)

[node name="Floor01_48" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -94.9628, 0.1, 57.4441)

[node name="Floor01_49" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -45.2273, 0.1, -4.44924)

[node name="Floor01_50" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 56.1809, 0.1, -80.7942)

[node name="Floor01_51" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 97.7936, 0.1, -1.01438)

[node name="Floor01_52" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 26.8254, 0.1, -4.0676)

[node name="Floor01_53" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 86.9281, 0.1, 58.1206)

[node name="Floor01_54" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -79.6061, 0.1, 41.7035)

[node name="Floor01_55" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -89.7377, 0.1, -44.8121)

[node name="Floor01_56" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 4.32566, 0.1, -66.8239)

[node name="Floor01_57" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 84.7604, 0.1, 48.6423)

[node name="Floor01_58" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -16.3197, 0.1, 88.1545)

[node name="Floor01_59" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 68.0301, 0.1, 78.1956)

[node name="Floor01_60" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -82.8433, 0.1, -90.7737)

[node name="Floor01_61" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 40.6838, 0.1, 74.5475)

[node name="Floor01_62" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -73.5575, 0.1, 18.9127)

[node name="Floor01_63" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 27.8666, 0.1, 78.1016)

[node name="Floor01_64" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 90.9774, 0.1, 14.7496)

[node name="Floor01_65" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -14.6748, 0.1, -2.55296)

[node name="Floor01_66" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -63.4304, 0.1, 70.0842)

[node name="Floor01_67" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 97.1571, 0.1, 82.9556)

[node name="Floor01_68" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 42.8589, 0.1, 36.1947)

[node name="Floor01_69" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -4.69759, 0.1, -42.5086)

[node name="Floor01_70" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -53.0785, 0.1, 68.9675)

[node name="Floor01_71" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -22.9121, 0.1, -83.8554)

[node name="Floor01_72" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -44.7064, 0.1, -17.4269)

[node name="Floor01_73" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -54.1489, 0.1, -52.3326)

[node name="Floor01_74" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 24.1945, 0.1, 66.3397)

[node name="Floor01_75" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 34.5182, 0.1, -33.0342)

[node name="Floor01_76" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -91.4412, 0.1, 77.1667)

[node name="Floor01_77" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 37.395, 0.1, -15.4887)

[node name="Floor01_78" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -22.1205, 0.1, -58.2053)

[node name="Floor01_79" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -25.2132, 0.1, -75.9632)

[node name="Floor01_80" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -14.7277, 0.1, 60.1846)

[node name="Floor01_81" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 54.0738, 0.1, 40.6843)

[node name="Floor01_82" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 80.9557, 0.1, -69.3479)

[node name="Floor01_83" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -92.4683, 0.1, -10.4052)

[node name="Floor01_84" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -52.9834, 0.1, 77.316)

[node name="Floor01_85" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -36.2727, 0.1, -61.5388)

[node name="Floor01_86" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -59.2364, 0.1, 94.9947)

[node name="Floor01_87" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -44.4772, 0.1, -76.1157)

[node name="Floor01_88" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -26.3558, 0.1, -40.8844)

[node name="Floor01_89" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 59.6662, 0.1, -73.5303)

[node name="Floor01_90" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 55.4095, 0.1, 52.921)

[node name="Floor01_91" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 72.2809, 0.1, 25.4562)

[node name="Floor01_92" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 44.7909, 0.1, 94.3774)

[node name="Floor01_93" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -5.27551, 0.1, 25.5375)

[node name="Floor01_94" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 51.8828, 0.1, -60.7682)

[node name="Floor01_95" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 81.2373, 0.1, -28.9233)

[node name="Floor01_96" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 0.893277, 0.1, -12.8395)

[node name="Floor01_97" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -65.8908, 0.1, 28.8829)

[node name="Floor01_98" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 90.3699, 0.1, 30.9312)

[node name="Floor01_99" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 57.3659, 0.1, -39.4629)

[node name="Floor01_100" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -25.0007, 0.1, 4.75575)

[node name="Floor01_101" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -64.5724, 0.1, -64.1496)

[node name="Floor01_102" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -72.4946, 0.1, -1.28724)

[node name="Floor01_103" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 92.6357, 0.1, -93.4272)

[node name="Floor01_104" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 27.0381, 0.1, -38.6047)

[node name="Floor01_105" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 37.34, 0.1, -74.018)

[node name="Floor01_106" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 34.3203, 0.1, 66.4306)

[node name="Floor01_107" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 15.394, 0.1, -68.9458)

[node name="Floor01_108" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 41.9772, 0.1, 21.8907)

[node name="Floor01_109" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -95.2945, 0.1, -0.930265)

[node name="Floor01_110" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 67.1741, 0.1, -37.9178)

[node name="Floor01_111" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 74.9261, 0.1, -59.3001)

[node name="Floor01_112" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -2.9334, 0.1, 61.5119)

[node name="Floor01_113" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -96.8009, 0.1, -74.7001)

[node name="Floor01_114" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -9.49266, 0.1, 77.836)

[node name="Floor01_115" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -84.38, 0.1, 48.5133)

[node name="Floor01_116" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -6.45232, 0.1, 42.6942)

[node name="Floor01_117" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -85.726, 0.1, 12.7469)

[node name="Floor01_118" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -35.1332, 0.1, 88.3334)

[node name="Floor01_119" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -4.5688, 0.1, 15.7605)

[node name="Floor01_120" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 5.86784, 0.1, 66.2283)

[node name="Floor01_121" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 16.3813, 0.1, 70.2833)

[node name="Floor01_122" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 94.54, 0.1, -85.4424)

[node name="Floor01_123" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 72.3782, 0.1, -29.269)

[node name="Floor01_124" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -34.2926, 0.1, -1.83765)

[node name="Floor01_125" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -21.0936, 0.1, 16.4333)

[node name="Floor01_126" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 34.6302, 0.1, 37.3171)

[node name="Floor01_127" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 94.364, 0.1, -16.6503)

[node name="Floor01_128" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 77.404, 0.1, 55.1962)

[node name="Floor01_129" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -73.0825, 0.1, 46.5794)

[node name="Floor01_130" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 91.1631, 0.1, -67.1039)

[node name="Floor01_131" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 14.3179, 0.1, -97.3455)

[node name="Floor01_132" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -1.83117, 0.1, 71.5557)

[node name="Floor01_133" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 10.773, 0.1, 33.7815)

[node name="Floor01_134" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 20.2118, 0.1, -14.4584)

[node name="Floor01_135" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -25.7357, 0.1, 27.2428)

[node name="Floor01_136" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 34.3884, 0.1, 90.4523)

[node name="Floor01_137" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 55.2437, 0.1, 80.5296)

[node name="Floor01_138" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -26.1733, 0.1, 83.9667)

[node name="Floor01_139" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -21.3987, 0.1, -24.3491)

[node name="Floor01_140" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -87.1004, 0.1, -19.6638)

[node name="Floor01_141" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 13.0047, 0.1, 78.3204)

[node name="Floor01_142" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -93.6143, 0.1, -59.6976)

[node name="Floor01_143" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -22.068, 0.1, 43.9865)

[node name="Floor01_144" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 28.438, 0.1, -15.9171)

[node name="Floor01_145" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -87.8201, 0.1, 33.7995)

[node name="Floor01_146" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 32.5746, 0.1, 18.2272)

[node name="Floor01_147" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -20.9757, 0.1, 96.3386)

[node name="Floor01_148" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 29.2902, 0.1, -74.9227)

[node name="Floor01_149" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -69.164, 0.1, 79.5363)

[node name="Floor01_150" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -21.3469, 0.1, 72.8861)

[node name="Floor01_151" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 64.1334, 0.1, -17.4082)

[node name="Floor01_152" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -79.0574, 0.1, -45.0294)

[node name="Floor01_153" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -37.3253, 0.1, 10.3467)

[node name="Floor01_154" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 95.4652, 0.1, 21.7328)

[node name="Floor01_155" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 66.5111, 0.1, -91.6562)

[node name="Floor01_156" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -43.4754, 0.1, -95.8766)

[node name="Floor01_157" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -45.1381, 0.1, 27.0408)

[node name="Floor01_158" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 48.0723, 0.1, 84.1344)

[node name="Floor01_159" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -96.7715, 0.1, 86.8438)

[node name="Floor01_160" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -58.8264, 0.1, 33.8792)

[node name="Floor01_161" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 63.3491, 0.1, 35.8059)

[node name="Floor01_162" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 43.6251, 0.1, 59.8797)

[node name="Floor01_163" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -9.77561, 0.1, 67.5205)

[node name="Floor01_164" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -66.7751, 0.1, -53.0112)

[node name="Floor01_165" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 62.3439, 0.1, 69.7637)

[node name="Floor01_166" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 12.0066, 0.1, 41.8009)

[node name="Floor01_167" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 93.3552, 0.1, 40.1331)

[node name="Floor01_168" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -62.3425, 0.1, 20.5625)

[node name="Floor01_169" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -95.5486, 0.1, 28.5933)

[node name="Floor01_170" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 23.0581, 0.1, 28.2278)

[node name="Floor01_171" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -42.6732, 0.1, -28.8472)

[node name="Floor01_172" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 27.3057, 0.1, 56.8184)

[node name="Floor01_173" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 27.1857, 0.1, -87.2937)

[node name="Floor01_174" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 41.6556, 0.1, -57.7018)

[node name="Floor01_175" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -53.8699, 0.1, -68.9635)

[node name="Floor01_176" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -58.8071, 0.1, 61.4484)

[node name="Floor01_177" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 79.4157, 0.1, 34.3803)

[node name="Floor01_178" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 75.8561, 0.1, 3.88225)

[node name="Floor01_179" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 2.9441, 0.1, 27.9254)

[node name="Floor01_180" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 44.8701, 0.1, -19.9036)

[node name="Floor01_181" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 45.8571, 0.1, 2.83338)

[node name="Floor01_182" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -26.4115, 0.1, -7.81055)

[node name="Floor01_183" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -89.1033, 0.1, 95.4725)

[node name="Floor01_184" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 44.336, 0.1, -31.4736)

[node name="Floor01_185" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -40.0104, 0.1, 69.237)

[node name="Floor01_186" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -25.3747, 0.1, -93.8989)

[node name="Floor01_187" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 3.13225, 0.1, 12.0047)

[node name="Floor01_188" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -6.52211, 0.1, -20.0537)

[node name="Floor01_189" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -58.8293, 0.1, -42.8816)

[node name="Floor01_190" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -25.9676, 0.1, 61.7955)

[node name="Floor01_191" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -36.6106, 0.1, 57.3738)

[node name="Floor01_192" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 64.6016, 0.1, 11.6054)

[node name="Floor01_193" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -85.9017, 0.1, -35.7142)

[node name="Floor01_194" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 78.8329, 0.1, -50.2248)

[node name="Floor01_195" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -95.3686, 0.1, -23.8362)

[node name="Floor01_196" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 86.1819, 0.1, 88.1601)

[node name="Floor01_197" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 0.938653, 0.1, -31.7024)

[node name="Floor01_198" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -39.6765, 0.1, 19.8555)

[node name="Floor01_199" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -66.2721, 0.1, -29.0745)

[node name="Floor01_200" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -70.508, 0.1, 62.863)

[node name="Floor01_201" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -78.571, 0.1, 90.6629)

[node name="Floor01_202" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -75.9762, 0.1, -31.3123)

[node name="Floor01_203" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -89.8726, 0.1, 22.0996)

[node name="Floor01_204" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -88.8938, 0.1, -83.1213)

[node name="Floor01_205" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 14.3685, 0.1, -85.409)

[node name="Floor01_206" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -12.0143, 0.1, -32.389)

[node name="Floor01_207" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -23.5082, 0.1, -16.613)

[node name="Floor01_208" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -11.0557, 0.1, -78.8909)

[node name="Floor01_209" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 58.1552, 0.1, -29.4543)

[node name="Floor01_210" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -57.2855, 0.1, -93.5067)

[node name="Floor01_211" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -47.644, 0.1, -85.3743)

[node name="Floor01_212" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -28.2951, 0.1, -64.5962)

[node name="Floor01_213" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 54.3518, 0.1, -17.713)

[node name="Floor01_214" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -72.4544, 0.1, 36.2584)

[node name="Floor01_215" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 57.498, 0.1, 17.9371)

[node name="Floor01_216" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 97.5511, 0.1, -34.4908)

[node name="Floor01_217" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -97.8392, 0.1, 49.2699)

[node name="Floor01_218" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -66.229, 0.1, 90.6034)

[node name="Floor01_219" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 97.99, 0.1, -25.0073)

[node name="Floor01_220" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 42.8868, 0.1, 44.6394)

[node name="Floor01_221" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 1.12595, 0.1, 36.7047)

[node name="Floor01_222" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 35.2794, 0.1, -95.298)

[node name="Floor01_223" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 26.1058, 0.1, 94.4725)

[node name="Floor01_224" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -31.2263, 0.1, 43.2191)

[node name="Floor01_225" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 60.2723, 0.1, 60.7519)

[node name="Floor01_226" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 18.6813, 0.1, -58.4891)

[node name="Floor01_227" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -0.662808, 0.1, -84.9938)

[node name="Floor01_228" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -34.756, 0.1, -89.7585)

[node name="Floor01_229" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 34.8435, 0.1, 26.1244)

[node name="Floor01_230" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 35.7084, 0.1, 5.00799)

[node name="Floor01_231" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -95.2764, 0.1, 40.2918)

[node name="Floor01_232" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -32.5971, 0.1, 78.2874)

[node name="Floor01_233" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -10.0127, 0.1, 96.8922)

[node name="Floor01_234" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -75.5671, 0.1, -85.2217)

[node name="Floor01_235" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -55.3241, 0.1, -12.6725)

[node name="Floor01_236" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 68.6485, 0.1, 57.6067)

[node name="Floor01_237" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 81.5748, 0.1, -93.4848)

[node name="Floor01_238" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -86.7025, 0.1, 2.90317)

[node name="Floor01_239" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 29.8286, 0.1, -25.1207)

[node name="Floor01_240" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -38.0742, 0.1, 96.536)

[node name="Floor01_241" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 71.1467, 0.1, 70.7604)

[node name="Floor01_242" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 96.3222, 0.1, 8.5619)

[node name="Floor01_243" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 3.89054, 0.1, 80.0176)

[node name="Floor01_244" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 72.878, 0.1, -15.475)

[node name="Floor01_245" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -76.2774, 0.1, -19.8916)

[node name="Floor01_246" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -50.0045, 0.1, -60.4968)

[node name="Floor01_247" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 72.3246, 0.1, 43.2033)

[node name="Floor01_248" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -55.1324, 0.1, -4.12822)

[node name="Floor01_249" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 65.3764, 0.1, 97.4123)

[node name="Floor01_250" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 44.9268, 0.1, -96.8284)

[node name="Floor01_251" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -34.0594, 0.1, -12.5641)

[node name="Floor01_252" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 56.3394, 0.1, 94.0717)

[node name="Floor01_253" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 83.0627, 0.1, 22.0873)

[node name="Floor01_254" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 10.4499, 0.1, -60.2996)

[node name="Floor01_255" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -17.2568, 0.1, 31.8884)

[node name="Floor01_256" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -78.5029, 0.1, 54.3125)

[node name="Floor01_257" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -94.2343, 0.1, -92.043)

[node name="Floor01_258" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -12.4987, 0.1, 13.4949)

[node name="Floor01_259" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 73.2024, 0.1, -83.885)

[node name="Floor01_260" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 51.4859, 0.1, 66.6431)

[node name="Floor01_261" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -40.7134, 0.1, 44.6995)

[node name="Floor01_262" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 97.9723, 0.1, 59.1004)

[node name="Floor01_263" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -97.27, 0.1, -34.713)

[node name="Floor01_264" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -62.5724, 0.1, -72.5298)

[node name="Floor01_265" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 94.7536, 0.1, -75.8358)

[node name="Floor01_266" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -35.5506, 0.1, 31.6049)

[node name="Floor01_267" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 38.4219, 0.1, -43.4528)

[node name="Floor01_268" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 61.8824, 0.1, -50.0357)

[node name="Floor01_269" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 85.4993, 0.1, -60.4825)

[node name="Floor01_270" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -54.652, 0.1, 13.0821)

[node name="Floor01_271" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 65.3351, 0.1, 47.5595)

[node name="Floor01_272" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 45.6503, 0.1, -85.3127)

[node name="Floor01_273" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -77.3624, 0.1, 10.5329)

[node name="Floor01_274" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -27.1678, 0.1, 35.9163)

[node name="wall_0" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1.06598, 0, -0.10453, 0, 1, 0, 0.119309, 0, 0.933931, -47.5, 0, -50)

[node name="wall_1" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1.28592, 0, -0.189443, 0, 1, 0, 0.182671, 0, 1.33359, -42.5, 0, -50)

[node name="wall_2" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1.3913, 0, -0.0529276, 0, 1, 0, 0.0560999, 0, 1.31263, -35, 0, -50)

[node name="wall_3" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1.3273, 0, -0.143011, 0, 1, 0, 0.155567, 0, 1.22018, -27.5, 0, -50)

[node name="wall_4" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1.20677, 0, 0.111519, 0, 1, 0, -0.102018, 0, 1.31915, -20, 0, -50)

[node name="wall_5" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(0.844525, 0, 0.254438, 0, 1, 0, -0.187739, 0, 1.14456, -12.5, 0, -50)

[node name="wall_6" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1.13797, 0, -0.439048, 0, 1, 0, 0.39562, 0, 1.26289, -5, 0, -50)

[node name="wall_7" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1.08657, 0, -0.247767, 0, 1, 0, 0.352696, 0, 0.763311, 2.5, 0, -50)

[node name="wall_8" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1.27094, 0, 0.315618, 0, 1, 0, -0.32527, 0, 1.23322, 10, 0, -50)

[node name="wall_9" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1.15483, 0, -0.219746, 0, 1, 0, 0.309718, 0, 0.819357, 17.5, 0, -50)

[node name="wall_10" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1.31941, 0, 0.232975, 0, 1, 0, -0.227086, 0, 1.35363, 25, 0, -50)

[node name="wall_11" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(0.979904, 0, -0.236423, 0, 1, 0, 0.263677, 0, 0.878619, 32.5, 0, -50)

[node name="wall_12" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1.03205, -0.00896962, 0.138835, 0, 0.998132, 0.0578691, -0.153198, -0.0604258, 0.935292, 40, 0.382019, -50)

[node name="wall_13" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1.01003, 0, 0.147582, 0, 1, 0, -0.174367, 0, 0.854872, 47.5, 0, -50)

[node name="wall_14" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1.03462, 0.00250856, 0.0607735, 0, 0.99919, -0.0392463, -0.0646356, 0.0401544, 0.972798, -47.5, 0.313584, 50)

[node name="wall_15" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(0.890057, 0, 0.107442, 0, 1, 0, -0.0830929, 0, 1.15087, -42.5, 0, 50)

[node name="wall_16" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1.17738, 0, 0.0564868, 0, 1, 0, -0.0725331, 0, 0.916912, -35, 0, 50)

[node name="wall_17" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(0.938023, 0, -0.249211, 0, 1, 0, 0.271567, 0, 0.860803, -27.5, 0, 50)

[node name="wall_18" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(0.91056, 0, -0.138557, 0, 1, 0, 0.138495, 0, 0.910964, -20, 0, 50)

[node name="wall_19" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1.14754, 0, -0.421142, 0, 1, 0, 0.365184, 0, 1.32337, -12.5, 0, 50)

[node name="wall_20" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1.3058, 0, -0.393893, 0, 1, 0, 0.440447, 0, 1.16778, -5, 0, 50)

[node name="wall_21" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1.3269, 0, -0.284888, 0, 1, 0, 0.297412, 0, 1.27102, 2.5, 0, 50)

[node name="wall_22" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1.14894, 0, -0.104741, 0, 1, 0, 0.147491, 0, 0.815925, 10, 0, 50)

[node name="wall_23" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1.11317, 0, 0.077891, 0, 1, 0, -0.0950536, 0, 0.912182, 17.5, 0, 50)

[node name="wall_24" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1.02679, 0.00746554, 0.16794, 0, 0.998895, -0.0497457, -0.165196, 0.0464025, 1.04384, 25, 0.319671, 50)

[node name="wall_25" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1.14155, 0, -0.0984504, 0, 1, 0, 0.120681, 0, 0.931263, 32.5, 0, 50)

[node name="wall_26" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(0.791029, 0, -0.350108, 0, 1, 0, 0.275787, 0, 1.0042, 40, 0, 50)

[node name="wall_27" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1.06693, 0, -0.265794, 0, 1, 0, 0.262259, 0, 1.08131, 47.5, 0, 50)

[node name="wall_28" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-0.117053, 0, 1.09689, 0, 1, 0, -1.15452, 0, -0.11121, -50, 0, -45)

[node name="wall_29" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(0.3308, -0.0713394, 1.01819, 0, 0.997161, 0.0811407, -0.980162, -0.0240767, 0.343633, -50, 0.437612, -37.5)

[node name="wall_30" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(0.302741, -0.0941806, 0.936435, 0, 0.995186, 0.0959588, -1.05206, -0.0271013, 0.269468, -50, 0.461167, -30)

[node name="wall_31" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-0.121027, 0, 0.799884, 0, 1, 0, -0.829753, 0, -0.116671, -50, 0, -22.5)

[node name="wall_32" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(0.18879, -0.00718462, 1.05327, 0, 0.999973, 0.00790355, -0.895949, -0.00151391, 0.22194, -50, 0.343103, -15)

[node name="wall_33" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(0.257055, -0.00893814, 0.932635, 0, 0.999957, 0.00888666, -1.00052, -0.00229641, 0.239615, -50, 0.334126, -7.5)

[node name="wall_34" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(0.0208441, 0, 1.07811, 0, 1, 0, -1.10565, 0, 0.0203249, -50, 0, 0)

[node name="wall_35" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-0.287144, 0, 1.06173, 0, 1, 0, -0.854993, 0, -0.356574, -50, 0, 7.5)

[node name="wall_36" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(0.045874, 0, 1.03466, 0, 1, 0, -0.833536, 0, 0.056943, -50, 0, 15)

[node name="wall_37" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-0.312406, 0.0492266, 1.04358, 0, 0.998629, -0.0581549, -0.864849, -0.0177819, -0.376968, -50, 0.409248, 22.5)

[node name="wall_38" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(0.185069, 0, 0.947972, 0, 1, 0, -1.11245, 0, 0.157706, -50, 0, 30)

[node name="wall_39" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-0.0893274, 0, 0.867739, 0, 1, 0, -1.1204, 0, -0.0691834, -50, 0, 37.5)

[node name="wall_40" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(0.133618, 0, 0.992615, 0, 1, 0, -0.869357, 0, 0.152563, -50, 0, 45)

[node name="wall_41" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-0.238849, 0, 1.0311, 0, 1, 0, -1.05953, 0, -0.232441, 50, 0, -45)

[node name="wall_42" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-0.133488, 0, 1.04624, 0, 1, 0, -1.04426, 0, -0.133742, 50, 0, -37.5)

[node name="wall_43" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(0.396594, 0, 1.15467, 0, 1, 0, -1.30228, 0, 0.35164, 50, 0, -30)

[node name="wall_44" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(0.263435, 0, 1.3485, 0, 1, 0, -1.3711, 0, 0.259094, 50, 0, -22.5)

[node name="wall_45" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(0.156465, 0, 0.929426, 0, 1, 0, -1.05053, 0, 0.138429, 50, 0, -15)

[node name="wall_46" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(0.430859, 0, 1.29553, 0, 1, 0, -1.26338, 0, 0.441826, 50, 0, -7.5)

[node name="wall_47" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(0.126226, 0, 1.25089, 0, 1, 0, -1.23909, 0, 0.127427, 50, 0, 0)

[node name="wall_48" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-0.0955467, 0, 1.17918, 0, 1, 0, -0.964859, 0, -0.11677, 50, 0, 7.5)

[node name="wall_49" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-0.219932, 0, 1.18676, 0, 1, 0, -1.32499, 0, -0.196988, 50, 0, 15)

[node name="wall_50" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-0.0143792, 0, 0.827496, 0, 1, 0, -0.888235, 0, -0.013396, 50, 0, 22.5)

[node name="wall_51" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(0.144939, 0, 0.802254, 0, 1, 0, -1.03216, 0, 0.112654, 50, 0, 30)

[node name="wall_52" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-0.0649965, 0, 1.24378, 0, 1, 0, -1.2553, 0, -0.0643997, 50, 0, 37.5)

[node name="wall_53" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(0.24807, 0, 0.937115, 0, 1, 0, -0.76258, 0, 0.304847, 50, 0, 45)

[node name="tree_0" parent="NavigationRegion3D" instance=ExtResource("15_003du")]
transform = Transform3D(2.5, -0.00145593, -0.00167976, 0.00145725, 2.5, 0.00196474, 0.00167861, -0.00196571, 2.5, 16.0458, 0, -9.74389)
highlight_material = SubResource("StandardMaterial3D_01ds2")

[node name="PatrolPoint_0" type="Node3D" parent="NavigationRegion3D"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 13.5908, 1, -8.67021)
script = ExtResource("15_6ly8b")

[node name="tree_1" parent="NavigationRegion3D" instance=ExtResource("15_003du")]
transform = Transform3D(2.5, -0.00145593, -0.00167976, 0.00145725, 2.5, 0.00196474, 0.00167861, -0.00196571, 2.5, 4.02305, 0, -19.1017)
highlight_material = SubResource("StandardMaterial3D_01ds2")
preset_burnt_out_type = 1

[node name="PatrolPoint_1" type="Node3D" parent="NavigationRegion3D"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 1.75591, 1, -15.0501)
script = ExtResource("15_6ly8b")
point_id = 1

[node name="tree_2" parent="NavigationRegion3D" instance=ExtResource("15_003du")]
transform = Transform3D(2.5, -0.00145593, -0.00167976, 0.00145725, 2.5, 0.00196474, 0.00167861, -0.00196571, 2.5, -16.7453, 0, -12.8152)
highlight_material = SubResource("StandardMaterial3D_01ds2")

[node name="PatrolPoint_2" type="Node3D" parent="NavigationRegion3D"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -16.1549, 1, -13.7461)
script = ExtResource("15_6ly8b")
point_id = 2

[node name="tree_3" parent="NavigationRegion3D" instance=ExtResource("15_003du")]
transform = Transform3D(2.5, -0.00145593, -0.00167976, 0.00145725, 2.5, 0.00196474, 0.00167861, -0.00196571, 2.5, -5.14426, 0, -2.90108)
highlight_material = SubResource("StandardMaterial3D_01ds2")

[node name="PatrolPoint_3" type="Node3D" parent="NavigationRegion3D"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -9.015, 1, -4.84933)
script = ExtResource("15_6ly8b")
point_id = 3

[node name="chest_0" parent="NavigationRegion3D" instance=ExtResource("14_1ks1q")]
transform = Transform3D(-1.99884, 0, -0.068157, 0, 2, 0, 0.068157, 0, -1.99884, -16.2431, 0, -23.4605)

[node name="rock_0" parent="NavigationRegion3D" instance=ExtResource("21_xcdtp")]
transform = Transform3D(-0.944379, 0, 0.560736, 0, 1.09831, 0, -0.560736, 0, -0.944379, -9.45014, 0.01, 31.4624)

[node name="rock_1" parent="NavigationRegion3D" instance=ExtResource("21_xcdtp")]
transform = Transform3D(-0.151638, 0, 1.16397, 0, 1.1738, 0, -1.16397, 0, -0.151638, 31.4231, 0.01, 11.0949)

[node name="rock_2" parent="NavigationRegion3D" instance=ExtResource("21_xcdtp")]
transform = Transform3D(-0.537179, 0, 0.813769, 0, 0.97508, 0, -0.813769, 0, -0.537179, -12.6935, 0.01, 21.234)

[node name="rock_3" parent="NavigationRegion3D" instance=ExtResource("21_xcdtp")]
transform = Transform3D(-0.6323, 0, 0.711603, 0, 0.951936, 0, -0.711603, 0, -0.6323, 11.6042, 0.01, -23.3615)

[node name="rock_4" parent="NavigationRegion3D" instance=ExtResource("21_xcdtp")]
transform = Transform3D(-0.510454, 0, 0.628453, 0, 0.80964, 0, -0.628453, 0, -0.510454, -22.8197, 0.01, -3.38664)

[node name="rock_5" parent="NavigationRegion3D" instance=ExtResource("21_xcdtp")]
transform = Transform3D(0.226311, 0, 0.876541, 0, 0.905285, 0, -0.876541, 0, 0.226311, -2.9288, 0.01, 5.81222)

[node name="rock_6" parent="NavigationRegion3D" instance=ExtResource("21_xcdtp")]
transform = Transform3D(-0.860436, 0, -0.244334, 0, 0.894455, 0, 0.244334, 0, -0.860436, -9.93531, 0.01, -0.325793)

[node name="rock_7" parent="NavigationRegion3D" instance=ExtResource("21_xcdtp")]
transform = Transform3D(0.906858, 0, 0.164604, 0, 0.921676, 0, -0.164604, 0, 0.906858, -8.21134, 0.01, 9.08861)

[node name="rock_8" parent="NavigationRegion3D" instance=ExtResource("21_xcdtp")]
transform = Transform3D(-0.144841, 0, -0.873063, 0, 0.884996, 0, 0.873063, 0, -0.144841, -28.3447, 0.01, -14.4477)

[node name="rock_9" parent="NavigationRegion3D" instance=ExtResource("21_xcdtp")]
transform = Transform3D(0.200017, 0, 1.15508, 0, 1.17227, 0, -1.15508, 0, 0.200017, 21.1407, 0.01, 38.3)

[node name="rock_10" parent="NavigationRegion3D" instance=ExtResource("21_xcdtp")]
transform = Transform3D(-0.828098, 0, 0.176606, 0, 0.846721, 0, -0.176606, 0, -0.828098, -9.88483, 0.01, 38.2771)

[node name="rock_11" parent="NavigationRegion3D" instance=ExtResource("21_xcdtp")]
transform = Transform3D(0.378136, 0, 0.747651, 0, 0.837836, 0, -0.747651, 0, 0.378136, -30.7529, 0.01, -28.6852)

[node name="rock_12" parent="NavigationRegion3D" instance=ExtResource("21_xcdtp")]
transform = Transform3D(1.0808, 0, 0.14352, 0, 1.09029, 0, -0.14352, 0, 1.0808, 11.6139, 0.01, -18.7569)

[node name="rock_13" parent="NavigationRegion3D" instance=ExtResource("21_xcdtp")]
transform = Transform3D(0.0390674, 0, -1.1815, 0, 1.18215, 0, 1.1815, 0, 0.0390674, 15.351, 0.01, -33.861)

[node name="rock_14" parent="NavigationRegion3D" instance=ExtResource("21_xcdtp")]
transform = Transform3D(-0.667087, 0, -0.959989, 0, 1.16901, 0, 0.959989, 0, -0.667087, -28.9043, 0.01, -31.9772)

[node name="barrel_0" parent="NavigationRegion3D" instance=ExtResource("22_cin4e")]
transform = Transform3D(-0.671944, 0, -0.914718, 0, 1.135, 0, 0.914718, 0, -0.671944, -13.9807, 0, -8.16792)

[node name="barrel_1" parent="NavigationRegion3D" instance=ExtResource("22_cin4e")]
transform = Transform3D(0.733018, 0, -0.777001, 0, 1.0682, 0, 0.777001, 0, 0.733018, 12.575, 0, 2.10284)

[node name="decoration_0" parent="NavigationRegion3D" instance=ExtResource("23_6j2fk")]
transform = Transform3D(0.0455992, 0, 1.21049, 0, 1.21135, 0, -1.21049, 0, 0.0455992, -53.2002, 0, -86.4744)

[node name="decoration_1" parent="NavigationRegion3D" instance=ExtResource("23_6j2fk")]
transform = Transform3D(-0.920921, 0, -0.680066, 0, 1.14481, 0, 0.680066, 0, -0.920921, 18.3353, 0, -80.783)

[node name="decoration_2" parent="NavigationRegion3D" instance=ExtResource("23_6j2fk")]
transform = Transform3D(-0.10633, 0, 1.21321, 0, 1.21786, 0, -1.21321, 0, -0.10633, 91.4602, 0, -56.4115)

[node name="decoration_3" parent="NavigationRegion3D" instance=ExtResource("23_6j2fk")]
transform = Transform3D(0.629973, 0, -0.219659, 0, 0.66717, 0, 0.219659, 0, 0.629973, 90.6171, 0, 85.2779)

[node name="decoration_4" parent="NavigationRegion3D" instance=ExtResource("23_6j2fk")]
transform = Transform3D(-1.05983, 0, 0.225973, 0, 1.08366, 0, -0.225973, 0, -1.05983, -82.3226, 0, 94.008)

[node name="decoration_5" parent="NavigationRegion3D" instance=ExtResource("23_6j2fk")]
transform = Transform3D(0.212686, 0, 1.26857, 0, 1.28628, 0, -1.26857, 0, 0.212686, -32.8261, 0, 25.9722)

[node name="decoration_6" parent="NavigationRegion3D" instance=ExtResource("23_6j2fk")]
transform = Transform3D(0.872903, 0, 0.96759, 0, 1.30315, 0, -0.96759, 0, 0.872903, -15.0691, 0, 66.9052)

[node name="decoration_7" parent="NavigationRegion3D" instance=ExtResource("23_6j2fk")]
transform = Transform3D(-0.199793, 0, -1.18914, 0, 1.20581, 0, 1.18914, 0, -0.199793, -45.7651, 0, 69.8354)

[node name="decoration_8" parent="NavigationRegion3D" instance=ExtResource("23_6j2fk")]
transform = Transform3D(0.310472, 0, -1.27349, 0, 1.31079, 0, 1.27349, 0, 0.310472, -66.7094, 0, 11.5445)

[node name="decoration_9" parent="NavigationRegion3D" instance=ExtResource("23_6j2fk")]
transform = Transform3D(0.980165, 0, -0.113821, 0, 0.986752, 0, 0.113821, 0, 0.980165, -38.8581, 0, 36.1166)

[node name="decoration_10" parent="NavigationRegion3D" instance=ExtResource("23_6j2fk")]
transform = Transform3D(-1.04205, 0, 0.643712, 0, 1.22484, 0, -0.643712, 0, -1.04205, -92.8063, 0, -40.5705)

[node name="decoration_11" parent="NavigationRegion3D" instance=ExtResource("23_6j2fk")]
transform = Transform3D(0.847328, 0, -0.385383, 0, 0.930852, 0, 0.385383, 0, 0.847328, 9.03356, 0, -68.9579)

[node name="decoration_12" parent="NavigationRegion3D" instance=ExtResource("23_6j2fk")]
transform = Transform3D(-0.593681, 0, 0.609647, 0, 0.850956, 0, -0.609647, 0, -0.593681, -34.5181, 0, 65.748)

[node name="decoration_13" parent="NavigationRegion3D" instance=ExtResource("23_6j2fk")]
transform = Transform3D(-0.798216, 0, -0.698824, 0, 1.0609, 0, 0.698824, 0, -0.798216, -9.99698, 0, -47.607)

[node name="decoration_14" parent="NavigationRegion3D" instance=ExtResource("23_6j2fk")]
transform = Transform3D(-0.953141, 0, -0.761508, 0, 1.21999, 0, 0.761508, 0, -0.953141, -83.8278, 0, -73.9714)

[node name="decoration_15" parent="NavigationRegion3D" instance=ExtResource("23_6j2fk")]
transform = Transform3D(1.04103, 0, -0.789254, 0, 1.3064, 0, 0.789254, 0, 1.04103, 51.8853, 0, -47.3923)

[node name="decoration_16" parent="NavigationRegion3D" instance=ExtResource("23_6j2fk")]
transform = Transform3D(-0.502061, 0, 0.775795, 0, 0.92408, 0, -0.775795, 0, -0.502061, 29.7839, 0, -56.5405)

[node name="decoration_17" parent="NavigationRegion3D" instance=ExtResource("23_6j2fk")]
transform = Transform3D(-0.947484, 0, 0.0455314, 0, 0.948577, 0, -0.0455314, 0, -0.947484, 73.7571, 0, -7.8917)

[node name="decoration_18" parent="NavigationRegion3D" instance=ExtResource("23_6j2fk")]
transform = Transform3D(-0.751428, 0, 0.315831, 0, 0.815103, 0, -0.315831, 0, -0.751428, -79.0895, 0, 75.9411)

[node name="decoration_19" parent="NavigationRegion3D" instance=ExtResource("23_6j2fk")]
transform = Transform3D(-0.102087, 0, 1.00804, 0, 1.0132, 0, -1.00804, 0, -0.102087, 67.9791, 0, -84.2668)

[node name="PatrolPointManager" type="Node" parent="."]
script = ExtResource("24_j2uky")
