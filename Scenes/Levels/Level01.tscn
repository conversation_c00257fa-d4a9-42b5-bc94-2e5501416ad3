[gd_scene load_steps=38 format=3 uid="uid://c0rj4dwj18nv8"]

[ext_resource type="Script" uid="uid://bo0h8t35yls55" path="res://Scripts/Levels/Level01.gd" id="1_hk2b6"]
[ext_resource type="PackedScene" uid="uid://d2xyw4d37ymv4" path="res://Scenes/Minimap.tscn" id="2_j32d5"]
[ext_resource type="PackedScene" uid="uid://ctxvyh1qr52ue" path="res://Scenes/PlayerAvatar.tscn" id="3_0f42i"]
[ext_resource type="PackedScene" uid="uid://do1sypgxbecbd" path="res://Scenes/ItemBoxes.tscn" id="4_swvig"]
[ext_resource type="PackedScene" uid="uid://b4l4phqqbvprm" path="res://Scenes/SkillBoxUI.tscn" id="5_dbohx"]
[ext_resource type="Script" uid="uid://f54r1khulcvh" path="res://Scripts/ModularLevelGenerator.gd" id="6_6ly8b"]
[ext_resource type="Resource" uid="uid://cvji54t3pjwfe" path="res://Resources/LevelConfigs/AllModulesConfig.tres" id="7_r5102"]
[ext_resource type="PackedScene" uid="uid://d2b5h1mlxxy7d" path="res://Scenes/FogOfWar.tscn" id="9_003du"]
[ext_resource type="PackedScene" uid="uid://b88l8pk1ebe1x" path="res://Scenes/player.tscn" id="11_82jtv"]
[ext_resource type="PackedScene" uid="uid://c3h8fj2xsp5oy" path="res://Scenes/Prefabs/Chest.tscn" id="14_1ks1q"]
[ext_resource type="PackedScene" uid="uid://dwusy8dd8usvo" path="res://Scenes/Prefabs/Wall.tscn" id="14_df8b0"]
[ext_resource type="PackedScene" uid="uid://crtnthqkksmri" path="res://Scenes/Prefabs/Tree.tscn" id="15_003du"]
[ext_resource type="Script" uid="uid://c3tr23vwvnmwf" path="res://Scripts/PatrolPoint.gd" id="15_6ly8b"]
[ext_resource type="Texture2D" uid="uid://c2ny0yi07rvcf" path="res://Environment/Floor/Floor01_Rocks_BaseColor.png" id="15_df8b0"]
[ext_resource type="Texture2D" uid="uid://dhfikoo16s5n0" path="res://Environment/Floor/Rocks_Metallic.png" id="16_003du"]
[ext_resource type="PackedScene" uid="uid://ddttv643pel23" path="res://Environment/Floor/Floor01_Custom.tscn" id="17_2bvpm"]
[ext_resource type="Resource" path="res://Resources/Items/Trap.tres" id="17_j32d5"]
[ext_resource type="Resource" path="res://Resources/Items/Torch.tres" id="18_j32d5"]
[ext_resource type="Script" uid="uid://pfva6p6rllgp" path="res://Scripts/Tree.gd" id="18_oh2bt"]
[ext_resource type="PackedScene" uid="uid://rvgn0irsuwao" path="res://Scenes/EnemyBoy01.tscn" id="19_0f42i"]
[ext_resource type="PackedScene" uid="uid://brxqv3iv3op6u" path="res://Scenes/ProgressBar3D.tscn" id="19_fcnrs"]
[ext_resource type="Script" uid="uid://dhw1dgex7wmiy" path="res://Scripts/ChestInteractable.gd" id="21_1ks1q"]
[ext_resource type="PackedScene" uid="uid://ervckea7fk57" path="res://Scenes/Enemy02.tscn" id="21_dbohx"]
[ext_resource type="PackedScene" uid="uid://b5dqjsb63wbhl" path="res://Scenes/Prefabs/Rock.tscn" id="21_xcdtp"]
[ext_resource type="PackedScene" uid="uid://bdq3b4e0mlgo4" path="res://Scenes/Prefabs/Barrel.tscn" id="22_cin4e"]
[ext_resource type="Resource" uid="uid://bjih53ivkm0qi" path="res://Resources/GasItem.tres" id="22_xcdtp"]
[ext_resource type="PackedScene" uid="uid://c6k7j3t3flhst" path="res://Scenes/Prefabs/Decoration.tscn" id="23_6j2fk"]
[ext_resource type="AudioStream" uid="uid://dgskkp7epyn0x" path="res://ChestOpening.mp3" id="23_cin4e"]
[ext_resource type="Script" uid="uid://de18ote8otj6u" path="res://Scripts/PatrolPointManager.gd" id="24_j2uky"]
[ext_resource type="Script" uid="uid://dt3st7cac7lok" path="res://Scripts/UI/UIManager.gd" id="25_ui_manager"]
[ext_resource type="Script" path="res://Scripts/CameraFollower.gd" id="26_camera_follower"]

[sub_resource type="ProceduralSkyMaterial" id="ProceduralSkyMaterial_gbvua"]
sky_top_color = Color(0.2, 0.4, 0.8, 1)
sky_horizon_color = Color(0.5, 0.7, 0.9, 1)
ground_bottom_color = Color(0.3, 0.5, 0.7, 1)
ground_horizon_color = Color(0.5, 0.7, 0.9, 1)

[sub_resource type="Sky" id="Sky_m0r78"]
sky_material = SubResource("ProceduralSkyMaterial_gbvua")

[sub_resource type="Environment" id="Environment_j4i7h"]
background_mode = 2
sky = SubResource("Sky_m0r78")
ambient_light_source = 3
ambient_light_color = Color(0.9, 0.9, 0.95, 1)
ambient_light_energy = 0.5
tonemap_mode = 2
ssao_enabled = true
ssao_radius = 2.0
ssao_intensity = 1.5
glow_enabled = true
glow_strength = 1.2

[sub_resource type="NavigationMesh" id="NavigationMesh_xcdtp"]
vertices = PackedVector3Array(45.831, 0.37304, -108.767, 44.331, 0.37304, -109.017, 44.081, 0.37304, -108.517, 42.581, 0.37304, -108.267, 42.581, 0.37304, -107.017, 46.831, 0.37304, -105.017, 47.081, 0.37304, -107.517, 41.831, 0.37304, -106.267, 40.581, 0.37304, -106.517, 39.581, 0.37304, -104.767, 50.581, 0.37304, -102.517, 50.331, 0.37304, -104.267, 47.831, 0.37304, -104.017, 49.831, 0.37304, -100.517, 38.831, 0.37304, -104.517, 38.831, 0.62304, -53.0166, 44.831, 0.37304, -53.0166, -21.919, 0.37304, -107.017, -22.919, 0.37304, -108.267, -23.919, 0.37304, -108.517, -25.669, 0.37304, -108.267, -26.919, 0.37304, -106.017, -22.169, 0.37304, -104.517, -28.919, 0.37304, -105.517, -28.919, 0.37304, -104.517, -23.419, 0.37304, -54.2666, -22.169, 0.37304, -54.0166, -21.419, 0.37304, -104.017, -27.919, 0.37304, -53.5166, -4.91899, 0.37304, -107.017, -5.41899, 0.37304, -107.767, -7.16899, 0.37304, -108.517, -9.41899, 0.37304, -107.767, -9.41899, 0.37304, -106.267, -5.16899, 0.37304, -104.517, -9.91899, 0.37304, -105.767, -11.169, 0.37304, -106.017, -12.669, 0.37304, -104.267, -3.91899, 0.37304, -103.767, -12.669, 0.37304, -53.2666, -7.41899, 0.37304, -53.5166, -6.41899, 0.62304, -53.5166, -4.41899, 0.37304, -53.0166, -7.91899, 0.37304, -52.7666, -67.669, 0.37304, -105.267, -68.419, 0.37304, -104.767, -68.419, 0.37304, -103.517, -60.419, 0.37304, -102.767, -61.669, 0.37304, -103.767, -66.419, 0.37304, -105.017, -61.919, 0.37304, -106.517, -61.919, 0.37304, -107.267, -63.419, 0.37304, -107.767, -53.169, 0.37304, -103.767, -53.169, 0.37304, -104.517, -54.919, 0.37304, -104.767, -55.169, 0.37304, -105.267, -57.169, 0.37304, -104.267, -50.919, 0.37304, -100.267, -51.669, 0.37304, -100.267, -67.669, 0.37304, -69.2666, -55.169, 0.37304, -58.2666, -52.919, 0.37304, -101.267, -52.669, 0.37304, -103.517, -61.419, 0.37304, -106.267, -57.669, 0.37304, -103.017, -65.169, 0.37304, -107.267, -50.919, 0.37304, -54.0166, -50.669, 0.37304, -54.0166, -69.169, 0.37304, -103.017, 102.581, 0.37304, -105.767, 102.581, 0.37304, -106.517, 101.081, 0.37304, -107.017, 82.581, 0.37304, -105.017, 80.831, 0.37304, -105.267, 80.581, 0.37304, -104.767, 103.081, 0.37304, -68.5166, 103.581, 0.37304, -68.7666, 103.581, 0.37304, -70.5166, 102.831, 0.37304, -71.0166, 108.831, 0.37304, -90.5166, 109.331, 0.37304, -90.7666, 109.331, 0.37304, -92.5166, 106.831, 0.37304, -92.5166, 93.831, 0.37304, -103.517, 93.831, 0.37304, -104.267, 92.331, 0.37304, -105.017, 87.831, 0.37304, -105.517, 87.331, 0.37304, -105.017, 53.331, 0.37304, -53.5166, 53.581, 0.37304, -52.2666, 55.081, 0.37304, -52.0166, 109.581, 0.37304, -78.5166, 110.081, 0.37304, -78.7666, 110.081, 0.37304, -80.7666, 107.581, 0.37304, -80.5166, 79.081, 0.37304, -104.517, 79.081, 0.37304, -103.017, 83.581, 0.37304, -103.267, 92.331, 0.37304, -106.017, 91.331, 0.37304, -107.267, 89.581, 0.37304, -107.767, 87.831, 0.37304, -106.767, 71.331, 0.37304, -103.267, 69.331, 0.37304, -103.017, 69.331, 0.37304, -101.767, 78.581, 0.37304, -102.517, 76.831, 0.37304, -102.767, 75.581, 0.37304, -105.517, 71.831, 0.37304, -103.767, 85.331, 0.37304, -104.517, 85.331, 0.37304, -103.267, 106.331, 0.37304, -81.5166, 106.581, 0.37304, -84.0166, 106.081, 0.37304, -84.2666, 102.831, 0.37304, -72.2666, 103.331, 0.37304, -72.7666, 102.831, 0.37304, -103.017, 103.081, 0.37304, -105.517, 84.831, 0.37304, -102.767, 100.831, 0.37304, -64.2666, 102.581, 0.37304, -64.2666, 99.081, 0.37304, -106.517, 98.831, 0.37304, -105.267, 106.331, 0.37304, -74.5166, 109.081, 0.37304, -74.5166, 73.581, 0.37304, -106.017, 71.831, 0.37304, -105.017, 98.081, 0.37304, -104.517, 96.831, 0.37304, -104.517, 95.581, 0.37304, -102.767, 103.331, 0.37304, -102.517, 94.831, 0.37304, -102.767, 105.581, 0.37304, -72.5166, 68.331, 0.37304, -101.267, 105.581, 0.37304, -93.2666, 106.581, 0.37304, -102.267, 106.081, 0.37304, -85.7666, 108.331, 0.37304, -86.5166, 100.331, 0.37304, -63.0166, -83.919, 0.37304, -104.017, -84.169, 0.37304, -104.517, -85.419, 0.37304, -104.267, -95.169, 0.37304, -103.517, -96.919, 0.37304, -103.767, -97.169, 0.37304, -103.267, -98.669, 0.37304, -103.017, -98.669, 0.37304, -101.767, -94.169, 0.37304, -102.017, -103.419, 0.37304, -99.2666, -104.669, 0.37304, -98.5166, -104.419, 0.37304, -97.2666, -105.669, 0.37304, -96.5166, -105.419, 0.37304, -95.2666, -103.919, 0.37304, -81.2666, -105.169, 0.37304, -80.2666, -104.919, 0.37304, -79.2666, -102.669, 0.37304, -72.2666, -101.919, 0.37304, -71.2666, -70.169, 0.37304, -103.517, -70.919, 0.37304, -105.017, -71.919, 0.37304, -105.017, -77.419, 0.37304, -104.517, -77.919, 0.37304, -104.017, -87.669, 0.37304, -105.767, -88.169, 0.37304, -106.267, -89.919, 0.37304, -105.267, -89.919, 0.37304, -104.017, -86.169, 0.37304, -105.767, -90.419, 0.37304, -103.517, -75.669, 0.37304, -106.767, -77.419, 0.37304, -105.767, -73.669, 0.37304, -106.267, -103.919, 0.37304, -82.0166, -92.419, 0.37304, -103.267, -92.919, 0.37304, -102.017, -81.419, 0.37304, -102.267, -82.419, 0.37304, -104.017, -99.169, 0.37304, -101.267, -104.919, 0.37304, -88.5166, -80.669, 0.37304, -102.267, -79.669, 0.37304, -104.017, -100.669, 0.37304, -101.267, -103.169, 0.37304, -100.267, -102.419, 0.37304, -69.7666, -105.919, 0.37304, -94.7666, -105.919, 0.37304, -91.5166, -104.919, 0.37304, -91.0166, -105.919, 0.37304, -88.0166, -106.169, 0.37304, -83.0166, -104.669, 0.37304, -82.7666, -106.169, 0.37304, -78.5166, -106.419, 0.37304, -73.5166, 8.58101, 0.37304, -106.517, 6.83101, 0.37304, -106.767, 6.58101, 0.37304, -106.267, 5.08101, 0.37304, -106.017, 5.08101, 0.37304, -104.767, 9.33101, 0.37304, -102.767, 9.58101, 0.37304, -105.267, 4.58101, 0.37304, -104.267, 3.33101, 0.37304, -104.267, 2.08101, 0.37304, -102.517, 13.081, 0.37304, -99.7666, 13.081, 0.37304, -102.017, 10.581, 0.37304, -102.017, 13.581, 0.37304, -99.2666, 1.58101, 0.37304, -102.517, 1.58101, 0.37304, -53.0166, 7.83101, 0.37304, -53.0166, 14.831, 0.37304, -53.0166, 15.081, 0.37304, -99.2666, 35.831, 0.37304, -52.2666, 37.081, 0.37304, -52.2666, 37.331, 0.37304, -53.0166, 29.831, 0.37304, -54.2666, 30.081, 0.37304, -53.2666, 30.831, 0.37304, -52.7666, 33.831, 0.37304, -104.767, 33.081, 0.37304, -104.017, 22.081, 0.37304, -54.0166, 23.081, 0.37304, -53.2666, 24.581, 0.62304, -53.2666, 24.581, 0.37304, -99.5166, 22.081, 0.37304, -99.2666, 35.581, 0.37304, -106.767, 33.831, 0.37304, -105.767, 37.581, 0.37304, -106.267, 28.831, 0.37304, -102.267, 26.331, 0.37304, -101.767, 25.831, 0.37304, -100.517, 29.581, 0.37304, -103.517, -35.669, 0.37304, -103.517, -36.919, 0.37304, -103.767, -37.169, 0.37304, -103.267, -31.669, 0.37304, -106.017, -33.419, 0.37304, -106.267, -33.669, 0.37304, -105.767, -39.919, 0.37304, -101.517, -41.169, 0.37304, -100.767, -40.919, 0.37304, -99.7666, -34.669, 0.37304, -105.767, -36.419, 0.37304, -53.5166, -36.169, 0.37304, -52.7666, -33.419, 0.37304, -52.7666, -39.419, 0.37304, -103.017, -30.169, 0.37304, -103.767, -29.419, 0.37304, -103.767, -41.919, 0.37304, -99.2666, -41.169, 0.37304, -53.0166, -16.669, 0.37304, -105.767, -18.169, 0.37304, -105.517, -18.169, 0.37304, -104.267, -13.669, 0.37304, -104.517, -14.419, 0.37304, -105.767, -16.419, 0.37304, -106.267, -18.669, 0.37304, -103.767, -15.919, 0.37304, -53.5166, -21.669, 0.37304, -53.2666, -16.419, 0.37304, -52.5166, -1.41899, 0.37304, -52.2666, -0.668991, 0.37304, -52.2666, -0.418991, 0.37304, -53.0166, -0.168991, 0.37304, -104.267, -1.91899, 0.37304, -104.517, -2.16899, 0.37304, -104.017, 0.331009, 0.37304, -103.267, -42.919, 0.37304, -102.267, -43.419, 0.37304, -103.017, -45.169, 0.37304, -103.767, -47.419, 0.37304, -103.017, -47.419, 0.37304, -101.517, -43.169, 0.37304, -99.7666, -47.919, 0.37304, -101.017, -49.169, 0.37304, -101.267, -43.169, 0.37304, -52.7666, 59.331, 0.37304, -101.767, 57.831, 0.37304, -101.017, 57.831, 0.37304, -99.7666, 62.581, 0.37304, -101.017, 64.831, 0.37304, -103.767, 63.081, 0.37304, -102.767, 63.081, 0.37304, -101.517, 66.581, 0.37304, -103.267, 57.331, 0.37304, -99.2666, 52.581, 0.37304, -99.2666, 52.581, 0.37304, -54.0166, 45.831, 0.37304, -52.2666, 47.831, 0.37304, -52.7666, 48.081, 0.37304, -53.5166, 51.331, 0.37304, -99.2666, 49.581, 0.37304, -53.2666, 18.831, 0.37304, -100.017, 17.581, 0.37304, -99.2666, -103.169, 0.37304, -68.5166, -103.669, 0.37304, -68.5166, -103.669, 0.37304, -67.0166, -104.669, 0.37304, -66.5166, -104.919, 0.37304, -61.7666, -101.919, 0.37304, -60.7666, -100.669, 0.37304, -59.2666, 103.831, 0.37304, -59.2666, 104.081, 0.37304, -62.0166, 101.581, 0.37304, -62.0166, 104.831, 0.37304, -55.5166, 105.081, 0.37304, -58.0166, 107.581, 0.37304, -48.5166, 108.581, 0.37304, -54.7666, 106.081, 0.37304, -54.7666, 104.831, 0.37304, -48.5166, 55.831, 0.37304, -51.5166, 55.081, 0.37304, -48.0166, 104.331, 0.37304, -47.7666, -53.169, 0.37304, -50.7666, -51.669, 0.37304, -51.0166, -51.669, 0.37304, -52.7666, -99.419, 0.37304, -52.7666, -99.419, 0.37304, -51.2666, -101.419, 0.37304, -58.5166, -101.669, 0.37304, -53.7666, -50.669, 3.62304, -50.0166, -52.169, 3.62304, -49.5166, -52.169, 3.62304, -45.7666, -47.669, 3.62304, -45.7666, -47.169, 3.62304, -47.2666, -45.669, 3.62304, -47.2666, -44.919, 3.62304, -51.7666, -50.169, 3.62304, -52.7666, -31.169, 3.62304, -51.7666, -31.419, 3.62304, -48.5166, -29.669, 3.62304, -48.5166, -22.419, 3.62304, -48.2666, -22.419, 3.62304, -51.2666, -23.169, 3.62304, -51.7666, -28.919, 3.62304, -47.2666, -23.169, 3.62304, -52.7666, 30.581, 3.62304, -48.5166, 30.581, 3.62304, -51.5166, 29.581, 3.62304, -51.7666, 29.081, 3.62304, -52.7666, 22.831, 3.62304, -51.7666, 22.831, 3.62304, -48.2666, 23.831, 3.62304, -47.2666, 15.581, 3.62304, -48.0166, 15.831, 3.62304, -47.5166, 16.831, 3.62304, -47.5166, 21.831, 3.62304, -52.5166, 20.331, 3.62304, -52.5166, 16.331, 3.62304, -51.7666, 4.58101, 3.62304, -52.0166, 0.581009, 3.87304, -51.5166, 1.08101, 3.62304, -48.0166, 8.33101, 3.62304, -48.0166, 44.831, 3.62304, -48.7666, 45.831, 3.62304, -48.0166, 47.331, 3.62304, -48.2666, 48.331, 3.62304, -51.5166, 44.831, 3.62304, -50.7666, 52.331, 3.62304, -52.5166, 48.581, 3.62304, -52.0166, 52.831, 3.62304, -51.0166, 47.831, 3.62304, -47.7666, 47.581, 3.62304, -46.2666, 53.331, 3.62304, -46.0166, 54.331, 3.62304, -50.7666, -40.919, 3.62304, -46.5166, -40.169, 3.62304, -48.7666, -36.669, 3.62304, -48.2666, -38.169, 3.62304, -52.0166, -37.169, 3.62304, -49.0166, 0.0810089, 3.87304, -51.0166, -21.419, 3.62304, -52.0166, -16.169, 3.62304, -47.2666, -15.419, 3.62304, -48.2666, -16.919, 3.62304, -51.2666, -1.16899, 4.12304, -47.2666, -1.66899, 3.87304, -51.0166, -15.419, 3.62304, -51.2666, -10.419, 3.62304, -51.5166, -14.919, 3.62304, -52.0166, -6.91899, 3.62304, -47.2666, -7.79399, 4.12304, -48.3291, -8.66899, 3.62304, -49.3916, -6.16899, 4.12304, -48.2666, -5.41899, 3.87304, -52.0166, -7.44399, 4.12304, -49.7916, 44.081, 3.62304, -47.7666, 44.081, 3.62304, -51.7666, 38.331, 3.62304, -51.7666, 37.831, 3.62304, -51.0166, 37.331, 3.62304, -47.7666, -99.919, 0.37304, -50.5166, -101.419, 0.37304, -50.5166, -101.669, 0.37304, -49.2666, -102.919, 0.37304, -48.5166, -102.669, 0.37304, -47.2666, -103.919, 0.37304, -46.5166, -103.669, 0.37304, -45.2666, -104.169, 0.37304, -44.7666, -104.169, 0.37304, -41.5166, -100.669, 0.37304, -40.2666, -55.169, 0.37304, -44.7666, -53.419, 0.37304, -45.2666, -54.669, 0.37304, -41.7666, -28.919, 0.37304, -50.0166, -28.919, 0.37304, -49.5166, -28.169, 0.37304, -49.5166, -26.169, 0.37304, -50.2666, -23.919, 0.37304, -51.2666, -23.419, 0.37304, -50.2666, 23.581, 0.37304, -50.0166, 23.581, 0.37304, -49.5166, 24.331, 0.37304, -49.5166, 26.081, 0.37304, -50.2666, 25.831, 0.37304, -50.2666, 28.831, 0.37304, -50.2666, 28.831, 0.37304, -51.0166, 28.331, 0.37304, -51.2666, 18.581, 0.37304, -50.5166, 21.581, 0.37304, -50.2666, 21.331, 0.37304, -51.0166, -32.419, 0.37304, -50.5166, -31.169, 0.37304, -50.0166, -31.169, 0.37304, -50.5166, -21.419, 0.37304, -50.5166, -21.419, 0.37304, -50.0166, -20.669, 0.37304, -49.7666, -19.919, 0.37304, -50.0166, -17.669, 0.37304, -49.7666, -16.419, 0.37304, -50.0166, -16.419, 0.37304, -49.2666, -4.91899, 0.37304, -50.5166, -5.16899, 0.37304, -50.0166, -5.16899, 0.37304, -49.5166, -0.668991, 0.37304, -49.0166, -0.668991, 0.37304, -49.7666, -1.16899, 0.37304, -50.0166, -4.16899, 0.37304, -49.2666, 1.33101, 0.37304, -50.2666, 1.33101, 0.37304, -49.7666, 6.33101, 0.37304, -49.7666, 6.33101, 0.37304, -50.5166, 8.33101, 0.37304, -50.2666, 8.33101, 0.37304, -49.7666, 14.331, 0.37304, -49.7666, 14.331, 0.37304, -50.5166, 39.331, 0.37304, -50.2666, 39.331, 0.37304, -49.7666, 43.581, 0.37304, -49.7666, 43.581, 0.37304, -50.5166, -13.919, 0.37304, -49.7666, -8.41899, 0.37304, -49.5166, -8.41899, 0.37304, -50.0166, -14.169, 0.37304, -50.2666, 16.331, 0.37304, -50.2666, 16.331, 0.37304, -49.5166, 18.581, 0.37304, -50.0166, 34.831, 0.37304, -50.0166, 37.081, 0.37304, -49.5166, 37.081, 0.37304, -50.0166, 53.331, 0.37304, -48.5166, 53.331, 0.37304, -47.0166, 53.831, 0.37304, -48.0166, 53.831, 0.37304, -48.5166, 105.331, 0.37304, -42.5166, 105.581, 0.37304, -45.0166, 104.331, 0.37304, -46.2666, 53.331, 0.37304, -41.0166, 53.331, 0.37304, -39.0166, 54.331, 0.37304, -35.5166, 108.081, 0.37304, -35.5166, 109.081, 0.37304, -41.7666, 106.581, 0.37304, -41.5166, 60.1015, 0.87304, -42.2851, 63.0715, 0.37304, -42.2631, 58.5664, 0.37304, -42.2461, 60.1018, 0.87304, -40.7555, 58.5812, 0.37304, -43.7768, 58.5572, 0.37304, -39.2429, -46.419, 0.37304, -42.2666, -46.919, 0.37304, -42.0166, -46.919, 0.37304, -41.2666, -45.419, 0.62304, -34.2666, -43.669, 0.37304, -29.2666, -39.919, 0.37304, -45.0166, -37.669, 0.37304, -46.7666, -37.919, 0.37304, -47.7666, -38.419, 0.37304, -47.7666, -39.419, 0.37304, -47.5166, -2.16899, 0.37304, -0.766617, -1.91899, 0.37304, 0.233383, -0.668991, 0.37304, -0.0166168, 1.58101, 0.37304, -3.76662, -5.91899, 0.37304, -46.0166, -14.669, 0.37304, -46.7666, -14.919, 0.37304, -46.0166, -5.16899, 0.37304, -0.0166168, -4.16899, 0.62304, 0.233383, -2.91899, 0.37304, -0.766617, 1.58101, 0.37304, -4.76662, 2.58101, 0.62304, -5.51662, 2.58101, 0.37304, -7.01662, 1.08101, 0.37304, -8.26662, -16.669, 0.37304, -46.0166, -43.669, 0.37304, -28.0166, -45.169, 0.37304, -12.7666, -10.419, 0.37304, -5.26662, -13.4439, 0.87304, -42.2817, -13.4437, 0.37304, -40.7719, -17.8997, 0.87304, -18.2522, -19.4347, 0.37304, -18.2632, -16.4147, 0.37304, -18.2912, -11.9097, 0.87304, -18.2582, -11.94, 0.37304, -19.7659, -10.3943, 0.37304, -16.7896, -16.3951, 0.37304, -19.7488, -13.3947, 0.37304, -18.2692, -16.419, 0.37304, -15.2649, -11.939, 0.37304, -43.7587, -47.419, 0.37304, -33.5166, -47.169, 0.37304, -30.5166, -29.919, 0.37304, -46.0166, -45.669, 0.37304, -10.0166, -46.669, 0.37304, -19.2666, -45.669, 0.37304, -13.2666, -46.419, 0.37304, -46.0166, -23.419, 0.37304, -47.0166, -30.169, 0.37304, -47.2666, 49.581, 0.37304, -45.5166, 49.581, 0.37304, -45.0166, 50.331, 0.37304, -45.2666, 50.831, 0.37304, -47.2666, 50.831, 0.37304, -47.7666, 50.081, 0.37304, -47.7666, 45.081, 0.37304, -43.7666, 45.831, 0.37304, -44.2666, 46.331, 0.37304, -46.7666, 36.831, 0.37304, -46.5166, 46.831, 0.37304, -28.7666, 48.081, 0.37304, -32.7666, 47.331, 0.37304, -33.2666, 45.081, 0.37304, -42.5166, 23.081, 0.37304, -46.0166, 22.081, 0.37304, -15.5166, 45.081, 0.37304, -19.7666, 24.0571, 0.87304, -34.7528, 25.5921, 0.37304, -34.7418, 24.0823, 0.37304, -33.288, 24.0819, 0.37304, -36.2676, 47.331, 0.37304, -26.7666, 30.331, 0.37304, -47.2666, 16.331, 0.62304, -12.0166, 16.831, 0.62304, -11.2666, 18.081, 0.37304, -11.2666, 22.331, 0.37304, -47.0166, 16.331, 0.37304, -46.2666, -4.66899, 0.37304, -46.7666, -5.41899, 0.37304, -46.7666, 0.0810089, 0.37304, -45.7666, 2.83101, 0.37304, -7.51662, 4.33101, 0.62304, -7.51662, 5.33101, 0.37304, -8.26662, 5.33101, 0.37304, -46.7666, 1.33101, 0.37304, -46.7666, 6.08101, 0.37304, -8.26662, 6.33101, 0.37304, -7.51662, 7.83101, 0.37304, -7.76662, 12.081, 0.37304, -8.51662, 13.081, 0.37304, -9.01662, 13.081, 0.37304, -11.0166, 8.08101, 0.37304, -7.01662, 12.081, 0.37304, -7.51662, 13.581, 0.37304, -11.2666, 13.581, 0.37304, -46.7666, 14.581, 0.62304, -11.0166, 47.331, 3.62304, -39.0166, 52.081, 3.62304, -39.2666, 51.831, 3.62304, -40.7666, 46.581, 3.62304, -43.2666, -52.169, 3.62304, -44.5166, -53.669, 3.62304, -44.0166, -53.669, 3.62304, -42.7666, -48.419, 3.62304, -42.7666, -47.669, 3.62304, -43.2666, -51.669, 3.87304, -30.5166, -48.419, 4.12304, -31.0166, -48.669, 4.12304, -34.2666, -52.169, 3.62304, -37.0166, -51.0023, 4.12304, -36.1, -48.419, 3.62304, -42.0166, -46.919, 3.62304, -35.0166, -49.4139, 4.12304, -36.2537, -49.3989, 3.62304, -37.7647, -50.669, 0.37304, -42.0166, -51.669, 0.37304, -42.0166, -50.669, 0.37304, -39.5166, -50.419, 0.37304, -37.2666, -49.669, 0.37304, -36.7666, -99.419, 0.37304, -38.7666, -54.169, 0.37304, -39.0166, 48.581, 0.37304, -41.0166, 49.581, 0.37304, -38.7666, 49.331, 0.37304, -40.7666, 49.581, 3.62304, -34.0166, 49.081, 3.62304, -32.0166, 51.831, 3.62304, -31.2666, 52.581, 3.62304, -33.5166, 52.081, 3.62304, -35.0166, 52.831, 3.62304, -35.5166, 48.331, 3.62304, -34.7666, -99.419, 0.37304, -38.0166, -100.669, 0.37304, -37.2666, -100.419, 0.37304, -36.0166, -101.669, 0.37304, -35.2666, -101.419, 0.37304, -34.0166, -101.919, 0.37304, -31.7666, -52.919, 0.37304, -32.0166, -53.169, 0.37304, -35.2666, 49.831, 0.37304, -36.2666, 50.081, 0.37304, -35.7666, 50.831, 0.37304, -35.7666, 110.581, 0.37304, -31.5166, 110.581, 0.37304, -33.5166, 108.081, 0.37304, -33.5166, 107.081, 0.37304, -27.2666, 109.831, 0.37304, -27.5166, 107.581, 0.37304, -34.0166, 56.331, 0.37304, -29.5166, 56.331, 0.37304, -28.0166, 53.081, 0.37304, -30.5166, 50.581, 0.37304, -33.7666, 50.581, 0.37304, -33.0166, 51.081, 0.37304, -33.5166, -50.669, 0.37304, -32.0166, -49.919, 0.37304, -31.5166, -49.919, 0.37304, -33.0166, -50.669, 0.37304, -33.0166, -103.169, 0.37304, -27.7666, -103.669, 0.37304, -27.7666, -103.419, 0.37304, -26.5166, -101.919, 0.37304, -29.5166, -101.919, 0.37304, -20.2666, -100.419, 0.37304, -19.0166, -54.919, 0.37304, -21.7666, -54.169, 0.37304, -22.0166, -52.669, 0.37304, -26.7666, -104.419, 0.37304, -25.7666, -104.669, 0.37304, -21.0166, -54.919, 0.37304, -19.7666, 46.581, 3.62304, -21.0166, 46.581, 3.62304, -20.2666, 47.831, 3.62304, -20.0166, 53.081, 3.62304, -17.7666, 53.581, 3.62304, -20.2666, 52.831, 3.62304, -20.7666, 48.581, 3.62304, -19.2666, 48.581, 3.62304, -18.5166, 51.581, 3.62304, -29.7666, 48.331, 3.62304, -29.2666, 52.831, 3.62304, -22.0166, 55.081, 3.62304, -28.5166, 52.081, 3.62304, -29.2666, 48.831, 3.62304, -27.0166, -47.169, 3.87304, -10.0166, -46.419, 3.87304, -12.2666, -46.919, 3.87304, -12.7666, -50.919, 3.87304, -10.7666, -48.419, 4.12304, -29.7666, -47.919, 4.12304, -29.2666, -51.419, 3.62304, -26.2666, -52.169, 3.62304, -12.5166, -47.919, 3.62304, -19.7666, -52.919, 3.62304, -21.5166, -53.669, 3.62304, -20.5166, -45.169, 3.62304, -28.5166, -47.944, 4.12304, -27.2916, -47.924, 3.62304, -25.7696, 108.081, 0.37304, -23.5166, 107.831, 0.37304, -24.7666, 106.331, 0.37304, -25.2666, 107.331, 0.37304, -21.7666, 101.581, 0.37304, -16.7666, 103.581, 0.37304, -16.7666, 104.331, 0.37304, -18.5166, 106.331, 0.37304, -26.2666, 54.581, 0.37304, -22.5166, 107.081, 0.37304, -18.7666, 53.581, 0.37304, -25.7666, 53.831, 0.37304, -24.5166, 54.581, 0.37304, -26.7666, 54.331, 0.37304, -27.5166, -50.169, 0.37304, -23.5166, -50.169, 0.37304, -22.7666, -49.669, 0.37304, -22.7666, -48.419, 0.37304, -26.2666, -49.419, 0.37304, -26.5166, -46.419, 0.37304, -26.0166, -46.419, 0.37304, -24.7666, -45.919, 0.37304, -25.7666, -45.919, 0.37304, -26.5166, 49.581, 0.37304, -22.7666, 49.581, 0.37304, -22.2666, 50.331, 0.37304, -22.2666, 50.831, 0.37304, -25.0166, 50.581, 0.37304, -26.5166, 51.581, 0.37304, -26.2666, 54.081, 0.37304, -21.2666, 54.831, 0.37304, -21.0166, 100.581, 0.37304, -15.7666, 54.831, 0.37304, -19.2666, 54.081, 0.37304, -16.2666, -51.169, 0.37304, -20.5166, -50.419, 0.37304, -20.0166, -50.419, 0.37304, -20.5166, -102.669, 0.37304, -14.0166, -103.669, 0.37304, -13.5166, -103.419, 0.37304, -12.5166, -101.669, 0.37304, -16.2666, -102.919, 0.37304, -15.5166, -103.919, 0.37304, -12.0166, -103.919, 0.37304, -8.76662, -100.669, 0.37304, -7.51662, -53.169, 0.37304, -10.7666, 46.331, 0.37304, -13.7666, 47.081, 0.37304, -18.7666, 45.331, 0.37304, -19.2666, 18.581, 0.37304, -11.0166, 45.331, 0.37304, -11.7666, 45.081, 0.37304, -13.2666, 48.581, 4.12304, -5.26662, 51.081, 3.87304, -5.01662, 52.831, 3.87304, -5.76662, 51.581, 3.87304, -11.5166, 47.581, 4.12304, -13.2666, 46.831, 4.12304, -12.7666, 49.576, 3.62304, -15.2696, -50.419, 0.37304, -16.0166, -50.419, 0.37304, -14.2666, -49.669, 0.37304, -14.2666, -50.419, 0.37304, -18.0166, -51.169, 0.37304, -18.0166, 49.831, 0.37304, -16.7666, 50.081, 0.37304, -14.7666, 50.581, 0.37304, -17.0166, 50.581, 0.37304, -17.7666, 50.081, 0.37304, -17.7666, 104.831, 0.37304, -13.0166, 104.331, 0.37304, -13.7666, 102.581, 0.37304, -14.5166, 101.081, 0.37304, -14.0166, 104.581, 0.37304, -10.5166, 102.831, 0.37304, -0.766617, 103.831, 0.37304, -1.51662, 104.581, 0.37304, -3.51662, 53.831, 0.37304, -4.51662, 52.581, 0.37304, -4.26662, 53.331, 0.37304, -0.766617, 105.831, 0.37304, -9.51662, 105.081, 0.37304, -10.0166, 54.331, 0.37304, -6.01662, 100.581, 0.37304, -14.5166, 107.331, 0.37304, -3.51662, 108.331, 0.37304, -9.76662, 52.831, 0.37304, -11.0166, 45.581, 0.37304, -5.26662, 47.081, 0.37304, -5.51662, 47.081, 0.37304, -6.01662, 17.581, 0.37304, -7.01662, 18.081, 0.37304, -3.51662, 19.081, 0.37304, -3.51662, 19.581, 0.37304, -2.51662, 18.831, 0.37304, -9.26662, 33.0913, 0.87304, -7.76089, 31.5562, 0.37304, -7.77187, 34.5763, 0.37304, -7.7499, 37.5661, 0.37304, -9.2757, 46.081, 0.37304, -2.51662, 49.581, 0.37304, -6.26662, 50.581, 0.62304, -6.51662, 49.581, 0.37304, -11.2666, 48.331, 0.37304, -11.5166, -48.669, 0.37304, -10.2666, -48.669, 0.37304, -11.0166, -49.669, 0.37304, -11.0166, -50.669, 0.37304, -7.51662, -50.669, 0.37304, -6.76662, -49.419, 0.37304, -7.26662, -53.919, 0.62304, -5.01662, -52.419, 0.37304, -10.2666, -99.419, 0.37304, -6.51662, -99.419, 0.37304, -5.01662, -51.919, 3.62304, -2.01662, -49.169, 3.62304, -2.76662, -49.419, 3.87304, -4.26662, -52.669, 3.87304, -5.26662, -48.419, 3.87304, -4.76662, -46.919, 0.37304, -5.51662, 14.581, 5.12304, -9.76662, 14.581, 5.12304, -8.51662, 15.831, 5.12304, -8.51662, 15.831, 5.12304, -9.76662, 17.331, 0.37304, -0.266617, 18.081, 0.37304, -1.26662, 17.331, 0.37304, -1.76662, 8.33101, 0.37304, -5.76662, 7.58101, 0.37304, -5.51662, 7.58101, 0.37304, -4.26662, 17.331, 0.37304, -3.01662, 13.331, 0.37304, -6.51662, -0.168991, 0.37304, 0.483383, 6.08101, 0.37304, 0.233383, 2.83101, 0.37304, -3.01662, 8.33101, 0.37304, 0.233383, 6.83101, 0.37304, -3.26662, 6.58101, 0.37304, -0.516617, 7.58101, 0.37304, -0.516617, 4.08101, 5.12304, -6.01662, 4.08101, 5.12304, -4.76662, 5.33101, 5.12304, -4.76662, 5.33101, 5.12304, -6.01662, -6.66899, 0.37304, 2.98338, -5.66899, 0.62304, 1.98338, -5.91899, 0.37304, 0.983383, -47.919, 0.37304, -3.01662, -46.669, 0.37304, 2.73338, 48.081, 4.12304, -4.51662, 47.081, 3.62304, -4.26662, 47.081, 3.62304, -3.76662, 50.831, 3.62304, 1.73338, 52.331, 3.62304, 1.48338, 48.581, 3.62304, 2.48338, 49.081, 3.62304, 2.98338, 50.331, 3.62304, 2.73338, -53.169, 0.37304, 2.48338, -52.169, 0.37304, 2.48338, -52.169, 0.37304, 1.73338, -99.419, 0.37304, -3.26662, -53.669, 0.37304, 3.23338, -100.169, 0.37304, -2.76662, 48.831, 0.37304, -3.26662, 49.581, 0.37304, -1.51662, 49.331, 0.37304, -3.51662, -101.169, 0.37304, -2.76662, -101.669, 0.37304, -1.51662, -102.919, 0.37304, -0.766617, -102.669, 0.37304, 0.483383, -103.919, 0.37304, 1.23338, -103.919, 0.37304, 4.48338, -54.419, 0.37304, 7.73338, -53.169, 0.37304, 7.48338, -54.669, 0.37304, 8.23338, -104.919, 0.37304, 5.23338, -51.169, 3.62304, 3.73338, -52.169, 3.62304, 3.73338, -51.669, 3.62304, 7.73338, -48.669, 3.62304, 2.48338, -47.919, 3.62304, 1.73338, -50.669, 3.62304, 2.48338, -47.919, 3.62304, 7.98338, 46.331, 0.37304, 8.73338, 47.581, 0.37304, 2.98338, 19.581, 0.37304, -1.76662, 25.081, 0.37304, 11.9834, 25.581, 0.37304, 11.9834, 46.581, 0.37304, 9.73338, 12.081, 0.37304, 13.9834, 12.081, 0.37304, 14.2334, 13.581, 0.37304, 13.7334, 13.081, 0.87304, 12.2334, 12.331, 0.37304, 12.4834, 14.331, 0.37304, 14.4834, 23.081, 0.37304, 14.2334, 23.081, 0.37304, 13.4834, 12.831, 0.37304, 10.7334, 19.331, 0.37304, -1.26662, 53.831, 0.37304, 0.983383, 53.581, 0.62304, 2.73338, 54.331, 0.37304, 3.48338, 102.831, 0.37304, 0.733383, 103.581, 0.37304, 1.23338, 10.831, 0.62304, 9.48338, 11.081, 0.37304, 10.4834, 12.581, 0.37304, 10.2334, 8.33101, 0.37304, 1.48338, 9.33101, 0.37304, 9.98338, 49.831, 0.37304, -0.266617, 50.081, 0.37304, 1.48338, 50.581, 0.37304, 1.48338, 7.58101, 0.37304, 10.2334, 8.83101, 0.62304, 10.4834, -1.66899, 0.37304, 4.48338, -1.16899, 0.37304, 4.98338, 0.331009, 0.37304, 5.48338, 6.33101, 0.37304, 1.73338, 0.0810089, 0.37304, 1.98338, 0.331009, 0.37304, 6.98338, -0.168991, 0.37304, 7.23338, 7.33101, 0.37304, 10.7334, 106.081, 0.37304, 3.73338, 106.581, 0.37304, 3.48338, 106.581, 0.37304, 1.73338, 104.081, 0.37304, 1.73338, 109.581, 0.37304, 13.2334, 110.081, 0.37304, 12.9834, 110.081, 0.37304, 10.9834, 107.581, 0.37304, 10.9834, 106.331, 0.37304, 10.2334, 106.581, 0.37304, 7.73338, 106.081, 0.37304, 7.48338, 105.831, 0.37304, 5.23338, 53.081, 0.37304, 8.73338, 53.331, 0.37304, 17.9834, 103.081, 0.37304, 19.2334, 105.831, 0.37304, 18.9834, 105.831, 0.37304, 17.7334, 106.331, 0.37304, 17.2334, 109.081, 0.37304, 16.9834, -4.16899, 5.12304, 1.48338, -4.16899, 5.12304, 2.73338, -2.91899, 5.12304, 2.73338, -2.91899, 5.12304, 1.48338, -1.91899, 0.37304, 5.73338, -6.41899, 0.37304, 4.48338, 6.33101, 0.37304, 12.9834, 7.33101, 0.62304, 12.2334, -4.66899, 0.37304, 22.7334, -2.16899, 0.37304, 22.2334, 6.33101, 0.37304, 14.2334, -1.41899, 0.37304, 7.23338, -1.91899, 0.37304, 6.73338, -47.419, 0.37304, 3.23338, -46.419, 0.37304, 8.73338, -46.919, 0.37304, 11.2334, -45.669, 0.37304, 16.7334, -46.169, 0.37304, 19.7334, -31.3982, 0.87304, 14.7445, -29.9132, 0.37304, 14.7555, -17.9175, 0.87304, 17.7318, -19.4025, 0.37304, 17.7208, -16.4325, 0.37304, 17.7428, -28.4284, 0.37304, 13.2467, -16.4428, 0.37304, 16.2571, -35.8979, 0.37304, 16.2143, -0.418991, 0.37304, 22.4834, 50.831, 3.62304, 3.48338, 48.331, 3.62304, 17.2334, 50.831, 3.62304, 17.9834, 51.331, 3.62304, 17.2334, 51.831, 3.62304, 8.23338, 52.831, 3.62304, 3.98338, 47.831, 3.62304, 7.98338, 52.081, 3.62304, 17.2334, -50.919, 0.37304, 3.98338, -50.169, 0.37304, 6.48338, -50.169, 0.37304, 3.98338, 50.081, 0.37304, 4.98338, 50.331, 0.37304, 6.73338, 50.331, 0.37304, 5.48338, 51.081, 0.37304, 4.23338, 50.331, 0.37304, 3.98338, -106.419, 0.37304, 7.23338, -107.169, 0.37304, 7.23338, -106.919, 0.37304, 8.73338, -105.419, 0.37304, 5.23338, -105.169, 0.37304, 23.9834, -103.919, 0.37304, 25.9834, -101.669, 0.37304, 25.7334, -107.419, 0.37304, 16.9834, -52.919, 0.37304, 18.7334, -107.919, 0.37304, 9.23338, -108.169, 0.37304, 14.2334, -107.669, 0.37304, 14.4834, -108.669, 0.37304, 17.7334, -108.669, 0.37304, 22.7334, -52.169, 3.62304, 8.73338, -53.419, 3.62304, 8.98338, -53.419, 3.62304, 9.48338, -50.419, 3.62304, 18.9834, -47.419, 3.62304, 18.9834, -46.919, 3.62304, 17.2334, -48.419, 3.62304, 10.4834, -51.919, 3.62304, 17.7334, 45.331, 0.37304, 17.2334, 47.081, 0.37304, 16.7334, 45.081, 0.37304, 17.7334, 27.331, 0.37304, 13.9834, 27.331, 0.37304, 14.7334, 26.331, 0.37304, 15.4834, -50.419, 0.37304, 12.9834, -50.419, 0.37304, 10.4834, -51.419, 0.37304, 10.4834, -50.169, 0.37304, 16.4834, -49.419, 0.37304, 16.4834, 49.331, 0.37304, 10.4834, 49.581, 0.37304, 13.2334, 50.081, 0.37304, 11.4834, 50.081, 0.37304, 10.4834, 8.83101, 5.12304, 11.7334, 8.83101, 5.12304, 12.9834, 10.081, 5.12304, 12.9834, 10.081, 5.12304, 11.7334, 24.581, 2.62304, 13.7334, 24.831, 2.62304, 14.7334, 25.581, 2.62304, 14.4834, 25.331, 2.62304, 13.4834, 11.581, 0.37304, 14.7334, 12.081, 0.37304, 15.7334, 8.33101, 0.37304, 14.9834, 6.58101, 0.37304, 14.7334, 0.0810089, 0.37304, 22.7334, 0.331009, 0.37304, 25.7334, 14.081, 0.37304, 27.7334, 3.10121, 0.87304, 22.2452, 4.58623, 0.37304, 22.2561, 4.56608, 0.37304, 19.2243, 13.581, 0.37304, 15.9834, 14.331, 0.37304, 15.2334, 17.831, 0.37304, 29.4834, 18.081, 0.37304, 29.9834, 24.581, 0.37304, 16.2334, 16.831, 0.37304, 29.4834, 14.081, 0.37304, 29.9834, 15.081, 0.37304, 30.7334, 44.831, 0.37304, 30.2334, 46.581, 0.37304, 29.9834, 46.581, 0.37304, 24.9834, 47.331, 0.37304, 24.7334, 25.581, 0.37304, 16.2334, 18.581, 0.37304, 30.9834, 44.581, 0.37304, 30.9834, 47.831, 3.62304, 17.9834, 46.581, 3.62304, 18.2334, 46.581, 3.62304, 18.7334, 48.831, 3.62304, 25.2334, 50.581, 3.62304, 25.2334, 51.081, 3.62304, 24.2334, 52.581, 3.62304, 23.9834, 105.581, 0.37304, 23.2334, 105.331, 0.37304, 21.4834, 103.331, 0.37304, 21.4834, 104.831, 0.37304, 24.4834, 52.331, 0.37304, 18.7334, 54.081, 0.37304, 23.2334, 102.831, 0.37304, 20.9834, 102.581, 0.37304, 27.4834, 104.581, 0.37304, 27.4834, 101.831, 0.37304, 29.7334, 53.831, 0.37304, 29.7334, -53.169, 0.37304, 23.2334, -51.919, 0.37304, 19.7334, -52.919, 0.37304, 19.2334, -100.669, 0.37304, 26.4834, -100.419, 0.37304, 27.9834, -53.169, 0.37304, 27.9834, 48.331, 0.37304, 18.7334, 49.331, 0.37304, 20.9834, 49.081, 0.37304, 18.7334, -50.919, 3.62304, 32.2334, -50.419, 3.62304, 32.9834, -47.919, 3.62304, 32.9834, -47.919, 3.62304, 24.4834, -48.669, 3.62304, 22.9834, -51.919, 3.62304, 23.7334, -51.919, 3.62304, 32.2334, -48.669, 3.62304, 23.9834, -47.169, 0.37304, 22.7334, -46.669, 0.37304, 23.7334, -5.16899, 0.37304, 23.7334, -5.16899, 0.37304, 22.9834, 49.831, 0.37304, 22.2334, 50.081, 0.37304, 23.7334, 50.581, 0.37304, 23.9834, -1.16899, 3.62304, 25.4834, -1.16899, 3.62304, 23.9834, -1.66899, 3.37304, 23.4834, -3.66899, 3.62304, 23.9834, -3.41899, 3.62304, 25.7334, -22.169, 0.37304, 46.2334, -21.919, 0.37304, 47.2334, -21.169, 0.37304, 47.2334, -16.419, 0.37304, 46.4834, -35.669, 0.37304, 44.9834, -35.419, 0.37304, 46.2334, -34.669, 0.37304, 46.2334, -30.419, 0.37304, 44.4834, -42.169, 0.87304, 45.2334, -39.919, 0.37304, 45.7334, -37.169, 0.37304, 44.9834, -40.919, 0.37304, 45.1709, -45.669, 0.37304, 32.9834, -28.669, 0.37304, 44.7334, -45.669, 0.37304, 33.9834, -47.169, 0.37304, 38.4834, -44.919, 0.37304, 39.2334, -27.919, 0.37304, 47.2334, -23.419, 0.37304, 46.2334, -6.91899, 0.37304, 45.7334, -42.719, 0.37304, 44.0334, -46.669, 0.37304, 46.7334, -43.294, 0.37304, 45.6084, -46.419, 0.37304, 32.7334, -12.919, 0.37304, 46.7334, -4.91899, 0.37304, 26.2334, -10.4387, 0.87304, 25.2111, -11.9237, 0.37304, 25.2501, -10.4434, 0.37304, 26.7577, -8.9037, 0.37304, 25.2221, 51.081, 3.62304, 25.9834, 47.831, 3.62304, 25.9834, 47.831, 3.62304, 30.4834, 52.581, 3.62304, 29.9834, 47.081, 3.62304, 31.2334, 51.831, 3.62304, 33.4834, 52.581, 3.62304, 25.9834, 47.831, 3.62304, 39.2334, 52.331, 3.62304, 39.9834, 53.331, 3.62304, 39.7334, 46.081, 3.62304, 31.2334, -50.169, 0.37304, 31.7334, -49.669, 0.37304, 31.7334, -49.669, 0.37304, 25.9834, -50.669, 0.37304, 25.7334, -0.918991, 0.37304, 26.9834, -0.918991, 0.37304, 27.2334, -3.91899, 0.37304, 26.9834, -6.16899, 0.37304, 46.2334, -6.16899, 0.37304, 47.2334, -0.668991, 0.37304, 45.9834, 49.331, 0.37304, 26.4834, 49.831, 0.37304, 29.2334, 50.331, 0.37304, 27.7334, 50.331, 0.37304, 26.4834, 1.58101, 0.37304, 45.7334, 1.83101, 0.37304, 46.2334, 2.58101, 0.37304, 46.2334, 7.58101, 0.37304, 44.7334, 9.33101, 0.37304, 44.7334, 9.83101, 0.37304, 46.2334, 10.331, 0.37304, 46.2334, 16.581, 0.37304, 44.2334, 15.581, 0.37304, 32.4834, 16.581, 0.37304, 32.9834, -100.419, 0.37304, 28.7334, -101.419, 0.37304, 29.2334, -101.669, 0.37304, 33.9834, -100.169, 0.37304, 34.4834, -53.169, 0.37304, 32.9834, 53.331, 0.37304, 33.9834, 54.331, 0.37304, 37.7334, 102.581, 0.37304, 30.9834, 101.831, 0.37304, 30.4834, 16.581, 2.37304, 30.9834, 16.581, 2.37304, 31.4834, 17.331, 2.37304, 31.4834, 45.581, 0.37304, 39.2334, 46.331, 0.37304, 38.9834, 46.331, 0.37304, 38.2334, 17.081, 0.37304, 44.4834, 17.831, 0.37304, 46.7334, 22.831, 0.37304, 45.7334, 23.581, 0.37304, 46.9834, 30.581, 0.37304, 45.2334, 18.331, 0.37304, 32.4834, 31.081, 0.37304, 47.2334, 39.081, 0.37304, 47.2334, 46.831, 0.37304, 46.9834, 45.581, 0.37304, 40.7334, 24.0562, 0.87304, 34.2281, 25.5913, 0.37304, 34.2391, 22.5664, 0.37304, 35.7539, 105.581, 0.37304, 33.4834, 105.581, 0.37304, 31.2334, 103.081, 0.37304, 31.4834, 106.831, 0.37304, 37.4834, 107.081, 0.37304, 35.2334, 54.831, 0.37304, 40.7334, 53.081, 0.62304, 41.2334, 54.081, 0.37304, 46.4834, 109.831, 0.37304, 44.2334, 110.581, 0.37304, 38.4834, 108.081, 0.37304, 38.4834, 107.831, 0.37304, 44.7334, 107.081, 0.37304, 46.4834, 49.331, 0.37304, 35.7334, 49.581, 0.37304, 37.9834, 50.331, 0.37304, 38.2334, 49.331, 0.37304, 32.7334, 48.081, 0.37304, 32.7334, -51.919, 0.37304, 34.2334, -51.919, 0.37304, 33.4834, -52.919, 0.37304, 33.4834, -53.419, 0.37304, 38.9834, -99.419, 0.37304, 35.2334, -99.419, 0.37304, 38.9834, -51.919, 3.62304, 38.2334, -51.919, 3.62304, 38.7334, -50.919, 3.62304, 39.2334, -46.419, 3.62304, 40.4834, -46.419, 3.62304, 39.9834, -48.419, 3.62304, 39.2334, -49.419, 3.62304, 48.7334, -50.419, 3.62304, 49.2334, -49.669, 3.62304, 52.2334, -48.419, 3.62304, 37.7334, -47.169, 3.62304, 34.4834, -45.919, 3.62304, 51.4834, -45.919, 3.62304, 47.9834, -47.669, 3.62304, 48.2334, -48.169, 3.62304, 47.7334, -50.919, 3.62304, 40.2334, -50.419, 3.62304, 47.2334, -52.669, 3.62304, 46.9834, -49.419, 3.62304, 47.9834, -49.419, 0.37304, 33.7334, -49.419, 0.37304, 34.9834, -48.919, 0.37304, 34.4834, -52.419, 0.37304, 40.9834, -52.419, 0.37304, 39.9834, -53.419, 0.37304, 39.4834, -53.919, 0.37304, 46.4834, -99.419, 0.37304, 45.9834, 51.581, 3.62304, 40.4834, 51.581, 3.62304, 41.4834, 46.831, 3.62304, 40.2334, 48.331, 4.12304, 47.4834, 52.831, 4.12304, 47.4834, 52.331, 3.62304, 45.0834, 48.031, 3.62304, 46.0334, -48.919, 0.37304, 41.4834, -48.919, 0.37304, 40.9834, -49.669, 0.37304, 40.7334, -49.669, 0.37304, 42.9834, -50.669, 0.37304, 46.2334, -50.169, 0.37304, 46.9834, -49.919, 0.37304, 44.2334, 49.081, 0.37304, 41.9834, 49.581, 0.37304, 43.4834, 49.581, 0.37304, 41.9834, 49.831, 0.37304, 46.2334, 50.331, 0.37304, 45.9834, -29.919, 4.12304, 50.9834, -28.419, 3.87304, 48.4834, -29.169, 3.87304, 47.9834, -29.669, 3.62304, 45.7334, -35.419, 3.62304, 47.7334, -35.169, 3.62304, 52.7334, -31.2315, 3.62304, 51.4209, 15.331, 3.62304, 51.2334, 15.831, 3.62304, 52.4834, 19.581, 3.62304, 51.4834, 19.081, 3.62304, 47.7334, 17.081, 3.62304, 47.9834, 14.081, 3.62304, 51.2334, 10.581, 3.62304, 47.4834, 11.581, 3.62304, 52.2334, 16.081, 3.62304, 45.7334, -101.419, 0.37304, 49.4834, -102.669, 0.37304, 49.2334, -102.919, 0.37304, 49.7334, -106.919, 0.37304, 53.7334, -107.919, 0.37304, 54.2334, -107.669, 0.37304, 55.4834, -105.919, 0.37304, 51.7334, -106.919, 0.37304, 52.2334, -99.419, 0.37304, 46.7334, -100.919, 0.37304, 47.7334, -100.919, 0.37304, 48.9834, -108.169, 0.37304, 55.9834, -108.169, 0.37304, 58.9834, -105.419, 0.37304, 50.2334, -105.419, 0.37304, 59.7334, -103.169, 0.37304, 62.2334, -101.169, 0.37304, 61.9834, -100.169, 0.37304, 62.9834, -59.419, 0.37304, 61.4834, -50.669, 0.37304, 53.2334, -51.919, 0.37304, 48.4834, -53.919, 0.37304, 47.7334, -57.669, 0.37304, 59.9834, 1.08101, 3.62304, 47.7334, 0.581009, 3.62304, 46.9834, -0.918991, 3.62304, 47.4834, -0.918991, 3.62304, 50.9834, -0.168991, 3.62304, 51.4834, 9.33101, 3.62304, 47.7334, 8.58101, 3.62304, 45.9834, 8.08101, 3.62304, 45.9834, 1.83101, 3.62304, 47.7334, 5.33101, 3.62304, 51.9834, 7.08101, 3.62304, 51.9834, 0.331009, 3.62304, 53.2334, 7.83101, 3.62304, 53.2334, -36.419, 3.62304, 47.2334, -36.669, 3.62304, 46.4834, -37.919, 3.62304, 46.4834, -37.419, 3.62304, 51.9834, -36.669, 3.62304, 53.2334, -44.919, 3.62304, 51.9834, -40.669, 3.62304, 47.2334, -42.419, 3.62304, 46.7334, -44.669, 3.62304, 53.2334, -38.169, 3.62304, 51.9834, 104.831, 0.37304, 56.9834, 106.331, 0.37304, 56.9834, 106.331, 0.37304, 55.9834, 107.081, 0.37304, 55.2334, 107.331, 0.37304, 48.4834, 106.831, 0.37304, 47.9834, 109.831, 0.37304, 54.9834, 110.581, 0.37304, 48.9834, 108.081, 0.37304, 48.9834, 55.081, 0.37304, 51.4834, 22.581, 3.62304, 50.9834, 23.331, 3.62304, 52.7334, 24.081, 3.62304, 52.7334, 29.831, 3.62304, 51.2334, 31.331, 3.62304, 48.4834, 30.581, 3.62304, 48.4834, 29.831, 3.62304, 47.7334, 24.081, 3.62304, 48.2334, 22.581, 3.62304, 48.2334, 22.081, 3.62304, 46.9834, 29.831, 3.62304, 46.7334, -14.919, 3.62304, 51.4834, -14.419, 3.62304, 52.7334, -13.919, 3.62304, 52.7334, -16.169, 3.62304, 47.9834, -16.169, 3.62304, 51.4834, -7.41899, 3.62304, 47.2334, -8.66899, 3.62304, 51.7334, -7.41899, 3.62304, 48.2334, -5.91899, 3.62304, 48.7334, -6.16899, 3.62304, 52.2334, 44.831, 3.87304, 51.7334, 45.581, 3.87304, 52.7334, 46.581, 3.62304, 52.7334, 43.581, 3.62304, 48.4834, 43.331, 3.62304, 51.7334, 47.081, 4.12304, 48.4834, 44.7477, 4.12304, 48.4834, 53.581, 3.62304, 51.2334, -22.669, 4.12304, 48.4834, -23.169, 3.87304, 47.4834, -23.669, 3.87304, 47.4834, -29.169, 4.12304, 51.7334, -28.169, 4.12304, 51.7334, -22.669, 4.12304, 50.7334, -22.169, 3.62304, 52.2334, -21.369, 3.62304, 48.3834, -34.669, 0.37304, 49.4834, -34.669, 0.37304, 49.9834, -34.169, 0.37304, 49.9834, -32.919, 0.37304, 49.2334, -31.169, 0.37304, 47.9834, -30.919, 0.37304, 48.9834, 26.581, 0.37304, 49.2334, 29.331, 0.37304, 49.2334, 29.081, 0.37304, 48.2334, 13.581, 0.37304, 48.9834, 13.581, 0.37304, 48.4834, 13.081, 0.37304, 48.4834, 10.831, 0.37304, 49.4834, 11.581, 0.37304, 49.7334, 30.331, 3.62304, 51.7334, 36.831, 3.62304, 51.4834, 37.331, 3.62304, 48.4834, 46.081, 0.37304, 50.4834, 46.081, 0.37304, 50.9834, 46.581, 0.37304, 50.9834, 50.081, 0.37304, 49.7334, 49.581, 0.37304, 49.2334, 46.831, 0.37304, 49.4834, 52.581, 0.37304, 49.4834, 52.581, 0.37304, 48.4834, 51.331, 0.37304, 48.4834, 19.331, 0.37304, 49.2334, 21.081, 0.37304, 49.2334, 20.831, 0.37304, 48.7334, -39.919, 0.37304, 48.9834, -39.919, 0.37304, 49.7334, -39.419, 0.37304, 49.7334, -23.919, 0.37304, 49.4834, -23.919, 0.37304, 48.9834, -24.919, 0.37304, 48.9834, -27.169, 0.37304, 49.4834, -26.919, 0.37304, 50.2334, -9.16899, 0.37304, 49.7334, -9.16899, 0.37304, 48.9834, -9.66899, 0.37304, 48.9834, -11.419, 0.37304, 49.7334, -12.919, 0.37304, 49.7334, -12.919, 0.37304, 50.2334, 5.08101, 0.37304, 49.4834, 5.08101, 0.37304, 48.9834, 4.33101, 0.37304, 48.9834, 2.58101, 0.37304, 49.4834, 2.58101, 0.37304, 50.2334, -18.169, 0.37304, 49.4834, -16.169, 0.37304, 49.7334, -16.169, 0.37304, 49.2334, 34.081, 0.37304, 49.7334, 36.581, 0.37304, 49.9834, 36.581, 0.37304, 49.4834, 38.331, 0.37304, 49.7334, 38.581, 0.37304, 50.2334, 39.081, 0.37304, 49.9834, 41.831, 0.37304, 49.7334, 43.581, 0.37304, 50.2334, 43.581, 0.37304, 49.7334, 54.331, 0.37304, 52.2334, 59.081, 0.37304, 57.2334, 104.081, 0.37304, 57.7334, -28.669, 0.37304, 100.233, -26.919, 0.37304, 100.733, -26.669, 0.37304, 100.233, -24.669, 0.37304, 99.9834, -24.419, 0.37304, 99.4834, -22.169, 0.37304, 53.4834, -23.419, 0.37304, 53.2334, -28.419, 0.37304, 53.2334, -23.419, 0.37304, 52.2334, -22.169, 0.37304, 99.4834, -4.91899, 0.37304, 101.233, -3.41899, 0.37304, 101.233, -2.41899, 0.37304, 100.233, -0.918991, 0.37304, 54.4834, -1.41899, 0.37304, 52.2334, -5.16899, 0.37304, 53.2334, -0.418991, 0.37304, 100.233, 0.581009, 0.37304, 99.4834, 0.331009, 0.37304, 54.7334, 73.831, 0.37304, 104.483, 74.081, 0.37304, 104.983, 75.581, 0.37304, 104.483, 72.081, 0.37304, 103.983, 72.331, 0.37304, 104.483, 86.831, 0.37304, 100.233, 87.331, 0.37304, 100.733, 88.581, 0.37304, 100.733, 91.331, 0.37304, 100.233, 91.831, 0.37304, 99.4834, 103.081, 0.37304, 89.4834, 103.581, 0.37304, 89.2334, 103.331, 0.37304, 87.4834, 79.331, 0.37304, 101.233, 81.331, 0.37304, 101.483, 81.331, 0.37304, 99.9834, 107.081, 0.37304, 79.4834, 107.581, 0.37304, 78.9834, 107.581, 0.37304, 76.9834, 105.081, 0.37304, 77.2334, 76.831, 0.37304, 102.233, 78.331, 0.37304, 102.233, 103.831, 0.37304, 76.2334, 104.081, 0.37304, 73.7334, 103.581, 0.37304, 73.4834, 100.331, 0.37304, 85.4834, 100.831, 0.37304, 84.9834, 88.831, 0.37304, 101.233, 103.331, 0.37304, 85.2334, 103.331, 0.37304, 83.7334, 101.081, 0.37304, 87.4834, 99.331, 0.37304, 94.2334, 99.831, 0.37304, 93.7334, 100.331, 0.37304, 86.7334, 81.831, 0.37304, 99.4834, 83.831, 0.37304, 99.4834, 84.581, 0.37304, 100.483, 103.831, 0.37304, 83.2334, 65.081, 0.37304, 62.9834, 57.081, 0.37304, 99.4834, 64.331, 0.37304, 99.4834, 68.581, 0.37304, 102.233, 69.581, 0.37304, 104.233, 103.581, 0.37304, 72.2334, 106.581, 0.37304, 83.2334, 99.331, 0.37304, 99.4834, 64.831, 0.37304, 100.983, 49.331, 0.37304, 53.2334, 102.581, 0.37304, 93.4834, 49.831, 0.37304, 99.4834, -32.169, 0.37304, 99.4834, -31.669, 0.37304, 100.233, -30.169, 0.37304, 53.2334, -36.419, 0.37304, 54.7334, -30.669, 0.37304, 52.4834, -36.169, 0.37304, 99.4834, 17.331, 0.37304, 99.9834, 18.331, 0.37304, 100.233, 18.581, 0.37304, 99.7334, 14.831, 0.37304, 99.7334, 17.081, 0.37304, 100.483, 19.831, 0.37304, 99.4834, 23.331, 0.37304, 54.2334, 22.331, 0.37304, 53.9834, 15.331, 0.37304, 53.9834, 22.081, 0.37304, 52.4834, 23.331, 0.37304, 99.4834, -46.419, 0.37304, 104.483, -45.919, 0.37304, 103.983, -45.919, 0.37304, 102.733, -50.169, 0.37304, 103.983, -48.169, 0.37304, 104.983, -45.919, 0.37304, 54.4834, -46.169, 0.37304, 52.7334, -48.919, 0.37304, 53.7334, -50.419, 0.37304, 53.7334, -45.169, 0.37304, 54.7334, -53.669, 0.37304, 102.233, -52.419, 0.37304, 104.233, -45.169, 0.37304, 102.233, -58.419, 0.37304, 100.233, -15.669, 0.37304, 53.7334, -15.669, 0.37304, 52.7334, -14.919, 0.37304, 53.9834, -17.419, 0.37304, 99.4834, -14.919, 0.37304, 100.483, 14.581, 0.37304, 52.7334, 13.831, 0.37304, 52.7334, 12.081, 0.37304, 99.4834, 12.331, 0.37304, 99.9834, 9.58101, 0.37304, 54.2334, 10.831, 0.37304, 99.4834, 10.331, 0.37304, 99.9834, 37.081, 0.37304, 52.9834, 28.581, 0.37304, 52.7334, 30.331, 0.37304, 99.4834, 36.581, 0.37304, 99.7334, 38.081, 0.37304, 99.7334, 40.581, 0.37304, 100.733, 41.831, 0.37304, 99.7334, 45.331, 0.37304, 99.4834, 44.331, 0.37304, 53.7334, 44.331, 0.37304, 52.9834, 45.331, 0.37304, 54.2334, -7.91899, 0.37304, 53.7334, -8.16899, 0.37304, 53.2334, -9.91899, 0.37304, 53.2334, -9.41899, 0.37304, 102.983, -7.66899, 0.37304, 103.983, -6.16899, 0.37304, 103.483, -13.169, 0.37304, 101.233, -12.169, 0.37304, 103.233, 7.08101, 0.37304, 54.7334, 6.58101, 0.37304, 53.4834, 5.83101, 0.37304, 53.2334, 2.58101, 0.37304, 99.4834, 7.83101, 0.37304, 101.983, 9.58101, 0.37304, 101.483, 5.83101, 0.37304, 100.983, 3.58101, 0.37304, 101.233, -38.669, 0.37304, 101.483, -37.419, 0.37304, 101.483, -37.169, 0.37304, 100.233, -42.419, 0.37304, 101.233, -38.919, 0.37304, 101.983, -37.419, 0.37304, 54.7334, -37.919, 0.37304, 53.4834, -42.669, 0.37304, 54.2334, -43.669, 0.37304, 102.233, 103.831, 0.37304, 59.2334, 104.331, 0.37304, 58.9834, 102.581, 0.37304, 64.2334, 103.331, 0.37304, 63.4834, -61.669, 0.37304, 99.4834, -60.419, 0.37304, 100.483, -77.919, 0.37304, 99.7334, -76.919, 0.37304, 99.9834, -76.669, 0.37304, 99.4834, -104.669, 0.37304, 65.9834, -105.919, 0.37304, 66.4834, -105.669, 0.37304, 67.9834, -103.419, 0.37304, 80.4834, -104.419, 0.37304, 80.9834, -104.419, 0.37304, 82.4834, -104.669, 0.37304, 91.7334, -105.919, 0.37304, 92.4834, -105.669, 0.37304, 93.7334, -102.919, 0.37304, 90.2334, -104.169, 0.37304, 90.2334, -106.669, 0.37304, 94.4834, -106.919, 0.37304, 95.7334, -106.919, 0.37304, 99.2334, -103.419, 0.37304, 100.483, -84.419, 0.37304, 99.4834, -83.419, 0.37304, 100.233, -81.919, 0.37304, 99.7334, -95.169, 0.37304, 100.483, -93.669, 0.37304, 100.483, -92.669, 0.37304, 99.4834, -103.169, 0.37304, 74.7334, -102.169, 0.37304, 76.7334, -100.419, 0.37304, 76.4834, -100.419, 0.37304, 63.7334, -99.669, 0.37304, 102.233, -97.919, 0.37304, 103.233, -96.169, 0.37304, 102.733, -100.419, 0.37304, 78.4834, -102.669, 0.37304, 78.7334, -79.669, 0.37304, 100.733, -102.169, 0.37304, 102.483, -104.169, 0.37304, 64.4834, -99.919, 0.37304, 77.9834, -102.419, 0.37304, 89.7334, -105.419, 0.37304, 82.9834, -105.669, 0.37304, 87.7334, -102.919, 0.37304, 88.4834, -106.669, 0.37304, 68.4834, -106.919, 0.37304, 73.4834, -69.169, 0.37304, 99.4834, 105.081, 0.37304, 71.2334, 105.831, 0.37304, 65.4834, 103.331, 0.37304, 65.4834, 104.081, 0.37304, 71.4834)
polygons = [PackedInt32Array(2, 1, 0), PackedInt32Array(3, 2, 4), PackedInt32Array(4, 2, 0), PackedInt32Array(4, 0, 6), PackedInt32Array(4, 6, 5), PackedInt32Array(8, 7, 9), PackedInt32Array(9, 7, 5), PackedInt32Array(11, 10, 12), PackedInt32Array(12, 10, 13), PackedInt32Array(9, 5, 14), PackedInt32Array(14, 5, 13), PackedInt32Array(14, 13, 16), PackedInt32Array(14, 16, 15), PackedInt32Array(13, 5, 12), PackedInt32Array(7, 4, 5), PackedInt32Array(18, 17, 19), PackedInt32Array(19, 17, 20), PackedInt32Array(20, 17, 22), PackedInt32Array(20, 22, 21), PackedInt32Array(24, 23, 21), PackedInt32Array(25, 27, 26), PackedInt32Array(22, 27, 21), PackedInt32Array(21, 27, 24), PackedInt32Array(24, 27, 25), PackedInt32Array(24, 25, 28), PackedInt32Array(30, 29, 31), PackedInt32Array(31, 29, 32), PackedInt32Array(32, 29, 33), PackedInt32Array(33, 29, 34), PackedInt32Array(37, 36, 35), PackedInt32Array(34, 38, 35), PackedInt32Array(35, 38, 37), PackedInt32Array(37, 38, 40), PackedInt32Array(37, 40, 39), PackedInt32Array(38, 41, 40), PackedInt32Array(38, 42, 41), PackedInt32Array(35, 33, 34), PackedInt32Array(40, 43, 39), PackedInt32Array(45, 44, 46), PackedInt32Array(46, 44, 49), PackedInt32Array(46, 49, 48), PackedInt32Array(46, 48, 47), PackedInt32Array(50, 52, 51), PackedInt32Array(55, 54, 53), PackedInt32Array(55, 57, 56), PackedInt32Array(59, 58, 60), PackedInt32Array(60, 58, 61), PackedInt32Array(53, 63, 62), PackedInt32Array(50, 64, 48), PackedInt32Array(57, 55, 65), PackedInt32Array(65, 55, 53), PackedInt32Array(65, 53, 62), PackedInt32Array(52, 50, 66), PackedInt32Array(66, 50, 49), PackedInt32Array(49, 50, 48), PackedInt32Array(67, 61, 68), PackedInt32Array(68, 61, 58), PackedInt32Array(62, 59, 65), PackedInt32Array(65, 59, 47), PackedInt32Array(46, 47, 69), PackedInt32Array(69, 47, 59), PackedInt32Array(69, 59, 60), PackedInt32Array(70, 72, 71), PackedInt32Array(75, 74, 73), PackedInt32Array(77, 76, 78), PackedInt32Array(78, 76, 79), PackedInt32Array(81, 80, 82), PackedInt32Array(82, 80, 83), PackedInt32Array(85, 84, 86), PackedInt32Array(86, 84, 87), PackedInt32Array(87, 84, 88), PackedInt32Array(89, 91, 90), PackedInt32Array(93, 92, 94), PackedInt32Array(94, 92, 95), PackedInt32Array(96, 75, 97), PackedInt32Array(97, 75, 73), PackedInt32Array(97, 73, 98), PackedInt32Array(99, 86, 100), PackedInt32Array(100, 86, 101), PackedInt32Array(101, 86, 102), PackedInt32Array(102, 86, 87), PackedInt32Array(104, 103, 105), PackedInt32Array(105, 103, 107), PackedInt32Array(105, 107, 106), PackedInt32Array(109, 108, 103), PackedInt32Array(103, 108, 107), PackedInt32Array(111, 110, 88), PackedInt32Array(113, 112, 114), PackedInt32Array(114, 112, 116), PackedInt32Array(114, 116, 115), PackedInt32Array(70, 118, 117), PackedInt32Array(97, 98, 106), PackedInt32Array(106, 98, 119), PackedInt32Array(121, 120, 76), PackedInt32Array(76, 120, 79), PackedInt32Array(72, 123, 122), PackedInt32Array(92, 125, 124), PackedInt32Array(127, 126, 109), PackedInt32Array(109, 126, 108), PackedInt32Array(129, 128, 130), PackedInt32Array(130, 128, 117), PackedInt32Array(130, 117, 131), PackedInt32Array(115, 79, 120), PackedInt32Array(111, 88, 119), PackedInt32Array(119, 88, 84), PackedInt32Array(119, 84, 132), PackedInt32Array(124, 133, 116), PackedInt32Array(123, 72, 128), PackedInt32Array(128, 72, 70), PackedInt32Array(128, 70, 117), PackedInt32Array(105, 106, 134), PackedInt32Array(134, 106, 119), PackedInt32Array(134, 119, 132), PackedInt32Array(130, 131, 132), PackedInt32Array(132, 131, 136), PackedInt32Array(132, 136, 135), PackedInt32Array(83, 80, 135), PackedInt32Array(135, 80, 138), PackedInt32Array(135, 138, 137), PackedInt32Array(95, 92, 112), PackedInt32Array(112, 92, 124), PackedInt32Array(112, 124, 116), PackedInt32Array(137, 114, 135), PackedInt32Array(135, 114, 115), PackedInt32Array(132, 135, 134), PackedInt32Array(134, 135, 139), PackedInt32Array(134, 139, 91), PackedInt32Array(134, 91, 89), PackedInt32Array(120, 139, 115), PackedInt32Array(115, 139, 135), PackedInt32Array(142, 141, 140), PackedInt32Array(145, 144, 143), PackedInt32Array(146, 145, 147), PackedInt32Array(147, 145, 143), PackedInt32Array(147, 143, 148), PackedInt32Array(151, 150, 149), PackedInt32Array(153, 152, 151), PackedInt32Array(155, 154, 156), PackedInt32Array(156, 154, 157), PackedInt32Array(157, 154, 158), PackedInt32Array(160, 159, 161), PackedInt32Array(161, 159, 162), PackedInt32Array(162, 159, 163), PackedInt32Array(165, 164, 166), PackedInt32Array(166, 164, 167), PackedInt32Array(168, 142, 164), PackedInt32Array(164, 142, 167), PackedInt32Array(167, 142, 169), PackedInt32Array(171, 170, 162), PackedInt32Array(162, 170, 172), PackedInt32Array(162, 172, 161), PackedInt32Array(158, 154, 173), PackedInt32Array(175, 174, 169), PackedInt32Array(140, 177, 176), PackedInt32Array(178, 175, 151), PackedInt32Array(151, 175, 179), PackedInt32Array(179, 175, 173), PackedInt32Array(173, 175, 180), PackedInt32Array(180, 181, 163), PackedInt32Array(182, 149, 183), PackedInt32Array(60, 184, 158), PackedInt32Array(186, 185, 153), PackedInt32Array(163, 69, 180), PackedInt32Array(180, 69, 60), PackedInt32Array(180, 60, 173), PackedInt32Array(173, 60, 158), PackedInt32Array(186, 153, 187), PackedInt32Array(187, 153, 151), PackedInt32Array(140, 176, 142), PackedInt32Array(142, 176, 169), PackedInt32Array(169, 176, 175), PackedInt32Array(175, 176, 180), PackedInt32Array(182, 178, 149), PackedInt32Array(149, 178, 151), PackedInt32Array(163, 159, 69), PackedInt32Array(147, 148, 178), PackedInt32Array(178, 148, 175), PackedInt32Array(190, 189, 188), PackedInt32Array(179, 187, 151), PackedInt32Array(188, 179, 190), PackedInt32Array(190, 179, 173), PackedInt32Array(191, 156, 192), PackedInt32Array(192, 156, 157), PackedInt32Array(195, 194, 193), PackedInt32Array(196, 195, 197), PackedInt32Array(197, 195, 193), PackedInt32Array(197, 193, 199), PackedInt32Array(197, 199, 198), PackedInt32Array(202, 201, 200), PackedInt32Array(198, 200, 197), PackedInt32Array(205, 204, 203), PackedInt32Array(203, 206, 205), PackedInt32Array(205, 206, 198), PackedInt32Array(198, 206, 200), PackedInt32Array(200, 206, 202), PackedInt32Array(202, 206, 207), PackedInt32Array(207, 206, 209), PackedInt32Array(207, 209, 208), PackedInt32Array(211, 210, 206), PackedInt32Array(206, 210, 209), PackedInt32Array(213, 212, 214), PackedInt32Array(214, 212, 217), PackedInt32Array(214, 217, 216), PackedInt32Array(214, 216, 215), PackedInt32Array(219, 218, 14), PackedInt32Array(221, 220, 222), PackedInt32Array(222, 220, 215), PackedInt32Array(215, 220, 224), PackedInt32Array(215, 224, 223), PackedInt32Array(226, 225, 218), PackedInt32Array(218, 225, 227), PackedInt32Array(218, 227, 14), PackedInt32Array(230, 229, 228), PackedInt32Array(228, 219, 223), PackedInt32Array(223, 219, 14), PackedInt32Array(223, 14, 215), PackedInt32Array(215, 14, 15), PackedInt32Array(228, 231, 219), PackedInt32Array(214, 215, 15), PackedInt32Array(223, 230, 228), PackedInt32Array(232, 234, 233), PackedInt32Array(237, 236, 235), PackedInt32Array(240, 239, 238), PackedInt32Array(232, 241, 237), PackedInt32Array(244, 243, 242), PackedInt32Array(238, 245, 234), PackedInt32Array(235, 246, 237), PackedInt32Array(237, 246, 232), PackedInt32Array(234, 232, 238), PackedInt32Array(238, 232, 240), PackedInt32Array(240, 232, 246), PackedInt32Array(240, 246, 247), PackedInt32Array(244, 242, 28), PackedInt32Array(28, 242, 248), PackedInt32Array(28, 248, 240), PackedInt32Array(28, 240, 247), PackedInt32Array(242, 249, 248), PackedInt32Array(247, 24, 28), PackedInt32Array(251, 250, 252), PackedInt32Array(252, 250, 254), PackedInt32Array(252, 254, 253), PackedInt32Array(250, 255, 254), PackedInt32Array(253, 37, 256), PackedInt32Array(256, 37, 26), PackedInt32Array(26, 37, 257), PackedInt32Array(257, 37, 39), PackedInt32Array(26, 27, 256), PackedInt32Array(253, 256, 252), PackedInt32Array(258, 26, 259), PackedInt32Array(259, 26, 257), PackedInt32Array(261, 260, 262), PackedInt32Array(262, 260, 42), PackedInt32Array(264, 263, 265), PackedInt32Array(265, 263, 266), PackedInt32Array(207, 208, 262), PackedInt32Array(266, 207, 265), PackedInt32Array(265, 207, 38), PackedInt32Array(38, 207, 262), PackedInt32Array(38, 262, 42), PackedInt32Array(268, 267, 269), PackedInt32Array(269, 267, 270), PackedInt32Array(270, 267, 271), PackedInt32Array(271, 267, 272), PackedInt32Array(273, 58, 274), PackedInt32Array(273, 271, 272), PackedInt32Array(273, 272, 248), PackedInt32Array(273, 248, 58), PackedInt32Array(58, 248, 68), PackedInt32Array(68, 248, 275), PackedInt32Array(275, 248, 249), PackedInt32Array(277, 276, 278), PackedInt32Array(278, 276, 279), PackedInt32Array(281, 280, 282), PackedInt32Array(282, 280, 283), PackedInt32Array(286, 285, 284), PackedInt32Array(282, 283, 279), PackedInt32Array(279, 283, 134), PackedInt32Array(278, 279, 284), PackedInt32Array(284, 279, 134), PackedInt32Array(284, 134, 286), PackedInt32Array(286, 134, 89), PackedInt32Array(288, 287, 289), PackedInt32Array(289, 287, 16), PackedInt32Array(289, 16, 290), PackedInt32Array(290, 16, 13), PackedInt32Array(291, 289, 286), PackedInt32Array(286, 289, 290), PackedInt32Array(286, 290, 285), PackedInt32Array(292, 224, 293), PackedInt32Array(293, 224, 220), PackedInt32Array(293, 220, 211), PackedInt32Array(211, 220, 210), PackedInt32Array(296, 295, 294), PackedInt32Array(296, 294, 184), PackedInt32Array(297, 296, 298), PackedInt32Array(298, 296, 299), PackedInt32Array(299, 296, 300), PackedInt32Array(300, 296, 184), PackedInt32Array(300, 184, 60), PackedInt32Array(300, 60, 61), PackedInt32Array(303, 302, 301), PackedInt32Array(301, 305, 304), PackedInt32Array(139, 303, 301), PackedInt32Array(307, 306, 308), PackedInt32Array(308, 306, 309), PackedInt32Array(301, 304, 139), PackedInt32Array(139, 304, 312), PackedInt32Array(139, 312, 310), PackedInt32Array(310, 312, 311), PackedInt32Array(309, 312, 308), PackedInt32Array(308, 312, 304), PackedInt32Array(310, 91, 139), PackedInt32Array(315, 314, 313), PackedInt32Array(315, 313, 67), PackedInt32Array(67, 313, 61), PackedInt32Array(61, 313, 316), PackedInt32Array(316, 313, 317), PackedInt32Array(318, 300, 319), PackedInt32Array(319, 300, 316), PackedInt32Array(316, 300, 61), PackedInt32Array(321, 320, 322), PackedInt32Array(322, 320, 324), PackedInt32Array(322, 324, 323), PackedInt32Array(325, 324, 326), PackedInt32Array(326, 324, 320), PackedInt32Array(326, 320, 327), PackedInt32Array(330, 329, 328), PackedInt32Array(332, 331, 333), PackedInt32Array(333, 331, 334), PackedInt32Array(330, 328, 334), PackedInt32Array(334, 328, 333), PackedInt32Array(333, 328, 335), PackedInt32Array(338, 337, 336), PackedInt32Array(338, 336, 339), PackedInt32Array(339, 336, 342), PackedInt32Array(339, 342, 341), PackedInt32Array(339, 341, 340), PackedInt32Array(345, 344, 343), PackedInt32Array(347, 346, 340), PackedInt32Array(343, 348, 345), PackedInt32Array(345, 348, 347), PackedInt32Array(345, 347, 340), PackedInt32Array(345, 340, 341), PackedInt32Array(350, 349, 351), PackedInt32Array(351, 349, 352), PackedInt32Array(343, 352, 348), PackedInt32Array(348, 352, 349), PackedInt32Array(354, 353, 355), PackedInt32Array(355, 353, 357), PackedInt32Array(355, 357, 356), PackedInt32Array(359, 358, 356), PackedInt32Array(356, 358, 360), PackedInt32Array(363, 362, 361), PackedInt32Array(355, 356, 361), PackedInt32Array(361, 356, 360), PackedInt32Array(361, 360, 364), PackedInt32Array(361, 364, 363), PackedInt32Array(365, 325, 366), PackedInt32Array(366, 325, 326), PackedInt32Array(369, 368, 367), PackedInt32Array(367, 368, 329), PackedInt32Array(329, 368, 328), PackedInt32Array(369, 366, 368), PackedInt32Array(368, 366, 326), PackedInt32Array(351, 370, 350), PackedInt32Array(332, 371, 331), PackedInt32Array(331, 371, 374), PackedInt32Array(331, 374, 373), PackedInt32Array(331, 373, 372), PackedInt32Array(370, 351, 376), PackedInt32Array(376, 351, 375), PackedInt32Array(374, 377, 373), PackedInt32Array(377, 379, 373), PackedInt32Array(373, 379, 378), PackedInt32Array(373, 378, 382), PackedInt32Array(373, 382, 381), PackedInt32Array(373, 381, 380), PackedInt32Array(384, 376, 383), PackedInt32Array(383, 376, 375), PackedInt32Array(384, 385, 378), PackedInt32Array(378, 385, 382), PackedInt32Array(382, 385, 381), PackedInt32Array(381, 383, 380), PackedInt32Array(383, 385, 384), PackedInt32Array(385, 383, 381), PackedInt32Array(357, 353, 386), PackedInt32Array(389, 388, 390), PackedInt32Array(390, 388, 387), PackedInt32Array(390, 387, 357), PackedInt32Array(390, 357, 386), PackedInt32Array(336, 337, 390), PackedInt32Array(390, 337, 389), PackedInt32Array(393, 392, 391), PackedInt32Array(395, 394, 393), PackedInt32Array(397, 396, 395), PackedInt32Array(399, 398, 397), PackedInt32Array(393, 391, 395), PackedInt32Array(397, 395, 399), PackedInt32Array(399, 395, 400), PackedInt32Array(400, 395, 391), PackedInt32Array(402, 401, 313), PackedInt32Array(313, 401, 317), PackedInt32Array(391, 317, 400), PackedInt32Array(400, 317, 401), PackedInt32Array(400, 401, 403), PackedInt32Array(405, 404, 406), PackedInt32Array(406, 404, 407), PackedInt32Array(407, 404, 408), PackedInt32Array(408, 409, 407), PackedInt32Array(411, 410, 412), PackedInt32Array(412, 410, 414), PackedInt32Array(412, 414, 413), PackedInt32Array(416, 415, 417), PackedInt32Array(417, 415, 413), PackedInt32Array(414, 417, 413), PackedInt32Array(420, 419, 418), PackedInt32Array(423, 422, 421), PackedInt32Array(425, 424, 426), PackedInt32Array(426, 424, 427), PackedInt32Array(426, 427, 428), PackedInt32Array(428, 427, 429), PackedInt32Array(429, 430, 428), PackedInt32Array(432, 431, 433), PackedInt32Array(435, 434, 436), PackedInt32Array(436, 434, 437), PackedInt32Array(436, 437, 433), PackedInt32Array(436, 433, 431), PackedInt32Array(439, 438, 440), PackedInt32Array(440, 438, 441), PackedInt32Array(443, 442, 444), PackedInt32Array(444, 442, 445), PackedInt32Array(447, 446, 448), PackedInt32Array(448, 446, 449), PackedInt32Array(451, 450, 452), PackedInt32Array(452, 450, 453), PackedInt32Array(456, 455, 454), PackedInt32Array(459, 458, 457), PackedInt32Array(463, 462, 460), PackedInt32Array(460, 462, 461), PackedInt32Array(465, 464, 466), PackedInt32Array(466, 464, 311), PackedInt32Array(469, 468, 467), PackedInt32Array(470, 472, 471), PackedInt32Array(467, 478, 469), PackedInt32Array(469, 478, 470), PackedInt32Array(470, 464, 472), PackedInt32Array(464, 474, 311), PackedInt32Array(311, 477, 467), PackedInt32Array(467, 475, 478), PackedInt32Array(478, 474, 470), PackedInt32Array(470, 474, 464), PackedInt32Array(474, 477, 311), PackedInt32Array(477, 475, 467), PackedInt32Array(475, 476, 478), PackedInt32Array(478, 476, 474), PackedInt32Array(474, 473, 477), PackedInt32Array(477, 473, 475), PackedInt32Array(475, 473, 476), PackedInt32Array(476, 473, 474), PackedInt32Array(311, 312, 466), PackedInt32Array(480, 479, 481), PackedInt32Array(481, 479, 484), PackedInt32Array(481, 484, 482), PackedInt32Array(482, 484, 483), PackedInt32Array(486, 485, 487), PackedInt32Array(487, 485, 488), PackedInt32Array(488, 485, 484), PackedInt32Array(490, 489, 491), PackedInt32Array(491, 489, 492), PackedInt32Array(495, 494, 493), PackedInt32Array(496, 498, 497), PackedInt32Array(500, 499, 501), PackedInt32Array(501, 499, 502), PackedInt32Array(504, 503, 495), PackedInt32Array(493, 518, 495), PackedInt32Array(495, 508, 504), PackedInt32Array(504, 510, 505), PackedInt32Array(505, 517, 506), PackedInt32Array(506, 514, 502), PackedInt32Array(502, 513, 493), PackedInt32Array(518, 507, 495), PackedInt32Array(493, 508, 518), PackedInt32Array(508, 510, 504), PackedInt32Array(495, 507, 508), PackedInt32Array(510, 517, 505), PackedInt32Array(517, 514, 506), PackedInt32Array(514, 513, 502), PackedInt32Array(513, 508, 493), PackedInt32Array(518, 508, 507), PackedInt32Array(508, 515, 510), PackedInt32Array(510, 509, 517), PackedInt32Array(517, 516, 514), PackedInt32Array(514, 512, 513), PackedInt32Array(513, 515, 508), PackedInt32Array(515, 509, 510), PackedInt32Array(509, 511, 517), PackedInt32Array(516, 512, 514), PackedInt32Array(517, 511, 516), PackedInt32Array(512, 516, 513), PackedInt32Array(513, 516, 515), PackedInt32Array(515, 511, 509), PackedInt32Array(511, 515, 516), PackedInt32Array(519, 482, 520), PackedInt32Array(520, 482, 483), PackedInt32Array(499, 492, 502), PackedInt32Array(502, 492, 498), PackedInt32Array(502, 498, 496), PackedInt32Array(502, 496, 506), PackedInt32Array(483, 484, 504), PackedInt32Array(504, 484, 521), PackedInt32Array(504, 521, 503), PackedInt32Array(506, 522, 505), PackedInt32Array(492, 489, 498), PackedInt32Array(524, 523, 505), PackedInt32Array(505, 523, 504), PackedInt32Array(479, 525, 484), PackedInt32Array(521, 526, 503), PackedInt32Array(527, 521, 485), PackedInt32Array(485, 521, 484), PackedInt32Array(529, 528, 530), PackedInt32Array(530, 528, 531), PackedInt32Array(531, 528, 532), PackedInt32Array(532, 528, 533), PackedInt32Array(535, 534, 536), PackedInt32Array(536, 534, 537), PackedInt32Array(540, 539, 538), PackedInt32Array(543, 547, 544), PackedInt32Array(544, 546, 540), PackedInt32Array(540, 537, 541), PackedInt32Array(537, 548, 542), PackedInt32Array(542, 548, 543), PackedInt32Array(547, 546, 544), PackedInt32Array(543, 545, 547), PackedInt32Array(546, 537, 540), PackedInt32Array(537, 546, 548), PackedInt32Array(548, 547, 543), PackedInt32Array(547, 545, 546), PackedInt32Array(543, 548, 545), PackedInt32Array(546, 545, 548), PackedInt32Array(544, 538, 549), PackedInt32Array(537, 534, 541), PackedInt32Array(544, 540, 538), PackedInt32Array(542, 550, 537), PackedInt32Array(553, 552, 551), PackedInt32Array(554, 542, 555), PackedInt32Array(555, 542, 543), PackedInt32Array(555, 543, 553), PackedInt32Array(555, 553, 551), PackedInt32Array(557, 556, 493), PackedInt32Array(493, 556, 558), PackedInt32Array(493, 558, 502), PackedInt32Array(559, 501, 502), PackedInt32Array(561, 560, 559), PackedInt32Array(559, 502, 561), PackedInt32Array(561, 502, 558), PackedInt32Array(561, 558, 563), PackedInt32Array(561, 563, 562), PackedInt32Array(566, 565, 564), PackedInt32Array(568, 567, 569), PackedInt32Array(569, 567, 564), PackedInt32Array(569, 564, 561), PackedInt32Array(571, 570, 566), PackedInt32Array(571, 566, 567), PackedInt32Array(567, 566, 564), PackedInt32Array(569, 561, 572), PackedInt32Array(572, 561, 573), PackedInt32Array(573, 561, 562), PackedInt32Array(574, 572, 551), PackedInt32Array(551, 572, 555), PackedInt32Array(555, 572, 573), PackedInt32Array(577, 576, 575), PackedInt32Array(578, 362, 575), PackedInt32Array(575, 362, 577), PackedInt32Array(577, 362, 363), PackedInt32Array(581, 580, 579), PackedInt32Array(583, 582, 323), PackedInt32Array(323, 582, 579), PackedInt32Array(323, 579, 322), PackedInt32Array(585, 584, 586), PackedInt32Array(586, 584, 588), PackedInt32Array(588, 584, 587), PackedInt32Array(586, 591, 590), PackedInt32Array(590, 592, 589), PackedInt32Array(589, 581, 579), PackedInt32Array(581, 592, 587), PackedInt32Array(587, 592, 588), PackedInt32Array(588, 591, 586), PackedInt32Array(591, 592, 590), PackedInt32Array(592, 581, 589), PackedInt32Array(592, 591, 588), PackedInt32Array(579, 582, 589), PackedInt32Array(595, 594, 593), PackedInt32Array(596, 595, 597), PackedInt32Array(597, 595, 593), PackedInt32Array(400, 403, 598), PackedInt32Array(598, 403, 599), PackedInt32Array(602, 601, 600), PackedInt32Array(607, 606, 603), PackedInt32Array(603, 606, 604), PackedInt32Array(604, 606, 605), PackedInt32Array(607, 603, 608), PackedInt32Array(608, 603, 609), PackedInt32Array(608, 609, 576), PackedInt32Array(576, 609, 575), PackedInt32Array(612, 611, 610), PackedInt32Array(614, 613, 612), PackedInt32Array(615, 614, 612), PackedInt32Array(612, 610, 615), PackedInt32Array(615, 610, 599), PackedInt32Array(615, 599, 617), PackedInt32Array(615, 617, 616), PackedInt32Array(610, 598, 599), PackedInt32Array(619, 618, 620), PackedInt32Array(620, 618, 601), PackedInt32Array(622, 621, 623), PackedInt32Array(623, 621, 625), PackedInt32Array(623, 625, 624), PackedInt32Array(623, 624, 626), PackedInt32Array(626, 624, 627), PackedInt32Array(627, 624, 628), PackedInt32Array(629, 469, 627), PackedInt32Array(627, 469, 626), PackedInt32Array(626, 469, 470), PackedInt32Array(632, 631, 630), PackedInt32Array(636, 635, 633), PackedInt32Array(633, 635, 634), PackedInt32Array(639, 638, 637), PackedInt32Array(637, 640, 639), PackedInt32Array(639, 640, 641), PackedInt32Array(641, 640, 642), PackedInt32Array(645, 644, 643), PackedInt32Array(646, 639, 647), PackedInt32Array(647, 639, 641), PackedInt32Array(643, 648, 642), PackedInt32Array(645, 643, 616), PackedInt32Array(616, 643, 642), PackedInt32Array(616, 642, 640), PackedInt32Array(616, 640, 615), PackedInt32Array(651, 650, 649), PackedInt32Array(653, 652, 654), PackedInt32Array(654, 652, 655), PackedInt32Array(655, 652, 656), PackedInt32Array(605, 657, 604), PackedInt32Array(604, 657, 658), PackedInt32Array(651, 649, 659), PackedInt32Array(659, 649, 662), PackedInt32Array(659, 662, 661), PackedInt32Array(659, 661, 660), PackedInt32Array(657, 661, 658), PackedInt32Array(658, 661, 662), PackedInt32Array(655, 651, 654), PackedInt32Array(654, 651, 659), PackedInt32Array(664, 663, 665), PackedInt32Array(665, 663, 666), PackedInt32Array(584, 585, 667), PackedInt32Array(667, 668, 584), PackedInt32Array(584, 668, 669), PackedInt32Array(666, 670, 665), PackedInt32Array(665, 670, 671), PackedInt32Array(671, 670, 673), PackedInt32Array(671, 673, 672), PackedInt32Array(672, 676, 671), PackedInt32Array(671, 676, 674), PackedInt32Array(674, 675, 668), PackedInt32Array(668, 675, 669), PackedInt32Array(669, 676, 672), PackedInt32Array(676, 675, 674), PackedInt32Array(675, 676, 669), PackedInt32Array(678, 677, 679), PackedInt32Array(679, 677, 680), PackedInt32Array(683, 682, 681), PackedInt32Array(679, 683, 684), PackedInt32Array(684, 683, 681), PackedInt32Array(684, 681, 628), PackedInt32Array(628, 681, 685), PackedInt32Array(686, 683, 680), PackedInt32Array(680, 683, 679), PackedInt32Array(628, 624, 684), PackedInt32Array(690, 689, 687), PackedInt32Array(687, 689, 688), PackedInt32Array(692, 691, 693), PackedInt32Array(693, 691, 695), PackedInt32Array(693, 695, 694), PackedInt32Array(699, 698, 696), PackedInt32Array(696, 698, 697), PackedInt32Array(701, 700, 702), PackedInt32Array(702, 700, 703), PackedInt32Array(703, 700, 704), PackedInt32Array(704, 705, 703), PackedInt32Array(706, 685, 707), PackedInt32Array(707, 685, 708), PackedInt32Array(708, 685, 681), PackedInt32Array(708, 710, 709), PackedInt32Array(708, 709, 707), PackedInt32Array(713, 712, 711), PackedInt32Array(716, 715, 714), PackedInt32Array(714, 718, 717), PackedInt32Array(720, 719, 716), PackedInt32Array(720, 716, 714), PackedInt32Array(714, 717, 642), PackedInt32Array(720, 714, 721), PackedInt32Array(721, 714, 642), PackedInt32Array(721, 642, 648), PackedInt32Array(721, 648, 722), PackedInt32Array(723, 725, 724), PackedInt32Array(553, 543, 726), PackedInt32Array(726, 543, 728), PackedInt32Array(726, 728, 727), PackedInt32Array(725, 723, 544), PackedInt32Array(544, 723, 728), PackedInt32Array(544, 728, 543), PackedInt32Array(730, 729, 731), PackedInt32Array(731, 729, 732), PackedInt32Array(732, 729, 733), PackedInt32Array(733, 729, 734), PackedInt32Array(733, 735, 732), PackedInt32Array(732, 735, 652), PackedInt32Array(652, 735, 656), PackedInt32Array(656, 735, 733), PackedInt32Array(738, 737, 736), PackedInt32Array(740, 739, 736), PackedInt32Array(736, 739, 738), PackedInt32Array(744, 743, 745), PackedInt32Array(745, 743, 741), PackedInt32Array(741, 743, 742), PackedInt32Array(747, 746, 748), PackedInt32Array(748, 746, 749), PackedInt32Array(749, 746, 750), PackedInt32Array(752, 751, 753), PackedInt32Array(753, 751, 750), PackedInt32Array(750, 751, 749), PackedInt32Array(756, 755, 754), PackedInt32Array(758, 757, 753), PackedInt32Array(750, 758, 753), PackedInt32Array(754, 759, 756), PackedInt32Array(756, 759, 760), PackedInt32Array(756, 760, 749), PackedInt32Array(756, 749, 751), PackedInt32Array(762, 761, 757), PackedInt32Array(757, 761, 753), PackedInt32Array(763, 710, 759), PackedInt32Array(759, 710, 760), PackedInt32Array(760, 710, 708), PackedInt32Array(764, 766, 765), PackedInt32Array(769, 768, 767), PackedInt32Array(770, 769, 767), PackedInt32Array(767, 771, 770), PackedInt32Array(770, 774, 764), PackedInt32Array(764, 727, 766), PackedInt32Array(727, 775, 771), PackedInt32Array(771, 773, 770), PackedInt32Array(774, 775, 764), PackedInt32Array(770, 772, 774), PackedInt32Array(764, 775, 727), PackedInt32Array(775, 773, 771), PackedInt32Array(773, 772, 770), PackedInt32Array(774, 772, 775), PackedInt32Array(775, 772, 773), PackedInt32Array(771, 726, 727), PackedInt32Array(764, 776, 770), PackedInt32Array(778, 777, 779), PackedInt32Array(779, 777, 780), PackedInt32Array(782, 781, 783), PackedInt32Array(783, 781, 786), PackedInt32Array(783, 786, 784), PackedInt32Array(784, 786, 785), PackedInt32Array(788, 787, 722), PackedInt32Array(722, 787, 789), PackedInt32Array(722, 789, 721), PackedInt32Array(787, 790, 789), PackedInt32Array(792, 791, 793), PackedInt32Array(793, 791, 794), PackedInt32Array(793, 794, 795), PackedInt32Array(795, 794, 666), PackedInt32Array(795, 666, 663), PackedInt32Array(522, 506, 796), PackedInt32Array(800, 799, 797), PackedInt32Array(797, 799, 798), PackedInt32Array(803, 802, 801), PackedInt32Array(806, 805, 804), PackedInt32Array(768, 807, 767), PackedInt32Array(767, 807, 808), PackedInt32Array(570, 571, 804), PackedInt32Array(804, 571, 808), PackedInt32Array(491, 492, 809), PackedInt32Array(809, 492, 811), PackedInt32Array(809, 811, 810), PackedInt32Array(806, 804, 812), PackedInt32Array(812, 804, 808), PackedInt32Array(812, 808, 803), PackedInt32Array(812, 803, 801), PackedInt32Array(808, 807, 803), PackedInt32Array(814, 810, 813), PackedInt32Array(813, 810, 811), PackedInt32Array(815, 813, 812), PackedInt32Array(812, 813, 806), PackedInt32Array(815, 814, 813), PackedInt32Array(819, 818, 816), PackedInt32Array(816, 818, 817), PackedInt32Array(822, 821, 820), PackedInt32Array(506, 496, 822), PackedInt32Array(823, 796, 824), PackedInt32Array(824, 796, 506), PackedInt32Array(824, 506, 822), PackedInt32Array(824, 822, 820), PackedInt32Array(826, 825, 827), PackedInt32Array(827, 825, 730), PackedInt32Array(827, 730, 828), PackedInt32Array(828, 730, 829), PackedInt32Array(831, 830, 832), PackedInt32Array(832, 830, 828), PackedInt32Array(828, 830, 827), PackedInt32Array(825, 729, 730), PackedInt32Array(834, 833, 835), PackedInt32Array(835, 833, 787), PackedInt32Array(836, 790, 787), PackedInt32Array(833, 837, 787), PackedInt32Array(787, 837, 836), PackedInt32Array(836, 837, 838), PackedInt32Array(841, 840, 839), PackedInt32Array(838, 843, 842), PackedInt32Array(845, 844, 843), PackedInt32Array(845, 843, 838), PackedInt32Array(847, 846, 845), PackedInt32Array(837, 849, 848), PackedInt32Array(848, 850, 837), PackedInt32Array(837, 850, 838), PackedInt32Array(838, 850, 845), PackedInt32Array(845, 850, 847), PackedInt32Array(847, 850, 851), PackedInt32Array(854, 853, 852), PackedInt32Array(855, 857, 856), PackedInt32Array(856, 857, 792), PackedInt32Array(792, 857, 791), PackedInt32Array(857, 855, 852), PackedInt32Array(852, 855, 854), PackedInt32Array(854, 855, 858), PackedInt32Array(776, 860, 859), PackedInt32Array(862, 861, 863), PackedInt32Array(863, 861, 770), PackedInt32Array(863, 770, 859), PackedInt32Array(859, 770, 776), PackedInt32Array(859, 864, 863), PackedInt32Array(866, 865, 867), PackedInt32Array(867, 865, 869), PackedInt32Array(867, 869, 868), PackedInt32Array(872, 871, 870), PackedInt32Array(872, 870, 862), PackedInt32Array(862, 870, 873), PackedInt32Array(862, 873, 801), PackedInt32Array(862, 801, 874), PackedInt32Array(801, 802, 874), PackedInt32Array(867, 868, 870), PackedInt32Array(870, 868, 873), PackedInt32Array(874, 861, 862), PackedInt32Array(876, 875, 877), PackedInt32Array(877, 875, 878), PackedInt32Array(878, 875, 751), PackedInt32Array(878, 879, 877), PackedInt32Array(875, 756, 751), PackedInt32Array(880, 882, 881), PackedInt32Array(880, 884, 883), PackedInt32Array(812, 801, 883), PackedInt32Array(883, 801, 882), PackedInt32Array(882, 801, 873), PackedInt32Array(882, 880, 883), PackedInt32Array(887, 886, 885), PackedInt32Array(884, 889, 888), PackedInt32Array(891, 890, 892), PackedInt32Array(892, 890, 894), PackedInt32Array(892, 894, 893), PackedInt32Array(893, 894, 810), PackedInt32Array(892, 893, 895), PackedInt32Array(895, 893, 883), PackedInt32Array(895, 883, 888), PackedInt32Array(888, 883, 884), PackedInt32Array(894, 809, 810), PackedInt32Array(888, 897, 895), PackedInt32Array(895, 897, 896), PackedInt32Array(899, 898, 900), PackedInt32Array(900, 898, 901), PackedInt32Array(903, 902, 904), PackedInt32Array(904, 902, 905), PackedInt32Array(908, 907, 906), PackedInt32Array(901, 909, 879), PackedInt32Array(879, 909, 908), PackedInt32Array(879, 908, 906), PackedInt32Array(910, 877, 911), PackedInt32Array(911, 877, 912), PackedInt32Array(912, 877, 906), PackedInt32Array(906, 877, 879), PackedInt32Array(914, 913, 912), PackedInt32Array(914, 912, 915), PackedInt32Array(915, 912, 902), PackedInt32Array(902, 912, 905), PackedInt32Array(905, 912, 906), PackedInt32Array(901, 898, 909), PackedInt32Array(915, 902, 916), PackedInt32Array(920, 919, 917), PackedInt32Array(917, 919, 918), PackedInt32Array(891, 921, 890), PackedInt32Array(890, 921, 922), PackedInt32Array(924, 923, 897), PackedInt32Array(897, 923, 896), PackedInt32Array(926, 925, 927), PackedInt32Array(927, 925, 928), PackedInt32Array(928, 925, 922), PackedInt32Array(922, 921, 929), PackedInt32Array(922, 929, 928), PackedInt32Array(824, 931, 930), PackedInt32Array(933, 932, 931), PackedInt32Array(931, 824, 933), PackedInt32Array(933, 824, 820), PackedInt32Array(923, 927, 896), PackedInt32Array(896, 927, 928), PackedInt32Array(933, 942, 934), PackedInt32Array(934, 938, 925), PackedInt32Array(925, 941, 922), PackedInt32Array(922, 941, 820), PackedInt32Array(820, 940, 933), PackedInt32Array(942, 938, 934), PackedInt32Array(933, 940, 942), PackedInt32Array(938, 937, 925), PackedInt32Array(925, 939, 941), PackedInt32Array(941, 940, 820), PackedInt32Array(942, 936, 938), PackedInt32Array(940, 935, 942), PackedInt32Array(937, 939, 925), PackedInt32Array(938, 941, 937), PackedInt32Array(939, 937, 941), PackedInt32Array(941, 938, 940), PackedInt32Array(936, 940, 938), PackedInt32Array(942, 935, 936), PackedInt32Array(940, 936, 935), PackedInt32Array(927, 943, 926), PackedInt32Array(831, 832, 944), PackedInt32Array(947, 946, 945), PackedInt32Array(944, 949, 948), PackedInt32Array(831, 944, 950), PackedInt32Array(950, 944, 948), PackedInt32Array(950, 948, 947), PackedInt32Array(950, 947, 945), PackedInt32Array(948, 951, 947), PackedInt32Array(954, 953, 952), PackedInt32Array(957, 956, 955), PackedInt32Array(959, 958, 955), PackedInt32Array(955, 958, 957), PackedInt32Array(962, 961, 960), PackedInt32Array(963, 851, 960), PackedInt32Array(960, 851, 962), PackedInt32Array(965, 964, 966), PackedInt32Array(966, 964, 967), PackedInt32Array(962, 851, 967), PackedInt32Array(967, 851, 966), PackedInt32Array(966, 851, 850), PackedInt32Array(966, 850, 968), PackedInt32Array(971, 970, 969), PackedInt32Array(969, 962, 971), PackedInt32Array(971, 962, 967), PackedInt32Array(972, 967, 973), PackedInt32Array(973, 967, 964), PackedInt32Array(976, 975, 974), PackedInt32Array(979, 978, 977), PackedInt32Array(854, 858, 974), PackedInt32Array(974, 858, 980), PackedInt32Array(974, 980, 976), PackedInt32Array(976, 980, 981), PackedInt32Array(981, 980, 977), PackedInt32Array(977, 980, 979), PackedInt32Array(864, 983, 982), PackedInt32Array(982, 984, 864), PackedInt32Array(864, 984, 985), PackedInt32Array(864, 985, 863), PackedInt32Array(984, 987, 986), PackedInt32Array(984, 986, 985), PackedInt32Array(990, 989, 988), PackedInt32Array(992, 991, 988), PackedInt32Array(988, 991, 990), PackedInt32Array(996, 995, 993), PackedInt32Array(993, 995, 994), PackedInt32Array(1000, 999, 997), PackedInt32Array(997, 999, 998), PackedInt32Array(1004, 1003, 1001), PackedInt32Array(1001, 1003, 1002), PackedInt32Array(1005, 865, 866), PackedInt32Array(1005, 866, 1006), PackedInt32Array(1007, 1005, 1006), PackedInt32Array(1010, 1013, 1011), PackedInt32Array(1011, 1013, 1006), PackedInt32Array(1006, 1014, 1007), PackedInt32Array(1007, 1014, 1008), PackedInt32Array(1008, 1014, 1009), PackedInt32Array(1009, 1012, 1010), PackedInt32Array(1010, 1012, 1013), PackedInt32Array(1013, 1014, 1006), PackedInt32Array(1014, 1012, 1009), PackedInt32Array(1012, 1014, 1013), PackedInt32Array(1009, 943, 1008), PackedInt32Array(1008, 943, 927), PackedInt32Array(1006, 1015, 1011), PackedInt32Array(870, 871, 1016), PackedInt32Array(1016, 871, 1019), PackedInt32Array(1016, 1019, 1017), PackedInt32Array(1017, 1019, 1018), PackedInt32Array(1017, 1020, 1016), PackedInt32Array(1022, 1021, 1020), PackedInt32Array(1020, 1021, 1011), PackedInt32Array(1015, 1016, 1011), PackedInt32Array(1011, 1016, 1020), PackedInt32Array(1024, 1023, 1025), PackedInt32Array(1025, 1023, 984), PackedInt32Array(984, 1026, 1025), PackedInt32Array(1018, 1027, 1028), PackedInt32Array(1028, 1027, 987), PackedInt32Array(1028, 987, 984), PackedInt32Array(1028, 984, 1029), PackedInt32Array(984, 1023, 1029), PackedInt32Array(1018, 1019, 1027), PackedInt32Array(1032, 1031, 1030), PackedInt32Array(1034, 1033, 1035), PackedInt32Array(1035, 1033, 946), PackedInt32Array(946, 1033, 1030), PackedInt32Array(1030, 1033, 1032), PackedInt32Array(1030, 945, 946), PackedInt32Array(946, 1036, 1035), PackedInt32Array(1037, 1040, 1038), PackedInt32Array(1038, 1040, 1039), PackedInt32Array(1041, 911, 1042), PackedInt32Array(1042, 911, 1043), PackedInt32Array(1043, 911, 912), PackedInt32Array(1039, 1040, 1043), PackedInt32Array(1043, 1040, 1045), PackedInt32Array(1043, 1045, 1044), PackedInt32Array(1044, 1046, 1043), PackedInt32Array(1043, 1046, 1042), PackedInt32Array(1042, 1046, 1047), PackedInt32Array(1050, 1049, 1048), PackedInt32Array(1050, 1048, 968), PackedInt32Array(968, 1048, 1051), PackedInt32Array(968, 1051, 966), PackedInt32Array(1052, 1051, 1053), PackedInt32Array(1053, 1051, 1048), PackedInt32Array(1056, 1055, 1054), PackedInt32Array(1058, 1057, 1059), PackedInt32Array(1059, 1057, 1060), PackedInt32Array(978, 1061, 977), PackedInt32Array(977, 1061, 1062), PackedInt32Array(1064, 1060, 1062), PackedInt32Array(1062, 1060, 1057), PackedInt32Array(1062, 1057, 1063), PackedInt32Array(1061, 1064, 1062), PackedInt32Array(1065, 934, 1066), PackedInt32Array(1066, 934, 1068), PackedInt32Array(1066, 1068, 1067), PackedInt32Array(934, 925, 1068), PackedInt32Array(1071, 1070, 1069), PackedInt32Array(1073, 1072, 1074), PackedInt32Array(1074, 1072, 1075), PackedInt32Array(1075, 1072, 1076), PackedInt32Array(1078, 1077, 1079), PackedInt32Array(1079, 1077, 1080), PackedInt32Array(1082, 1081, 1083), PackedInt32Array(1083, 1081, 1084), PackedInt32Array(1085, 1088, 1086), PackedInt32Array(1086, 1088, 1087), PackedInt32Array(1090, 1084, 1089), PackedInt32Array(1093, 1092, 1091), PackedInt32Array(1095, 1094, 1090), PackedInt32Array(1077, 1090, 1080), PackedInt32Array(1080, 1090, 1096), PackedInt32Array(1085, 1097, 1088), PackedInt32Array(1088, 1097, 1087), PackedInt32Array(1087, 1097, 1093), PackedInt32Array(1087, 1093, 1091), PackedInt32Array(1087, 1091, 1089), PackedInt32Array(1087, 1089, 1084), PackedInt32Array(1085, 1099, 1097), PackedInt32Array(1097, 1099, 1098), PackedInt32Array(1097, 1098, 1093), PackedInt32Array(1077, 1095, 1090), PackedInt32Array(1084, 1081, 1087), PackedInt32Array(1089, 1100, 1066), PackedInt32Array(1096, 1101, 1080), PackedInt32Array(1089, 1104, 1090), PackedInt32Array(1090, 1105, 1096), PackedInt32Array(1096, 1105, 1102), PackedInt32Array(1102, 1106, 1067), PackedInt32Array(1067, 1104, 1066), PackedInt32Array(1066, 1104, 1089), PackedInt32Array(1104, 1105, 1090), PackedInt32Array(1105, 1106, 1102), PackedInt32Array(1106, 1103, 1067), PackedInt32Array(1067, 1103, 1104), PackedInt32Array(1104, 1103, 1105), PackedInt32Array(1105, 1103, 1106), PackedInt32Array(1034, 1107, 1033), PackedInt32Array(1033, 1107, 1108), PackedInt32Array(1108, 1107, 1109), PackedInt32Array(1109, 1107, 1110), PackedInt32Array(1111, 1109, 1112), PackedInt32Array(1112, 1109, 1110), PackedInt32Array(1107, 1113, 1110), PackedInt32Array(1115, 1114, 1116), PackedInt32Array(1116, 1114, 1112), PackedInt32Array(1112, 1114, 1111), PackedInt32Array(1111, 1114, 1117), PackedInt32Array(1119, 1118, 1120), PackedInt32Array(1120, 1118, 1121), PackedInt32Array(1122, 1010, 1123), PackedInt32Array(1123, 1010, 1011), PackedInt32Array(1123, 1011, 1021), PackedInt32Array(1124, 1122, 1123), PackedInt32Array(1127, 1126, 1125), PackedInt32Array(1125, 1096, 1127), PackedInt32Array(1127, 1096, 1123), PackedInt32Array(1123, 1096, 1124), PackedInt32Array(1096, 1102, 1124), PackedInt32Array(1131, 1130, 1128), PackedInt32Array(1128, 1130, 1129), PackedInt32Array(1133, 1132, 1134), PackedInt32Array(1134, 1132, 1135), PackedInt32Array(1137, 1136, 1138), PackedInt32Array(1138, 1136, 1139), PackedInt32Array(1022, 1140, 1021), PackedInt32Array(1021, 1140, 1139), PackedInt32Array(1021, 1139, 1136), PackedInt32Array(1021, 1136, 1135), PackedInt32Array(1132, 1127, 1135), PackedInt32Array(1135, 1127, 1021), PackedInt32Array(1021, 1127, 1123), PackedInt32Array(1140, 1141, 1139), PackedInt32Array(1143, 1142, 1144), PackedInt32Array(1144, 1142, 1145), PackedInt32Array(1142, 1052, 1145), PackedInt32Array(1145, 1052, 1146), PackedInt32Array(1146, 1052, 1053), PackedInt32Array(1147, 1047, 1148), PackedInt32Array(1148, 1047, 1150), PackedInt32Array(1148, 1150, 1149), PackedInt32Array(1047, 1046, 1150), PackedInt32Array(1153, 1152, 1151), PackedInt32Array(1155, 1154, 1156), PackedInt32Array(1156, 1154, 1029), PackedInt32Array(1159, 1158, 1157), PackedInt32Array(1159, 1157, 1139), PackedInt32Array(1161, 1160, 1159), PackedInt32Array(1139, 1162, 1028), PackedInt32Array(1163, 1161, 1164), PackedInt32Array(1164, 1161, 1165), PackedInt32Array(1165, 1161, 1166), PackedInt32Array(1029, 1168, 1028), PackedInt32Array(1028, 1169, 1139), PackedInt32Array(1139, 1169, 1159), PackedInt32Array(1159, 1169, 1161), PackedInt32Array(1161, 1029, 1166), PackedInt32Array(1168, 1167, 1028), PackedInt32Array(1029, 1161, 1168), PackedInt32Array(1028, 1167, 1169), PackedInt32Array(1169, 1168, 1161), PackedInt32Array(1168, 1169, 1167), PackedInt32Array(1029, 1154, 1166), PackedInt32Array(1162, 1139, 1141), PackedInt32Array(1170, 1172, 1171), PackedInt32Array(1174, 1173, 1170), PackedInt32Array(1170, 1173, 1172), PackedInt32Array(1177, 1176, 1175), PackedInt32Array(1179, 1178, 1180), PackedInt32Array(1178, 1181, 1180), PackedInt32Array(1180, 1181, 1173), PackedInt32Array(1181, 1182, 1173), PackedInt32Array(1173, 1182, 1149), PackedInt32Array(1149, 1182, 1175), PackedInt32Array(1175, 1182, 1177), PackedInt32Array(1175, 1148, 1149), PackedInt32Array(1149, 1172, 1173), PackedInt32Array(1184, 1183, 1185), PackedInt32Array(1185, 1183, 1186), PackedInt32Array(1186, 1183, 1187), PackedInt32Array(1189, 1188, 1190), PackedInt32Array(1190, 1188, 1191), PackedInt32Array(1191, 1193, 1192), PackedInt32Array(1190, 1191, 1146), PackedInt32Array(1146, 1191, 1192), PackedInt32Array(1146, 1192, 1145), PackedInt32Array(1196, 1195, 1194), PackedInt32Array(1199, 1198, 1197), PackedInt32Array(1202, 1201, 1200), PackedInt32Array(1059, 1204, 1058), PackedInt32Array(1058, 1204, 1203), PackedInt32Array(1058, 1203, 1194), PackedInt32Array(1194, 1203, 1196), PackedInt32Array(1207, 1206, 1205), PackedInt32Array(1208, 1207, 1200), PackedInt32Array(1200, 1207, 1202), PackedInt32Array(1202, 1207, 1205), PackedInt32Array(1196, 1199, 1209), PackedInt32Array(1209, 1199, 1197), PackedInt32Array(1209, 1197, 1210), PackedInt32Array(1210, 1197, 1208), PackedInt32Array(1211, 1209, 1210), PackedInt32Array(1208, 1200, 1212), PackedInt32Array(1208, 1212, 1210), PackedInt32Array(1196, 1203, 1199), PackedInt32Array(1215, 1214, 1213), PackedInt32Array(1217, 1216, 1218), PackedInt32Array(1218, 1216, 1219), PackedInt32Array(1218, 1219, 1191), PackedInt32Array(1191, 1219, 1220), PackedInt32Array(1191, 1220, 1193), PackedInt32Array(1221, 1114, 1115), PackedInt32Array(1114, 1221, 1222), PackedInt32Array(1114, 1222, 1223), PackedInt32Array(1223, 1222, 1226), PackedInt32Array(1223, 1226, 1227), PackedInt32Array(1227, 1226, 1224), PackedInt32Array(1224, 1226, 1225), PackedInt32Array(1229, 1228, 1230), PackedInt32Array(1230, 1228, 1231), PackedInt32Array(1234, 1233, 1232), PackedInt32Array(1234, 1230, 1231), PackedInt32Array(1233, 1234, 1231), PackedInt32Array(1237, 1236, 1235), PackedInt32Array(1239, 1238, 1236), PackedInt32Array(1240, 1242, 1241), PackedInt32Array(1240, 1246, 1242), PackedInt32Array(1242, 1246, 1243), PackedInt32Array(1243, 1246, 1244), PackedInt32Array(1244, 1246, 1245), PackedInt32Array(1248, 1247, 1249), PackedInt32Array(1249, 1247, 1251), PackedInt32Array(1249, 1251, 1250), PackedInt32Array(1251, 1247, 1252), PackedInt32Array(1254, 1253, 1252), PackedInt32Array(1252, 1253, 1251), PackedInt32Array(1251, 1253, 1255), PackedInt32Array(1256, 1258, 1257), PackedInt32Array(1261, 1260, 1259), PackedInt32Array(1259, 1263, 1262), PackedInt32Array(1266, 1265, 1264), PackedInt32Array(1268, 1267, 1261), PackedInt32Array(1258, 1262, 1269), PackedInt32Array(1261, 1259, 1262), PackedInt32Array(1258, 1256, 1262), PackedInt32Array(1262, 1256, 1261), PackedInt32Array(1261, 1256, 1268), PackedInt32Array(1268, 1256, 1270), PackedInt32Array(1271, 1270, 1272), PackedInt32Array(1272, 1270, 1256), PackedInt32Array(1272, 1256, 1266), PackedInt32Array(1272, 1266, 1273), PackedInt32Array(1273, 1266, 1264), PackedInt32Array(1273, 1264, 1274), PackedInt32Array(1276, 1275, 1277), PackedInt32Array(1277, 1275, 1278), PackedInt32Array(1277, 1278, 1274), PackedInt32Array(1277, 1274, 1219), PackedInt32Array(1219, 1274, 1264), PackedInt32Array(1219, 1264, 1220), PackedInt32Array(1280, 1279, 1281), PackedInt32Array(1281, 1279, 1282), PackedInt32Array(1282, 1279, 1283), PackedInt32Array(1285, 1284, 1286), PackedInt32Array(1286, 1284, 1289), PackedInt32Array(1286, 1289, 1288), PackedInt32Array(1286, 1288, 1287), PackedInt32Array(1279, 1287, 1283), PackedInt32Array(1283, 1287, 1290), PackedInt32Array(1290, 1287, 1288), PackedInt32Array(1291, 1289, 1254), PackedInt32Array(1254, 1289, 1284), PackedInt32Array(1254, 1284, 1253), PackedInt32Array(1294, 1293, 1292), PackedInt32Array(1296, 1295, 1245), PackedInt32Array(1245, 1295, 1244), PackedInt32Array(1205, 1206, 1297), PackedInt32Array(1297, 1206, 1299), PackedInt32Array(1297, 1299, 1298), PackedInt32Array(1294, 1292, 1298), PackedInt32Array(1298, 1292, 1301), PackedInt32Array(1298, 1301, 1297), PackedInt32Array(1297, 1301, 1300), PackedInt32Array(1295, 1301, 1244), PackedInt32Array(1244, 1301, 1292), PackedInt32Array(1304, 1303, 1302), PackedInt32Array(1304, 1302, 1305), PackedInt32Array(1305, 1302, 1306), PackedInt32Array(1306, 1302, 1307), PackedInt32Array(1310, 1309, 1308), PackedInt32Array(1306, 1310, 1305), PackedInt32Array(1305, 1310, 1308), PackedInt32Array(1307, 1302, 1182), PackedInt32Array(1182, 1302, 1311), PackedInt32Array(1182, 1311, 1177), PackedInt32Array(1314, 1313, 1312), PackedInt32Array(1317, 1316, 1315), PackedInt32Array(1317, 1315, 1318), PackedInt32Array(1318, 1315, 1319), PackedInt32Array(1319, 1315, 1312), PackedInt32Array(1312, 1315, 1314), PackedInt32Array(1321, 1320, 1250), PackedInt32Array(1250, 1320, 1312), PackedInt32Array(1250, 1312, 1249), PackedInt32Array(1312, 1320, 1319), PackedInt32Array(1319, 1322, 1318), PackedInt32Array(1325, 1324, 1323), PackedInt32Array(1323, 1327, 1326), PackedInt32Array(1330, 1329, 1328), PackedInt32Array(1328, 1329, 1325), PackedInt32Array(1328, 1325, 1323), PackedInt32Array(1328, 1323, 1326), PackedInt32Array(1281, 1282, 1331), PackedInt32Array(1331, 1282, 1332), PackedInt32Array(1331, 1332, 1330), PackedInt32Array(1330, 1332, 1329), PackedInt32Array(1335, 1334, 1333), PackedInt32Array(1336, 1339, 1337), PackedInt32Array(1337, 1339, 1333), PackedInt32Array(1333, 1339, 1338), PackedInt32Array(1333, 1338, 1335), PackedInt32Array(1335, 1338, 1224), PackedInt32Array(1335, 1224, 1225), PackedInt32Array(1335, 1225, 1340), PackedInt32Array(1343, 1342, 1341), PackedInt32Array(1345, 1344, 1240), PackedInt32Array(1341, 1346, 1343), PackedInt32Array(1343, 1346, 1241), PackedInt32Array(1241, 1346, 1345), PackedInt32Array(1241, 1345, 1240), PackedInt32Array(1341, 1348, 1346), PackedInt32Array(1346, 1348, 1347), PackedInt32Array(1347, 1348, 1327), PackedInt32Array(1327, 1348, 1326), PackedInt32Array(1350, 1349, 1351), PackedInt32Array(1351, 1349, 1352), PackedInt32Array(1352, 1349, 1353), PackedInt32Array(1353, 1354, 1352), PackedInt32Array(1357, 1356, 1355), PackedInt32Array(1359, 1358, 1360), PackedInt32Array(1360, 1358, 1362), PackedInt32Array(1360, 1362, 1361), PackedInt32Array(1315, 1316, 1363), PackedInt32Array(1363, 1316, 1364), PackedInt32Array(1364, 1316, 1365), PackedInt32Array(1336, 1337, 1365), PackedInt32Array(1365, 1337, 1364), PackedInt32Array(1367, 1366, 1368), PackedInt32Array(1368, 1366, 1371), PackedInt32Array(1368, 1371, 1370), PackedInt32Array(1368, 1370, 1369), PackedInt32Array(1373, 1372, 1374), PackedInt32Array(1374, 1372, 1369), PackedInt32Array(1374, 1369, 1370), PackedInt32Array(1377, 1376, 1375), PackedInt32Array(1380, 1379, 1378), PackedInt32Array(1382, 1381, 1383), PackedInt32Array(1383, 1381, 1385), PackedInt32Array(1383, 1385, 1384), PackedInt32Array(1387, 1386, 1388), PackedInt32Array(1388, 1386, 1389), PackedInt32Array(1391, 1390, 1389), PackedInt32Array(1389, 1386, 1391), PackedInt32Array(1393, 1392, 1394), PackedInt32Array(1394, 1392, 1395), PackedInt32Array(1395, 1392, 1396), PackedInt32Array(1399, 1398, 1397), PackedInt32Array(1402, 1401, 1400), PackedInt32Array(1405, 1404, 1403), PackedInt32Array(1408, 1407, 1406), PackedInt32Array(1409, 1311, 1410), PackedInt32Array(1410, 1311, 1411), PackedInt32Array(1411, 1311, 1302), PackedInt32Array(1414, 1413, 1412), PackedInt32Array(1416, 1415, 1414), PackedInt32Array(1414, 1412, 1416), PackedInt32Array(1416, 1412, 1417), PackedInt32Array(1417, 1412, 1418), PackedInt32Array(1418, 1412, 1419), PackedInt32Array(1419, 1420, 1418), PackedInt32Array(1417, 1421, 1416), PackedInt32Array(1423, 1422, 1424), PackedInt32Array(1424, 1422, 1425), PackedInt32Array(1425, 1422, 1426), PackedInt32Array(1426, 1422, 1427), PackedInt32Array(1428, 1424, 1429), PackedInt32Array(1429, 1424, 1430), PackedInt32Array(1430, 1424, 1425), PackedInt32Array(1433, 1432, 1431), PackedInt32Array(1431, 1435, 1434), PackedInt32Array(1437, 1436, 1438), PackedInt32Array(1438, 1436, 1439), PackedInt32Array(1439, 1436, 1440), PackedInt32Array(1441, 1443, 1442), PackedInt32Array(1446, 1445, 1444), PackedInt32Array(1448, 1447, 1449), PackedInt32Array(1449, 1447, 1450), PackedInt32Array(1444, 1452, 1451), PackedInt32Array(1454, 1453, 1455), PackedInt32Array(1455, 1453, 1457), PackedInt32Array(1455, 1457, 1456), PackedInt32Array(1439, 1458, 1438), PackedInt32Array(1460, 1459, 1457), PackedInt32Array(1443, 1441, 1461), PackedInt32Array(1461, 1441, 1463), PackedInt32Array(1461, 1463, 1462), PackedInt32Array(1464, 1461, 1462), PackedInt32Array(1446, 1444, 1465), PackedInt32Array(1465, 1444, 1451), PackedInt32Array(1467, 1466, 1436), PackedInt32Array(1436, 1466, 1440), PackedInt32Array(1440, 1466, 1462), PackedInt32Array(1460, 1457, 1468), PackedInt32Array(1468, 1457, 1447), PackedInt32Array(1447, 1457, 1450), PackedInt32Array(1450, 1457, 1453), PackedInt32Array(1471, 1470, 1469), PackedInt32Array(1431, 1434, 1433), PackedInt32Array(1433, 1434, 1451), PackedInt32Array(1451, 1434, 1472), PackedInt32Array(1451, 1472, 1471), PackedInt32Array(1434, 1473, 1472), PackedInt32Array(1451, 1471, 1465), PackedInt32Array(1465, 1471, 1456), PackedInt32Array(1456, 1471, 1474), PackedInt32Array(1474, 1471, 1469), PackedInt32Array(1447, 1475, 1468), PackedInt32Array(1462, 1476, 1440), PackedInt32Array(1472, 1477, 1471), PackedInt32Array(1478, 1409, 1410), PackedInt32Array(1479, 1463, 1441), PackedInt32Array(1464, 1462, 1456), PackedInt32Array(1456, 1462, 1466), PackedInt32Array(1456, 1466, 1465), PackedInt32Array(1410, 1469, 1478), PackedInt32Array(1478, 1469, 1470), PackedInt32Array(1478, 1470, 1480), PackedInt32Array(1474, 1455, 1456), PackedInt32Array(1482, 1481, 1412), PackedInt32Array(1412, 1481, 1484), PackedInt32Array(1412, 1484, 1483), PackedInt32Array(1412, 1483, 1419), PackedInt32Array(1484, 1485, 1483), PackedInt32Array(1481, 1486, 1484), PackedInt32Array(1489, 1488, 1487), PackedInt32Array(1487, 1491, 1490), PackedInt32Array(1489, 1487, 1490), PackedInt32Array(1489, 1490, 1492), PackedInt32Array(1492, 1490, 1495), PackedInt32Array(1492, 1495, 1494), PackedInt32Array(1492, 1494, 1493), PackedInt32Array(1495, 1496, 1494), PackedInt32Array(1493, 1497, 1492), PackedInt32Array(1499, 1498, 1500), PackedInt32Array(1500, 1498, 1502), PackedInt32Array(1500, 1502, 1501), PackedInt32Array(1505, 1504, 1503), PackedInt32Array(1278, 1275, 1506), PackedInt32Array(1503, 1507, 1505), PackedInt32Array(1505, 1507, 1506), PackedInt32Array(1509, 1508, 1501), PackedInt32Array(1501, 1508, 1500), PackedInt32Array(1500, 1508, 1510), PackedInt32Array(1508, 1511, 1510), PackedInt32Array(1510, 1511, 1278), PackedInt32Array(1510, 1278, 1506), PackedInt32Array(1510, 1506, 1507), PackedInt32Array(1417, 1513, 1512), PackedInt32Array(1512, 1514, 1417), PackedInt32Array(1417, 1514, 1515), PackedInt32Array(1417, 1515, 1421), PackedInt32Array(1514, 1516, 1515), PackedInt32Array(1518, 1517, 1495), PackedInt32Array(1520, 1519, 1490), PackedInt32Array(1490, 1519, 1521), PackedInt32Array(1490, 1521, 1495), PackedInt32Array(1495, 1521, 1518), PackedInt32Array(1519, 1522, 1521), PackedInt32Array(1522, 1523, 1521), PackedInt32Array(1525, 1524, 1493), PackedInt32Array(1493, 1524, 1526), PackedInt32Array(1526, 1524, 1527), PackedInt32Array(1526, 1497, 1493), PackedInt32Array(1530, 1529, 1528), PackedInt32Array(1528, 1531, 1530), PackedInt32Array(1524, 1533, 1532), PackedInt32Array(1532, 1534, 1524), PackedInt32Array(1524, 1534, 1531), PackedInt32Array(1524, 1531, 1528), PackedInt32Array(1524, 1528, 1527), PackedInt32Array(1536, 1535, 1537), PackedInt32Array(1537, 1535, 1514), PackedInt32Array(1514, 1535, 1516), PackedInt32Array(1540, 1539, 1538), PackedInt32Array(1538, 1542, 1541), PackedInt32Array(1540, 1538, 1422), PackedInt32Array(1422, 1538, 1541), PackedInt32Array(1541, 1516, 1422), PackedInt32Array(1422, 1516, 1535), PackedInt32Array(1422, 1535, 1427), PackedInt32Array(1544, 1543, 1545), PackedInt32Array(1545, 1543, 1430), PackedInt32Array(1430, 1543, 1546), PackedInt32Array(1430, 1546, 1429), PackedInt32Array(1548, 1547, 1523), PackedInt32Array(1523, 1547, 1549), PackedInt32Array(1550, 1546, 1549), PackedInt32Array(1549, 1546, 1523), PackedInt32Array(1523, 1546, 1543), PackedInt32Array(1523, 1543, 1521), PackedInt32Array(1531, 1534, 1480), PackedInt32Array(1480, 1534, 1478), PackedInt32Array(1552, 1551, 1553), PackedInt32Array(1553, 1551, 1554), PackedInt32Array(1551, 1555, 1554), PackedInt32Array(1557, 1556, 1558), PackedInt32Array(1558, 1556, 1486), PackedInt32Array(1558, 1486, 1553), PackedInt32Array(1558, 1553, 1554), PackedInt32Array(1556, 1484, 1486), PackedInt32Array(1559, 1510, 1554), PackedInt32Array(1554, 1510, 1507), PackedInt32Array(1554, 1507, 1558), PackedInt32Array(1411, 1561, 1560), PackedInt32Array(1562, 1560, 1563), PackedInt32Array(1560, 1562, 1411), PackedInt32Array(1411, 1562, 1469), PackedInt32Array(1411, 1469, 1410), PackedInt32Array(1565, 1564, 1511), PackedInt32Array(1511, 1564, 1274), PackedInt32Array(1511, 1274, 1278), PackedInt32Array(1568, 1567, 1566), PackedInt32Array(1571, 1570, 1569), PackedInt32Array(1574, 1573, 1572), PackedInt32Array(1577, 1576, 1575), PackedInt32Array(1575, 1579, 1578), PackedInt32Array(1580, 1577, 1581), PackedInt32Array(1581, 1577, 1582), PackedInt32Array(1582, 1577, 1583), PackedInt32Array(1586, 1585, 1584), PackedInt32Array(1589, 1588, 1587), PackedInt32Array(1591, 1590, 1592), PackedInt32Array(1592, 1590, 1571), PackedInt32Array(1592, 1571, 1569), PackedInt32Array(1592, 1569, 1593), PackedInt32Array(1595, 1594, 1596), PackedInt32Array(1596, 1594, 1587), PackedInt32Array(1598, 1597, 1572), PackedInt32Array(1566, 1599, 1586), PackedInt32Array(1594, 1600, 1583), PackedInt32Array(1577, 1575, 1578), PackedInt32Array(1593, 1569, 1601), PackedInt32Array(1586, 1584, 1568), PackedInt32Array(1568, 1584, 1603), PackedInt32Array(1568, 1603, 1597), PackedInt32Array(1568, 1597, 1602), PackedInt32Array(1577, 1578, 1603), PackedInt32Array(1586, 1568, 1566), PackedInt32Array(1604, 1574, 1605), PackedInt32Array(1605, 1574, 1606), PackedInt32Array(1607, 1571, 1608), PackedInt32Array(1608, 1571, 1590), PackedInt32Array(1609, 1568, 1602), PackedInt32Array(1572, 1597, 1574), PackedInt32Array(1574, 1597, 1606), PackedInt32Array(1606, 1597, 1603), PackedInt32Array(1592, 1273, 1602), PackedInt32Array(1602, 1273, 1274), PackedInt32Array(1602, 1274, 1609), PackedInt32Array(1609, 1274, 1564), PackedInt32Array(1587, 1594, 1589), PackedInt32Array(1589, 1594, 1583), PackedInt32Array(1589, 1583, 1577), PackedInt32Array(1589, 1577, 1603), PackedInt32Array(1584, 1589, 1603), PackedInt32Array(1592, 1593, 1273), PackedInt32Array(1610, 1613, 1611), PackedInt32Array(1611, 1613, 1612), PackedInt32Array(1613, 1474, 1612), PackedInt32Array(1612, 1474, 1562), PackedInt32Array(1562, 1474, 1469)]
agent_height = 1.75
agent_radius = 0.375
agent_max_climb = 0.5
edge_max_length = 12.0
filter_low_hanging_obstacles = true
filter_ledge_spans = true
filter_walkable_low_height_spans = true

[sub_resource type="StandardMaterial3D" id="StandardMaterial3D_cin4e"]
albedo_texture = ExtResource("15_df8b0")
metallic = 0.2
metallic_texture = ExtResource("16_003du")
roughness = 0.8
uv1_scale = Vector3(10, 10, 1)

[sub_resource type="StandardMaterial3D" id="StandardMaterial3D_01ds2"]
transparency = 1
albedo_color = Color(1, 1, 0, 0.3)
emission_enabled = true
emission = Color(1, 1, 0, 1)

[node name="level01" type="Node3D"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 0, 0, 1.94383)
script = ExtResource("1_hk2b6")
minimap_scene = ExtResource("2_j32d5")
player_avatar_scene = ExtResource("3_0f42i")
item_boxes_scene = ExtResource("4_swvig")
skill_box_ui_scene = ExtResource("5_dbohx")

[node name="WorldEnvironment" type="WorldEnvironment" parent="."]
environment = SubResource("Environment_j4i7h")

[node name="DirectionalLight3D" type="DirectionalLight3D" parent="."]
transform = Transform3D(0.707107, -0.5, 0.5, 0, 0.707107, 0.707107, -0.707107, -0.5, 0.5, 0, 0, 0)
light_color = Color(1, 0.95, 0.8, 1)
light_energy = 1.2
shadow_enabled = true
shadow_opacity = 0.6
shadow_blur = 1.5

[node name="ModularLevelGenerator" type="Node3D" parent="."]
script = ExtResource("6_6ly8b")
level_config = ExtResource("7_r5102")
level_size = Vector2(200, 200)

[node name="FogOfWar" parent="." instance=ExtResource("9_003du")]
player_vision_radius = 8.0
enabled = false

[node name="Camera3D" type="Camera3D" parent="."]
unique_name_in_owner = true
transform = Transform3D(1, 0, 0, 0, 0.866025, 0.5, 0, -0.5, 0.866025, 0, 30, 50)
v_offset = 20.0
projection = 1
current = true
size = 44.805
near = 0.022
script = ExtResource("26_camera_follower")
target_path = NodePath("../Player")
follow_speed = 10.0
position_offset = Vector3(0, 10, 55)

[node name="UIManager" type="Node" parent="."]
script = ExtResource("25_ui_manager")
border_width = 100.0

[node name="Player" parent="." instance=ExtResource("11_82jtv")]

[node name="Chest" parent="." instance=ExtResource("14_1ks1q")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, 4.57862, 0, -14.4123)
interaction_distance = 3.0
item_resource = ExtResource("17_j32d5")

[node name="Chest2" parent="." instance=ExtResource("14_1ks1q")]
transform = Transform3D(1.5, 0, 0, 0, 1.5, 0, 0, 0, 1.5, 20, 0.165, -3.779)
interaction_distance = 3.0
item_resource = ExtResource("18_j32d5")

[node name="EnemyBoy01" parent="." instance=ExtResource("19_0f42i")]
transform = Transform3D(0.8, 0, 0, 0, 0.751754, 0.273616, 0, -0.273616, 0.751754, -10.0014, 0, -8.65824)

[node name="Enemy02" parent="." instance=ExtResource("21_dbohx")]
transform = Transform3D(2.1, 0, 0, 0, 1.97335, 0.718242, 0, -0.718242, 1.97335, 7.86917, 0, -7.768)

[node name="NavigationRegion3D" type="NavigationRegion3D" parent="."]
navigation_mesh = SubResource("NavigationMesh_xcdtp")

[node name="Floor" type="Node3D" parent="NavigationRegion3D"]

[node name="@CSGBox3D@165804" type="CSGBox3D" parent="NavigationRegion3D/Floor"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 0, -0.05, 0)
use_collision = true
size = Vector3(200, 0.1, 200)
material = SubResource("StandardMaterial3D_cin4e")

[node name="FloorTiles" type="Node3D" parent="NavigationRegion3D/Floor"]

[node name="Floor01_0" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -95.3055, 0.1, -76.8006)

[node name="Floor01_1" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 85.4917, 0.1, 14.7555)

[node name="Floor01_2" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -55.3689, 0.1, 43.0169)

[node name="Floor01_3" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -29.9655, 0.1, 82.8461)

[node name="Floor01_4" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 53.9677, 0.1, -33.8969)

[node name="Floor01_5" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 25.302, 0.1, 79.8135)

[node name="Floor01_6" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -47.0903, 0.1, 59.0197)

[node name="Floor01_7" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 71.4621, 0.1, 6.62059)

[node name="Floor01_8" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 82.8662, 0.1, 37.9691)

[node name="Floor01_9" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 20.9312, 0.1, 70.0348)

[node name="Floor01_10" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -0.543672, 0.1, 63.6683)

[node name="Floor01_11" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -36.763, 0.1, 2.85691)

[node name="Floor01_12" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -24.791, 0.1, 17.2301)

[node name="Floor01_13" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 79.5729, 0.1, -28.1531)

[node name="Floor01_14" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 88.8513, 0.1, 56.1511)

[node name="Floor01_15" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -63.7722, 0.1, -84.5371)

[node name="Floor01_16" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 18.6079, 0.1, 93.4057)

[node name="Floor01_17" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 58.8353, 0.1, 20.774)

[node name="Floor01_18" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -68.7693, 0.1, -68.966)

[node name="Floor01_19" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 29.6362, 0.1, 27.2938)

[node name="Floor01_20" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -84.6298, 0.1, -62.4577)

[node name="Floor01_21" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 77.6623, 0.1, -19.0671)

[node name="Floor01_22" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 85.1973, 0.1, -49.8745)

[node name="Floor01_23" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 17.7984, 0.1, 39.7916)

[node name="Floor01_24" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 58.2928, 0.1, 90.4545)

[node name="Floor01_25" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 12.5976, 0.1, -79.1859)

[node name="Floor01_26" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 0.238451, 0.1, -95.5055)

[node name="Floor01_27" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -80.1724, 0.1, 73.8931)

[node name="Floor01_28" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -41.4168, 0.1, -90.6292)

[node name="Floor01_29" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -67.9714, 0.1, 88.4166)

[node name="Floor01_30" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 17.0353, 0.1, 82.6328)

[node name="Floor01_31" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -31.6341, 0.1, 29.4456)

[node name="Floor01_32" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 67.3903, 0.1, 35.0736)

[node name="Floor01_33" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 54.9467, 0.1, -8.63559)

[node name="Floor01_34" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -60.7904, 0.1, -15.6628)

[node name="Floor01_35" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 60.9496, 0.1, 80.4784)

[node name="Floor01_36" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -55.0058, 0.1, 84.4005)

[node name="Floor01_37" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -15.173, 0.1, -68.0558)

[node name="Floor01_38" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -30.7534, 0.1, 67.1881)

[node name="Floor01_39" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 4.30004, 0.1, 84.9862)

[node name="Floor01_40" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -39.8142, 0.1, 77.9141)

[node name="Floor01_41" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 92.7449, 0.1, 37.8159)

[node name="Floor01_42" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -41.7589, 0.1, 11.9314)

[node name="Floor01_43" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 69.2781, 0.1, 78.2985)

[node name="Floor01_44" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 84.1285, 0.1, 69.3723)

[node name="Floor01_45" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 43.7432, 0.1, 22.9092)

[node name="Floor01_46" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -7.03318, 0.1, 40.8168)

[node name="Floor01_47" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -56.5293, 0.1, -77.5915)

[node name="Floor01_48" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -95.1777, 0.1, -86.3299)

[node name="Floor01_49" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 20.9359, 0.1, -12.5789)

[node name="Floor01_50" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 69.2766, 0.1, -72.632)

[node name="Floor01_51" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -59.1391, 0.1, 24.3637)

[node name="Floor01_52" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -72.7492, 0.1, -24.993)

[node name="Floor01_53" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -94.5478, 0.1, 84.5077)

[node name="Floor01_54" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -72.8273, 0.1, -49.6384)

[node name="Floor01_55" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -90.6561, 0.1, -56.9595)

[node name="Floor01_56" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -95.9003, 0.1, 70.1931)

[node name="Floor01_57" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 93.8457, 0.1, -95.7489)

[node name="Floor01_58" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 34.9794, 0.1, 59.5022)

[node name="Floor01_59" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 62.7569, 0.1, -5.07114)

[node name="Floor01_60" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -40.7546, 0.1, -35.7003)

[node name="Floor01_61" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -58.0616, 0.1, 10.042)

[node name="Floor01_62" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 41.2494, 0.1, -77.9816)

[node name="Floor01_63" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -73.8459, 0.1, 9.26433)

[node name="Floor01_64" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 87.0111, 0.1, -11.4076)

[node name="Floor01_65" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 39.299, 0.1, 36.2666)

[node name="Floor01_66" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 92.6884, 0.1, 27.8795)

[node name="Floor01_67" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -4.50658, 0.1, -7.70214)

[node name="Floor01_68" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -27.8907, 0.1, -74.7874)

[node name="Floor01_69" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -20.6479, 0.1, -1.40726)

[node name="Floor01_70" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -77.0519, 0.1, 26.155)

[node name="Floor01_71" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -43.8636, 0.1, 70.9094)

[node name="Floor01_72" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 41.9152, 0.1, 93.4986)

[node name="Floor01_73" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -86.0861, 0.1, 46.0501)

[node name="Floor01_74" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 31.0954, 0.1, -26.3515)

[node name="Floor01_75" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 8.21524, 0.1, -36.8316)

[node name="Floor01_76" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -82.778, 0.1, -48.293)

[node name="Floor01_77" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -62.9308, 0.1, -59.2607)

[node name="Floor01_78" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 29.3514, 0.1, -91.8712)

[node name="Floor01_79" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -14.3931, 0.1, 84.0366)

[node name="Floor01_80" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 42.8206, 0.1, -68.9517)

[node name="Floor01_81" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 26.9154, 0.1, -81.5212)

[node name="Floor01_82" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -27.8022, 0.1, -12.6239)

[node name="Floor01_83" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -47.5788, 0.1, -84.6161)

[node name="Floor01_84" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -69.5193, 0.1, 41.9425)

[node name="Floor01_85" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -54.732, 0.1, 93.862)

[node name="Floor01_86" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 63.4473, 0.1, -28.7938)

[node name="Floor01_87" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 10.4244, 0.1, 23.2599)

[node name="Floor01_88" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -77.7706, 0.1, 93.5744)

[node name="Floor01_89" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -4.89952, 0.1, -86.3108)

[node name="Floor01_90" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -29.0912, 0.1, -88.1803)

[node name="Floor01_91" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 61.2732, 0.1, -67.3688)

[node name="Floor01_92" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -18.0874, 0.1, -34.9309)

[node name="Floor01_93" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 8.13199, 0.1, 41.0894)

[node name="Floor01_94" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -84.6186, 0.1, 19.9371)

[node name="Floor01_95" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -35.1536, 0.1, -57.959)

[node name="Floor01_96" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 69.0223, 0.1, -63.6711)

[node name="Floor01_97" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 97.3032, 0.1, 17.4701)

[node name="Floor01_98" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 85.4978, 0.1, 2.90091)

[node name="Floor01_99" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 42.4526, 0.1, -12.0449)

[node name="Floor01_100" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -75.4107, 0.1, 84.1317)

[node name="Floor01_101" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -95.9237, 0.1, 95.9807)

[node name="Floor01_102" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -76.9952, 0.1, -3.1651)

[node name="Floor01_103" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -35.6245, 0.1, 60.354)

[node name="Floor01_104" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 95.1506, 0.1, -18.2621)

[node name="Floor01_105" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -84.5129, 0.1, -17.7547)

[node name="Floor01_106" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -62.975, 0.1, 56.2758)

[node name="Floor01_107" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -62.047, 0.1, -93.8121)

[node name="Floor01_108" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -11.354, 0.1, 73.2939)

[node name="Floor01_109" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -94.7957, 0.1, -94.8229)

[node name="Floor01_110" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 70.9934, 0.1, -4.29128)

[node name="Floor01_111" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -83.2459, 0.1, 59.8517)

[node name="Floor01_112" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -21.3464, 0.1, 36.3242)

[node name="Floor01_113" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -57.7334, 0.1, -1.60807)

[node name="Floor01_114" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 52.9965, 0.1, -90.5257)

[node name="Floor01_115" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -23.5537, 0.1, -24.5953)

[node name="Floor01_116" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -27.825, 0.1, 41.6939)

[node name="Floor01_117" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 83.049, 0.1, -96.2771)

[node name="Floor01_118" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -97.745, 0.1, 19.4497)

[node name="Floor01_119" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -57.6015, 0.1, -48.4123)

[node name="Floor01_120" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 19.3563, 0.1, -1.00986)

[node name="Floor01_121" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 74.9519, 0.1, 15.6126)

[node name="Floor01_122" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 44.2936, 0.1, -39.3245)

[node name="Floor01_123" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 68.1284, 0.1, 69.6892)

[node name="Floor01_124" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -90.7567, 0.1, -33.544)

[node name="Floor01_125" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -44.7068, 0.1, 88.4503)

[node name="Floor01_126" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 4.49183, 0.1, -77.0998)

[node name="Floor01_127" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 50.1532, 0.1, 83.7961)

[node name="Floor01_128" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 56.1367, 0.1, -51.574)

[node name="Floor01_129" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -37.7783, 0.1, 21.9911)

[node name="Floor01_130" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 0.435667, 0.1, -21.1555)

[node name="Floor01_131" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 37.1141, 0.1, 1.23061)

[node name="Floor01_132" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 4.84781, 0.1, 10.0276)

[node name="Floor01_133" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 6.73551, 0.1, -86.1753)

[node name="Floor01_134" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 55.3165, 0.1, 71.5906)

[node name="Floor01_135" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 82.6769, 0.1, 85.4157)

[node name="Floor01_136" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 85.0903, 0.1, -82.5255)

[node name="Floor01_137" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 6.72663, 0.1, -61.279)

[node name="Floor01_138" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -43.8032, 0.1, -14.3433)

[node name="Floor01_139" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 82.178, 0.1, -70.8367)

[node name="Floor01_140" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -43.3012, 0.1, 34.3386)

[node name="Floor01_141" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 67.7138, 0.1, 56.8556)

[node name="Floor01_142" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 95.4761, 0.1, -3.17274)

[node name="Floor01_143" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 47.9262, 0.1, -62.4884)

[node name="Floor01_144" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -90.6632, 0.1, 30.8672)

[node name="Floor01_145" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 72.92, 0.1, 25.8837)

[node name="Floor01_146" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 91.4809, 0.1, 63.7811)

[node name="Floor01_147" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -22.8492, 0.1, -44.4564)

[node name="Floor01_148" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -72.522, 0.1, -83.6886)

[node name="Floor01_149" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 90.7565, 0.1, -63.9881)

[node name="Floor01_150" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -84.7781, 0.1, 5.82389)

[node name="Floor01_151" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -38.9486, 0.1, -23.2685)

[node name="Floor01_152" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 16.8658, 0.1, -56.8022)

[node name="Floor01_153" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 90.7054, 0.1, 93.9725)

[node name="Floor01_154" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -22.2962, 0.1, -61.6196)

[node name="Floor01_155" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 36.7395, 0.1, 69.8352)

[node name="Floor01_156" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 15.0742, 0.1, 58.1358)

[node name="Floor01_157" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 57.1851, 0.1, 41.3359)

[node name="Floor01_158" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -5.86446, 0.1, 96.6698)

[node name="Floor01_159" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 37.6918, 0.1, 13.2212)

[node name="Floor01_160" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 24.5275, 0.1, -42.7354)

[node name="Floor01_161" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -14.2702, 0.1, -97.2465)

[node name="Floor01_162" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -17.2154, 0.1, 55.9029)

[node name="Floor01_163" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -61.7993, 0.1, -41.3145)

[node name="Floor01_164" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 58.4612, 0.1, 12.7204)

[node name="Floor01_165" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 42.9276, 0.1, -54.9197)

[node name="Floor01_166" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -93.8434, 0.1, -64.8834)

[node name="Floor01_167" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 0.0435784, 0.1, -35.664)

[node name="Floor01_168" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -46.1767, 0.1, 97.7747)

[node name="Floor01_169" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 81.5091, 0.1, -57.1938)

[node name="Floor01_170" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -43.4167, 0.1, -70.3014)

[node name="Floor01_171" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 27.116, 0.1, 90.4716)

[node name="Floor01_172" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 96.4782, 0.1, -86.0118)

[node name="Floor01_173" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 93.7648, 0.1, 8.13774)

[node name="Floor01_174" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 25.5967, 0.1, 5.6849)

[node name="Floor01_175" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 57.6823, 0.1, 1.64692)

[node name="Floor01_176" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -82.6774, 0.1, -31.7944)

[node name="Floor01_177" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -97.1051, 0.1, 10.9214)

[node name="Floor01_178" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -67.3993, 0.1, 77.689)

[node name="Floor01_179" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -85.9295, 0.1, -87.807)

[node name="Floor01_180" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -14.1984, 0.1, -11.7791)

[node name="Floor01_181" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 27.1359, 0.1, -71.0571)

[node name="Floor01_182" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 63.3134, 0.1, -79.695)

[node name="Floor01_183" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 87.7464, 0.1, -30.244)

[node name="Floor01_184" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -93.7057, 0.1, -24.2603)

[node name="Floor01_185" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 69.0687, 0.1, -43.6733)

[node name="Floor01_186" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 69.8412, 0.1, 45.3902)

[node name="Floor01_187" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -97.0313, 0.1, 55.7754)

[node name="Floor01_188" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -1.31237, 0.1, -54.3887)

[node name="Floor01_189" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -4.38057, 0.1, 17.1301)

[node name="Floor01_190" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 60.1936, 0.1, 49.3711)

[node name="Floor01_191" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 67.0651, 0.1, -94.5939)

[node name="Floor01_192" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 8.80603, 0.1, 77.9131)

[node name="Floor01_193" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 93.0888, 0.1, 71.9205)

[node name="Floor01_194" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -93.0635, 0.1, -44.8166)

[node name="Floor01_195" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 16.758, 0.1, -65.9514)

[node name="Floor01_196" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 50.6204, 0.1, -76.8634)

[node name="Floor01_197" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -52.3065, 0.1, -92.4324)

[node name="Floor01_198" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -25.535, 0.1, 93.6395)

[node name="Floor01_199" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 3.80086, 0.1, 55.3472)

[node name="Floor01_200" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 28.7608, 0.1, 38.8217)

[node name="Floor01_201" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -19.3909, 0.1, 72.192)

[node name="Floor01_202" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 75.8723, 0.1, 97.7216)

[node name="Floor01_203" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -55.1482, 0.1, -31.7646)

[node name="Floor01_204" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -5.27451, 0.1, 56.2033)

[node name="Floor01_205" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -74.6076, 0.1, 49.5754)

[node name="Floor01_206" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 82.4368, 0.1, 48.8099)

[node name="Floor01_207" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 4.57363, 0.1, -11.9981)

[node name="Floor01_208" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -72.846, 0.1, -33.4558)

[node name="Floor01_209" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 67.566, 0.1, -13.2367)

[node name="Floor01_210" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 47.3236, 0.1, 74.8402)

[node name="Floor01_211" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 10.6726, 0.1, -19.7103)

[node name="Floor01_212" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -4.37887, 0.1, -43.8487)

[node name="Floor01_213" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 35.4203, 0.1, -84.9855)

[node name="Floor01_214" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -52.7103, 0.1, -60.6564)

[node name="Floor01_215" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 73.3292, 0.1, -88.6064)

[node name="Floor01_216" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 73.924, 0.1, -50.9511)

[node name="Floor01_217" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -33.6037, 0.1, -67.1274)

[node name="Floor01_218" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 95.7918, 0.1, -48.2526)

[node name="Floor01_219" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -14.6282, 0.1, -57.7572)

[node name="Floor01_220" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -18.5953, 0.1, 24.8819)

[node name="Floor01_221" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 78.9278, 0.1, -9.25572)

[node name="Floor01_222" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 37.659, 0.1, -97.6885)

[node name="Floor01_223" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 20.9034, 0.1, -90.9064)

[node name="Floor01_224" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 85.1973, 0.1, 77.6244)

[node name="Floor01_225" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 9.91069, 0.1, 94.7718)

[node name="Floor01_226" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -31.3513, 0.1, -43.3899)

[node name="Floor01_227" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -82.3291, 0.1, -95.396)

[node name="Floor01_228" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 73.0986, 0.1, 88.524)

[node name="Floor01_229" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 97.8867, 0.1, 55.4297)

[node name="Floor01_230" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 54.24, 0.1, 58.0518)

[node name="Floor01_231" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 7.06136, 0.1, 2.24051)

[node name="Floor01_232" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 96.2405, 0.1, -35.1641)

[node name="Floor01_233" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -52.4684, 0.1, 66.486)

[node name="Floor01_234" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -92.8832, 0.1, -11.9811)

[node name="Floor01_235" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 33.6643, 0.1, -63.7794)

[node name="Floor01_236" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -37.3773, 0.1, 94.8496)

[node name="Floor01_237" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -65.5495, 0.1, -3.62692)

[node name="Floor01_238" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 15.4248, 0.1, -42.4582)

[node name="Floor01_239" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -86.141, 0.1, -5.22827)

[node name="Floor01_240" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -31.1786, 0.1, -97.2654)

[node name="Floor01_241" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 97.2253, 0.1, -74.1691)

[node name="Floor01_242" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -15.5813, 0.1, -84.9424)

[node name="Floor01_243" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 36.2574, 0.1, -34.5141)

[node name="Floor01_244" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 19.3268, 0.1, 21.7263)

[node name="Floor01_245" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 34.8712, 0.1, -16.8357)

[node name="Floor01_246" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 50.2062, 0.1, 92.0929)

[node name="Floor01_247" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -13.8993, 0.1, 64.9991)

[node name="Floor01_248" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -87.3922, 0.1, 69.4651)

[node name="Floor01_249" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 71.5866, 0.1, -30.2153)

[node name="Floor01_250" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -35.043, 0.1, -5.89582)

[node name="Floor01_251" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -10.566, 0.1, 10.0211)

[node name="Floor01_252" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 45.6909, 0.1, 3.50994)

[node name="Floor01_253" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 97.9142, 0.1, -27.116)

[node name="Floor01_254" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 15.4894, 0.1, 7.5146)

[node name="Floor01_255" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -3.23217, 0.1, 31.622)

[node name="Floor01_256" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -5.75705, 0.1, 83.4629)

[node name="Floor01_257" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 94.8187, 0.1, 83.5208)

[node name="Floor01_258" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -92.9992, 0.1, 2.95825)

[node name="Floor01_259" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -26.4495, 0.1, 5.38834)

[node name="Floor01_260" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -8.62117, 0.1, -30.0447)

[node name="Floor01_261" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -76.0295, 0.1, -63.905)

[node name="Floor01_262" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 40.517, 0.1, 81.0317)

[node name="Floor01_263" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 20.498, 0.1, -26.7937)

[node name="Floor01_264" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 29.6843, 0.1, -55.0572)

[node name="Floor01_265" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 27.2358, 0.1, -5.89358)

[node name="Floor01_266" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -60.0277, 0.1, 34.273)

[node name="Floor01_267" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 90.5209, 0.1, 48.2514)

[node name="Floor01_268" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 91.3313, 0.1, -55.5345)

[node name="Floor01_269" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -80.7812, 0.1, -74.1422)

[node name="Floor01_270" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 26.5888, 0.1, 63.2243)

[node name="Floor01_271" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -70.6575, 0.1, -96.4245)

[node name="Floor01_272" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 97.8548, 0.1, 44.9484)

[node name="Floor01_273" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 44.0917, 0.1, 54.5)

[node name="Floor01_274" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -74.0748, 0.1, -12.0612)

[node name="Floor01_275" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 61.7165, 0.1, -19.7411)

[node name="Floor01_276" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 8.99376, 0.1, -27.929)

[node name="wall_0" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1.33982, 0, -0.19, 0, 1, 0, 0.213985, 0, 1.18964, -47.5, 0, -50)

[node name="wall_1" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1.05102, 0, 0.122861, 0, 1, 0, -0.151383, 0, 0.852994, -42.5, 0, -50)

[node name="wall_2" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(0.865515, 0, 0.0533571, 0, 1, 0, -0.0512321, 0, 0.901415, -35, 0, -50)

[node name="wall_3" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(0.899318, 0, 0.179252, 0, 1, 0, -0.139622, 0, 1.15458, -27.5, 0, -50)

[node name="wall_4" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(0.870268, 0, -0.145145, 0, 1, 0, 0.123402, 0, 1.0236, -20, 0, -50)

[node name="wall_5" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1.1771, 0, -0.0986067, 0, 1, 0, 0.11222, 0, 1.03431, -12.5, 0, -50)

[node name="wall_6" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(0.932236, 0.00487882, -0.185064, 0, 0.999671, 0.0249899, 0.180521, -0.0251949, 0.955698, -5, 0.461959, -50)

[node name="wall_7" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(0.861733, 0, 0.035422, 0, 1, 0, -0.0296819, 0, 1.02838, 2.5, 0, -50)

[node name="wall_8" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(0.974997, 0, 0.0181762, 0, 1, 0, -0.017533, 0, 1.01077, 10, 0, -50)

[node name="wall_9" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(0.899853, 0, 0.146831, 0, 1, 0, -0.119591, 0, 1.10482, 17.5, 0, -50)

[node name="wall_10" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(0.861933, 0, 0.191569, 0, 1, 0, -0.144654, 0, 1.14148, 25, 0, -50)

[node name="wall_11" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1.05531, 0, -0.087067, 0, 1, 0, 0.107078, 0, 0.858084, 32.5, 0, -50)

[node name="wall_12" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(0.827808, 0, -0.0234505, 0, 1, 0, 0.0187228, 0, 1.03684, 40, 0, -50)

[node name="wall_13" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(0.982033, 0, 0.215742, 0, 1, 0, -0.257195, 0, 0.823754, 47.5, 0, -50)

[node name="wall_14" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1.12745, 0, 0.26322, 0, 1, 0, -0.339162, 0, 0.875001, -47.5, 0, 50)

[node name="wall_15" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1.2191, 0, 0.23652, 0, 1, 0, -0.219088, 0, 1.3161, -42.5, 0, 50)

[node name="wall_16" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1.1419, 0, 0.39329, 0, 1, 0, -0.389603, 0, 1.1527, -35, 0, 50)

[node name="wall_17" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(0.918457, -0.0138927, 0.15266, 0, 0.996803, 0.070369, -0.162176, -0.078679, 0.864566, -27.5, 0.482901, 50)

[node name="wall_18" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1.10152, 0, 0.104052, 0, 1, 0, -0.117602, 0, 0.974605, -20, 0, 50)

[node name="wall_19" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1.03948, 0, 0.168382, 0, 1, 0, -0.152358, 0, 1.1488, -12.5, 0, 50)

[node name="wall_20" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1.14437, 0, 0.212816, 0, 1, 0, -0.271387, 0, 0.897389, -5, 0, 50)

[node name="wall_21" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1.25894, 0, 0.307647, 0, 1, 0, -0.306434, 0, 1.26392, 2.5, 0, 50)

[node name="wall_22" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1.28078, 0, 0.357646, 0, 1, 0, -0.39365, 0, 1.16363, 10, 0, 50)

[node name="wall_23" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(0.97507, 0, 0.24095, 0, 1, 0, -0.235632, 0, 0.997076, 17.5, 0, 50)

[node name="wall_24" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1.01159, 0, 0.271816, 0, 1, 0, -0.250668, 0, 1.09694, 25, 0, 50)

[node name="wall_25" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(0.898198, 0, 0.0171293, 0, 1, 0, -0.0173538, 0, 0.886577, 32.5, 0, 50)

[node name="wall_26" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(0.974111, 0, -0.0153864, 0, 1, 0, 0.016203, 0, 0.925018, 40, 0, 50)

[node name="wall_27" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1.08661, 0.0169536, 0.228121, 0, 0.996877, -0.0841693, -0.238857, 0.0771257, 1.03777, 47.5, 0.36171, 50)

[node name="wall_28" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(0.000695107, 0, 1.15207, 0, 1, 0, -0.897538, 0, 0.00089223, -50, 0, -45)

[node name="wall_29" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-0.281443, 0, 1.22401, 0, 1, 0, -1.31123, 0, -0.262721, -50, 0, -37.5)

[node name="wall_30" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-0.0977057, -0.0287125, 0.883016, 0, 0.999584, 0.0255734, -1.07573, 0.00260788, -0.0802017, -50, 0.465073, -30)

[node name="wall_31" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(0.409613, 0, 1.29951, 0, 1, 0, -1.30999, 0, 0.406333, -50, 0, -22.5)

[node name="wall_32" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-0.220334, 0, 1.29, 0, 1, 0, -1.20187, 0, -0.23649, -50, 0, -15)

[node name="wall_33" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(0.270334, 0.017048, 1.00803, 0, 0.999844, -0.0184967, -0.979503, 0.00470509, 0.278206, -50, 0.374558, -7.5)

[node name="wall_34" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-0.246503, 0, 0.802651, 0, 1, 0, -1.02897, 0, -0.192286, -50, 0, 0)

[node name="wall_35" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-0.108942, 0, 0.987502, 0, 1, 0, -0.79542, 0, -0.13525, -50, 0, 7.5)

[node name="wall_36" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-0.224779, 0, 1.18786, 0, 1, 0, -1.23465, 0, -0.21626, -50, 0, 15)

[node name="wall_37" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(0.247465, 0, 0.808973, 0, 1, 0, -0.869417, 0, 0.23026, -50, 0, 22.5)

[node name="wall_38" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-0.0054466, 0, 1.09231, 0, 1, 0, -1.05072, 0, -0.00566216, -50, 0, 30)

[node name="wall_39" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(0.242799, 0, 0.870082, 0, 1, 0, -0.818273, 0, 0.258172, -50, 0, 37.5)

[node name="wall_40" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(0.252428, 0, 1.11083, 0, 1, 0, -1.07879, 0, 0.259927, -50, 0, 45)

[node name="wall_41" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(0.303857, 0, 1.34021, 0, 1, 0, -1.22885, 0, 0.331394, 50, 0, -45)

[node name="wall_42" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-0.273726, 0, 1.13978, 0, 1, 0, -1.13459, 0, -0.274978, 50, 0, -37.5)

[node name="wall_43" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(0.218879, 0, 0.800283, 0, 1, 0, -0.852998, 0, 0.205352, 50, 0, -30)

[node name="wall_44" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(0.424386, 0, 1.30891, 0, 1, 0, -1.29319, 0, 0.429545, 50, 0, -22.5)

[node name="wall_45" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(0.222895, 0, 1.1565, 0, 1, 0, -1.09253, 0, 0.235945, 50, 0, -15)

[node name="wall_46" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-0.286795, 0.0532886, 1.07289, 0, 0.998475, -0.0614417, -1.06126, -0.0144007, -0.289938, 50, 0.393175, -7.5)

[node name="wall_47" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-0.218849, 0, 1.01743, 0, 1, 0, -0.933701, 0, -0.238475, 50, 0, 0)

[node name="wall_48" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(0.19278, 0, 1.01659, 0, 1, 0, -0.804061, 0, 0.243735, 50, 0, 7.5)

[node name="wall_49" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-0.0456282, 0, 1.02871, 0, 1, 0, -1.10253, 0, -0.0425731, 50, 0, 15)

[node name="wall_50" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-0.319903, 0, 1.0334, 0, 1, 0, -0.951383, 0, -0.347481, 50, 0, 22.5)

[node name="wall_51" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(0.0182967, 0, 1.18568, 0, 1, 0, -0.812998, 0, 0.026684, 50, 0, 30)

[node name="wall_52" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-0.289662, 0, 1.29636, 0, 1, 0, -1.27509, 0, -0.294495, 50, 0, 37.5)

[node name="wall_53" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-0.202131, 0, 1.1263, 0, 1, 0, -1.03444, 0, -0.220081, 50, 0, 45)

[node name="tree_0" type="Node3D" parent="NavigationRegion3D" instance=ExtResource("15_003du")]
transform = Transform3D(2.5, -0.00145593, -0.00167976, 0.00145725, 2.5, 0.00196474, 0.00167861, -0.00196571, 2.5, 9.46675, 0, 12.4572)
script = ExtResource("18_oh2bt")
highlight_material = SubResource("StandardMaterial3D_01ds2")
progress_bar_scene = ExtResource("19_fcnrs")

[node name="PatrolPoint_0" type="Node3D" parent="NavigationRegion3D"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 13.1387, 1, 14.9812)
script = ExtResource("15_6ly8b")

[node name="tree_1" type="Node3D" parent="NavigationRegion3D" instance=ExtResource("15_003du")]
transform = Transform3D(2.5, -0.00145593, -0.00167976, 0.00145725, 2.5, 0.00196474, 0.00167861, -0.00196571, 2.5, 15.3192, 0, -9.09476)
script = ExtResource("18_oh2bt")
highlight_material = SubResource("StandardMaterial3D_01ds2")
preset_burnt_out_type = 1
progress_bar_scene = ExtResource("19_fcnrs")

[node name="PatrolPoint_1" type="Node3D" parent="NavigationRegion3D"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 18.5202, 1, -2.35433)
script = ExtResource("15_6ly8b")
point_id = 1

[node name="tree_2" type="Node3D" parent="NavigationRegion3D" instance=ExtResource("15_003du")]
transform = Transform3D(2.5, -0.00145593, -0.00167976, 0.00145725, 2.5, 0.00196474, 0.00167861, -0.00196571, 2.5, -3.48938, 0, 2.18476)
script = ExtResource("18_oh2bt")
highlight_material = SubResource("StandardMaterial3D_01ds2")
progress_bar_scene = ExtResource("19_fcnrs")

[node name="PatrolPoint_2" type="Node3D" parent="NavigationRegion3D"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -0.693455, 1, 6.22343)
script = ExtResource("15_6ly8b")
point_id = 2

[node name="tree_3" type="Node3D" parent="NavigationRegion3D" instance=ExtResource("15_003du")]
transform = Transform3D(2.5, -0.00145593, -0.00167976, 0.00145725, 2.5, 0.00196474, 0.00167861, -0.00196571, 2.5, 4.7154, 0, -5.42467)
script = ExtResource("18_oh2bt")
highlight_material = SubResource("StandardMaterial3D_01ds2")
progress_bar_scene = ExtResource("19_fcnrs")

[node name="PatrolPoint_3" type="Node3D" parent="NavigationRegion3D"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 7.16357, 1, 0.71277)
script = ExtResource("15_6ly8b")
point_id = 3

[node name="chest_0" type="Area3D" parent="NavigationRegion3D" instance=ExtResource("14_1ks1q")]
transform = Transform3D(-1.99328, 0, -0.163775, 0, 2, 0, 0.163775, 0, -1.99328, -2.38278, 0, 24.6309)
collision_layer = 8
collision_mask = 16
script = ExtResource("21_1ks1q")
interaction_distance = 1.5
item_resource = ExtResource("22_xcdtp")
unlock_sound = ExtResource("23_cin4e")

[node name="rock_0" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("21_xcdtp")]
transform = Transform3D(0.718678, 0, -0.517125, 0, 0.885391, 0, 0.517125, 0, 0.718678, 27.6237, 0.01, -21.8463)
collision_layer = 2
collision_mask = 0

[node name="rock_1" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("21_xcdtp")]
transform = Transform3D(0.708936, 0, -0.75094, 0, 1.03272, 0, 0.75094, 0, 0.708936, 24.3636, 0.01, -34.9074)
collision_layer = 2
collision_mask = 0

[node name="rock_2" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("21_xcdtp")]
transform = Transform3D(-0.849402, 0, -0.666022, 0, 1.07938, 0, 0.666022, 0, -0.849402, 3.768, 0.01, 22.4222)
collision_layer = 2
collision_mask = 0

[node name="rock_3" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("21_xcdtp")]
transform = Transform3D(0.234926, 0, 0.905545, 0, 0.935522, 0, -0.905545, 0, 0.234926, 23.9407, 0.01, 34.7168)
collision_layer = 2
collision_mask = 0

[node name="rock_4" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("21_xcdtp")]
transform = Transform3D(-0.454022, 0, -0.940477, 0, 1.04433, 0, 0.940477, 0, -0.454022, -12.3828, 0.01, -18.1744)
collision_layer = 2
collision_mask = 0

[node name="rock_5" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("21_xcdtp")]
transform = Transform3D(-0.434318, 0, 0.93374, 0, 1.02981, 0, -0.93374, 0, -0.434318, -37.9314, 0.01, 15.5889)
collision_layer = 2
collision_mask = 0

[node name="rock_6" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("21_xcdtp")]
transform = Transform3D(-0.65736, 0, 0.805438, 0, 1.03964, 0, -0.805438, 0, -0.65736, -18.0943, 0.01, 17.5066)
collision_layer = 2
collision_mask = 0

[node name="rock_7" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("21_xcdtp")]
transform = Transform3D(0.929307, 0, 0.508368, 0, 1.05927, 0, -0.508368, 0, 0.929307, -32.0918, 0.01, 14.6581)
collision_layer = 2
collision_mask = 0

[node name="rock_8" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("21_xcdtp")]
transform = Transform3D(1.19755, 0, -0.0626412, 0, 1.19919, 0, 0.0626412, 0, 1.19755, -18.223, 0.01, -18.1611)
collision_layer = 2
collision_mask = 0

[node name="rock_9" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("21_xcdtp")]
transform = Transform3D(0.565605, 0, 0.75848, 0, 0.94615, 0, -0.75848, 0, 0.565605, -42.1841, 0.01, 44.8249)
collision_layer = 2
collision_mask = 0

[node name="rock_10" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("21_xcdtp")]
transform = Transform3D(0.469806, 0, -1.02034, 0, 1.1233, 0, 1.02034, 0, 0.469806, -13.0906, 0.01, -41.5616)
collision_layer = 2
collision_mask = 0

[node name="rock_11" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("21_xcdtp")]
transform = Transform3D(0.612392, 0, 0.552683, 0, 0.824914, 0, -0.552683, 0, 0.612392, -14.1945, 0.01, 32.5753)
collision_layer = 2
collision_mask = 0

[node name="rock_12" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("21_xcdtp")]
transform = Transform3D(-0.577507, 0, -0.669136, 0, 0.883888, 0, 0.669136, 0, -0.577507, 44.4951, 0.01, -29.4833)
collision_layer = 2
collision_mask = 0

[node name="rock_13" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("21_xcdtp")]
transform = Transform3D(-0.354372, 0, 0.840337, 0, 0.912, 0, -0.840337, 0, -0.354372, 14.9505, 0.01, -26.7757)
collision_layer = 2
collision_mask = 0

[node name="rock_14" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("21_xcdtp")]
transform = Transform3D(-0.87803, 0, 0.049611, 0, 0.87943, 0, -0.049611, 0, -0.87803, -9.96906, 0.01, 25.6339)
collision_layer = 2
collision_mask = 0

[node name="barrel_0" type="StaticBody3D" parent="NavigationRegion3D" groups=["destructible"] instance=ExtResource("22_cin4e")]
transform = Transform3D(0.901285, 0, 0.395155, 0, 0.984104, 0, -0.395155, 0, 0.901285, 16.873, 0, 31.252)
collision_layer = 3
collision_mask = 3

[node name="barrel_1" type="StaticBody3D" parent="NavigationRegion3D" groups=["destructible"] instance=ExtResource("22_cin4e")]
transform = Transform3D(-0.701435, 0, 0.937633, 0, 1.17097, 0, -0.937633, 0, -0.701435, 25.1221, 0, 14.1583)
collision_layer = 3
collision_mask = 3

[node name="decoration_0" type="Node3D" parent="NavigationRegion3D" instance=ExtResource("23_6j2fk")]
transform = Transform3D(0.605118, 0, -0.195014, 0, 0.635765, 0, 0.195014, 0, 0.605118, 75.6344, 0, 3.73994)

[node name="decoration_1" type="Node3D" parent="NavigationRegion3D" instance=ExtResource("23_6j2fk")]
transform = Transform3D(-0.609759, 0, 0.274416, 0, 0.668663, 0, -0.274416, 0, -0.609759, 77.4533, 0, 55.8637)

[node name="decoration_2" type="Node3D" parent="NavigationRegion3D" instance=ExtResource("23_6j2fk")]
transform = Transform3D(0.818616, 0, -0.956142, 0, 1.25871, 0, 0.956142, 0, 0.818616, 60.3633, 0, -41.8805)

[node name="decoration_3" type="Node3D" parent="NavigationRegion3D" instance=ExtResource("23_6j2fk")]
transform = Transform3D(0.867317, 0, -0.174468, 0, 0.884691, 0, 0.174468, 0, 0.867317, 13.8038, 0, 12.6519)

[node name="decoration_4" type="Node3D" parent="NavigationRegion3D" instance=ExtResource("23_6j2fk")]
transform = Transform3D(-0.786682, 0, -0.936502, 0, 1.22307, 0, 0.936502, 0, -0.786682, 58.3095, 0, -93.2066)

[node name="decoration_5" type="Node3D" parent="NavigationRegion3D" instance=ExtResource("23_6j2fk")]
transform = Transform3D(-0.470545, 0, 1.0258, 0, 1.12858, 0, -1.0258, 0, -0.470545, 64.0614, 0, -38.8996)

[node name="decoration_6" type="Node3D" parent="NavigationRegion3D" instance=ExtResource("23_6j2fk")]
transform = Transform3D(0.824001, 0, 0.104398, 0, 0.830588, 0, -0.104398, 0, 0.824001, -15.6489, 0, 92.4679)

[node name="decoration_7" type="Node3D" parent="NavigationRegion3D" instance=ExtResource("23_6j2fk")]
transform = Transform3D(-0.440637, 0, -0.657405, 0, 0.791418, 0, 0.657405, 0, -0.440637, -44.1566, 0, -19.5819)

[node name="decoration_8" type="Node3D" parent="NavigationRegion3D" instance=ExtResource("23_6j2fk")]
transform = Transform3D(0.391749, 0, 0.580077, 0, 0.699969, 0, -0.580077, 0, 0.391749, 44.8152, 0, 59.9571)

[node name="decoration_9" type="Node3D" parent="NavigationRegion3D" instance=ExtResource("23_6j2fk")]
transform = Transform3D(-0.448213, 0, 0.540973, 0, 0.702528, 0, -0.540973, 0, -0.448213, 18.4486, 0, -74.8782)

[node name="decoration_10" type="Node3D" parent="NavigationRegion3D" instance=ExtResource("23_6j2fk")]
transform = Transform3D(0.319745, 0, 0.621892, 0, 0.699275, 0, -0.621892, 0, 0.319745, 29.1402, 0, -16.8076)

[node name="decoration_11" type="Node3D" parent="NavigationRegion3D" instance=ExtResource("23_6j2fk")]
transform = Transform3D(0.700001, 0, -0.281253, 0, 0.75439, 0, 0.281253, 0, 0.700001, -81.3402, 0, 33.82)

[node name="decoration_12" type="Node3D" parent="NavigationRegion3D" instance=ExtResource("23_6j2fk")]
transform = Transform3D(-0.686923, 0, 0.983661, 0, 1.19977, 0, -0.983661, 0, -0.686923, 33.2021, 0, -6.78464)

[node name="decoration_13" type="Node3D" parent="NavigationRegion3D" instance=ExtResource("23_6j2fk")]
transform = Transform3D(-0.641872, 0, -0.317326, 0, 0.716027, 0, 0.317326, 0, -0.641872, -88.9354, 0, 82.4658)

[node name="decoration_14" type="Node3D" parent="NavigationRegion3D" instance=ExtResource("23_6j2fk")]
transform = Transform3D(0.414025, 0, 1.11986, 0, 1.19394, 0, -1.11986, 0, 0.414025, 70.5056, 0, -83.8126)

[node name="decoration_15" type="Node3D" parent="NavigationRegion3D" instance=ExtResource("23_6j2fk")]
transform = Transform3D(-0.969268, 0, 0.143899, 0, 0.979891, 0, -0.143899, 0, -0.969268, -65.0492, 0, 68.9371)

[node name="decoration_16" type="Node3D" parent="NavigationRegion3D" instance=ExtResource("23_6j2fk")]
transform = Transform3D(-0.831926, 0, 0.00190188, 0, 0.831928, 0, -0.00190188, 0, -0.831926, -53.1308, 0, -5.38665)

[node name="decoration_17" type="Node3D" parent="NavigationRegion3D" instance=ExtResource("23_6j2fk")]
transform = Transform3D(-0.215528, 0, 0.966591, 0, 0.990328, 0, -0.966591, 0, -0.215528, -3.91303, 0, 9.26139)

[node name="decoration_18" type="Node3D" parent="NavigationRegion3D" instance=ExtResource("23_6j2fk")]
transform = Transform3D(1.0417, 0, -0.237979, 0, 1.06854, 0, 0.237979, 0, 1.0417, 76.3882, 0, -41.7703)

[node name="decoration_19" type="Node3D" parent="NavigationRegion3D" instance=ExtResource("23_6j2fk")]
transform = Transform3D(-0.976592, 0, 0.724411, 0, 1.21594, 0, -0.724411, 0, -0.976592, -23.1008, 0, -81.0091)

[node name="PatrolPointManager" type="Node" parent="."]
script = ExtResource("24_j2uky")
