[gd_scene load_steps=38 format=3 uid="uid://c0rj4dwj18nv8"]

[ext_resource type="Script" uid="uid://bo0h8t35yls55" path="res://Scripts/Levels/Level01.gd" id="1_hk2b6"]
[ext_resource type="PackedScene" uid="uid://d2xyw4d37ymv4" path="res://Scenes/Minimap.tscn" id="2_j32d5"]
[ext_resource type="PackedScene" uid="uid://ctxvyh1qr52ue" path="res://Scenes/PlayerAvatar.tscn" id="3_0f42i"]
[ext_resource type="PackedScene" uid="uid://do1sypgxbecbd" path="res://Scenes/ItemBoxes.tscn" id="4_swvig"]
[ext_resource type="PackedScene" uid="uid://b4l4phqqbvprm" path="res://Scenes/SkillBoxUI.tscn" id="5_dbohx"]
[ext_resource type="Script" uid="uid://f54r1khulcvh" path="res://Scripts/ModularLevelGenerator.gd" id="6_6ly8b"]
[ext_resource type="Resource" uid="uid://cvji54t3pjwfe" path="res://Resources/LevelConfigs/AllModulesConfig.tres" id="7_r5102"]
[ext_resource type="PackedScene" uid="uid://d2b5h1mlxxy7d" path="res://Scenes/FogOfWar.tscn" id="9_003du"]
[ext_resource type="PackedScene" uid="uid://b88l8pk1ebe1x" path="res://Scenes/player.tscn" id="11_82jtv"]
[ext_resource type="PackedScene" uid="uid://c3h8fj2xsp5oy" path="res://Scenes/Prefabs/Chest.tscn" id="14_1ks1q"]
[ext_resource type="PackedScene" uid="uid://dwusy8dd8usvo" path="res://Scenes/Prefabs/Wall.tscn" id="14_df8b0"]
[ext_resource type="PackedScene" uid="uid://crtnthqkksmri" path="res://Scenes/Prefabs/Tree.tscn" id="15_003du"]
[ext_resource type="Script" uid="uid://c3tr23vwvnmwf" path="res://Scripts/PatrolPoint.gd" id="15_6ly8b"]
[ext_resource type="Texture2D" uid="uid://c2ny0yi07rvcf" path="res://Environment/Floor/Floor01_Rocks_BaseColor.png" id="15_df8b0"]
[ext_resource type="Texture2D" uid="uid://dhfikoo16s5n0" path="res://Environment/Floor/Rocks_Metallic.png" id="16_003du"]
[ext_resource type="PackedScene" uid="uid://ddttv643pel23" path="res://Environment/Floor/Floor01_Custom.tscn" id="17_2bvpm"]
[ext_resource type="Resource" path="res://Resources/Items/Trap.tres" id="17_j32d5"]
[ext_resource type="Resource" path="res://Resources/Items/Torch.tres" id="18_j32d5"]
[ext_resource type="Script" uid="uid://pfva6p6rllgp" path="res://Scripts/Tree.gd" id="18_oh2bt"]
[ext_resource type="PackedScene" uid="uid://rvgn0irsuwao" path="res://Scenes/EnemyBoy01.tscn" id="19_0f42i"]
[ext_resource type="PackedScene" uid="uid://brxqv3iv3op6u" path="res://Scenes/ProgressBar3D.tscn" id="19_fcnrs"]
[ext_resource type="Script" uid="uid://dhw1dgex7wmiy" path="res://Scripts/ChestInteractable.gd" id="21_1ks1q"]
[ext_resource type="PackedScene" uid="uid://ervckea7fk57" path="res://Scenes/Enemy02.tscn" id="21_dbohx"]
[ext_resource type="PackedScene" uid="uid://b5dqjsb63wbhl" path="res://Scenes/Prefabs/Rock.tscn" id="21_xcdtp"]
[ext_resource type="PackedScene" uid="uid://bdq3b4e0mlgo4" path="res://Scenes/Prefabs/Barrel.tscn" id="22_cin4e"]
[ext_resource type="Resource" uid="uid://bjih53ivkm0qi" path="res://Resources/GasItem.tres" id="22_xcdtp"]
[ext_resource type="PackedScene" uid="uid://c6k7j3t3flhst" path="res://Scenes/Prefabs/Decoration.tscn" id="23_6j2fk"]
[ext_resource type="AudioStream" uid="uid://dgskkp7epyn0x" path="res://ChestOpening.mp3" id="23_cin4e"]
[ext_resource type="Script" uid="uid://de18ote8otj6u" path="res://Scripts/PatrolPointManager.gd" id="24_j2uky"]
[ext_resource type="Script" uid="uid://dt3st7cac7lok" path="res://Scripts/UI/UIManager.gd" id="25_ui_manager"]
[ext_resource type="Script" path="res://Scripts/CameraFollower.gd" id="26_camera_follower"]

[sub_resource type="ProceduralSkyMaterial" id="ProceduralSkyMaterial_gbvua"]
sky_top_color = Color(0.2, 0.4, 0.8, 1)
sky_horizon_color = Color(0.5, 0.7, 0.9, 1)
ground_bottom_color = Color(0.3, 0.5, 0.7, 1)
ground_horizon_color = Color(0.5, 0.7, 0.9, 1)

[sub_resource type="Sky" id="Sky_m0r78"]
sky_material = SubResource("ProceduralSkyMaterial_gbvua")

[sub_resource type="Environment" id="Environment_j4i7h"]
background_mode = 2
sky = SubResource("Sky_m0r78")
ambient_light_source = 3
ambient_light_color = Color(0.9, 0.9, 0.95, 1)
ambient_light_energy = 0.5
tonemap_mode = 2
ssao_enabled = true
ssao_radius = 2.0
ssao_intensity = 1.5
glow_enabled = true
glow_strength = 1.2

[sub_resource type="NavigationMesh" id="NavigationMesh_xcdtp"]
vertices = PackedVector3Array(-38.8604, 0.37304, -53.2533, -40.1104, 0.37304, -53.2533, -38.3604, 0.37304, -52.7533, -38.3604, 0.37304, -53.7533, -36.6104, 0.37304, -56.0033, -38.3604, 0.37304, -55.0033, -33.8604, 0.37304, -54.0033, -34.8604, 0.37304, -55.5033, -21.8604, 0.37304, -54.5033, -21.6104, 0.37304, -53.2533, -21.1104, 0.37304, -53.2533, -21.6104, 0.37304, -55.5033, -23.1104, 0.37304, -56.0033, -24.8604, 0.37304, -55.5033, -25.8604, 0.37304, -53.7533, -25.3604, 0.37304, -53.5033, -14.3604, 0.37304, -56.0033, -16.6104, 0.37304, -55.0033, -16.6104, 0.37304, -54.5033, -15.1104, 0.37304, -54.7533, -14.1104, 0.37304, -55.5033, -14.6104, 0.37304, -53.2533, -12.3604, 0.37304, -52.7533, -12.1104, 0.37304, -53.7533, -12.8604, 0.37304, -55.5033, 25.3896, 0.37304, -55.5033, 23.8896, 0.37304, -54.7533, 23.8896, 0.37304, -53.5033, 28.3896, 0.37304, -53.5033, 27.3896, 0.37304, -55.2533, -37.3604, 3.62304, -47.5033, -37.3604, 3.62304, -46.7533, -36.8604, 3.62304, -46.7533, -38.1104, 3.62304, -51.5033, -38.1104, 3.62304, -48.0033, -25.1104, 3.87304, -48.5033, -26.1104, 4.12304, -51.7533, -27.8604, 4.12304, -51.5033, -29.6104, 3.87304, -48.5033, -29.1104, 3.87304, -47.5033, -31.1104, 3.62304, -48.5033, -30.0271, 4.12304, -49.5033, -29.1104, 3.62304, -54.0033, -31.3603, 3.62304, -50.7733, -45.1104, 0.37304, -53.0033, -43.6104, 0.37304, -53.0033, -45.3604, 0.37304, -53.5033, -45.6104, 0.37304, -53.0033, -45.3604, 0.37304, -52.2533, -47.1104, 0.37304, -52.7533, -22.1104, 3.87304, -49.2533, -21.3604, 3.62304, -47.5033, -20.8604, 3.62304, -47.5033, -22.1104, 4.12304, -51.5033, -22.6104, 4.12304, -52.7533, -15.6104, 3.62304, -49.2533, -15.3604, 3.62304, -51.7533, -16.1104, 3.62304, -53.2533, -20.8604, 3.62304, -51.5033, 22.8896, 3.62304, -45.2533, 23.3896, 3.62304, -45.2533, 23.8896, 3.62304, -46.7533, 10.6396, 3.62304, -51.5033, 11.1396, 3.62304, -48.7533, 13.1396, 3.62304, -49.0033, 14.6396, 3.62304, -52.5033, 15.6396, 3.62304, -53.2533, 22.6396, 3.62304, -50.7533, 36.8896, 3.62304, -46.7533, 37.6396, 3.62304, -46.7533, 38.1396, 3.62304, -47.7533, 36.6396, 3.62304, -50.5033, 35.3896, 3.62304, -50.5033, 46.1396, 3.62304, -51.7533, 45.1396, 3.62304, -53.0033, 44.3896, 3.62304, -53.0033, 43.3896, 3.62304, -49.2533, 44.6396, 3.62304, -49.2533, 45.1396, 3.62304, -48.5033, 30.6396, 3.62304, -52.2533, 29.6396, 3.62304, -51.7533, 29.8896, 3.62304, -49.0033, 45.1396, 3.62304, -47.7533, 46.3896, 3.62304, -47.7533, 47.1396, 3.62304, -47.0033, 47.1396, 3.62304, -46.0033, 52.3896, 3.62304, -46.5033, 53.3896, 3.62304, -48.2533, 53.1396, 3.62304, -52.0033, 51.6396, 3.62304, -52.0033, 52.3896, 3.62304, -47.7533, 50.6396, 3.62304, -52.7533, 1.8896, 3.87304, -52.2533, 0.639603, 3.87304, -52.2533, 0.389603, 3.87304, -51.0033, -0.360397, 3.87304, -50.5033, -0.110397, 3.87304, -48.7533, 8.1396, 3.87304, -50.7533, 7.8896, 3.87304, -48.0033, 6.8896, 3.87304, -46.5033, 5.6396, 3.87304, -50.7533, 29.1396, 3.62304, -48.5033, 23.3896, 3.62304, -52.2533, 23.3896, 3.62304, -51.2533, 24.3896, 3.62304, -47.5033, 29.1396, 3.62304, -47.0033, -45.3604, 3.62304, -50.7533, -48.8604, 3.62304, -51.5033, -49.1104, 3.62304, -50.2533, -44.6104, 3.62304, -50.7533, -47.1104, 3.62304, -48.2533, -44.6104, 3.62304, -47.7533, -50.1104, 3.62304, -49.7533, -51.1104, 3.62304, -46.0033, -48.3604, 3.62304, -45.0033, -44.1104, 3.62304, -51.7533, -18.1104, 0.37304, -51.0033, -15.8604, 0.37304, -51.0033, -16.3604, 0.37304, -51.7533, -9.3604, 3.62304, -47.2533, -8.6104, 3.62304, -47.2533, -8.6104, 3.62304, -48.0033, -1.1104, 3.87304, -48.2533, -6.3604, 3.62304, -51.7533, -6.6104, 3.62304, -50.2533, -7.8604, 3.62304, -49.7533, -13.1104, 3.62304, -51.7533, -7.8604, 3.62304, -48.7533, -1.1104, 3.62304, -47.2533, -31.6104, 0.37304, -50.7533, -31.6104, 0.37304, -51.2533, -32.3604, 0.37304, -51.2533, -33.6104, 0.37304, -50.5033, -36.8604, 0.37304, -49.0033, -37.1104, 0.37304, -49.7533, -24.6104, 0.37304, -51.2533, -26.6104, 0.37304, -50.5033, -26.6104, 0.37304, -50.0033, -25.8604, 0.37304, -50.0033, -23.8604, 0.37304, -50.7533, -23.8604, 0.37304, -51.2533, -41.8604, 0.37304, -50.2533, -39.3604, 0.37304, -49.7533, -39.6104, 0.37304, -50.2533, 20.8896, 0.37304, -48.5033, 21.8896, 0.37304, -48.7533, 21.6396, 0.37304, -49.2533, 19.8896, 0.37304, -49.2533, 17.3896, 0.37304, -50.2533, 17.3896, 0.37304, -49.7533, 26.3896, 0.37304, -50.2533, 26.3896, 0.37304, -49.7533, 28.1396, 0.37304, -49.2533, 28.6396, 0.37304, -50.0033, -51.6104, 0.37304, -48.7533, -51.8604, 0.37304, -49.5033, -52.6104, 0.37304, -48.2533, -52.8604, 0.37304, -46.7533, -53.1104, 0.37304, -48.2533, -53.1104, 0.37304, -41.0033, -52.6104, 0.37304, -42.5033, -53.3604, 0.37304, -42.7533, -54.1104, 0.37304, -41.2533, -53.8604, 0.37304, -46.2533, -54.1104, 0.37304, -44.7533, -53.3604, 0.37304, -43.7533, 3.3896, 0.37304, -49.5033, 6.3896, 0.37304, -48.5033, 5.8896, 0.37304, -49.5033, 53.6396, 0.37304, -47.0033, 54.6396, 0.37304, -43.7533, 55.1396, 0.37304, -47.2533, 54.6396, 0.37304, -47.2533, 55.6396, 0.37304, -47.5033, 55.6396, 0.37304, -49.2533, 54.6396, 0.37304, -49.2533, -46.3604, 0.37304, -47.0033, -46.8604, 0.37304, -45.0033, -44.3604, 0.37304, -44.5033, -43.8604, 0.37304, -46.2533, -22.1104, 0.37304, -46.0033, -28.3604, 0.37304, -46.2533, -30.1104, 0.37304, -46.2533, -37.3604, 0.37304, -45.2533, -43.8604, 0.37304, -42.7533, -46.8604, 0.37304, -33.2533, -43.8604, 0.37304, -44.0033, -42.8604, 0.37304, -46.2533, -46.3604, 0.37304, -12.5033, -47.1104, 0.37304, -11.7533, -46.6104, 0.62304, -5.50326, -42.6104, 0.37304, -46.7533, -38.3604, 0.37304, -45.5033, -38.6104, 0.37304, -46.7533, -30.6104, 0.37304, -47.2533, -46.8604, 0.37304, -29.0033, -45.3604, 0.37304, -20.7533, -20.1104, 0.87304, -1.75326, -20.6104, 0.37304, -46.2533, -21.2581, 0.37304, -2.6169, -20.1271, 0.37304, -3.2366, -28.369, 0.87304, -28.2708, -28.3588, 0.37304, -26.745, -28.3792, 0.37304, -29.7466, -29.854, 0.37304, -28.2318, -20.3604, 0.37304, -1.75326, -22.8604, 0.37304, -48.0033, -17.3604, 0.37304, -2.50326, -17.1104, 0.37304, -1.75326, -16.1104, 0.62304, -2.00326, 4.6396, 0.37304, -13.0033, 4.6396, 0.37304, -14.2533, 0.639603, 0.37304, -47.2533, 0.139603, 0.37304, -47.2533, -0.360397, 0.37304, -46.0033, 7.6396, 0.37304, -45.0033, 5.6396, 0.62304, -15.0033, 5.3896, 0.37304, -16.0033, 5.8896, 0.37304, -10.5033, 7.1396, 0.37304, -11.0033, 7.1396, 0.37304, -12.5033, 5.1396, 0.37304, -12.5033, 8.1396, 0.37304, -26.5033, 8.6396, 0.37304, -29.2533, 5.8896, 0.37304, -9.75326, 5.8896, 0.37304, -17.0033, 7.1396, 0.62304, -16.7533, 8.8896, 0.37304, -17.7533, 9.3896, 0.37304, -26.0033, -7.8604, 0.37304, -45.7533, -9.1104, 0.37304, -45.7533, -1.8604, 0.37304, -46.0033, -7.3604, 0.37304, -47.5033, -17.8604, 0.37304, -2.50326, -19.1104, 0.37304, -46.7533, -15.1104, 0.37304, -48.0033, 12.1396, 0.37304, -47.7533, 8.6396, 0.37304, -46.7533, 8.3896, 0.37304, -45.5033, 11.3896, 0.37304, -29.2533, 11.8896, 0.37304, -28.5033, 16.1396, 0.37304, -46.2533, 39.3896, 0.37304, -46.7533, 38.6396, 0.37304, -46.2533, 38.3896, 0.37304, -45.2533, 43.8896, 0.37304, -44.2533, 44.1396, 0.37304, -46.5033, 43.8896, 0.37304, -47.7533, 45.8896, 0.37304, -45.0033, 45.8896, 0.37304, -46.5033, 46.6396, 0.37304, -28.0033, 43.8896, 0.37304, -42.7533, 37.1396, 0.37304, -45.2533, 24.1396, 0.37304, -43.7533, 19.3896, 0.37304, -18.7533, 31.6219, 0.87304, -26.7578, 30.1369, 0.37304, -26.7687, 31.6217, 0.37304, -28.2775, 33.1569, 0.37304, -26.7468, 36.127, 0.87304, -26.7748, 36.1267, 0.37304, -28.2445, 33.1171, 0.37304, -25.271, 37.662, 0.37304, -26.7638, 34.642, 0.37304, -26.7358, 11.6396, 0.37304, -26.5033, 16.3896, 0.37304, -19.2533, 29.8896, 0.37304, -45.7533, 47.6396, 0.37304, -32.7533, 25.1396, 0.37304, -46.2533, 30.3896, 0.37304, -47.5033, 30.3896, 0.37304, -46.2533, -19.3604, 0.62304, -1.50326, 52.6396, 3.62304, -42.5033, 53.3896, 3.62304, -42.5033, 51.8896, 3.62304, -41.7533, 47.3896, 3.62304, -44.5033, 48.8896, 3.87304, -32.2533, 52.3896, 3.87304, -32.2533, 52.8896, 3.62304, -35.0033, 48.1396, 3.62304, -35.5033, 53.6396, 3.62304, -36.0033, 51.8896, 3.62304, -40.5033, 45.1396, 3.62304, -43.2533, -53.3604, 3.62304, -35.7533, -53.3604, 3.62304, -35.2533, -52.3604, 3.62304, -35.2533, -51.8604, 3.62304, -44.0033, -51.1104, 3.62304, -42.2533, -47.8604, 3.62304, -44.0033, -47.8604, 3.62304, -35.0033, -45.3604, 3.62304, -43.2533, -51.3604, 3.62304, -34.5033, -51.6104, 3.62304, -31.2533, -48.1104, 3.62304, -31.2533, -46.6104, 0.37304, -40.5033, -46.3604, 0.37304, -39.5033, -45.8604, 0.37304, -41.0033, -46.1104, 0.37304, -41.7533, -50.8604, 0.37304, -36.5033, -50.8604, 0.37304, -36.0033, -49.8604, 0.37304, -36.0033, -49.6104, 0.37304, -38.5033, -50.1104, 0.37304, -38.0033, -48.6104, 0.37304, -41.0033, -48.6104, 0.37304, -41.5033, -49.6104, 0.37304, -41.5033, 49.8896, 0.37304, -36.2533, 51.1396, 0.37304, -35.7533, 49.3896, 0.37304, -40.0033, 48.6396, 0.37304, -40.0033, 47.8896, 3.87304, -28.5033, 48.3896, 3.87304, -28.0033, 51.3896, 3.87304, -28.0033, 48.6396, 3.62304, -22.0033, 51.6396, 3.62304, -22.0033, -51.6104, 3.62304, -29.2533, -48.1104, 3.62304, -28.5033, -53.6104, 3.62304, -28.5033, -51.8604, 3.62304, -17.5033, -47.1104, 3.62304, -19.7533, -46.6104, 3.62304, -20.2533, -47.3604, 3.62304, -17.5033, 50.3896, 0.37304, -29.0033, 50.3896, 0.37304, -30.0033, 49.6396, 0.37304, -30.2533, 49.1396, 0.37304, -29.0033, 54.6396, 0.37304, -29.2533, 53.8896, 0.37304, -30.2533, 53.1396, 0.37304, -30.0033, 52.6396, 0.37304, -27.7533, 54.3896, 0.37304, -26.7533, 57.6396, 0.37304, -23.7533, 58.1396, 0.37304, -24.0033, 58.1396, 0.37304, -26.0033, 55.6396, 0.37304, -25.7533, 52.8896, 0.37304, -20.2533, 54.1396, 0.37304, -19.7533, 57.1396, 0.37304, -19.7533, 9.6396, 2.62304, -28.0033, 9.6396, 2.62304, -27.2533, 10.3896, 2.62304, -27.2533, 10.3896, 2.62304, -28.0033, 47.3896, 0.37304, -24.0033, 47.1396, 0.37304, -27.5033, 47.3896, 0.37304, -20.5033, 19.8896, 0.37304, -18.5033, 46.6396, 0.37304, -16.0033, 19.8896, 0.37304, -16.2533, -50.3604, 0.37304, -23.0033, -50.3604, 0.37304, -20.7533, -49.1104, 0.37304, -20.5033, -50.1104, 0.37304, -23.7533, -51.3604, 0.37304, -26.7533, -50.3604, 0.37304, -26.7533, -48.1104, 0.62304, -26.7533, -47.3604, 0.37304, -23.5033, -47.6104, 0.62304, -26.5033, 49.3896, 0.37304, -26.7533, 49.8896, 0.37304, -24.5033, 49.8896, 0.37304, -26.7533, 9.3896, 0.37304, -17.0033, 10.8896, 0.37304, -17.0033, 11.3896, 0.37304, -15.5033, 14.8896, 0.37304, -15.7533, 15.3896, 0.37304, -18.2533, 11.1396, 0.37304, -25.7533, -53.8604, 0.37304, -22.7533, -55.1104, 0.37304, -22.2533, -54.8604, 0.37304, -21.0033, -53.1104, 0.37304, -17.7533, -55.8604, 0.37304, -15.2533, -53.6104, 0.37304, -14.7533, -56.1104, 0.37304, -20.2533, 47.6396, 3.62304, -14.7533, 47.6396, 3.62304, -13.2533, 48.1396, 3.62304, -13.0033, 51.6396, 3.62304, -19.5033, 52.1396, 3.62304, -13.5033, 52.8896, 3.62304, -19.0033, 52.1396, 3.62304, -12.7533, 48.1396, 3.87304, -5.50326, 52.3896, 3.62304, -5.50326, 52.6396, 3.62304, -12.5033, 47.6396, 3.62304, -5.50326, 50.8896, 0.37304, -18.2533, 50.8896, 0.37304, -18.7533, 49.8896, 0.37304, -18.7533, 50.1396, 0.37304, -16.2533, 49.8896, 0.37304, -15.2533, 49.3896, 0.37304, -14.0033, 50.1396, 0.37304, -13.7533, 18.1396, 3.62304, -17.0033, 18.6396, 3.62304, -17.2533, 16.8896, 3.62304, -17.7533, 16.3896, 3.62304, -15.7533, 16.6396, 3.62304, -15.2533, 17.8896, 3.62304, -15.2533, -51.6104, 3.87304, -5.25326, -52.1104, 4.12304, -4.75326, -48.6104, 3.62304, -4.50326, -48.6104, 3.62304, -5.50326, -48.3604, 3.62304, -12.5033, -49.6104, 3.62304, -13.2533, -49.1104, 3.62304, -12.0033, -51.6104, 3.62304, -7.00326, -47.8604, 3.62304, -6.00326, -49.6104, 3.62304, -11.5033, -52.3604, 3.62304, -13.5033, -52.1104, 3.62304, -11.5033, -47.6104, 3.62304, -13.0033, 54.1396, 0.37304, -15.7533, 54.1396, 0.37304, -16.5033, 53.6396, 0.37304, -14.7533, 55.3896, 0.37304, -14.7533, 53.8896, 0.37304, -8.50326, 56.8896, 0.37304, -8.75326, 57.3896, 0.37304, -13.0033, 57.8896, 0.37304, -13.2533, 57.8896, 0.37304, -15.0033, -50.3604, 0.37304, -13.7533, -49.8604, 0.37304, -13.7533, -49.8604, 0.37304, -16.0033, -50.3604, 0.37304, -16.2533, 47.3896, 0.37304, -3.75326, 47.3896, 0.37304, -4.25326, 46.3896, 0.37304, -4.50326, 46.1396, 0.37304, 1.74674, 8.1396, 0.37304, -9.25326, 6.8896, 0.37304, -8.75326, 6.8896, 0.37304, -8.50326, 8.1396, 0.37304, -10.7533, 9.6396, 0.37304, -12.5033, 46.1396, 0.37304, -13.5033, 6.6396, 0.37304, 16.4967, 7.1396, 0.37304, 17.2467, 8.8896, 0.37304, 16.9967, 18.8896, 0.37304, -13.7533, 15.6396, 0.37304, -14.0033, 45.3896, 0.37304, 16.7467, 46.6396, 0.37304, 15.9967, 47.3896, 0.37304, 11.9967, 46.6396, 0.37304, 9.49674, 10.6396, 0.37304, 18.2467, 45.6396, 0.37304, 18.2467, 46.8896, 0.37304, -12.5033, 46.1396, 0.37304, -12.7533, 31.6518, 0.87304, -1.2396, 30.1168, 0.37304, -1.25058, 30.1182, 0.87304, 6.26787, 31.6271, 0.37304, 0.235081, 28.6332, 0.37304, 6.25688, 19.6592, 0.87304, 12.2408, 22.659, 0.37304, 10.771, 31.6532, 0.37304, 6.22885, 30.1415, 0.37304, -2.77526, 18.154, 0.37304, 10.7381, 21.1442, 0.37304, 12.2518, 34.6218, 0.37304, -1.26762, 30.1629, 0.37304, 4.76516, 16.6595, 0.37304, 13.7605, 7.1396, 5.12304, -15.5033, 7.1396, 5.12304, -14.2533, 8.3896, 5.12304, -14.2533, 8.3896, 5.12304, -15.5033, 50.6396, 0.37304, -11.2533, 50.6396, 0.37304, -12.0033, 49.6396, 0.37304, -12.0033, 50.1396, 0.37304, -10.0033, 49.6396, 0.37304, -6.25326, 50.3896, 0.37304, -6.25326, -50.8604, 0.37304, -11.0033, -50.1104, 0.37304, -8.50326, -50.1104, 0.37304, -11.0033, -15.6104, 0.62304, -1.50326, -53.6104, 0.37304, -6.00326, -52.8604, 0.37304, -6.00326, -53.1104, 0.37304, -8.75326, -53.6104, 0.37304, -8.75326, -15.8604, 0.37304, 13.9967, -15.6104, 0.37304, 14.7467, -14.1104, 0.37304, 14.7467, -14.6104, 0.37304, 4.24674, -15.6104, 0.37304, 4.74674, -13.8604, 0.37304, 15.4967, -12.3604, 0.37304, 15.4967, 0.639603, 0.37304, 21.7467, 2.1396, 0.37304, 20.9967, 2.1396, 0.37304, 19.9967, -15.1104, 0.37304, 0.246738, -16.6104, 0.37304, 2.24674, -14.6104, 0.37304, 2.74674, 3.1396, 0.37304, 19.4967, 3.1396, 0.37304, 17.4967, 3.6396, 0.37304, 16.9967, 4.8896, 0.62304, 17.2467, -0.860397, 0.37304, 21.4967, -12.1104, 0.37304, 17.2467, 4.65489, 0.87304, 0.235428, 3.15501, 0.37304, 1.7453, 4.64518, 0.37304, 3.24912, -21.8604, 0.37304, 1.49674, -20.8604, 0.62304, 0.246738, -21.1104, 0.37304, -0.753262, -45.8604, 0.37304, 1.49674, -47.3604, 0.37304, -4.50326, 51.3896, 3.87304, 17.2467, 51.8896, 3.87304, 17.2467, 52.1396, 3.87304, 16.4967, 48.6396, 3.87304, -5.00326, 48.6396, 3.87304, -3.75326, 52.8896, 4.12304, -3.75326, 47.6396, 3.87304, 0.496738, 51.8896, 4.12304, 2.49674, 47.8896, 4.12304, 16.7467, 53.1396, 3.87304, 10.9967, 52.3896, 3.87304, 10.2467, 48.8896, 4.12304, 11.4967, 47.6396, 3.62304, 4.99674, 47.8896, 3.62304, 8.99674, 51.9729, 3.62304, 3.78841, 48.3896, 4.12304, 10.2467, 49.6146, 3.62304, 3.22174, -52.8604, 4.12304, -4.75326, -52.8604, 4.12304, -4.00326, -51.3604, 4.12304, 1.99674, -49.1104, 3.87304, 2.49674, -48.3604, 3.87304, 1.74674, -47.1104, 3.62304, 1.49674, -51.3604, 0.37304, -2.75326, -50.1104, 0.37304, -2.25326, -50.3604, 0.37304, -4.50326, -51.6104, 0.37304, -4.50326, 51.1396, 0.37304, -3.25326, 51.1396, 0.37304, -4.00326, 49.8896, 0.37304, -4.25326, 49.3896, 0.37304, -1.25326, 49.3896, 0.37304, 0.496738, 50.6396, 0.37304, 0.246738, -49.6104, 0.37304, -0.503262, -49.6104, 0.37304, 0.746738, -49.1104, 0.37304, 0.746738, 55.8896, 0.37304, 1.99674, 56.3896, 0.37304, 1.74674, 56.3896, 0.37304, -0.253262, 53.6396, 0.37304, -0.503262, 53.1396, 0.37304, 1.74674, 53.3896, 0.37304, 5.74674, 55.3896, 0.37304, 5.74674, -19.3604, 5.12304, -0.253262, -19.3604, 5.12304, 0.996738, -18.1104, 5.12304, 0.996738, -18.1104, 5.12304, -0.253262, -46.1104, 0.37304, 2.74674, -47.6104, 0.37304, 2.99674, -46.8604, 0.37304, 6.49674, -21.1104, 0.37304, 2.74674, -21.8604, 0.37304, 2.24674, -38.978, 0.37304, 2.59968, -40.4045, 0.87304, 2.62909, -41.831, 0.37304, 2.6585, -51.8604, 4.12304, 2.74674, -52.6104, 3.62304, 2.74674, -52.6104, 3.62304, 4.24674, -51.1104, 3.62304, 10.4967, -48.3604, 3.62304, 10.4967, -47.6104, 3.62304, 8.74674, -50.8854, 3.62304, 3.22174, -46.3604, 0.37304, 9.99674, -47.1104, 0.37304, 10.2467, -47.1104, 0.37304, 10.9967, -19.1104, 0.37304, 14.7467, -17.8604, 0.62304, 14.9967, -17.6104, 0.37304, 14.4967, -16.8604, 0.37304, 4.24674, -16.8604, 0.37304, 2.74674, -46.1104, 0.37304, 15.9967, -19.3604, 0.37304, 15.9967, -32.8549, 0.87304, 10.7392, -34.34, 0.37304, 10.7283, -31.3699, 0.37304, 10.7502, -22.3599, 0.87304, 10.7662, -23.8449, 0.37304, 10.7552, -32.8597, 0.37304, 12.226, -22.37, 0.37304, 9.24037, -28.3653, 0.37304, 7.75358, 49.6396, 0.37304, 8.74674, 50.3896, 0.37304, 8.99674, 49.8896, 0.37304, 3.24674, 49.1396, 0.37304, 3.24674, -53.8604, 0.37304, 3.49674, -54.8604, 0.37304, 3.99674, -54.6104, 0.37304, 4.99674, -52.6104, 0.37304, 9.99674, -55.8604, 0.37304, 5.99674, -55.6104, 0.37304, 6.99674, -53.8604, 0.37304, 10.2467, -56.1104, 0.37304, 10.7467, -54.1104, 0.37304, 11.2467, -56.1104, 0.37304, 7.49674, -51.1104, 0.37304, 3.74674, -50.6104, 0.37304, 5.24674, -50.6104, 0.37304, 3.74674, 53.3896, 0.37304, 15.2467, 53.3896, 0.37304, 16.2467, 54.6396, 0.37304, 16.2467, 55.1396, 0.37304, 12.4967, 54.3896, 0.37304, 10.2467, 55.6396, 0.37304, 12.2467, 55.6396, 0.37304, 10.2467, -51.6104, 3.62304, 11.2467, -52.8604, 3.62304, 11.2467, -52.1104, 3.62304, 14.4967, -52.1104, 3.62304, 15.4967, -52.8604, 3.62304, 15.9967, -48.6104, 3.62304, 16.9967, -48.1104, 3.62304, 16.2467, -47.3604, 3.62304, 16.2467, 51.1396, 0.37304, 11.9967, 51.1396, 0.37304, 11.2467, 49.8896, 0.37304, 11.2467, 49.8896, 0.37304, 13.4967, 50.6396, 0.37304, 15.2467, 49.3896, 0.37304, 14.2467, 49.3896, 0.37304, 15.4967, -50.6104, 0.37304, 13.7467, -50.1104, 0.37304, 13.7467, -50.3604, 0.37304, 11.4967, -51.3604, 0.37304, 11.4967, -54.6104, 3.62304, 16.4967, -54.8604, 3.62304, 16.9967, -52.1104, 3.62304, 24.4967, -46.8604, 3.62304, 24.4967, -46.1104, 3.62304, 24.2467, -46.1104, 0.37304, 16.9967, -47.1104, 0.37304, 17.4967, -47.1104, 0.37304, 17.9967, -45.3604, 0.37304, 22.4967, -20.6104, 0.37304, 20.4967, -19.8604, 0.37304, 19.2467, -20.3604, 0.37304, 18.9967, -20.3604, 0.37304, 17.4967, -19.3604, 0.62304, 16.7467, -36.6251, 0.37304, 21.7909, -35.1692, 0.87304, 21.6732, -32.2575, 0.37304, 21.4379, -17.8604, 5.12304, 16.2467, -17.8604, 5.12304, 17.4967, -16.6104, 5.12304, 17.4967, -16.6104, 5.12304, 16.2467, 51.6396, 3.62304, 24.7467, 52.3896, 3.62304, 24.2467, 52.3896, 3.62304, 23.4967, 50.6396, 3.62304, 18.4967, 48.1396, 3.62304, 21.4967, 48.1396, 3.62304, 22.7467, 46.8896, 3.62304, 17.4967, 46.8896, 3.62304, 18.2467, 50.6396, 3.87304, 17.7467, 51.6396, 3.62304, 25.9967, 46.3896, 3.62304, 23.4967, 48.3896, 3.62304, 32.7467, 52.6396, 3.62304, 30.7467, 52.8896, 3.62304, 36.4967, 53.8896, 3.62304, 31.7467, 47.8896, 3.62304, 35.7467, -1.3604, 0.37304, 21.9967, -12.6104, 0.37304, 17.7467, -14.1104, 0.37304, 17.4967, -15.1104, 0.37304, 19.2467, -1.3604, 0.37304, 22.9967, -12.8604, 0.37304, 46.4967, -8.8604, 0.37304, 47.2467, -8.3604, 0.37304, 45.4967, -16.8604, 0.37304, 46.4967, -16.8604, 0.87304, 19.2467, -4.8604, 0.37304, 45.9967, 48.3896, 0.37304, 17.9967, 49.1396, 0.37304, 19.9967, 48.8896, 0.37304, 17.9967, -51.1104, 0.37304, 20.9967, -50.3604, 0.37304, 21.2467, -51.1104, 0.37304, 18.2467, -52.1104, 0.37304, 18.2467, 45.1396, 0.37304, 22.2467, 46.6396, 0.37304, 21.9967, 44.8896, 0.37304, 22.7467, 10.1396, 0.37304, 20.2467, 9.1396, 0.37304, 20.9967, 8.3896, 2.62304, 19.2467, 9.1396, 2.62304, 19.2467, 8.8896, 2.62304, 18.4967, 8.3896, 2.62304, 18.4967, 4.6396, 5.12304, 18.7467, 4.6396, 5.12304, 19.9967, 5.8896, 5.12304, 19.9967, 5.8896, 5.12304, 18.7467, -20.6104, 0.37304, 45.9967, -17.8403, 0.37304, 25.7486, -17.8504, 0.37304, 21.2327, -28.8604, 0.37304, 46.7467, -24.3604, 0.37304, 47.7467, -23.8604, 0.37304, 45.7467, -29.1104, 0.37304, 45.4967, -45.6104, 0.37304, 41.4967, -31.6104, 0.37304, 45.7467, -47.1104, 0.37304, 41.7467, -46.1104, 0.37304, 47.2467, -36.6104, 0.37304, 47.4967, -44.6104, 0.37304, 25.2467, -46.1104, 0.37304, 25.7467, -46.1104, 0.37304, 33.7467, -31.3848, 0.87304, 28.7711, -31.3596, 0.37304, 30.2359, -29.875, 0.37304, 27.2673, 46.6396, 0.37304, 42.2467, 46.6396, 0.37304, 40.9967, 45.6396, 0.37304, 40.7467, 36.1396, 0.37304, 46.7467, 45.6396, 0.37304, 46.2467, 28.6396, 0.37304, 48.2467, 29.3896, 0.37304, 47.9967, 29.8896, 0.37304, 46.4967, 23.6396, 0.37304, 45.4967, 23.8896, 0.37304, 46.7467, 7.8896, 0.37304, 20.7467, 6.8896, 0.37304, 21.7467, 31.3896, 0.37304, 46.4967, 35.8896, 0.37304, 47.9967, 7.3896, 0.62304, 46.2467, 45.6396, 0.37304, 39.7467, 16.6254, 0.87304, 27.2469, 18.1604, 0.37304, 27.2579, 33.1411, 0.87304, 30.2451, 16.6157, 0.37304, 28.7606, 31.6561, 0.37304, 30.2341, 33.1314, 0.37304, 31.7588, 13.6151, 0.37304, 25.7612, 13.6396, 0.37304, 47.2467, 46.8896, 0.37304, 34.4967, 46.8896, 0.37304, 32.2467, 3.3896, 0.37304, 21.7467, 0.639603, 0.37304, 23.4967, -0.610397, 0.37304, 23.7467, -0.610397, 0.37304, 46.9967, 6.6396, 0.37304, 46.7467, 6.6396, 0.37304, 47.7467, -54.6104, 0.37304, 21.2467, -55.1104, 0.37304, 21.2467, -54.8604, 0.37304, 22.4967, -53.3604, 0.37304, 24.7467, -55.3604, 0.37304, 26.2467, -53.3604, 0.37304, 26.7467, -55.3604, 0.37304, 22.9967, -47.3604, 3.62304, 24.9967, -52.1104, 3.62304, 29.9967, -47.3604, 3.62304, 32.7467, -52.8604, 3.62304, 39.9967, -48.3604, 3.62304, 40.9967, -47.1104, 3.62304, 40.4967, -52.8604, 3.62304, 30.4967, -51.6104, 3.62304, 47.4967, -47.6104, 3.62304, 46.9967, 49.3896, 0.37304, 28.9967, 50.1396, 0.37304, 29.4967, 49.3896, 0.37304, 26.4967, 48.8896, 0.37304, 26.7467, -50.3604, 0.37304, 27.2467, -50.3604, 0.37304, 29.2467, -49.8604, 0.37304, 29.2467, -49.8604, 0.37304, 27.2467, 53.8896, 0.37304, 29.7467, 54.1396, 0.37304, 30.4967, 54.8896, 0.37304, 30.4967, -50.6104, 0.37304, 32.2467, -49.8604, 0.37304, 32.4967, -49.6104, 0.37304, 31.9967, -49.8604, 0.37304, 31.2467, -50.3604, 0.37304, 31.2467, 50.1396, 0.37304, 31.7467, 50.1396, 0.37304, 32.4967, 50.8896, 0.37304, 32.2467, 50.8896, 0.37304, 31.7467, 54.1396, 0.37304, 36.7467, 54.1396, 0.37304, 37.2467, 55.8896, 0.37304, 37.7467, 58.3896, 0.37304, 36.4967, 56.3896, 0.37304, 34.2467, 54.8896, 0.37304, 32.9967, 58.8896, 0.37304, 36.2467, 58.8896, 0.37304, 34.2467, 55.3896, 0.37304, 40.4967, 57.8896, 0.37304, 40.4967, -50.6104, 0.37304, 34.9967, -49.8604, 0.37304, 34.9967, -49.8604, 0.37304, 34.4967, -50.8604, 0.37304, 34.4967, 49.8896, 0.37304, 36.2467, 50.3896, 0.37304, 36.2467, 50.6396, 0.37304, 35.4967, 50.6396, 0.37304, 34.4967, 49.8896, 0.37304, 34.4967, 54.3896, 3.62304, 39.4967, 54.3896, 3.62304, 38.4967, 53.6396, 3.62304, 38.4967, 52.8896, 3.62304, 37.7467, 48.1396, 3.62304, 41.4967, 46.8896, 3.62304, 46.9967, 52.3896, 3.62304, 47.9967, 47.1396, 3.62304, 39.7467, 48.1396, 3.62304, 40.2467, 46.1396, 3.62304, 47.4967, 53.3896, 0.37304, 40.9967, 53.6396, 0.37304, 42.4967, 54.3896, 0.37304, 39.2467, 54.1396, 0.37304, 38.4967, -53.8604, 0.37304, 41.7467, -54.3604, 0.37304, 41.7467, -54.1104, 0.37304, 42.9967, -52.8604, 0.37304, 48.2467, -51.6104, 0.37304, 49.9967, -50.3604, 0.37304, 51.7467, -50.3604, 0.37304, 48.7467, -52.3604, 0.37304, 48.9967, -55.1104, 0.37304, 43.7467, -55.3604, 0.37304, 44.9967, -55.3604, 0.37304, 48.4967, 49.6396, 0.37304, 44.2467, 49.6396, 0.37304, 44.9967, 50.3896, 0.37304, 44.7467, 50.8896, 0.37304, 42.2467, 50.8896, 0.37304, 41.7467, 49.8896, 0.37304, 41.9967, -50.8604, 0.37304, 41.9967, -50.3604, 0.37304, 43.4967, -50.3604, 0.37304, 41.9967, 49.1396, 0.37304, 46.2467, 49.1396, 0.37304, 47.2467, 49.6396, 0.37304, 47.2467, 49.6396, 0.37304, 45.2467, -7.1104, 3.62304, 46.7467, -7.6104, 3.62304, 46.7467, -7.6104, 3.62304, 47.7467, -11.1104, 3.62304, 52.4967, -8.6104, 3.62304, 52.9967, -8.1104, 3.62304, 52.2467, -8.3604, 3.62304, 48.7467, -10.8604, 3.62304, 48.2467, 7.8896, 3.62304, 51.9967, 7.8896, 3.62304, 48.7467, 6.6396, 3.62304, 49.2467, 5.1396, 3.62304, 52.2467, 1.6396, 3.62304, 48.2467, -0.860397, 3.62304, 48.4967, 2.3896, 3.62304, 51.7467, -7.3604, 3.62304, 52.2467, 1.3896, 3.62304, 54.4967, 24.3896, 3.62304, 51.4967, 24.6396, 3.62304, 48.4967, 23.3896, 3.87304, 47.9967, 21.6396, 4.12304, 51.2467, 14.6396, 3.62304, 47.9967, 14.1396, 3.62304, 48.4967, 15.8896, 4.12304, 52.2467, 16.8896, 4.12304, 51.9967, 22.3896, 3.62304, 46.7467, 7.8896, 3.62304, 47.4967, 15.1396, 4.12304, 53.2467, -39.3604, 3.62304, 48.7467, -39.6104, 3.62304, 51.4967, -37.3604, 3.62304, 51.7467, -36.1104, 3.62304, 48.7467, -30.1104, 3.62304, 46.9967, -33.1104, 3.62304, 51.2467, -30.6104, 3.62304, 50.9967, -29.3604, 3.62304, 47.9967, -36.3604, 3.62304, 52.4967, -23.8604, 3.62304, 52.7467, -23.8604, 3.62304, 49.2467, -23.1104, 3.62304, 48.7467, -12.3604, 3.62304, 52.2467, -12.8604, 3.62304, 53.4967, -16.6104, 3.62304, 47.7467, -22.8604, 3.62304, 47.2467, 34.8896, 3.62304, 52.4967, 37.3896, 3.62304, 53.2467, 37.8896, 3.62304, 52.4967, 36.8896, 3.62304, 49.4967, 35.3896, 3.62304, 49.2467, 37.3896, 3.62304, 47.9967, 46.6396, 3.62304, 52.2467, 47.1396, 3.62304, 51.7467, 45.8896, 3.62304, 47.4967, 52.6396, 3.62304, 51.9967, 28.8896, 3.62304, 52.9967, 29.3896, 3.62304, 52.9967, 30.1396, 3.62304, 51.2467, 29.8896, 3.62304, 49.7467, 30.8896, 3.62304, 47.7467, -49.1104, 3.62304, 48.4967, -49.1104, 3.62304, 51.4967, -32.1104, 0.37304, 48.7467, -30.8604, 0.37304, 48.9967, -30.8604, 0.37304, 48.4967, 21.6396, 0.37304, 49.7467, 21.8896, 0.37304, 48.9967, 21.6396, 0.37304, 48.4967, 17.1396, 0.37304, 49.2467, 17.1396, 0.37304, 49.7467, -6.6104, 0.37304, 49.2467, -6.6104, 0.37304, 50.2467, -4.1104, 0.37304, 50.2467, -3.1104, 0.37304, 50.7467, -1.1104, 0.37304, 50.4967, 41.6396, 0.37304, 49.4967, 44.1396, 0.37304, 49.7467, 44.1396, 0.37304, 49.2467, 8.8896, 0.37304, 50.2467, 12.3896, 0.37304, 50.4967, 12.3896, 0.37304, 49.9967, 8.3896, 0.37304, 49.7467, -16.1104, 0.37304, 49.7467, -15.6104, 0.37304, 50.4967, -15.6104, 0.37304, 49.7467, -11.3604, 0.37304, 49.9967, -9.6104, 0.37304, 50.4967, -9.6104, 0.37304, 49.9967)
polygons = [PackedInt32Array(2, 1, 0), PackedInt32Array(2, 0, 3), PackedInt32Array(3, 5, 4), PackedInt32Array(7, 6, 4), PackedInt32Array(4, 6, 3), PackedInt32Array(3, 6, 2), PackedInt32Array(9, 8, 10), PackedInt32Array(10, 8, 11), PackedInt32Array(11, 8, 12), PackedInt32Array(12, 8, 13), PackedInt32Array(13, 8, 15), PackedInt32Array(13, 15, 14), PackedInt32Array(20, 19, 16), PackedInt32Array(16, 19, 17), PackedInt32Array(17, 19, 18), PackedInt32Array(20, 24, 19), PackedInt32Array(19, 24, 21), PackedInt32Array(21, 24, 23), PackedInt32Array(21, 23, 22), PackedInt32Array(26, 25, 27), PackedInt32Array(27, 25, 29), PackedInt32Array(27, 29, 28), PackedInt32Array(32, 31, 30), PackedInt32Array(30, 34, 33), PackedInt32Array(38, 37, 39), PackedInt32Array(39, 37, 36), PackedInt32Array(39, 36, 35), PackedInt32Array(40, 41, 38), PackedInt32Array(38, 41, 37), PackedInt32Array(37, 43, 42), PackedInt32Array(42, 43, 33), PackedInt32Array(33, 43, 30), PackedInt32Array(30, 40, 32), PackedInt32Array(40, 43, 41), PackedInt32Array(41, 43, 37), PackedInt32Array(43, 40, 30), PackedInt32Array(45, 44, 46), PackedInt32Array(47, 46, 44), PackedInt32Array(44, 48, 47), PackedInt32Array(47, 48, 49), PackedInt32Array(52, 51, 50), PackedInt32Array(53, 50, 54), PackedInt32Array(54, 50, 35), PackedInt32Array(54, 35, 36), PackedInt32Array(56, 55, 57), PackedInt32Array(57, 55, 58), PackedInt32Array(58, 55, 50), PackedInt32Array(50, 55, 52), PackedInt32Array(50, 53, 58), PackedInt32Array(61, 60, 59), PackedInt32Array(63, 62, 64), PackedInt32Array(64, 62, 65), PackedInt32Array(65, 66, 64), PackedInt32Array(64, 66, 67), PackedInt32Array(64, 67, 61), PackedInt32Array(64, 61, 59), PackedInt32Array(69, 68, 70), PackedInt32Array(70, 68, 71), PackedInt32Array(71, 68, 72), PackedInt32Array(74, 73, 75), PackedInt32Array(75, 73, 76), PackedInt32Array(75, 76, 70), PackedInt32Array(75, 70, 71), PackedInt32Array(73, 78, 77), PackedInt32Array(80, 79, 81), PackedInt32Array(81, 79, 72), PackedInt32Array(81, 72, 68), PackedInt32Array(73, 77, 76), PackedInt32Array(82, 78, 83), PackedInt32Array(83, 78, 73), PackedInt32Array(86, 85, 84), PackedInt32Array(87, 90, 88), PackedInt32Array(88, 90, 89), PackedInt32Array(89, 90, 91), PackedInt32Array(91, 90, 86), PackedInt32Array(91, 86, 84), PackedInt32Array(91, 84, 73), PackedInt32Array(84, 83, 73), PackedInt32Array(94, 93, 92), PackedInt32Array(96, 95, 94), PackedInt32Array(62, 63, 97), PackedInt32Array(97, 63, 98), PackedInt32Array(94, 92, 96), PackedInt32Array(96, 92, 100), PackedInt32Array(96, 100, 98), PackedInt32Array(96, 98, 99), PackedInt32Array(100, 97, 98), PackedInt32Array(81, 101, 80), PackedInt32Array(80, 101, 104), PackedInt32Array(80, 104, 103), PackedInt32Array(80, 103, 102), PackedInt32Array(103, 104, 67), PackedInt32Array(67, 104, 61), PackedInt32Array(101, 105, 104), PackedInt32Array(108, 107, 106), PackedInt32Array(109, 111, 106), PackedInt32Array(106, 111, 110), PackedInt32Array(106, 110, 108), PackedInt32Array(108, 110, 112), PackedInt32Array(112, 110, 113), PackedInt32Array(113, 110, 114), PackedInt32Array(109, 115, 111), PackedInt32Array(111, 115, 33), PackedInt32Array(111, 33, 34), PackedInt32Array(116, 118, 117), PackedInt32Array(121, 120, 119), PackedInt32Array(96, 122, 95), PackedInt32Array(95, 122, 124), PackedInt32Array(95, 124, 123), PackedInt32Array(121, 119, 125), PackedInt32Array(125, 119, 126), PackedInt32Array(126, 119, 56), PackedInt32Array(56, 119, 55), PackedInt32Array(127, 121, 125), PackedInt32Array(125, 124, 127), PackedInt32Array(127, 124, 122), PackedInt32Array(127, 122, 128), PackedInt32Array(130, 129, 131), PackedInt32Array(131, 129, 132), PackedInt32Array(134, 132, 133), PackedInt32Array(133, 132, 129), PackedInt32Array(140, 139, 135), PackedInt32Array(135, 139, 138), PackedInt32Array(135, 138, 136), PackedInt32Array(136, 138, 137), PackedInt32Array(143, 142, 141), PackedInt32Array(145, 144, 146), PackedInt32Array(146, 144, 147), PackedInt32Array(149, 148, 147), PackedInt32Array(147, 148, 146), PackedInt32Array(151, 150, 152), PackedInt32Array(152, 150, 153), PackedInt32Array(155, 154, 156), PackedInt32Array(156, 154, 157), PackedInt32Array(157, 158, 156), PackedInt32Array(160, 159, 161), PackedInt32Array(161, 159, 162), PackedInt32Array(163, 157, 164), PackedInt32Array(164, 157, 165), PackedInt32Array(165, 161, 164), PackedInt32Array(164, 161, 162), PackedInt32Array(157, 154, 165), PackedInt32Array(168, 167, 166), PackedInt32Array(172, 171, 169), PackedInt32Array(169, 171, 170), PackedInt32Array(171, 172, 173), PackedInt32Array(173, 172, 174), PackedInt32Array(174, 172, 175), PackedInt32Array(178, 177, 179), PackedInt32Array(179, 177, 176), PackedInt32Array(181, 180, 182), PackedInt32Array(182, 180, 183), PackedInt32Array(183, 180, 184), PackedInt32Array(184, 180, 185), PackedInt32Array(178, 179, 186), PackedInt32Array(186, 179, 187), PackedInt32Array(188, 190, 189), PackedInt32Array(187, 191, 186), PackedInt32Array(186, 191, 184), PackedInt32Array(184, 191, 192), PackedInt32Array(192, 191, 193), PackedInt32Array(183, 194, 182), PackedInt32Array(198, 203, 180), PackedInt32Array(180, 203, 185), PackedInt32Array(185, 204, 195), PackedInt32Array(195, 204, 196), PackedInt32Array(196, 202, 199), PackedInt32Array(199, 200, 197), PackedInt32Array(200, 202, 198), PackedInt32Array(198, 201, 203), PackedInt32Array(203, 204, 185), PackedInt32Array(204, 202, 196), PackedInt32Array(202, 200, 199), PackedInt32Array(202, 201, 198), PackedInt32Array(201, 204, 203), PackedInt32Array(204, 201, 202), PackedInt32Array(197, 205, 199), PackedInt32Array(199, 205, 190), PackedInt32Array(199, 190, 188), PackedInt32Array(199, 188, 196), PackedInt32Array(181, 206, 180), PackedInt32Array(184, 192, 183), PackedInt32Array(208, 207, 209), PackedInt32Array(209, 207, 210), PackedInt32Array(210, 207, 211), PackedInt32Array(213, 212, 214), PackedInt32Array(214, 212, 215), PackedInt32Array(217, 216, 211), PackedInt32Array(219, 218, 220), PackedInt32Array(220, 218, 221), PackedInt32Array(217, 211, 222), PackedInt32Array(222, 211, 223), PackedInt32Array(221, 218, 210), PackedInt32Array(210, 218, 224), PackedInt32Array(210, 224, 209), PackedInt32Array(226, 225, 227), PackedInt32Array(227, 225, 228), PackedInt32Array(228, 225, 222), PackedInt32Array(229, 215, 230), PackedInt32Array(230, 215, 223), PackedInt32Array(229, 232, 231), PackedInt32Array(231, 214, 229), PackedInt32Array(229, 214, 215), PackedInt32Array(222, 225, 217), PackedInt32Array(207, 233, 211), PackedInt32Array(211, 233, 223), PackedInt32Array(223, 233, 230), PackedInt32Array(230, 233, 234), PackedInt32Array(234, 235, 230), PackedInt32Array(236, 238, 237), PackedInt32Array(238, 236, 215), PackedInt32Array(215, 236, 241), PackedInt32Array(215, 241, 239), PackedInt32Array(239, 241, 240), PackedInt32Array(239, 223, 215), PackedInt32Array(243, 242, 244), PackedInt32Array(244, 242, 247), PackedInt32Array(244, 247, 246), PackedInt32Array(244, 246, 245), PackedInt32Array(249, 248, 246), PackedInt32Array(246, 248, 245), PackedInt32Array(240, 256, 254), PackedInt32Array(254, 261, 250), PackedInt32Array(250, 260, 251), PackedInt32Array(251, 260, 252), PackedInt32Array(252, 257, 253), PackedInt32Array(253, 256, 240), PackedInt32Array(256, 261, 254), PackedInt32Array(261, 262, 250), PackedInt32Array(250, 262, 260), PackedInt32Array(260, 257, 252), PackedInt32Array(257, 256, 253), PackedInt32Array(256, 255, 261), PackedInt32Array(261, 259, 262), PackedInt32Array(262, 259, 260), PackedInt32Array(260, 258, 257), PackedInt32Array(257, 255, 256), PackedInt32Array(255, 258, 261), PackedInt32Array(261, 263, 259), PackedInt32Array(259, 263, 260), PackedInt32Array(258, 255, 257), PackedInt32Array(260, 263, 258), PackedInt32Array(258, 263, 261), PackedInt32Array(264, 240, 265), PackedInt32Array(265, 240, 254), PackedInt32Array(253, 266, 252), PackedInt32Array(251, 267, 250), PackedInt32Array(245, 251, 244), PackedInt32Array(244, 251, 252), PackedInt32Array(268, 266, 253), PackedInt32Array(270, 269, 252), PackedInt32Array(240, 241, 253), PackedInt32Array(266, 270, 252), PackedInt32Array(197, 200, 271), PackedInt32Array(271, 200, 233), PackedInt32Array(233, 200, 198), PackedInt32Array(233, 198, 234), PackedInt32Array(86, 273, 272), PackedInt32Array(272, 274, 86), PackedInt32Array(86, 274, 275), PackedInt32Array(86, 275, 85), PackedInt32Array(277, 276, 278), PackedInt32Array(278, 276, 279), PackedInt32Array(278, 279, 280), PackedInt32Array(280, 279, 281), PackedInt32Array(281, 279, 275), PackedInt32Array(275, 279, 282), PackedInt32Array(275, 274, 281), PackedInt32Array(285, 284, 283), PackedInt32Array(286, 113, 287), PackedInt32Array(287, 113, 114), PackedInt32Array(287, 114, 288), PackedInt32Array(285, 283, 289), PackedInt32Array(289, 283, 287), PackedInt32Array(289, 287, 288), PackedInt32Array(289, 288, 290), PackedInt32Array(289, 291, 285), PackedInt32Array(292, 291, 293), PackedInt32Array(293, 291, 289), PackedInt32Array(297, 296, 294), PackedInt32Array(294, 296, 295), PackedInt32Array(299, 298, 300), PackedInt32Array(300, 298, 302), PackedInt32Array(300, 302, 301), PackedInt32Array(304, 303, 305), PackedInt32Array(305, 303, 301), PackedInt32Array(305, 301, 302), PackedInt32Array(309, 308, 306), PackedInt32Array(306, 308, 307), PackedInt32Array(311, 310, 312), PackedInt32Array(312, 310, 276), PackedInt32Array(312, 276, 277), PackedInt32Array(311, 312, 313), PackedInt32Array(313, 312, 314), PackedInt32Array(292, 293, 315), PackedInt32Array(315, 293, 316), PackedInt32Array(315, 316, 317), PackedInt32Array(317, 316, 320), PackedInt32Array(317, 320, 319), PackedInt32Array(317, 319, 318), PackedInt32Array(319, 321, 318), PackedInt32Array(323, 322, 324), PackedInt32Array(324, 322, 325), PackedInt32Array(327, 326, 328), PackedInt32Array(328, 326, 329), PackedInt32Array(329, 326, 330), PackedInt32Array(332, 331, 333), PackedInt32Array(333, 331, 334), PackedInt32Array(329, 330, 334), PackedInt32Array(336, 335, 337), PackedInt32Array(337, 335, 331), PackedInt32Array(331, 335, 334), PackedInt32Array(334, 335, 329), PackedInt32Array(341, 340, 338), PackedInt32Array(338, 340, 339), PackedInt32Array(250, 343, 342), PackedInt32Array(342, 344, 250), PackedInt32Array(250, 344, 346), PackedInt32Array(250, 346, 345), PackedInt32Array(250, 345, 254), PackedInt32Array(346, 347, 345), PackedInt32Array(348, 351, 349), PackedInt32Array(349, 351, 350), PackedInt32Array(353, 351, 352), PackedInt32Array(352, 351, 348), PackedInt32Array(356, 355, 354), PackedInt32Array(359, 358, 357), PackedInt32Array(361, 360, 227), PackedInt32Array(362, 361, 363), PackedInt32Array(363, 361, 364), PackedInt32Array(365, 227, 228), PackedInt32Array(364, 361, 265), PackedInt32Array(265, 361, 227), PackedInt32Array(265, 227, 365), PackedInt32Array(265, 365, 264), PackedInt32Array(367, 366, 368), PackedInt32Array(368, 366, 369), PackedInt32Array(368, 369, 372), PackedInt32Array(372, 369, 370), PackedInt32Array(370, 369, 371), PackedInt32Array(373, 375, 374), PackedInt32Array(313, 314, 376), PackedInt32Array(376, 378, 377), PackedInt32Array(379, 375, 377), PackedInt32Array(377, 375, 373), PackedInt32Array(377, 373, 376), PackedInt32Array(376, 373, 313), PackedInt32Array(379, 382, 375), PackedInt32Array(375, 382, 381), PackedInt32Array(375, 381, 380), PackedInt32Array(380, 383, 375), PackedInt32Array(385, 384, 386), PackedInt32Array(386, 384, 387), PackedInt32Array(390, 389, 388), PackedInt32Array(390, 388, 387), PackedInt32Array(387, 388, 386), PackedInt32Array(393, 392, 391), PackedInt32Array(395, 394, 396), PackedInt32Array(396, 394, 391), PackedInt32Array(391, 394, 393), PackedInt32Array(400, 399, 397), PackedInt32Array(397, 399, 398), PackedInt32Array(401, 403, 402), PackedInt32Array(400, 397, 405), PackedInt32Array(405, 397, 404), PackedInt32Array(405, 404, 406), PackedInt32Array(405, 406, 401), PackedInt32Array(406, 403, 401), PackedInt32Array(407, 318, 402), PackedInt32Array(402, 318, 321), PackedInt32Array(404, 408, 406), PackedInt32Array(401, 402, 409), PackedInt32Array(409, 402, 321), PackedInt32Array(411, 410, 412), PackedInt32Array(410, 413, 412), PackedInt32Array(412, 413, 416), PackedInt32Array(412, 416, 415), PackedInt32Array(412, 415, 414), PackedInt32Array(417, 416, 418), PackedInt32Array(418, 416, 413), PackedInt32Array(420, 419, 421), PackedInt32Array(421, 419, 422), PackedInt32Array(424, 423, 425), PackedInt32Array(425, 423, 426), PackedInt32Array(429, 428, 427), PackedInt32Array(219, 220, 430), PackedInt32Array(430, 220, 431), PackedInt32Array(432, 347, 346), PackedInt32Array(434, 433, 435), PackedInt32Array(435, 433, 429), PackedInt32Array(430, 431, 427), PackedInt32Array(427, 431, 437), PackedInt32Array(427, 437, 436), PackedInt32Array(363, 437, 362), PackedInt32Array(362, 437, 431), PackedInt32Array(439, 438, 440), PackedInt32Array(440, 438, 441), PackedInt32Array(442, 435, 429), PackedInt32Array(438, 426, 441), PackedInt32Array(438, 443, 442), PackedInt32Array(445, 444, 425), PackedInt32Array(432, 445, 347), PackedInt32Array(347, 445, 436), PackedInt32Array(429, 455, 442), PackedInt32Array(442, 456, 438), PackedInt32Array(438, 453, 426), PackedInt32Array(426, 457, 445), PackedInt32Array(445, 454, 436), PackedInt32Array(436, 455, 429), PackedInt32Array(455, 459, 442), PackedInt32Array(456, 452, 438), PackedInt32Array(442, 459, 456), PackedInt32Array(453, 457, 426), PackedInt32Array(438, 452, 453), PackedInt32Array(457, 454, 445), PackedInt32Array(454, 447, 436), PackedInt32Array(436, 447, 455), PackedInt32Array(455, 451, 459), PackedInt32Array(456, 451, 452), PackedInt32Array(459, 451, 456), PackedInt32Array(453, 449, 457), PackedInt32Array(452, 448, 453), PackedInt32Array(457, 446, 454), PackedInt32Array(454, 446, 447), PackedInt32Array(447, 450, 455), PackedInt32Array(455, 452, 451), PackedInt32Array(449, 446, 457), PackedInt32Array(453, 458, 449), PackedInt32Array(448, 458, 453), PackedInt32Array(452, 450, 448), PackedInt32Array(446, 449, 447), PackedInt32Array(450, 452, 455), PackedInt32Array(447, 458, 450), PackedInt32Array(458, 447, 449), PackedInt32Array(448, 450, 458), PackedInt32Array(429, 427, 436), PackedInt32Array(445, 425, 426), PackedInt32Array(463, 462, 460), PackedInt32Array(460, 462, 461), PackedInt32Array(465, 464, 466), PackedInt32Array(466, 464, 467), PackedInt32Array(469, 468, 467), PackedInt32Array(467, 468, 466), PackedInt32Array(472, 471, 470), PackedInt32Array(428, 429, 224), PackedInt32Array(224, 429, 473), PackedInt32Array(224, 473, 209), PackedInt32Array(477, 476, 474), PackedInt32Array(474, 476, 475), PackedInt32Array(479, 478, 480), PackedInt32Array(480, 478, 482), PackedInt32Array(480, 482, 481), PackedInt32Array(483, 480, 484), PackedInt32Array(484, 480, 481), PackedInt32Array(487, 486, 485), PackedInt32Array(489, 488, 490), PackedInt32Array(490, 488, 429), PackedInt32Array(492, 491, 487), PackedInt32Array(493, 433, 494), PackedInt32Array(485, 495, 487), PackedInt32Array(487, 495, 492), PackedInt32Array(492, 495, 496), PackedInt32Array(492, 496, 484), PackedInt32Array(493, 492, 484), PackedInt32Array(433, 499, 429), PackedInt32Array(429, 498, 490), PackedInt32Array(490, 498, 481), PackedInt32Array(481, 498, 484), PackedInt32Array(484, 498, 493), PackedInt32Array(493, 499, 433), PackedInt32Array(499, 497, 429), PackedInt32Array(429, 497, 498), PackedInt32Array(498, 499, 493), PackedInt32Array(499, 498, 497), PackedInt32Array(429, 488, 473), PackedInt32Array(501, 500, 502), PackedInt32Array(502, 500, 503), PackedInt32Array(504, 190, 503), PackedInt32Array(503, 190, 502), PackedInt32Array(502, 190, 205), PackedInt32Array(507, 506, 505), PackedInt32Array(381, 508, 380), PackedInt32Array(509, 508, 381), PackedInt32Array(381, 510, 509), PackedInt32Array(509, 510, 511), PackedInt32Array(511, 510, 512), PackedInt32Array(505, 513, 507), PackedInt32Array(507, 513, 516), PackedInt32Array(507, 516, 514), PackedInt32Array(514, 516, 515), PackedInt32Array(520, 515, 516), PackedInt32Array(515, 518, 519), PackedInt32Array(519, 521, 512), PackedInt32Array(512, 521, 511), PackedInt32Array(511, 521, 517), PackedInt32Array(517, 519, 518), PackedInt32Array(518, 515, 520), PackedInt32Array(519, 517, 521), PackedInt32Array(523, 522, 398), PackedInt32Array(526, 525, 524), PackedInt32Array(526, 524, 527), PackedInt32Array(527, 524, 399), PackedInt32Array(399, 524, 398), PackedInt32Array(398, 524, 523), PackedInt32Array(531, 530, 528), PackedInt32Array(528, 530, 529), PackedInt32Array(533, 532, 534), PackedInt32Array(534, 532, 535), PackedInt32Array(535, 532, 537), PackedInt32Array(535, 537, 536), PackedInt32Array(540, 539, 538), PackedInt32Array(542, 541, 543), PackedInt32Array(543, 541, 544), PackedInt32Array(544, 541, 545), PackedInt32Array(547, 546, 541), PackedInt32Array(541, 546, 545), PackedInt32Array(551, 550, 548), PackedInt32Array(548, 550, 549), PackedInt32Array(553, 552, 554), PackedInt32Array(554, 552, 559), PackedInt32Array(554, 559, 558), PackedInt32Array(554, 558, 557), PackedInt32Array(554, 557, 556), PackedInt32Array(554, 556, 555), PackedInt32Array(552, 503, 559), PackedInt32Array(559, 503, 558), PackedInt32Array(558, 503, 557), PackedInt32Array(557, 503, 556), PackedInt32Array(556, 503, 500), PackedInt32Array(562, 561, 560), PackedInt32Array(562, 560, 524), PackedInt32Array(525, 566, 524), PackedInt32Array(524, 566, 562), PackedInt32Array(562, 565, 563), PackedInt32Array(563, 565, 564), PackedInt32Array(565, 566, 525), PackedInt32Array(566, 565, 562), PackedInt32Array(569, 568, 567), PackedInt32Array(570, 572, 571), PackedInt32Array(572, 570, 478), PackedInt32Array(478, 570, 482), PackedInt32Array(482, 570, 573), PackedInt32Array(573, 570, 555), PackedInt32Array(555, 574, 573), PackedInt32Array(555, 567, 554), PackedInt32Array(570, 583, 555), PackedInt32Array(555, 584, 567), PackedInt32Array(567, 575, 569), PackedInt32Array(575, 582, 576), PackedInt32Array(576, 580, 570), PackedInt32Array(583, 584, 555), PackedInt32Array(570, 580, 583), PackedInt32Array(584, 578, 567), PackedInt32Array(567, 578, 575), PackedInt32Array(582, 581, 576), PackedInt32Array(575, 578, 582), PackedInt32Array(576, 581, 580), PackedInt32Array(583, 581, 584), PackedInt32Array(580, 581, 583), PackedInt32Array(584, 577, 578), PackedInt32Array(582, 579, 581), PackedInt32Array(578, 577, 582), PackedInt32Array(581, 579, 584), PackedInt32Array(584, 579, 577), PackedInt32Array(582, 577, 579), PackedInt32Array(588, 587, 585), PackedInt32Array(585, 587, 586), PackedInt32Array(590, 589, 591), PackedInt32Array(591, 589, 592), PackedInt32Array(593, 591, 594), PackedInt32Array(594, 591, 595), PackedInt32Array(595, 591, 592), PackedInt32Array(597, 596, 595), PackedInt32Array(595, 596, 598), PackedInt32Array(595, 598, 594), PackedInt32Array(601, 600, 599), PackedInt32Array(603, 602, 604), PackedInt32Array(604, 602, 605), PackedInt32Array(605, 602, 606), PackedInt32Array(607, 605, 608), PackedInt32Array(608, 605, 606), PackedInt32Array(611, 610, 609), PackedInt32Array(564, 609, 563), PackedInt32Array(615, 614, 612), PackedInt32Array(612, 614, 613), PackedInt32Array(611, 609, 612), PackedInt32Array(612, 609, 564), PackedInt32Array(612, 564, 615), PackedInt32Array(615, 564, 616), PackedInt32Array(618, 617, 619), PackedInt32Array(619, 617, 620), PackedInt32Array(620, 617, 621), PackedInt32Array(623, 622, 621), PackedInt32Array(621, 622, 620), PackedInt32Array(625, 624, 626), PackedInt32Array(626, 624, 627), PackedInt32Array(629, 628, 613), PackedInt32Array(631, 630, 632), PackedInt32Array(632, 630, 614), PackedInt32Array(614, 630, 613), PackedInt32Array(613, 630, 629), PackedInt32Array(634, 633, 635), PackedInt32Array(635, 633, 636), PackedInt32Array(639, 638, 637), PackedInt32Array(641, 640, 576), PackedInt32Array(576, 640, 636), PackedInt32Array(576, 636, 633), PackedInt32Array(576, 633, 575), PackedInt32Array(639, 637, 640), PackedInt32Array(640, 637, 644), PackedInt32Array(640, 644, 643), PackedInt32Array(640, 643, 642), PackedInt32Array(640, 642, 636), PackedInt32Array(648, 647, 645), PackedInt32Array(645, 647, 646), PackedInt32Array(650, 649, 651), PackedInt32Array(651, 649, 654), PackedInt32Array(651, 654, 653), PackedInt32Array(651, 653, 652), PackedInt32Array(655, 513, 656), PackedInt32Array(656, 513, 657), PackedInt32Array(656, 657, 652), PackedInt32Array(656, 652, 653), PackedInt32Array(505, 657, 513), PackedInt32Array(654, 658, 659), PackedInt32Array(659, 658, 661), PackedInt32Array(659, 661, 660), PackedInt32Array(661, 663, 660), PackedInt32Array(660, 663, 664), PackedInt32Array(664, 663, 662), PackedInt32Array(654, 649, 658), PackedInt32Array(666, 496, 665), PackedInt32Array(665, 496, 495), PackedInt32Array(667, 666, 668), PackedInt32Array(668, 666, 665), PackedInt32Array(668, 665, 669), PackedInt32Array(672, 671, 670), PackedInt32Array(670, 673, 672), PackedInt32Array(672, 673, 669), PackedInt32Array(669, 673, 668), PackedInt32Array(668, 673, 674), PackedInt32Array(669, 675, 672), PackedInt32Array(678, 677, 676), PackedInt32Array(680, 679, 681), PackedInt32Array(681, 679, 682), PackedInt32Array(443, 684, 683), PackedInt32Array(683, 685, 443), PackedInt32Array(443, 685, 442), PackedInt32Array(442, 685, 686), PackedInt32Array(686, 685, 687), PackedInt32Array(691, 690, 688), PackedInt32Array(688, 690, 689), PackedInt32Array(695, 694, 692), PackedInt32Array(692, 694, 693), PackedInt32Array(696, 697, 673), PackedInt32Array(673, 697, 674), PackedInt32Array(674, 698, 638), PackedInt32Array(638, 698, 637), PackedInt32Array(637, 697, 696), PackedInt32Array(697, 698, 674), PackedInt32Array(698, 697, 637), PackedInt32Array(699, 702, 700), PackedInt32Array(700, 702, 701), PackedInt32Array(702, 704, 703), PackedInt32Array(705, 703, 706), PackedInt32Array(706, 703, 707), PackedInt32Array(707, 703, 704), PackedInt32Array(710, 709, 708), PackedInt32Array(701, 702, 703), PackedInt32Array(636, 642, 708), PackedInt32Array(708, 642, 643), PackedInt32Array(708, 643, 644), PackedInt32Array(708, 644, 637), PackedInt32Array(696, 713, 637), PackedInt32Array(637, 713, 708), PackedInt32Array(708, 712, 710), PackedInt32Array(710, 712, 703), PackedInt32Array(703, 712, 701), PackedInt32Array(701, 712, 696), PackedInt32Array(696, 712, 713), PackedInt32Array(713, 711, 708), PackedInt32Array(708, 711, 712), PackedInt32Array(712, 711, 713), PackedInt32Array(715, 714, 716), PackedInt32Array(716, 714, 718), PackedInt32Array(716, 718, 717), PackedInt32Array(720, 719, 721), PackedInt32Array(721, 719, 723), PackedInt32Array(721, 723, 722), PackedInt32Array(725, 724, 687), PackedInt32Array(726, 717, 727), PackedInt32Array(729, 735, 685), PackedInt32Array(685, 731, 687), PackedInt32Array(687, 736, 725), PackedInt32Array(725, 736, 728), PackedInt32Array(728, 733, 722), PackedInt32Array(722, 735, 729), PackedInt32Array(735, 732, 685), PackedInt32Array(731, 736, 687), PackedInt32Array(685, 734, 731), PackedInt32Array(736, 733, 728), PackedInt32Array(733, 734, 722), PackedInt32Array(722, 734, 735), PackedInt32Array(732, 734, 685), PackedInt32Array(735, 734, 732), PackedInt32Array(731, 730, 736), PackedInt32Array(734, 733, 731), PackedInt32Array(736, 730, 733), PackedInt32Array(731, 733, 730), PackedInt32Array(716, 717, 729), PackedInt32Array(729, 717, 726), PackedInt32Array(729, 726, 722), PackedInt32Array(722, 737, 728), PackedInt32Array(738, 729, 739), PackedInt32Array(739, 729, 685), PackedInt32Array(726, 721, 722), PackedInt32Array(486, 740, 485), PackedInt32Array(485, 740, 741), PackedInt32Array(743, 742, 741), PackedInt32Array(740, 725, 741), PackedInt32Array(741, 725, 728), PackedInt32Array(741, 728, 744), PackedInt32Array(741, 744, 743), PackedInt32Array(744, 745, 743), PackedInt32Array(747, 746, 748), PackedInt32Array(748, 746, 749), PackedInt32Array(748, 749, 752), PackedInt32Array(752, 749, 750), PackedInt32Array(750, 749, 751), PackedInt32Array(669, 742, 675), PackedInt32Array(675, 742, 743), PackedInt32Array(630, 631, 753), PackedInt32Array(630, 753, 754), PackedInt32Array(754, 753, 755), PackedInt32Array(757, 756, 758), PackedInt32Array(758, 756, 755), PackedInt32Array(755, 756, 759), PackedInt32Array(755, 759, 754), PackedInt32Array(761, 760, 757), PackedInt32Array(757, 760, 756), PackedInt32Array(765, 764, 762), PackedInt32Array(762, 764, 763), PackedInt32Array(769, 768, 766), PackedInt32Array(766, 768, 767), PackedInt32Array(772, 771, 770), PackedInt32Array(776, 775, 777), PackedInt32Array(777, 775, 774), PackedInt32Array(777, 774, 773), PackedInt32Array(781, 780, 778), PackedInt32Array(778, 780, 779), PackedInt32Array(783, 782, 784), PackedInt32Array(784, 782, 785), PackedInt32Array(785, 782, 786), PackedInt32Array(786, 782, 787), PackedInt32Array(788, 785, 789), PackedInt32Array(789, 785, 786), PackedInt32Array(790, 784, 791), PackedInt32Array(791, 784, 785), PackedInt32Array(793, 792, 794), PackedInt32Array(794, 792, 795), PackedInt32Array(797, 796, 798), PackedInt32Array(798, 796, 799), PackedInt32Array(799, 796, 800), PackedInt32Array(803, 802, 801), PackedInt32Array(803, 801, 804), PackedInt32Array(804, 801, 805), PackedInt32Array(805, 801, 806), PackedInt32Array(806, 801, 807), PackedInt32Array(808, 664, 809), PackedInt32Array(809, 664, 804), PackedInt32Array(804, 664, 662), PackedInt32Array(805, 809, 804), PackedInt32Array(807, 810, 806), PackedInt32Array(814, 813, 811), PackedInt32Array(811, 813, 812), PackedInt32Array(816, 815, 817), PackedInt32Array(817, 815, 818), PackedInt32Array(821, 820, 819), PackedInt32Array(822, 821, 819), PackedInt32Array(823, 817, 824), PackedInt32Array(824, 817, 825), PackedInt32Array(825, 817, 818), PackedInt32Array(822, 819, 825), PackedInt32Array(818, 822, 825), PackedInt32Array(827, 826, 828), PackedInt32Array(828, 826, 829), PackedInt32Array(829, 826, 831), PackedInt32Array(829, 831, 830), PackedInt32Array(834, 833, 832), PackedInt32Array(836, 835, 837), PackedInt32Array(837, 835, 838), PackedInt32Array(841, 840, 839), PackedInt32Array(843, 842, 844), PackedInt32Array(844, 842, 845), PackedInt32Array(845, 842, 846), PackedInt32Array(849, 848, 847), PackedInt32Array(847, 850, 849), PackedInt32Array(849, 850, 853), PackedInt32Array(849, 853, 851), PackedInt32Array(851, 853, 852), PackedInt32Array(844, 845, 854), PackedInt32Array(854, 845, 841), PackedInt32Array(841, 839, 854), PackedInt32Array(854, 839, 852), PackedInt32Array(854, 852, 853), PackedInt32Array(854, 853, 855), PackedInt32Array(857, 856, 858), PackedInt32Array(858, 856, 859), PackedInt32Array(861, 860, 862), PackedInt32Array(862, 860, 863), PackedInt32Array(858, 859, 864), PackedInt32Array(864, 859, 863), PackedInt32Array(864, 863, 860), PackedInt32Array(848, 865, 861), PackedInt32Array(862, 866, 861), PackedInt32Array(861, 866, 847), PackedInt32Array(861, 847, 848), PackedInt32Array(868, 867, 869), PackedInt32Array(869, 867, 870), PackedInt32Array(874, 873, 871), PackedInt32Array(871, 873, 872), PackedInt32Array(871, 872, 870), PackedInt32Array(869, 870, 875), PackedInt32Array(875, 870, 872), PackedInt32Array(874, 877, 873), PackedInt32Array(873, 877, 876), PackedInt32Array(878, 876, 877), PackedInt32Array(846, 842, 879), PackedInt32Array(879, 880, 846), PackedInt32Array(846, 880, 881), PackedInt32Array(881, 880, 878), PackedInt32Array(878, 880, 876), PackedInt32Array(878, 882, 881), PackedInt32Array(884, 883, 885), PackedInt32Array(885, 883, 886), PackedInt32Array(886, 883, 887), PackedInt32Array(886, 888, 885), PackedInt32Array(885, 888, 891), PackedInt32Array(885, 891, 889), PackedInt32Array(889, 891, 890), PackedInt32Array(891, 810, 890), PackedInt32Array(890, 810, 807), PackedInt32Array(890, 807, 892), PackedInt32Array(894, 893, 895), PackedInt32Array(895, 893, 896), PackedInt32Array(896, 893, 856), PackedInt32Array(896, 856, 857), PackedInt32Array(896, 897, 895), PackedInt32Array(895, 897, 887), PackedInt32Array(895, 887, 883), PackedInt32Array(868, 899, 867), PackedInt32Array(867, 899, 898), PackedInt32Array(902, 901, 900), PackedInt32Array(904, 903, 905), PackedInt32Array(905, 903, 906), PackedInt32Array(906, 903, 907), PackedInt32Array(910, 909, 908), PackedInt32Array(912, 911, 910), PackedInt32Array(912, 910, 908), PackedInt32Array(915, 914, 913), PackedInt32Array(917, 916, 918), PackedInt32Array(918, 916, 919), PackedInt32Array(922, 921, 920), PackedInt32Array(925, 924, 923)]
agent_height = 1.75
agent_radius = 0.375
agent_max_climb = 0.5
edge_max_length = 12.0
filter_low_hanging_obstacles = true
filter_ledge_spans = true
filter_walkable_low_height_spans = true

[sub_resource type="StandardMaterial3D" id="StandardMaterial3D_cin4e"]
albedo_texture = ExtResource("15_df8b0")
metallic = 0.2
metallic_texture = ExtResource("16_003du")
roughness = 0.8
uv1_scale = Vector3(10, 10, 1)

[sub_resource type="StandardMaterial3D" id="StandardMaterial3D_01ds2"]
transparency = 1
albedo_color = Color(1, 1, 0, 0.3)
emission_enabled = true
emission = Color(1, 1, 0, 1)

[node name="level01" type="Node3D"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 0, 0, 1.94383)
script = ExtResource("1_hk2b6")
minimap_scene = ExtResource("2_j32d5")
player_avatar_scene = ExtResource("3_0f42i")
item_boxes_scene = ExtResource("4_swvig")
skill_box_ui_scene = ExtResource("5_dbohx")

[node name="WorldEnvironment" type="WorldEnvironment" parent="."]
environment = SubResource("Environment_j4i7h")

[node name="DirectionalLight3D" type="DirectionalLight3D" parent="."]
transform = Transform3D(0.707107, -0.5, 0.5, 0, 0.707107, 0.707107, -0.707107, -0.5, 0.5, 0, 0, 0)
light_color = Color(1, 0.95, 0.8, 1)
light_energy = 1.2
shadow_enabled = true
shadow_opacity = 0.6
shadow_blur = 1.5

[node name="ModularLevelGenerator" type="Node3D" parent="."]
script = ExtResource("6_6ly8b")
level_config = ExtResource("7_r5102")

[node name="FogOfWar" parent="." instance=ExtResource("9_003du")]
player_vision_radius = 8.0
enabled = false

[node name="Camera3D" type="Camera3D" parent="."]
unique_name_in_owner = true
transform = Transform3D(1, 0, 0, 0, 0.866025, 0.5, 0, -0.5, 0.866025, 0, 30, 50)
v_offset = 20.0
projection = 1
current = true
size = 44.805
near = 0.022
script = ExtResource("26_camera_follower")
target_path = NodePath("../Player")
follow_speed = 10.0
position_offset = Vector3(0, 10, 55)

[node name="UIManager" type="Node" parent="."]
script = ExtResource("25_ui_manager")
border_width = 100.0

[node name="Player" parent="." instance=ExtResource("11_82jtv")]

[node name="Chest" parent="." instance=ExtResource("14_1ks1q")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, 4.57862, 0, -14.4123)
interaction_distance = 3.0
item_resource = ExtResource("17_j32d5")

[node name="Chest2" parent="." instance=ExtResource("14_1ks1q")]
transform = Transform3D(1.5, 0, 0, 0, 1.5, 0, 0, 0, 1.5, 20, 0.165, -3.779)
interaction_distance = 3.0
item_resource = ExtResource("18_j32d5")

[node name="EnemyBoy01" parent="." instance=ExtResource("19_0f42i")]
transform = Transform3D(0.8, 0, 0, 0, 0.751754, 0.273616, 0, -0.273616, 0.751754, -10.0014, 0, -8.65824)

[node name="Enemy02" parent="." instance=ExtResource("21_dbohx")]
transform = Transform3D(2.1, 0, 0, 0, 1.97335, 0.718242, 0, -0.718242, 1.97335, 7.86917, 0, -7.768)

[node name="NavigationRegion3D" type="NavigationRegion3D" parent="."]
navigation_mesh = SubResource("NavigationMesh_xcdtp")

[node name="Floor" type="Node3D" parent="NavigationRegion3D"]

[node name="@CSGBox3D@165226" type="CSGBox3D" parent="NavigationRegion3D/Floor"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 0, -0.05, 0)
use_collision = true
size = Vector3(100, 0.1, 100)
material = SubResource("StandardMaterial3D_cin4e")

[node name="FloorTiles" type="Node3D" parent="NavigationRegion3D/Floor"]

[node name="Floor01_0" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -19.6672, 0.1, 37.436)

[node name="Floor01_1" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 27.0176, 0.1, 41.3302)

[node name="Floor01_2" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 29.5923, 0.1, -7.53781)

[node name="Floor01_3" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 26.9745, 0.1, -23.2993)

[node name="Floor01_4" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -2.3253, 0.1, -33.1365)

[node name="Floor01_5" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -21.4122, 0.1, -44.5669)

[node name="Floor01_6" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -5.35293, 0.1, 33.199)

[node name="Floor01_7" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -30.3353, 0.1, 45.6097)

[node name="Floor01_8" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -45.1864, 0.1, -18.7147)

[node name="Floor01_9" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -11.3907, 0.1, -2.45987)

[node name="Floor01_10" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -1.67299, 0.1, -13.6863)

[node name="Floor01_11" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 26.5399, 0.1, 29.1067)

[node name="Floor01_12" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -30.3336, 0.1, -44.6752)

[node name="Floor01_13" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 10.3493, 0.1, 8.63257)

[node name="Floor01_14" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 32.0381, 0.1, -16.1196)

[node name="Floor01_15" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 14.2081, 0.1, -23.4394)

[node name="Floor01_16" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -42.6979, 0.1, -7.03577)

[node name="Floor01_17" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -36.2405, 0.1, 38.2818)

[node name="Floor01_18" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -40.3902, 0.1, 31.1488)

[node name="Floor01_19" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -11.8841, 0.1, 15.1688)

[node name="Floor01_20" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -33.5939, 0.1, -25.1634)

[node name="Floor01_21" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -39.6891, 0.1, -36.8046)

[node name="Floor01_22" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -11.382, 0.1, 26.4388)

[node name="Floor01_23" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -21.4726, 0.1, -8.99775)

[node name="Floor01_24" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 37.3783, 0.1, 43.8758)

[node name="Floor01_25" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -20.2708, 0.1, 22.8285)

[node name="Floor01_26" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -7.59179, 0.1, -20.8784)

[node name="Floor01_27" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -27.3734, 0.1, -36.4384)

[node name="Floor01_28" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -33.9334, 0.1, -5.20769)

[node name="Floor01_29" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 22.9687, 0.1, 7.4628)

[node name="Floor01_30" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -11.6706, 0.1, -41.0289)

[node name="Floor01_31" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -37.5051, 0.1, 15.984)

[node name="Floor01_32" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 11.7983, 0.1, 44.6695)

[node name="Floor01_33" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 46.0795, 0.1, 40.781)

[node name="Floor01_34" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -43.1624, 0.1, -44.522)

[node name="Floor01_35" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 32.5972, 0.1, -39.9659)

[node name="Floor01_36" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 4.38513, 0.1, 33.7924)

[node name="Floor01_37" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -3.87179, 0.1, 20.9503)

[node name="Floor01_38" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 9.5581, 0.1, -8.41562)

[node name="Floor01_39" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 43.6223, 0.1, 6.1257)

[node name="Floor01_40" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -22.156, 0.1, -17.511)

[node name="Floor01_41" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 10.2511, 0.1, -42.507)

[node name="Floor01_42" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 19.0224, 0.1, -44.2598)

[node name="Floor01_43" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 33.2085, 0.1, 11.9563)

[node name="Floor01_44" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -0.72127, 0.1, 43.6889)

[node name="Floor01_45" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -25.8541, 0.1, 30.567)

[node name="Floor01_46" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 6.5228, 0.1, -34.783)

[node name="Floor01_47" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 24.2959, 0.1, 16.186)

[node name="Floor01_48" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -44.9706, 0.1, 7.4775)

[node name="Floor01_49" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 20.0809, 0.1, -8.25863)

[node name="Floor01_50" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 42.8729, 0.1, 16.6934)

[node name="Floor01_51" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 29.1612, 0.1, -31.1371)

[node name="Floor01_52" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 3.73895, 0.1, -21.898)

[node name="Floor01_53" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -29.6372, 0.1, 19.6319)

[node name="Floor01_54" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -2.12014, 0.1, -0.417799)

[node name="Floor01_55" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 15.9553, 0.1, 35.0785)

[node name="Floor01_56" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 39.7204, 0.1, 30.9825)

[node name="Floor01_57" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -9.11544, 0.1, 44.4936)

[node name="Floor01_58" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 1.02961, 0.1, 13.8198)

[node name="Floor01_59" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -10.6523, 0.1, 6.51001)

[node name="Floor01_60" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 38.9503, 0.1, -34.1183)

[node name="Floor01_61" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -13.87, 0.1, -29.4274)

[node name="Floor01_62" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 45.031, 0.1, -8.45239)

[node name="Floor01_63" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 14.4748, 0.1, -33.8441)

[node name="Floor01_64" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 45.3266, 0.1, -19.4146)

[node name="Floor01_65" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -10.5862, 0.1, -11.8082)

[node name="Floor01_66" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -32.1087, 0.1, 3.89882)

[node name="Floor01_67" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 10.3026, 0.1, 24.4003)

[node name="Floor01_68" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 42.7748, 0.1, -42.7449)

[node name="Floor01_69" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 10.4601, 0.1, -0.297115)

[node name="Floor01_70" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -43.1549, 0.1, -27.8233)

[node name="Floor01_71" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -19.1593, 0.1, -36.7483)

[node name="Floor01_72" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -44.3993, 0.1, 45.3387)

[node name="Floor01_73" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 24.0855, 0.1, -37.8373)

[node name="Floor01_74" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 37.251, 0.1, -2.83914)

[node name="Floor01_75" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -44.2933, 0.1, 22.938)

[node name="wall_0" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(0.809441, 0, -0.163244, 0, 1, 0, 0.165696, 0, 0.797465, -47.5, 0, -50)

[node name="wall_1" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(0.8465, 0, -0.0276837, 0, 1, 0, 0.0236722, 0, 0.989948, -42.5, 0, -50)

[node name="wall_2" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1.28094, 0, 0.307596, 0, 1, 0, -0.330079, 0, 1.1937, -35, 0, -50)

[node name="wall_3" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1.00476, 0.0259303, 0.256055, 0, 0.995677, -0.0855638, -0.292112, 0.0891912, 0.88074, -27.5, 0.437718, -50)

[node name="wall_4" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(0.921842, 0, 0.315334, 0, 1, 0, -0.301343, 0, 0.964642, -20, 0, -50)

[node name="wall_5" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(0.84179, 0, -0.257398, 0, 1, 0, 0.277436, 0, 0.78099, -12.5, 0, -50)

[node name="wall_6" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(0.836466, 0, -0.209363, 0, 1, 0, 0.199665, 0, 0.877095, -5, 0, -50)

[node name="wall_7" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1.02848, 0.00568313, -0.302509, 0, 0.999822, 0.0189094, 0.325282, -0.0179689, 0.956475, 2.5, 0.305688, -50)

[node name="wall_8" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1.10031, 0, 0.211089, 0, 1, 0, -0.291431, 0, 0.796975, 10, 0, -50)

[node name="wall_9" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1.26851, 0, -0.412984, 0, 1, 0, 0.450012, 0, 1.16413, 17.5, 0, -50)

[node name="wall_10" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(0.864294, 0, -0.136057, 0, 1, 0, 0.100302, 0, 1.17238, 25, 0, -50)

[node name="wall_11" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1.05889, 0, -0.265807, 0, 1, 0, 0.337352, 0, 0.834321, 32.5, 0, -50)

[node name="wall_12" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1.06719, 0, 0.249605, 0, 1, 0, -0.302836, 0, 0.879605, 40, 0, -50)

[node name="wall_13" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1.1129, 0, 0.0410179, 0, 1, 0, -0.0438791, 0, 1.04033, 47.5, 0, -50)

[node name="wall_14" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(0.808064, 0, 0.0177637, 0, 1, 0, -0.0172086, 0, 0.834127, -47.5, 0, 50)

[node name="wall_15" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1.14749, 0, -0.00887475, 0, 1, 0, 0.0119348, 0, 0.85327, -42.5, 0, 50)

[node name="wall_16" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1.04477, 0, 0.271359, 0, 1, 0, -0.307721, 0, 0.921315, -35, 0, 50)

[node name="wall_17" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1.00383, 0, -0.234818, 0, 1, 0, 0.255375, 0, 0.92303, -27.5, 0, 50)

[node name="wall_18" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1.39101, 0, -0.145567, 0, 1, 0, 0.152797, 0, 1.32519, -20, 0, 50)

[node name="wall_19" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(0.865574, 0, -0.191523, 0, 1, 0, 0.154532, 0, 1.07277, -12.5, 0, 50)

[node name="wall_20" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1.34094, 0, -0.306037, 0, 1, 0, 0.313803, 0, 1.30775, -5, 0, 50)

[node name="wall_21" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(0.899593, 0, -0.136393, 0, 1, 0, 0.138932, 0, 0.883151, 2.5, 0, 50)

[node name="wall_22" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1.09028, 0, -0.201104, 0, 1, 0, 0.191929, 0, 1.1424, 10, 0, 50)

[node name="wall_23" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1.07901, -0.0154817, 0.173287, 0, 0.995085, 0.110299, -0.170795, -0.0978068, 1.09475, 17.5, 0.338457, 50)

[node name="wall_24" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(0.944384, 0, -0.24614, 0, 1, 0, 0.273336, 0, 0.850421, 25, 0, 50)

[node name="wall_25" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1.02816, 0, -0.257812, 0, 1, 0, 0.298085, 0, 0.889248, 32.5, 0, 50)

[node name="wall_26" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1.18445, 0, 0.047416, 0, 1, 0, -0.0480314, 0, 1.16927, 40, 0, 50)

[node name="wall_27" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1.00805, 0, -0.0618997, 0, 1, 0, 0.067632, 0, 0.92261, 47.5, 0, 50)

[node name="wall_28" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(0.253741, 0, 0.82048, 0, 1, 0, -0.853536, 0, 0.243914, -50, 0, -45)

[node name="wall_29" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(0.380567, 0, 1.29742, 0, 1, 0, -1.25878, 0, 0.392249, -50, 0, -37.5)

[node name="wall_30" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(0.0386061, 0, 0.95585, 0, 1, 0, -0.931981, 0, 0.0395948, -50, 0, -30)

[node name="wall_31" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-0.186975, 0, 1.34217, 0, 1, 0, -1.23326, 0, -0.203486, -50, 0, -22.5)

[node name="wall_32" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(0.0866199, 0, 1.18524, 0, 1, 0, -0.851196, 0, 0.120613, -50, 0, -15)

[node name="wall_33" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-0.0883569, 0, 1.00989, 0, 1, 0, -0.816806, 0, -0.109243, -50, 0, -7.5)

[node name="wall_34" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-0.237787, 0.0887453, 1.06622, 0, 0.995828, -0.100457, -0.993863, -0.0212328, -0.255099, -50, 0.333494, 0)

[node name="wall_35" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-0.215591, 0, 0.961528, 0, 1, 0, -0.976334, 0, -0.212322, -50, 0, 7.5)

[node name="wall_36" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-0.151552, 0, 1.13246, 0, 1, 0, -0.814116, 0, -0.210813, -50, 0, 15)

[node name="wall_37" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-0.44104, 0, 1.31977, 0, 1, 0, -1.26478, 0, -0.460215, -50, 0, 22.5)

[node name="wall_38" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(0.0620591, 0, 1.20536, 0, 1, 0, -1.39477, 0, 0.0536311, -50, 0, 30)

[node name="wall_39" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-0.0660928, 0, 1.33753, 0, 1, 0, -1.34277, 0, -0.0658348, -50, 0, 37.5)

[node name="wall_40" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-0.168154, 0, 1.05037, 0, 1, 0, -1.04758, 0, -0.168601, -50, 0, 45)

[node name="wall_41" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-0.318846, 0, 1.27009, 0, 1, 0, -1.34316, 0, -0.3015, 50, 0, -45)

[node name="wall_42" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-0.451787, 0, 1.26874, 0, 1, 0, -1.2954, 0, -0.442488, 50, 0, -37.5)

[node name="wall_43" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(0.201883, 0.0211798, 0.929886, 0, 0.999764, -0.0207333, -0.881217, 0.00485219, 0.213033, 50, 0.336469, -30)

[node name="wall_44" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-0.0521186, 0, 0.841257, 0, 1, 0, -0.973651, 0, -0.0450317, 50, 0, -22.5)

[node name="wall_45" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(0.11596, 0, 1.17668, 0, 1, 0, -0.841841, 0, 0.162082, 50, 0, -15)

[node name="wall_46" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(0.0580535, 0, 1.1707, 0, 1, 0, -0.975069, 0, 0.069701, 50, 0, -7.5)

[node name="wall_47" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(0.165938, -0.044183, 1.07747, 0, 0.998993, 0.049121, -0.945749, -0.00775221, 0.18905, 50, 0.356699, 0)

[node name="wall_48" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-0.0729739, 0, 1.13634, 0, 1, 0, -1.12619, 0, -0.0736312, 50, 0, 7.5)

[node name="wall_49" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(0.167665, 0.0234688, 1.06061, 0, 0.999716, -0.0257174, -0.92439, 0.00425675, 0.192373, 50, 0.404436, 15)

[node name="wall_50" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-0.301469, 0, 0.926147, 0, 1, 0, -1.03943, 0, -0.268613, 50, 0, 22.5)

[node name="wall_51" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-0.279241, 0, 1.20079, 0, 1, 0, -1.32687, 0, -0.252708, 50, 0, 30)

[node name="wall_52" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(0.238593, 0, 1.26329, 0, 1, 0, -1.21746, 0, 0.247573, 50, 0, 37.5)

[node name="wall_53" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(0.29143, 0, 1.3601, 0, 1, 0, -1.34396, 0, 0.29493, 50, 0, 45)

[node name="tree_0" type="Node3D" parent="NavigationRegion3D" instance=ExtResource("15_003du")]
transform = Transform3D(2.5, -0.00145593, -0.00167976, 0.00145725, 2.5, 0.00196474, 0.00167861, -0.00196571, 2.5, -18.69, 0, 0.404652)
script = ExtResource("18_oh2bt")
highlight_material = SubResource("StandardMaterial3D_01ds2")
preset_burnt_out_type = 1
progress_bar_scene = ExtResource("19_fcnrs")

[node name="PatrolPoint_0" type="Node3D" parent="NavigationRegion3D"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -15.7241, 1, 3.51459)
script = ExtResource("15_6ly8b")

[node name="tree_1" type="Node3D" parent="NavigationRegion3D" instance=ExtResource("15_003du")]
transform = Transform3D(2.5, -0.00145593, -0.00167976, 0.00145725, 2.5, 0.00196474, 0.00167861, -0.00196571, 2.5, 5.362, 0, 19.358)
script = ExtResource("18_oh2bt")
highlight_material = SubResource("StandardMaterial3D_01ds2")
progress_bar_scene = ExtResource("19_fcnrs")

[node name="PatrolPoint_1" type="Node3D" parent="NavigationRegion3D"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -0.341977, 1, 22.5675)
script = ExtResource("15_6ly8b")
point_id = 1

[node name="tree_2" type="Node3D" parent="NavigationRegion3D" instance=ExtResource("15_003du")]
transform = Transform3D(2.5, -0.00145593, -0.00167976, 0.00145725, 2.5, 0.00196474, 0.00167861, -0.00196571, 2.5, 7.81569, 0, -14.8398)
script = ExtResource("18_oh2bt")
highlight_material = SubResource("StandardMaterial3D_01ds2")
progress_bar_scene = ExtResource("19_fcnrs")

[node name="PatrolPoint_2" type="Node3D" parent="NavigationRegion3D"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 7.10227, 1, -9.95745)
script = ExtResource("15_6ly8b")
point_id = 2

[node name="tree_3" type="Node3D" parent="NavigationRegion3D" instance=ExtResource("15_003du")]
transform = Transform3D(2.5, -0.00145593, -0.00167976, 0.00145725, 2.5, 0.00196474, 0.00167861, -0.00196571, 2.5, -17.1467, 0, 16.9226)
script = ExtResource("18_oh2bt")
highlight_material = SubResource("StandardMaterial3D_01ds2")
progress_bar_scene = ExtResource("19_fcnrs")

[node name="PatrolPoint_3" type="Node3D" parent="NavigationRegion3D"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -13.1241, 1, 16.6165)
script = ExtResource("15_6ly8b")
point_id = 3

[node name="chest_0" type="Area3D" parent="NavigationRegion3D" instance=ExtResource("14_1ks1q")]
transform = Transform3D(-0.367394, 0, -1.96597, 0, 2, 0, 1.96597, 0, -0.367394, 17.4699, 0, -16.498)
collision_layer = 8
collision_mask = 16
script = ExtResource("21_1ks1q")
interaction_distance = 1.5
item_resource = ExtResource("22_xcdtp")
unlock_sound = ExtResource("23_cin4e")

[node name="rock_0" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("21_xcdtp")]
transform = Transform3D(-0.182366, 0, -0.800692, 0, 0.821198, 0, 0.800692, 0, -0.182366, 36.0624, 0.01, -26.2987)
collision_layer = 2
collision_mask = 0

[node name="rock_1" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("21_xcdtp")]
transform = Transform3D(0.508414, 0, -0.77789, 0, 0.9293, 0, 0.77789, 0, 0.508414, -28.3198, 0.01, -28.1377)
collision_layer = 2
collision_mask = 0

[node name="rock_2" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("21_xcdtp")]
transform = Transform3D(-1.01629, 0, -0.245806, 0, 1.04559, 0, 0.245806, 0, -1.01629, 33.7608, 0.01, 29.8373)
collision_layer = 2
collision_mask = 0

[node name="rock_3" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("21_xcdtp")]
transform = Transform3D(0.522226, 0, 0.850302, 0, 0.997865, 0, -0.850302, 0, 0.522226, 32.3764, 0.01, -1.11675)
collision_layer = 2
collision_mask = 0

[node name="rock_4" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("21_xcdtp")]
transform = Transform3D(1.08304, 0, -0.310195, 0, 1.12659, 0, 0.310195, 0, 1.08304, 32.0601, 0.01, -26.2527)
collision_layer = 2
collision_mask = 0

[node name="rock_5" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("21_xcdtp")]
transform = Transform3D(0.447146, 0, 1.02209, 0, 1.11562, 0, -1.02209, 0, 0.447146, -32.6621, 0.01, 11.4739)
collision_layer = 2
collision_mask = 0

[node name="rock_6" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("21_xcdtp")]
transform = Transform3D(0.00117832, 0, 0.902554, 0, 0.902555, 0, -0.902554, 0, 0.00117832, 17.1938, 0.01, 23.5711)
collision_layer = 2
collision_mask = 0

[node name="rock_7" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("21_xcdtp")]
transform = Transform3D(-0.395154, 0, -0.698922, 0, 0.802894, 0, 0.698922, 0, -0.395154, 19.4298, 0.01, 11.84)
collision_layer = 2
collision_mask = 0

[node name="rock_8" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("21_xcdtp")]
transform = Transform3D(-1.1147, 0, -0.0374759, 0, 1.11533, 0, 0.0374759, 0, -1.1147, -18.8173, 0.01, -13.5896)
collision_layer = 2
collision_mask = 0

[node name="rock_9" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("21_xcdtp")]
transform = Transform3D(-0.561989, 0, 0.607618, 0, 0.827667, 0, -0.607618, 0, -0.561989, -24.4693, 0.01, -28.5598)
collision_layer = 2
collision_mask = 0

[node name="rock_10" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("21_xcdtp")]
transform = Transform3D(-0.417277, 0, 0.775468, 0, 0.880608, 0, -0.775468, 0, -0.417277, 30.6316, 0.01, 5.98138)
collision_layer = 2
collision_mask = 0

[node name="rock_11" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("21_xcdtp")]
transform = Transform3D(-0.67557, 0, -0.668476, 0, 0.950397, 0, 0.668476, 0, -0.67557, -40.2477, 0.01, 2.55538)
collision_layer = 2
collision_mask = 0

[node name="rock_12" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("21_xcdtp")]
transform = Transform3D(-0.448945, 0, 0.746687, 0, 0.87126, 0, -0.746687, 0, -0.448945, 8.17674, 0.01, -18.5802)
collision_layer = 2
collision_mask = 0

[node name="rock_13" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("21_xcdtp")]
transform = Transform3D(0.101239, 0, 1.02856, 0, 1.03353, 0, -1.02856, 0, 0.101239, -22.3782, 0.01, 10.5008)
collision_layer = 2
collision_mask = 0

[node name="rock_14" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("21_xcdtp")]
transform = Transform3D(0.930038, 0, -0.228635, 0, 0.957729, 0, 0.228635, 0, 0.930038, 16.4167, 0.01, 27.01)
collision_layer = 2
collision_mask = 0

[node name="barrel_0" type="StaticBody3D" parent="NavigationRegion3D" groups=["destructible"] instance=ExtResource("22_cin4e")]
transform = Transform3D(-0.552018, 0, -1.02131, 0, 1.16095, 0, 1.02131, 0, -0.552018, 8.66614, 0, 18.9335)
collision_layer = 3
collision_mask = 3

[node name="barrel_1" type="StaticBody3D" parent="NavigationRegion3D" groups=["destructible"] instance=ExtResource("22_cin4e")]
transform = Transform3D(0.247012, 0, 1.14264, 0, 1.16903, 0, -1.14264, 0, 0.247012, 10.0278, 0, -27.6311)
collision_layer = 3
collision_mask = 3

[node name="decoration_0" type="Node3D" parent="NavigationRegion3D" instance=ExtResource("23_6j2fk")]
transform = Transform3D(-0.863233, 0, -1.02479, 0, 1.33991, 0, 1.02479, 0, -0.863233, 24.283, 0, -2.33731)

[node name="decoration_1" type="Node3D" parent="NavigationRegion3D" instance=ExtResource("23_6j2fk")]
transform = Transform3D(0.289418, 0, 0.672147, 0, 0.731809, 0, -0.672147, 0, 0.289418, 34.0962, 0, 5.23989)

[node name="decoration_2" type="Node3D" parent="NavigationRegion3D" instance=ExtResource("23_6j2fk")]
transform = Transform3D(-0.866022, 0, 0.149751, 0, 0.878874, 0, -0.149751, 0, -0.866022, 9.67588, 0, 37.2354)

[node name="decoration_3" type="Node3D" parent="NavigationRegion3D" instance=ExtResource("23_6j2fk")]
transform = Transform3D(0.389936, 0, 0.901187, 0, 0.981931, 0, -0.901187, 0, 0.389936, -34.2911, 0, -18.1471)

[node name="decoration_4" type="Node3D" parent="NavigationRegion3D" instance=ExtResource("23_6j2fk")]
transform = Transform3D(0.476139, 0, 1.18402, 0, 1.27617, 0, -1.18402, 0, 0.476139, 36.0774, 0, 6.01395)

[node name="decoration_5" type="Node3D" parent="NavigationRegion3D" instance=ExtResource("23_6j2fk")]
transform = Transform3D(1.22698, 0, -0.209532, 0, 1.24474, 0, 0.209532, 0, 1.22698, -31.9545, 0, 28.3085)

[node name="decoration_6" type="Node3D" parent="NavigationRegion3D" instance=ExtResource("23_6j2fk")]
transform = Transform3D(-0.162658, 0, 0.585588, 0, 0.607759, 0, -0.585588, 0, -0.162658, 20.8899, 0, -33.1439)

[node name="decoration_7" type="Node3D" parent="NavigationRegion3D" instance=ExtResource("23_6j2fk")]
transform = Transform3D(-0.678443, 0, 0.19975, 0, 0.707237, 0, -0.19975, 0, -0.678443, 1.3621, 0, -44.2599)

[node name="decoration_8" type="Node3D" parent="NavigationRegion3D" instance=ExtResource("23_6j2fk")]
transform = Transform3D(0.858342, 0, 0.625652, 0, 1.06216, 0, -0.625652, 0, 0.858342, 4.66018, 0, -1.46936)

[node name="decoration_9" type="Node3D" parent="NavigationRegion3D" instance=ExtResource("23_6j2fk")]
transform = Transform3D(0.622471, 0, -0.675013, 0, 0.918212, 0, 0.675013, 0, 0.622471, 42.6877, 0, -13.6361)

[node name="decoration_10" type="Node3D" parent="NavigationRegion3D" instance=ExtResource("23_6j2fk")]
transform = Transform3D(0.380576, 0, 1.04916, 0, 1.11606, 0, -1.04916, 0, 0.380576, 7.94599, 0, 40.9141)

[node name="decoration_11" type="Node3D" parent="NavigationRegion3D" instance=ExtResource("23_6j2fk")]
transform = Transform3D(1.07167, 0, 0.273098, 0, 1.10592, 0, -0.273098, 0, 1.07167, -22.9843, 0, -30.7667)

[node name="decoration_12" type="Node3D" parent="NavigationRegion3D" instance=ExtResource("23_6j2fk")]
transform = Transform3D(-0.733954, 0, 0.432088, 0, 0.851698, 0, -0.432088, 0, -0.733954, -27.8622, 0, 40.5541)

[node name="decoration_13" type="Node3D" parent="NavigationRegion3D" instance=ExtResource("23_6j2fk")]
transform = Transform3D(-0.593786, 0, -0.0923292, 0, 0.600921, 0, 0.0923292, 0, -0.593786, 33.0125, 0, 22.6105)

[node name="decoration_14" type="Node3D" parent="NavigationRegion3D" instance=ExtResource("23_6j2fk")]
transform = Transform3D(0.425735, 0, -0.729484, 0, 0.844628, 0, 0.729484, 0, 0.425735, -4.95911, 0, 5.95074)

[node name="decoration_15" type="Node3D" parent="NavigationRegion3D" instance=ExtResource("23_6j2fk")]
transform = Transform3D(0.303231, 0, -0.598578, 0, 0.671003, 0, 0.598578, 0, 0.303231, 27.9512, 0, 20.6945)

[node name="decoration_16" type="Node3D" parent="NavigationRegion3D" instance=ExtResource("23_6j2fk")]
transform = Transform3D(1.05292, 0, 0.423094, 0, 1.13475, 0, -0.423094, 0, 1.05292, -34.4368, 0, 21.5808)

[node name="decoration_17" type="Node3D" parent="NavigationRegion3D" instance=ExtResource("23_6j2fk")]
transform = Transform3D(-1.18987, 0, 0.123312, 0, 1.19624, 0, -0.123312, 0, -1.18987, 5.36602, 0, 0.802782)

[node name="decoration_18" type="Node3D" parent="NavigationRegion3D" instance=ExtResource("23_6j2fk")]
transform = Transform3D(-0.895232, 0, 0.294479, 0, 0.942421, 0, -0.294479, 0, -0.895232, 16.4948, 0, 5.61412)

[node name="decoration_19" type="Node3D" parent="NavigationRegion3D" instance=ExtResource("23_6j2fk")]
transform = Transform3D(-0.212075, 0, 0.767105, 0, 0.795881, 0, -0.767105, 0, -0.212075, 40.3173, 0, -21.209)

[node name="PatrolPointManager" type="Node" parent="."]
script = ExtResource("24_j2uky")
