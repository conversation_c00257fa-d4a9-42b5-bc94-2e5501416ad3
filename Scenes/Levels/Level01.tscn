[gd_scene load_steps=30 format=3 uid="uid://c0rj4dwj18nv8"]

[ext_resource type="Script" uid="uid://bo0h8t35yls55" path="res://Scripts/Levels/Level01.gd" id="1_hk2b6"]
[ext_resource type="PackedScene" uid="uid://d2xyw4d37ymv4" path="res://Scenes/Minimap.tscn" id="2_j32d5"]
[ext_resource type="PackedScene" uid="uid://ctxvyh1qr52ue" path="res://Scenes/PlayerAvatar.tscn" id="3_0f42i"]
[ext_resource type="PackedScene" uid="uid://do1sypgxbecbd" path="res://Scenes/ItemBoxes.tscn" id="4_swvig"]
[ext_resource type="PackedScene" uid="uid://b4l4phqqbvprm" path="res://Scenes/SkillBoxUI.tscn" id="5_dbohx"]
[ext_resource type="Script" uid="uid://f54r1khulcvh" path="res://Scripts/ModularLevelGenerator.gd" id="6_6ly8b"]
[ext_resource type="Resource" uid="uid://cvji54t3pjwfe" path="res://Resources/LevelConfigs/AllModulesConfig.tres" id="7_r5102"]
[ext_resource type="PackedScene" uid="uid://d2b5h1mlxxy7d" path="res://Scenes/FogOfWar.tscn" id="9_003du"]
[ext_resource type="PackedScene" uid="uid://b88l8pk1ebe1x" path="res://Scenes/player.tscn" id="11_82jtv"]
[ext_resource type="PackedScene" uid="uid://c3h8fj2xsp5oy" path="res://Scenes/Prefabs/Chest.tscn" id="14_1ks1q"]
[ext_resource type="PackedScene" uid="uid://dwusy8dd8usvo" path="res://Scenes/Prefabs/Wall.tscn" id="14_df8b0"]
[ext_resource type="PackedScene" uid="uid://crtnthqkksmri" path="res://Scenes/Prefabs/Tree.tscn" id="15_003du"]
[ext_resource type="Script" uid="uid://c3tr23vwvnmwf" path="res://Scripts/PatrolPoint.gd" id="15_6ly8b"]
[ext_resource type="Texture2D" uid="uid://c2ny0yi07rvcf" path="res://Environment/Floor/Floor01_Rocks_BaseColor.png" id="15_df8b0"]
[ext_resource type="Texture2D" uid="uid://dhfikoo16s5n0" path="res://Environment/Floor/Rocks_Metallic.png" id="16_003du"]
[ext_resource type="PackedScene" uid="uid://ddttv643pel23" path="res://Environment/Floor/Floor01_Custom.tscn" id="17_2bvpm"]
[ext_resource type="Resource" path="res://Resources/Items/Trap.tres" id="17_j32d5"]
[ext_resource type="Resource" path="res://Resources/Items/Torch.tres" id="18_j32d5"]
[ext_resource type="PackedScene" uid="uid://rvgn0irsuwao" path="res://Scenes/EnemyBoy01.tscn" id="19_0f42i"]
[ext_resource type="PackedScene" uid="uid://ervckea7fk57" path="res://Scenes/Enemy02.tscn" id="21_dbohx"]
[ext_resource type="PackedScene" uid="uid://b5dqjsb63wbhl" path="res://Scenes/Prefabs/Rock.tscn" id="21_xcdtp"]
[ext_resource type="PackedScene" uid="uid://bdq3b4e0mlgo4" path="res://Scenes/Prefabs/Barrel.tscn" id="22_cin4e"]
[ext_resource type="PackedScene" uid="uid://c6k7j3t3flhst" path="res://Scenes/Prefabs/Decoration.tscn" id="23_6j2fk"]
[ext_resource type="Script" uid="uid://de18ote8otj6u" path="res://Scripts/PatrolPointManager.gd" id="24_j2uky"]

[sub_resource type="ProceduralSkyMaterial" id="ProceduralSkyMaterial_gbvua"]
sky_top_color = Color(0.2, 0.4, 0.8, 1)
sky_horizon_color = Color(0.5, 0.7, 0.9, 1)
ground_bottom_color = Color(0.3, 0.5, 0.7, 1)
ground_horizon_color = Color(0.5, 0.7, 0.9, 1)

[sub_resource type="Sky" id="Sky_m0r78"]
sky_material = SubResource("ProceduralSkyMaterial_gbvua")

[sub_resource type="Environment" id="Environment_j4i7h"]
background_mode = 2
sky = SubResource("Sky_m0r78")
ambient_light_source = 3
ambient_light_color = Color(0.85, 0.85, 0.9, 1)
ambient_light_energy = 0.4
tonemap_mode = 2
ssao_enabled = true
glow_enabled = true
fog_enabled = true
fog_light_color = Color(0.85, 0.9, 1, 1)
fog_density = 0.001

[sub_resource type="NavigationMesh" id="NavigationMesh_fcnrs"]
agent_height = 1.75
agent_radius = 0.375
agent_max_climb = 0.5
edge_max_length = 12.0
filter_low_hanging_obstacles = true
filter_ledge_spans = true
filter_walkable_low_height_spans = true

[sub_resource type="StandardMaterial3D" id="StandardMaterial3D_1ks1q"]
albedo_texture = ExtResource("15_df8b0")
metallic = 0.2
metallic_texture = ExtResource("16_003du")
roughness = 0.8
uv1_scale = Vector3(10, 10, 1)

[node name="level01" type="Node3D"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 0, 0, 1.94383)
script = ExtResource("1_hk2b6")
minimap_scene = ExtResource("2_j32d5")
player_avatar_scene = ExtResource("3_0f42i")
item_boxes_scene = ExtResource("4_swvig")
skill_box_ui_scene = ExtResource("5_dbohx")

[node name="WorldEnvironment" type="WorldEnvironment" parent="."]
environment = SubResource("Environment_j4i7h")

[node name="DirectionalLight3D" type="DirectionalLight3D" parent="."]
transform = Transform3D(0.999999, 0, 0, 0, 0.5, 0.866026, 0, -0.866025, 0.5, 0, 0, 0)
light_color = Color(1, 0.868175, 0.323518, 1)
light_energy = 1.5
shadow_enabled = true
shadow_opacity = 0.7
shadow_blur = 2.0

[node name="ModularLevelGenerator" type="Node3D" parent="."]
script = ExtResource("6_6ly8b")
level_config = ExtResource("7_r5102")

[node name="FogOfWar" parent="." instance=ExtResource("9_003du")]
player_vision_radius = 8.0
enabled = false

[node name="Camera3D" type="Camera3D" parent="."]
unique_name_in_owner = true
transform = Transform3D(-4.37114e-08, 0.965926, -0.258819, 0, 0.258819, 0.965926, 1, 4.2222e-08, -1.13133e-08, -10, 30.125, 0)
v_offset = -10.0
current = true
fov = 65.0
far = 90.3

[node name="Player" parent="." instance=ExtResource("11_82jtv")]

[node name="Chest" parent="." instance=ExtResource("14_1ks1q")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 4.57862, 0, 6.58062)
interaction_distance = 3.0
item_resource = ExtResource("17_j32d5")

[node name="Chest2" parent="." instance=ExtResource("14_1ks1q")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 9.81867, 0, 6.58062)
interaction_distance = 3.0
item_resource = ExtResource("18_j32d5")

[node name="EnemyBoy01" parent="." instance=ExtResource("19_0f42i")]
transform = Transform3D(0.8, 0, 0, 0, 0.8, 0, 0, 0, 0.8, -10.0014, 0, -8.65824)

[node name="Enemy02" parent="." instance=ExtResource("21_dbohx")]
transform = Transform3D(2.1, 0, 0, 0, 2.1, 0, 0, 0, 2.1, 7.86917, 0, -7.768)

[node name="NavigationRegion3D" type="NavigationRegion3D" parent="."]
navigation_mesh = SubResource("NavigationMesh_fcnrs")

[node name="Floor" type="Node3D" parent="NavigationRegion3D"]

[node name="@CSGBox3D@26052" type="CSGBox3D" parent="NavigationRegion3D/Floor"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 0, -0.05, 0)
use_collision = true
size = Vector3(100, 0.1, 100)
material = SubResource("StandardMaterial3D_1ks1q")

[node name="FloorTiles" type="Node3D" parent="NavigationRegion3D/Floor"]

[node name="Floor01_0" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -32.6151, 0.1, -22.5095)

[node name="Floor01_1" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -18.7372, 0.1, -2.34386)

[node name="Floor01_2" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 12.689, 0.1, -32.2374)

[node name="Floor01_3" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -33.5689, 0.1, 27.5932)

[node name="Floor01_4" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -25.3414, 0.1, -11.8629)

[node name="Floor01_5" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 37.341, 0.1, -6.7062)

[node name="Floor01_6" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -26.3614, 0.1, 21.8364)

[node name="Floor01_7" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 5.21507, 0.1, 25.6719)

[node name="Floor01_8" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 19.5358, 0.1, -8.41128)

[node name="Floor01_9" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -36.1098, 0.1, -43.4902)

[node name="Floor01_10" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 28.6231, 0.1, -3.2487)

[node name="Floor01_11" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 3.0473, 0.1, -7.59957)

[node name="Floor01_12" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 34.8751, 0.1, 41.0905)

[node name="Floor01_13" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 10.8596, 0.1, -3.76637)

[node name="Floor01_14" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -30.6015, 0.1, 7.27322)

[node name="Floor01_15" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 25.4124, 0.1, -22.2828)

[node name="Floor01_16" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 36.9655, 0.1, 11.1196)

[node name="Floor01_17" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -39.4613, 0.1, 11.7945)

[node name="Floor01_18" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 3.48263, 0.1, 37.1286)

[node name="Floor01_19" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -44.0585, 0.1, -38.0862)

[node name="Floor01_20" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 22.7999, 0.1, 7.65804)

[node name="Floor01_21" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -0.778659, 0.1, 14.6197)

[node name="Floor01_22" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -8.95924, 0.1, -40.3452)

[node name="Floor01_23" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 30.2012, 0.1, -43.9267)

[node name="Floor01_24" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 15.839, 0.1, 36.4504)

[node name="Floor01_25" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 42.2362, 0.1, 44.2512)

[node name="Floor01_26" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -41.4021, 0.1, -19.4223)

[node name="Floor01_27" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -16.0854, 0.1, -21.549)

[node name="Floor01_28" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 22.5512, 0.1, -36.0739)

[node name="Floor01_29" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -13.2983, 0.1, 10.015)

[node name="Floor01_30" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -26.2132, 0.1, 42.8055)

[node name="Floor01_31" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 3.64099, 0.1, -37.6394)

[node name="Floor01_32" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 15.5752, 0.1, 24.5165)

[node name="Floor01_33" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -3.97025, 0.1, 30.7206)

[node name="Floor01_34" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 0.275279, 0.1, -29.2754)

[node name="Floor01_35" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -6.78223, 0.1, -5.18477)

[node name="Floor01_36" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -13.8273, 0.1, -10.665)

[node name="Floor01_37" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 9.84984, 0.1, -17.6414)

[node name="Floor01_38" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -3.44902, 0.1, 45.0097)

[node name="Floor01_39" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -24.0259, 0.1, -20.5181)

[node name="Floor01_40" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -40.3278, 0.1, 1.56316)

[node name="Floor01_41" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -25.6536, 0.1, -43.8027)

[node name="Floor01_42" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 33.8075, 0.1, 31.142)

[node name="Floor01_43" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 43.8684, 0.1, -23.4588)

[node name="Floor01_44" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 34.3865, 0.1, -27.3291)

[node name="Floor01_45" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 30.7831, 0.1, 18.4826)

[node name="Floor01_46" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 9.09034, 0.1, 43.3068)

[node name="Floor01_47" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -7.72722, 0.1, -19.9773)

[node name="Floor01_48" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 41.8575, 0.1, 33.6434)

[node name="Floor01_49" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 42.0002, 0.1, -42.507)

[node name="Floor01_50" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 7.87757, 0.1, 10.4162)

[node name="Floor01_51" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 39.7066, 0.1, 18.7194)

[node name="Floor01_52" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -20.9806, 0.1, -31.0014)

[node name="Floor01_53" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -44.8449, 0.1, 42.9105)

[node name="Floor01_54" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 21.1123, 0.1, 15.5846)

[node name="Floor01_55" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 35.9234, 0.1, -18.1331)

[node name="Floor01_56" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 17.5619, 0.1, -44.0537)

[node name="Floor01_57" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -35.2111, 0.1, -10.3753)

[node name="Floor01_58" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -14.9712, 0.1, 30.316)

[node name="Floor01_59" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -0.595933, 0.1, -0.0147619)

[node name="Floor01_60" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 44.1118, 0.1, 6.56991)

[node name="Floor01_61" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -39.0844, 0.1, 33.4371)

[node name="Floor01_62" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -44.7555, 0.1, -29.8928)

[node name="Floor01_63" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 25.8973, 0.1, 35.2269)

[node name="Floor01_64" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -44.8332, 0.1, 25.108)

[node name="Floor01_65" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -43.7519, 0.1, -10.8787)

[node name="Floor01_66" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -11.0046, 0.1, 1.97471)

[node name="Floor01_67" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -2.47619, 0.1, 22.5138)

[node name="Floor01_68" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -22.17, 0.1, 6.14123)

[node name="Floor01_69" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 21.8446, 0.1, 43.2308)

[node name="Floor01_70" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -17.6868, 0.1, 42.5229)

[node name="Floor01_71" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -1.15694, 0.1, -44.9418)

[node name="Floor01_72" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -19.7967, 0.1, 15.8488)

[node name="Floor01_73" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -7.71052, 0.1, -27.9935)

[node name="Floor01_74" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -36.4377, 0.1, -30.3103)

[node name="Floor01_75" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -37.7098, 0.1, 19.971)

[node name="Floor01_76" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -29.0529, 0.1, 34.5943)

[node name="wall_0" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -50, 0, -50)

[node name="wall_1" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -50, 0, 50)

[node name="wall_2" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -48, 0, -50)

[node name="wall_3" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -48, 0, 50)

[node name="wall_4" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -46, 0, -50)

[node name="wall_5" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -46, 0, 50)

[node name="wall_6" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -44, 0, -50)

[node name="wall_7" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -44, 0, 50)

[node name="wall_8" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -42, 0, -50)

[node name="wall_9" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -42, 0, 50)

[node name="wall_10" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -40, 0, -50)

[node name="wall_11" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -40, 0, 50)

[node name="wall_12" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -38, 0, -50)

[node name="wall_13" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -38, 0, 50)

[node name="wall_14" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -36, 0, -50)

[node name="wall_15" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -36, 0, 50)

[node name="wall_16" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -34, 0, -50)

[node name="wall_17" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -34, 0, 50)

[node name="wall_18" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -32, 0, -50)

[node name="wall_19" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -32, 0, 50)

[node name="wall_20" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -30, 0, -50)

[node name="wall_21" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -30, 0, 50)

[node name="wall_22" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -28, 0, -50)

[node name="wall_23" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -28, 0, 50)

[node name="wall_24" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -26, 0, -50)

[node name="wall_25" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -26, 0, 50)

[node name="wall_26" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -24, 0, -50)

[node name="wall_27" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -24, 0, 50)

[node name="wall_28" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -22, 0, -50)

[node name="wall_29" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -22, 0, 50)

[node name="wall_30" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -20, 0, -50)

[node name="wall_31" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -20, 0, 50)

[node name="wall_32" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -18, 0, -50)

[node name="wall_33" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -18, 0, 50)

[node name="wall_34" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -16, 0, -50)

[node name="wall_35" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -16, 0, 50)

[node name="wall_36" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -14, 0, -50)

[node name="wall_37" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -14, 0, 50)

[node name="wall_38" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -12, 0, -50)

[node name="wall_39" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -12, 0, 50)

[node name="wall_40" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -10, 0, -50)

[node name="wall_41" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -10, 0, 50)

[node name="wall_42" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -8, 0, -50)

[node name="wall_43" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -8, 0, 50)

[node name="wall_44" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -6, 0, -50)

[node name="wall_45" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -6, 0, 50)

[node name="wall_46" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -4, 0, -50)

[node name="wall_47" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -4, 0, 50)

[node name="wall_48" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -2, 0, -50)

[node name="wall_49" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -2, 0, 50)

[node name="wall_50" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 0, 0, -50)

[node name="wall_51" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 0, 0, 50)

[node name="wall_52" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 2, 0, -50)

[node name="wall_53" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 2, 0, 50)

[node name="wall_54" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 4, 0, -50)

[node name="wall_55" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 4, 0, 50)

[node name="wall_56" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 6, 0, -50)

[node name="wall_57" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 6, 0, 50)

[node name="wall_58" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 8, 0, -50)

[node name="wall_59" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 8, 0, 50)

[node name="wall_60" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 10, 0, -50)

[node name="wall_61" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 10, 0, 50)

[node name="wall_62" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 12, 0, -50)

[node name="wall_63" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 12, 0, 50)

[node name="wall_64" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 14, 0, -50)

[node name="wall_65" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 14, 0, 50)

[node name="wall_66" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 16, 0, -50)

[node name="wall_67" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 16, 0, 50)

[node name="wall_68" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 18, 0, -50)

[node name="wall_69" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 18, 0, 50)

[node name="wall_70" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 20, 0, -50)

[node name="wall_71" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 20, 0, 50)

[node name="wall_72" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 22, 0, -50)

[node name="wall_73" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 22, 0, 50)

[node name="wall_74" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 24, 0, -50)

[node name="wall_75" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 24, 0, 50)

[node name="wall_76" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 26, 0, -50)

[node name="wall_77" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 26, 0, 50)

[node name="wall_78" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 28, 0, -50)

[node name="wall_79" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 28, 0, 50)

[node name="wall_80" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 30, 0, -50)

[node name="wall_81" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 30, 0, 50)

[node name="wall_82" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 32, 0, -50)

[node name="wall_83" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 32, 0, 50)

[node name="wall_84" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 34, 0, -50)

[node name="wall_85" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 34, 0, 50)

[node name="wall_86" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 36, 0, -50)

[node name="wall_87" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 36, 0, 50)

[node name="wall_88" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 38, 0, -50)

[node name="wall_89" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 38, 0, 50)

[node name="wall_90" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 40, 0, -50)

[node name="wall_91" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 40, 0, 50)

[node name="wall_92" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 42, 0, -50)

[node name="wall_93" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 42, 0, 50)

[node name="wall_94" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 44, 0, -50)

[node name="wall_95" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 44, 0, 50)

[node name="wall_96" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 46, 0, -50)

[node name="wall_97" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 46, 0, 50)

[node name="wall_98" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 48, 0, -50)

[node name="wall_99" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 48, 0, 50)

[node name="wall_100" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 50, 0, -50)

[node name="wall_101" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 50, 0, 50)

[node name="wall_102" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, -50, 0, -50)

[node name="wall_103" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, 50, 0, -50)

[node name="wall_104" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, -50, 0, -48)

[node name="wall_105" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, 50, 0, -48)

[node name="wall_106" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, -50, 0, -46)

[node name="wall_107" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, 50, 0, -46)

[node name="wall_108" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, -50, 0, -44)

[node name="wall_109" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, 50, 0, -44)

[node name="wall_110" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, -50, 0, -42)

[node name="wall_111" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, 50, 0, -42)

[node name="wall_112" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, -50, 0, -40)

[node name="wall_113" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, 50, 0, -40)

[node name="wall_114" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, -50, 0, -38)

[node name="wall_115" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, 50, 0, -38)

[node name="wall_116" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, -50, 0, -36)

[node name="wall_117" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, 50, 0, -36)

[node name="wall_118" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, -50, 0, -34)

[node name="wall_119" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, 50, 0, -34)

[node name="wall_120" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, -50, 0, -32)

[node name="wall_121" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, 50, 0, -32)

[node name="wall_122" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, -50, 0, -30)

[node name="wall_123" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, 50, 0, -30)

[node name="wall_124" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, -50, 0, -28)

[node name="wall_125" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, 50, 0, -28)

[node name="wall_126" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, -50, 0, -26)

[node name="wall_127" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, 50, 0, -26)

[node name="wall_128" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, -50, 0, -24)

[node name="wall_129" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, 50, 0, -24)

[node name="wall_130" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, -50, 0, -22)

[node name="wall_131" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, 50, 0, -22)

[node name="wall_132" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, -50, 0, -20)

[node name="wall_133" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, 50, 0, -20)

[node name="wall_134" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, -50, 0, -18)

[node name="wall_135" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, 50, 0, -18)

[node name="wall_136" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, -50, 0, -16)

[node name="wall_137" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, 50, 0, -16)

[node name="wall_138" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, -50, 0, -14)

[node name="wall_139" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, 50, 0, -14)

[node name="wall_140" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, -50, 0, -12)

[node name="wall_141" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, 50, 0, -12)

[node name="wall_142" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, -50, 0, -10)

[node name="wall_143" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, 50, 0, -10)

[node name="wall_144" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, -50, 0, -8)

[node name="wall_145" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, 50, 0, -8)

[node name="wall_146" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, -50, 0, -6)

[node name="wall_147" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, 50, 0, -6)

[node name="wall_148" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, -50, 0, -4)

[node name="wall_149" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, 50, 0, -4)

[node name="wall_150" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, -50, 0, -2)

[node name="wall_151" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, 50, 0, -2)

[node name="wall_152" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, -50, 0, 0)

[node name="wall_153" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, 50, 0, 0)

[node name="wall_154" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, -50, 0, 2)

[node name="wall_155" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, 50, 0, 2)

[node name="wall_156" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, -50, 0, 4)

[node name="wall_157" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, 50, 0, 4)

[node name="wall_158" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, -50, 0, 6)

[node name="wall_159" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, 50, 0, 6)

[node name="wall_160" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, -50, 0, 8)

[node name="wall_161" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, 50, 0, 8)

[node name="wall_162" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, -50, 0, 10)

[node name="wall_163" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, 50, 0, 10)

[node name="wall_164" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, -50, 0, 12)

[node name="wall_165" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, 50, 0, 12)

[node name="wall_166" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, -50, 0, 14)

[node name="wall_167" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, 50, 0, 14)

[node name="wall_168" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, -50, 0, 16)

[node name="wall_169" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, 50, 0, 16)

[node name="wall_170" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, -50, 0, 18)

[node name="wall_171" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, 50, 0, 18)

[node name="wall_172" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, -50, 0, 20)

[node name="wall_173" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, 50, 0, 20)

[node name="wall_174" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, -50, 0, 22)

[node name="wall_175" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, 50, 0, 22)

[node name="wall_176" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, -50, 0, 24)

[node name="wall_177" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, 50, 0, 24)

[node name="wall_178" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, -50, 0, 26)

[node name="wall_179" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, 50, 0, 26)

[node name="wall_180" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, -50, 0, 28)

[node name="wall_181" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, 50, 0, 28)

[node name="wall_182" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, -50, 0, 30)

[node name="wall_183" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, 50, 0, 30)

[node name="wall_184" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, -50, 0, 32)

[node name="wall_185" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, 50, 0, 32)

[node name="wall_186" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, -50, 0, 34)

[node name="wall_187" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, 50, 0, 34)

[node name="wall_188" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, -50, 0, 36)

[node name="wall_189" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, 50, 0, 36)

[node name="wall_190" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, -50, 0, 38)

[node name="wall_191" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, 50, 0, 38)

[node name="wall_192" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, -50, 0, 40)

[node name="wall_193" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, 50, 0, 40)

[node name="wall_194" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, -50, 0, 42)

[node name="wall_195" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, 50, 0, 42)

[node name="wall_196" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, -50, 0, 44)

[node name="wall_197" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, 50, 0, 44)

[node name="wall_198" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, -50, 0, 46)

[node name="wall_199" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, 50, 0, 46)

[node name="wall_200" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, -50, 0, 48)

[node name="wall_201" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, 50, 0, 48)

[node name="wall_202" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, -50, 0, 50)

[node name="wall_203" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, 50, 0, 50)

[node name="tree_0" parent="NavigationRegion3D" instance=ExtResource("15_003du")]
transform = Transform3D(2.5, -0.00145593, -0.00167976, 0.00145725, 2.5, 0.00196474, 0.00167861, -0.00196571, 2.5, 7.35857, 0, 18.6324)

[node name="PatrolPoint_0" type="Node3D" parent="NavigationRegion3D"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 4.91612, 1, 23.1039)
script = ExtResource("15_6ly8b")

[node name="tree_1" parent="NavigationRegion3D" instance=ExtResource("15_003du")]
transform = Transform3D(2.5, -0.00145593, -0.00167976, 0.00145725, 2.5, 0.00196474, 0.00167861, -0.00196571, 2.5, -6.58702, 0, 5.71167)

[node name="PatrolPoint_1" type="Node3D" parent="NavigationRegion3D"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -13.0413, 1, 7.18164)
script = ExtResource("15_6ly8b")
point_id = 1

[node name="tree_2" parent="NavigationRegion3D" instance=ExtResource("15_003du")]
transform = Transform3D(2.5, -0.00145593, -0.00167976, 0.00145725, 2.5, 0.00196474, 0.00167861, -0.00196571, 2.5, -3.97642, 0, -15.0901)
preset_burnt_out_type = 1

[node name="PatrolPoint_2" type="Node3D" parent="NavigationRegion3D"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -8.79105, 1, -14.9239)
script = ExtResource("15_6ly8b")
point_id = 2

[node name="tree_3" parent="NavigationRegion3D" instance=ExtResource("15_003du")]
transform = Transform3D(2.5, -0.00145593, -0.00167976, 0.00145725, 2.5, 0.00196474, 0.00167861, -0.00196571, 2.5, 15.5545, 0, -18.2857)

[node name="PatrolPoint_3" type="Node3D" parent="NavigationRegion3D"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 19.7062, 1, -20.9697)
script = ExtResource("15_6ly8b")
point_id = 3

[node name="chest_0" parent="NavigationRegion3D" instance=ExtResource("14_1ks1q")]
transform = Transform3D(0.999853, 0, 0.0171434, 0, 1, 0, -0.0171434, 0, 0.999853, -30.4957, 0, -5.68422)

[node name="rock_0" parent="NavigationRegion3D" instance=ExtResource("21_xcdtp")]
transform = Transform3D(0.553944, 0, -1.0221, 0, 1.16255, 0, 1.0221, 0, 0.553944, 43.8722, 0.01, 15.2824)

[node name="rock_1" parent="NavigationRegion3D" instance=ExtResource("21_xcdtp")]
transform = Transform3D(0.794654, 0, -0.313657, 0, 0.854316, 0, 0.313657, 0, 0.794654, 23.6508, 0.01, -16.0091)

[node name="rock_2" parent="NavigationRegion3D" instance=ExtResource("21_xcdtp")]
transform = Transform3D(-0.129917, 0, -0.867417, 0, 0.877092, 0, 0.867417, 0, -0.129917, 21.8462, 0.01, -30.3208)

[node name="rock_3" parent="NavigationRegion3D" instance=ExtResource("21_xcdtp")]
transform = Transform3D(0.425758, 0, 1.05431, 0, 1.13703, 0, -1.05431, 0, 0.425758, 39.9856, 0.01, 26.8663)

[node name="rock_4" parent="NavigationRegion3D" instance=ExtResource("21_xcdtp")]
transform = Transform3D(-0.485701, 0, 0.766707, 0, 0.907604, 0, -0.766707, 0, -0.485701, 16.7267, 0.01, 0.724851)

[node name="rock_5" parent="NavigationRegion3D" instance=ExtResource("21_xcdtp")]
transform = Transform3D(-0.845136, 0, -0.196346, 0, 0.867644, 0, 0.196346, 0, -0.845136, -38.0049, 0.01, 39.8318)

[node name="rock_6" parent="NavigationRegion3D" instance=ExtResource("21_xcdtp")]
transform = Transform3D(-0.531528, 0, 0.993223, 0, 1.12651, 0, -0.993223, 0, -0.531528, -21.6525, 0.01, 28.5652)

[node name="rock_7" parent="NavigationRegion3D" instance=ExtResource("21_xcdtp")]
transform = Transform3D(-0.809627, 0, 0.175415, 0, 0.828412, 0, -0.175415, 0, -0.809627, 37.3167, 0.01, -38.572)

[node name="rock_8" parent="NavigationRegion3D" instance=ExtResource("21_xcdtp")]
transform = Transform3D(0.556781, 0, -1.00525, 0, 1.14915, 0, 1.00525, 0, 0.556781, 26.3488, 0.01, 25.6819)

[node name="rock_9" parent="NavigationRegion3D" instance=ExtResource("21_xcdtp")]
transform = Transform3D(-0.533962, 0, 0.88801, 0, 1.03618, 0, -0.88801, 0, -0.533962, -16.3355, 0.01, -44.6829)

[node name="rock_10" parent="NavigationRegion3D" instance=ExtResource("21_xcdtp")]
transform = Transform3D(-0.495865, 0, 1.032, 0, 1.14494, 0, -1.032, 0, -0.495865, -6.91795, 0.01, -34.3381)

[node name="rock_11" parent="NavigationRegion3D" instance=ExtResource("21_xcdtp")]
transform = Transform3D(0.909686, 0, -0.22326, 0, 0.936682, 0, 0.22326, 0, 0.909686, 32.2452, 0.01, -11.7908)

[node name="rock_12" parent="NavigationRegion3D" instance=ExtResource("21_xcdtp")]
transform = Transform3D(-0.563294, 0, 0.78109, 0, 0.963017, 0, -0.78109, 0, -0.563294, -16.3981, 0.01, 2.28121)

[node name="rock_13" parent="NavigationRegion3D" instance=ExtResource("21_xcdtp")]
transform = Transform3D(0.565056, 0, -0.981746, 0, 1.13275, 0, 0.981746, 0, 0.565056, 42.8103, 0.01, 22.716)

[node name="rock_14" parent="NavigationRegion3D" instance=ExtResource("21_xcdtp")]
transform = Transform3D(0.523424, 0, -0.97176, 0, 1.10376, 0, 0.97176, 0, 0.523424, -19.4001, 0.01, -25.9144)

[node name="barrel_0" parent="NavigationRegion3D" instance=ExtResource("22_cin4e")]
transform = Transform3D(0.791734, 0, -0.162852, 0, 0.808309, 0, 0.162852, 0, 0.791734, 27.3624, 0, 28.5616)

[node name="barrel_1" parent="NavigationRegion3D" instance=ExtResource("22_cin4e")]
transform = Transform3D(-0.10302, 0, 1.13126, 0, 1.13594, 0, -1.13126, 0, -0.10302, -41.729, 0, 29.0562)

[node name="decoration_0" parent="NavigationRegion3D" instance=ExtResource("23_6j2fk")]
transform = Transform3D(0.191515, 0, -0.807359, 0, 0.829763, 0, 0.807359, 0, 0.191515, 9.49071, 0, 4.22591)

[node name="decoration_1" parent="NavigationRegion3D" instance=ExtResource("23_6j2fk")]
transform = Transform3D(0.0927748, 0, -1.2104, 0, 1.21395, 0, 1.2104, 0, 0.0927748, 17.6519, 0, -27.205)

[node name="decoration_2" parent="NavigationRegion3D" instance=ExtResource("23_6j2fk")]
transform = Transform3D(-0.840347, 0, -0.201223, 0, 0.864103, 0, 0.201223, 0, -0.840347, -31.5366, 0, -34.8785)

[node name="decoration_3" parent="NavigationRegion3D" instance=ExtResource("23_6j2fk")]
transform = Transform3D(-0.987823, 0, 0.150756, 0, 0.999261, 0, -0.150756, 0, -0.987823, 10.8039, 0, -37.279)

[node name="decoration_4" parent="NavigationRegion3D" instance=ExtResource("23_6j2fk")]
transform = Transform3D(1.1407, 0, -0.0482368, 0, 1.14172, 0, 0.0482368, 0, 1.1407, 22.2945, 0, -3.47329)

[node name="decoration_5" parent="NavigationRegion3D" instance=ExtResource("23_6j2fk")]
transform = Transform3D(-0.249825, 0, 1.37115, 0, 1.39372, 0, -1.37115, 0, -0.249825, -30.9298, 0, -39.5719)

[node name="decoration_6" parent="NavigationRegion3D" instance=ExtResource("23_6j2fk")]
transform = Transform3D(-0.213663, 0, -0.759269, 0, 0.78876, 0, 0.759269, 0, -0.213663, -27.2332, 0, 1.48211)

[node name="decoration_7" parent="NavigationRegion3D" instance=ExtResource("23_6j2fk")]
transform = Transform3D(1.04772, 0, -0.678433, 0, 1.24819, 0, 0.678433, 0, 1.04772, -23.5302, 0, 31.3856)

[node name="decoration_8" parent="NavigationRegion3D" instance=ExtResource("23_6j2fk")]
transform = Transform3D(-0.409453, 0, 0.918296, 0, 1.00544, 0, -0.918296, 0, -0.409453, 26.2706, 0, -12.0232)

[node name="decoration_9" parent="NavigationRegion3D" instance=ExtResource("23_6j2fk")]
transform = Transform3D(0.615754, 0, 1.15124, 0, 1.30557, 0, -1.15124, 0, 0.615754, -8.9919, 0, 39.6375)

[node name="decoration_10" parent="NavigationRegion3D" instance=ExtResource("23_6j2fk")]
transform = Transform3D(-0.737292, 0, 0.0649466, 0, 0.740147, 0, -0.0649466, 0, -0.737292, -41.1163, 0, -4.58208)

[node name="decoration_11" parent="NavigationRegion3D" instance=ExtResource("23_6j2fk")]
transform = Transform3D(1.00632, 0, -0.14469, 0, 1.01667, 0, 0.14469, 0, 1.00632, -13.5456, 0, -30.499)

[node name="decoration_12" parent="NavigationRegion3D" instance=ExtResource("23_6j2fk")]
transform = Transform3D(0.82895, 0, 0.812418, 0, 1.16068, 0, -0.812418, 0, 0.82895, 8.68412, 0, -41.0565)

[node name="decoration_13" parent="NavigationRegion3D" instance=ExtResource("23_6j2fk")]
transform = Transform3D(-0.0216403, 0, -1.03751, 0, 1.03773, 0, 1.03751, 0, -0.0216403, -31.6736, 0, 39.8953)

[node name="decoration_14" parent="NavigationRegion3D" instance=ExtResource("23_6j2fk")]
transform = Transform3D(0.6192, 0, 0.369116, 0, 0.720871, 0, -0.369116, 0, 0.6192, 42.8143, 0, -12.9459)

[node name="decoration_15" parent="NavigationRegion3D" instance=ExtResource("23_6j2fk")]
transform = Transform3D(0.80155, 0, 0.65873, 0, 1.0375, 0, -0.65873, 0, 0.80155, 15.0049, 0, 15.034)

[node name="decoration_16" parent="NavigationRegion3D" instance=ExtResource("23_6j2fk")]
transform = Transform3D(-0.240147, 0, 1.06594, 0, 1.09266, 0, -1.06594, 0, -0.240147, -13.4412, 0, -35.4225)

[node name="decoration_17" parent="NavigationRegion3D" instance=ExtResource("23_6j2fk")]
transform = Transform3D(0.988954, 0, 0.295685, 0, 1.03221, 0, -0.295685, 0, 0.988954, 44.053, 0, -33.3315)

[node name="decoration_18" parent="NavigationRegion3D" instance=ExtResource("23_6j2fk")]
transform = Transform3D(-0.0913402, 0, 0.845263, 0, 0.850184, 0, -0.845263, 0, -0.0913402, -13.7456, 0, 20.1547)

[node name="decoration_19" parent="NavigationRegion3D" instance=ExtResource("23_6j2fk")]
transform = Transform3D(-0.271241, 0, 1.30961, 0, 1.33741, 0, -1.30961, 0, -0.271241, 5.88616, 0, 3.37007)

[node name="PatrolPointManager" type="Node" parent="."]
script = ExtResource("24_j2uky")
