[gd_scene load_steps=38 format=3 uid="uid://c0rj4dwj18nv8"]

[ext_resource type="Script" uid="uid://bo0h8t35yls55" path="res://Scripts/Levels/Level01.gd" id="1_hk2b6"]
[ext_resource type="PackedScene" uid="uid://d2xyw4d37ymv4" path="res://Scenes/Minimap.tscn" id="2_j32d5"]
[ext_resource type="PackedScene" uid="uid://ctxvyh1qr52ue" path="res://Scenes/PlayerAvatar.tscn" id="3_0f42i"]
[ext_resource type="PackedScene" uid="uid://do1sypgxbecbd" path="res://Scenes/ItemBoxes.tscn" id="4_swvig"]
[ext_resource type="PackedScene" uid="uid://b4l4phqqbvprm" path="res://Scenes/SkillBoxUI.tscn" id="5_dbohx"]
[ext_resource type="Script" uid="uid://f54r1khulcvh" path="res://Scripts/ModularLevelGenerator.gd" id="6_6ly8b"]
[ext_resource type="Resource" uid="uid://cvji54t3pjwfe" path="res://Resources/LevelConfigs/AllModulesConfig.tres" id="7_r5102"]
[ext_resource type="PackedScene" uid="uid://d2b5h1mlxxy7d" path="res://Scenes/FogOfWar.tscn" id="9_003du"]
[ext_resource type="PackedScene" uid="uid://b88l8pk1ebe1x" path="res://Scenes/player.tscn" id="11_82jtv"]
[ext_resource type="PackedScene" uid="uid://c3h8fj2xsp5oy" path="res://Scenes/Prefabs/Chest.tscn" id="14_1ks1q"]
[ext_resource type="PackedScene" uid="uid://dwusy8dd8usvo" path="res://Scenes/Prefabs/Wall.tscn" id="14_df8b0"]
[ext_resource type="PackedScene" uid="uid://crtnthqkksmri" path="res://Scenes/Prefabs/Tree.tscn" id="15_003du"]
[ext_resource type="Script" uid="uid://c3tr23vwvnmwf" path="res://Scripts/PatrolPoint.gd" id="15_6ly8b"]
[ext_resource type="Texture2D" uid="uid://c2ny0yi07rvcf" path="res://Environment/Floor/Floor01_Rocks_BaseColor.png" id="15_df8b0"]
[ext_resource type="Texture2D" uid="uid://dhfikoo16s5n0" path="res://Environment/Floor/Rocks_Metallic.png" id="16_003du"]
[ext_resource type="PackedScene" uid="uid://ddttv643pel23" path="res://Environment/Floor/Floor01_Custom.tscn" id="17_2bvpm"]
[ext_resource type="Resource" path="res://Resources/Items/Trap.tres" id="17_j32d5"]
[ext_resource type="Resource" path="res://Resources/Items/Torch.tres" id="18_j32d5"]
[ext_resource type="Script" uid="uid://pfva6p6rllgp" path="res://Scripts/Tree.gd" id="18_oh2bt"]
[ext_resource type="PackedScene" uid="uid://rvgn0irsuwao" path="res://Scenes/EnemyBoy01.tscn" id="19_0f42i"]
[ext_resource type="PackedScene" uid="uid://brxqv3iv3op6u" path="res://Scenes/ProgressBar3D.tscn" id="19_fcnrs"]
[ext_resource type="Script" uid="uid://dhw1dgex7wmiy" path="res://Scripts/ChestInteractable.gd" id="21_1ks1q"]
[ext_resource type="PackedScene" uid="uid://ervckea7fk57" path="res://Scenes/Enemy02.tscn" id="21_dbohx"]
[ext_resource type="PackedScene" uid="uid://b5dqjsb63wbhl" path="res://Scenes/Prefabs/Rock.tscn" id="21_xcdtp"]
[ext_resource type="PackedScene" uid="uid://bdq3b4e0mlgo4" path="res://Scenes/Prefabs/Barrel.tscn" id="22_cin4e"]
[ext_resource type="Resource" uid="uid://bjih53ivkm0qi" path="res://Resources/GasItem.tres" id="22_xcdtp"]
[ext_resource type="PackedScene" uid="uid://c6k7j3t3flhst" path="res://Scenes/Prefabs/Decoration.tscn" id="23_6j2fk"]
[ext_resource type="AudioStream" uid="uid://dgskkp7epyn0x" path="res://ChestOpening.mp3" id="23_cin4e"]
[ext_resource type="Script" uid="uid://de18ote8otj6u" path="res://Scripts/PatrolPointManager.gd" id="24_j2uky"]
[ext_resource type="Script" uid="uid://dt3st7cac7lok" path="res://Scripts/UI/UIManager.gd" id="25_ui_manager"]
[ext_resource type="Script" path="res://Scripts/CameraFollower.gd" id="26_camera_follower"]

[sub_resource type="ProceduralSkyMaterial" id="ProceduralSkyMaterial_gbvua"]
sky_top_color = Color(0.2, 0.4, 0.8, 1)
sky_horizon_color = Color(0.5, 0.7, 0.9, 1)
ground_bottom_color = Color(0.3, 0.5, 0.7, 1)
ground_horizon_color = Color(0.5, 0.7, 0.9, 1)

[sub_resource type="Sky" id="Sky_m0r78"]
sky_material = SubResource("ProceduralSkyMaterial_gbvua")

[sub_resource type="Environment" id="Environment_j4i7h"]
background_mode = 2
sky = SubResource("Sky_m0r78")
ambient_light_source = 3
ambient_light_color = Color(0.9, 0.9, 0.95, 1)
ambient_light_energy = 0.5
tonemap_mode = 2
ssao_enabled = true
ssao_radius = 2.0
ssao_intensity = 1.5
glow_enabled = true
glow_strength = 1.2

[sub_resource type="NavigationMesh" id="NavigationMesh_xcdtp"]
vertices = PackedVector3Array(-51.3393, 0.37304, -40.9733, -52.3393, 0.37304, -40.4733, -52.0893, 0.37304, -39.7233, -49.3393, 0.37304, -40.4733, -46.8393, 0.37304, -29.2233, -47.0893, 0.37304, -28.4733, -46.8393, 0.37304, -27.7233, -45.8393, 0.37304, -27.2233, -45.8393, 0.37304, -29.4733, -46.8393, 0.37304, -45.9733, -47.3393, 0.37304, -46.7233, -47.5893, 0.37304, -44.2233, -33.0893, 0.37304, -54.4733, -34.3393, 0.37304, -54.7233, -35.0893, 0.37304, -54.2233, -35.5893, 0.37304, -52.7233, -30.8393, 0.37304, -48.7233, -30.8393, 0.37304, -52.2233, -49.3393, 0.37304, -44.2233, -49.3393, 0.37304, -43.2233, -32.5893, 0.37304, -54.9733, -45.0893, 0.37304, -22.7233, -40.5893, 0.37304, -22.7233, -40.0893, 0.37304, -24.2233, -37.5893, 0.37304, -54.4733, -39.5893, 0.37304, -54.9733, -39.8393, 0.37304, -54.4733, -36.3393, 0.37304, -52.7233, -45.8393, 0.37304, -24.9733, -50.8393, 0.37304, -42.4733, -45.5893, 0.37304, -45.9733, -47.8393, 0.37304, -40.2233, -28.3393, 0.37304, -45.7233, -28.5893, 0.37304, -46.9733, -30.5893, 0.37304, -47.2233, -38.8393, 0.37304, -48.2233, -38.8393, 0.37304, -49.4733, -30.8393, 0.37304, -57.2233, -32.5893, 0.37304, -56.2233, -40.5893, 0.37304, -54.4733, -28.0893, 0.37304, -55.7233, -28.8393, 0.37304, -56.7233, -28.3393, 0.37304, -53.4733, -45.8393, 0.37304, -30.9733, -37.8393, 0.37304, -23.9733, -27.8393, 0.37304, -45.4733, -36.5781, 0.87304, -29.4886, -36.6031, 0.37304, -30.9936, -38.1131, 0.37304, -29.4496, -35.1132, 0.37304, -32.4595, -37.5893, 0.37304, -23.4733, -11.8393, 0.37304, -55.9733, -13.3393, 0.37304, -56.2233, -13.5893, 0.37304, -55.7233, -10.3393, 0.37304, -54.4733, -15.0893, 0.37304, -55.4733, -15.3393, 0.37304, -53.7233, -14.3393, 0.37304, -53.4733, -8.33927, 0.37304, -52.9733, 4.66073, 0.37304, -11.2233, 5.91073, 0.62304, -10.9733, 6.91073, 0.37304, -11.7233, 8.41073, 0.37304, -45.2233, 0.410728, 0.37304, -46.4733, -14.0893, 0.37304, -46.7233, 4.16073, 0.37304, -10.4733, -9.33927, 0.37304, -54.4733, 0.160728, 0.37304, -55.7233, -0.589272, 0.37304, -56.7233, -2.08927, 0.37304, -57.2233, -4.83927, 0.37304, -54.4733, -6.33927, 0.37304, -54.7233, -7.58927, 0.37304, -52.9733, -3.58927, 0.37304, -52.2233, 7.91073, 0.37304, -11.4733, 0.410728, 0.37304, -52.7233, -0.0892715, 0.37304, -53.2233, -3.83927, 0.37304, -56.7233, 0.160728, 0.37304, -47.2233, -3.58927, 0.37304, -47.4733, 47.1607, 0.37304, -54.9733, 46.6607, 0.37304, -53.7233, 47.9107, 0.37304, -53.4733, 51.4107, 0.37304, -54.2233, 50.6607, 0.37304, -55.4733, 49.4107, 0.37304, -55.4733, 48.9107, 0.37304, -55.9733, 17.1607, 0.37304, -53.7233, 16.1607, 0.37304, -52.9733, 18.4107, 0.37304, -52.7233, 17.1607, 0.37304, -54.7233, 21.4107, 0.37304, -53.4733, 20.9107, 0.37304, -55.2233, 18.9107, 0.37304, -55.7233, 41.1607, 0.37304, -54.4733, 40.6607, 0.37304, -54.9733, 40.4107, 0.37304, -54.4733, 8.16073, 0.37304, -10.9733, 9.16073, 0.37304, -10.9733, 47.4107, 0.37304, -26.7233, 47.4107, 0.37304, -27.4733, 46.4107, 0.37304, -27.9733, 46.6607, 0.37304, -22.4733, 30.6607, 0.37304, -52.2233, 30.1607, 0.37304, -51.7233, 30.1607, 0.37304, -50.4733, 34.6607, 0.37304, -50.7233, 33.6607, 0.37304, -52.2233, 42.9107, 0.37304, -53.7233, 42.6607, 0.37304, -54.4733, 42.1607, 0.37304, -52.4733, 38.4107, 0.37304, -52.7233, 46.9107, 0.37304, -43.7233, 48.1607, 0.37304, -44.2233, 47.4107, 0.37304, -45.9733, 45.1607, 0.37304, -45.9733, 12.4107, 0.37304, -44.7233, 39.4107, 0.37304, -54.4733, 29.1607, 0.37304, -49.9733, 29.1607, 0.37304, -47.9733, 35.6607, 0.37304, -50.7233, 41.4107, 0.37304, -47.2233, 20.6607, 0.37304, -45.4733, 36.4107, 0.37304, -51.9733, 28.6607, 0.37304, -47.4733, 46.9107, 0.37304, -34.2233, 44.9107, 0.37304, -46.4733, 47.4107, 0.37304, -33.7233, 21.1607, 0.37304, -46.4733, 12.9107, 0.37304, -46.9733, 7.41073, 0.37304, -53.4733, 7.16073, 0.37304, -54.2233, 5.66073, 0.37304, -54.7233, 3.91073, 0.37304, -54.2233, -28.5893, 3.62304, -51.4733, -29.5893, 3.62304, -51.4733, -29.3393, 3.62304, -48.2233, -27.8393, 3.62304, -48.2233, -15.3393, 3.62304, -47.4733, -15.5893, 3.62304, -52.4733, -18.5893, 3.62304, -52.4733, -22.3393, 3.62304, -47.4733, -28.0893, 3.62304, -52.2233, -27.0893, 3.62304, -46.7233, -19.0893, 3.62304, -53.4733, 9.91073, 0.37304, -53.2233, 10.6607, 0.37304, -52.9733, 10.4107, 0.37304, -53.4733, -46.5893, 3.62304, -48.2233, -46.0893, 3.62304, -47.4733, -45.0893, 3.62304, -47.4733, -40.3393, 3.62304, -48.9733, -41.5893, 3.62304, -53.2233, -44.0893, 3.62304, -52.2233, -50.0893, 3.62304, -45.4733, -48.8393, 3.62304, -45.4733, -48.5893, 3.62304, -47.2233, -51.5893, 3.62304, -48.4733, -51.5893, 3.62304, -45.9733, -51.0893, 3.62304, -51.2233, -52.3393, 3.62304, -50.9733, -48.3393, 3.62304, -51.9733, -50.5893, 3.62304, -52.2233, -47.8393, 3.62304, -48.2233, -45.0893, 3.62304, -52.2233, -45.8393, 3.62304, -52.9733, 0.910728, 3.62304, -48.4733, 1.41073, 3.62304, -47.7233, 2.41073, 3.62304, -47.4733, 10.4107, 3.62304, -46.2233, 11.6607, 3.62304, -46.4733, 11.6607, 3.62304, -47.7233, -2.33927, 3.62304, -51.2233, -2.33927, 3.62304, -48.4733, 1.66073, 3.62304, -51.4733, 11.4107, 3.62304, -51.4733, 2.41073, 3.62304, -52.9733, 2.41073, 3.62304, -52.2233, 15.6607, 3.62304, -51.4733, 13.1607, 3.62304, -52.2233, 12.6607, 3.62304, -51.4733, 12.1607, 3.62304, -48.4733, 13.4107, 3.62304, -48.4733, 18.9107, 3.62304, -51.4733, 19.9107, 3.62304, -46.9733, 20.6607, 3.87304, -47.7233, 27.9107, 3.87304, -48.7233, 27.9107, 3.87304, -49.4733, 27.4107, 3.87304, -52.4733, 24.6607, 3.87304, -52.7233, 48.9107, 3.62304, -46.2233, 48.9107, 3.62304, -45.2233, 52.1607, 3.62304, -46.2233, 51.9107, 3.62304, -47.7233, 48.1607, 3.62304, -47.2233, 43.6607, 3.62304, -52.2233, 42.9107, 3.62304, -49.2233, 42.9107, 3.62304, -47.9733, 46.6607, 3.62304, -51.9733, 52.9107, 3.62304, -48.7233, 55.6607, 3.62304, -48.9733, 55.4107, 3.62304, -52.2233, 51.9107, 3.62304, -51.9733, 51.4107, 3.62304, -52.7233, -17.3393, 0.37304, -49.7233, -16.0893, 0.37304, -49.7233, -16.3393, 0.37304, -50.9733, -17.3393, 0.37304, -50.9733, 23.1607, 0.37304, -50.7233, 24.4107, 0.37304, -50.4733, 24.4107, 0.37304, -50.9733, 53.1607, 0.37304, -50.7233, 54.9107, 0.37304, -50.4733, 54.9107, 0.37304, -50.9733, -24.5893, 0.37304, -50.4733, -23.3393, 0.37304, -49.9733, -23.3393, 0.37304, -50.4733, 3.91073, 0.37304, -50.4733, 3.91073, 0.37304, -49.9733, 4.91073, 0.37304, -49.9733, 4.91073, 0.37304, -50.4733, 43.6607, 0.37304, -50.4733, 43.6607, 0.37304, -49.9733, 44.4107, 0.37304, -49.9733, 44.4107, 0.37304, -50.4733, 6.66073, 0.37304, -50.2233, 6.66073, 0.37304, -49.4733, 8.16073, 0.37304, -49.4733, 11.1607, 0.37304, -48.4733, 11.1607, 0.37304, -49.9733, 53.6607, 0.37304, -45.4733, 52.9107, 0.37304, -45.2233, 54.1607, 0.37304, -45.2233, 54.4107, 0.37304, -46.9733, 54.4107, 0.37304, -47.4733, 53.4107, 0.37304, -47.4733, 7.41073, 0.37304, -46.9733, 10.4107, 0.37304, -46.4733, 7.91073, 0.37304, -47.2233, -17.8393, 0.37304, 0.276688, -17.5893, 0.37304, 1.02669, -16.3393, 0.37304, 1.02669, 3.41073, 0.37304, -8.22331, 4.16073, 0.37304, -8.72331, -3.1726, 0.37304, -5.13998, -1.85594, 0.87304, -5.75665, -0.539272, 0.37304, -6.37331, -22.8393, 0.37304, -0.473312, -22.5893, 0.37304, 1.02669, -21.3393, 0.37304, 1.27669, -19.8393, 0.62304, 1.27669, -18.5893, 0.37304, 0.276688, -20.5893, 0.87304, 1.27669, 3.16073, 0.37304, -7.22331, -3.33927, 0.37304, -4.47331, -2.03927, 0.87304, -5.02331, -0.739273, 0.37304, -5.57331, -33.5893, 0.37304, -17.2233, -14.3393, 0.37304, -46.2233, -12.5803, 0.87304, -15.9945, -12.5656, 0.37304, -17.4753, -14.0653, 0.37304, -15.9555, -18.5699, 0.87304, -12.949, -17.0849, 0.37304, -12.988, -12.5801, 0.37304, -14.4748, -12.579, 0.87304, -6.98597, -15.5694, 0.37304, -9.95953, -18.6047, 0.37304, -11.4903, -11.094, 0.37304, -6.97498, -8.07504, 0.37304, -14.4918, -14.0939, 0.37304, -5.46511, -23.0752, 0.37304, -14.4517, -12.5994, 0.37304, -9.98755, -37.8393, 0.37304, -21.4733, -21.8393, 0.37304, -46.2233, -24.0893, 0.37304, -0.473312, 47.9107, 3.87304, -30.4733, 47.9107, 3.87304, -28.7233, 48.9107, 4.12304, -27.9733, 52.9107, 3.87304, -29.2233, 52.1607, 3.87304, -30.9733, 48.6607, 3.87304, -33.2233, 53.1607, 3.87304, -33.4733, 52.1607, 3.87304, -34.4733, 48.6607, 3.62304, -34.7233, 52.9107, 3.62304, -38.2233, 52.1607, 3.62304, -38.7233, 48.1607, 3.62304, -34.9733, 48.1607, 4.12304, -23.2233, 51.6607, 3.87304, -22.7233, 52.1607, 3.62304, -42.7233, 48.1607, 3.62304, -42.7233, 50.4107, 0.37304, -41.4733, 50.4107, 0.37304, -42.2233, 49.4107, 0.37304, -42.2233, 49.6607, 0.37304, -39.9733, 50.1607, 0.37304, -39.9733, -48.5893, 3.62304, -29.7233, -47.3393, 3.62304, -30.2233, -47.3393, 3.62304, -31.7233, -49.0893, 3.62304, -38.9733, -53.3393, 3.62304, -38.2233, -52.0893, 3.62304, -32.9733, -52.5893, 3.62304, -31.4733, -51.8393, 0.37304, -37.7233, -50.3393, 0.37304, -33.7233, -50.5893, 0.37304, -37.7233, 53.6607, 0.37304, -31.4733, 53.6607, 0.37304, -30.7233, 54.1607, 0.37304, -30.4733, 56.1607, 0.37304, -30.9733, 56.6607, 0.37304, -31.2233, 56.6607, 0.37304, -32.9733, 54.1607, 0.37304, -32.9733, 53.6607, 0.37304, -26.7233, 55.6607, 0.37304, -26.9733, -47.3393, 3.62304, -22.2233, -46.3393, 3.62304, -23.2233, -47.0893, 3.62304, -24.4733, -54.3393, 3.62304, -30.9733, -54.5893, 3.62304, -30.2233, -52.3393, 3.62304, -24.7233, -48.0893, 3.62304, -27.2233, -47.0893, 3.62304, -26.2233, -52.3393, 3.62304, -22.2233, 49.1607, 0.37304, -23.9733, 49.1607, 0.37304, -23.4733, 49.6607, 0.37304, -23.4733, 49.6607, 0.37304, -24.4733, 49.9107, 0.37304, -26.7233, 50.6607, 0.37304, -25.2233, 50.6607, 0.37304, -26.4733, -54.3393, 0.37304, -26.2233, -55.0893, 0.37304, -25.9733, -55.3393, 0.37304, -24.7233, -55.3393, 0.37304, -21.2233, -53.5893, 0.37304, -20.7233, -53.5893, 0.37304, -24.2233, -50.5893, 0.37304, -25.7233, -50.0893, 0.37304, -24.7233, -50.0893, 0.37304, -25.7233, -39.5893, 0.62304, 7.77669, -39.3393, 0.62304, 7.77669, -39.0893, 0.62304, 7.27669, -39.8393, 0.62304, 7.02669, -47.3393, 0.37304, -10.9733, -48.0893, 0.37304, -10.9733, -48.0893, 0.37304, -3.22331, -45.3393, 0.37304, -21.9733, -46.0893, 0.37304, -21.7233, -45.3393, 0.37304, -20.7233, -38.0893, 0.37304, 7.27669, -37.3393, 0.37304, 6.52669, -46.8393, 0.37304, -2.97331, -46.8393, 0.37304, -11.7233, -45.3393, 0.37304, -19.2233, -40.3393, 0.37304, -21.2233, -41.0893, 0.62304, 7.02669, -45.3393, 0.37304, 7.77669, -41.3393, 0.37304, 7.77669, -24.8393, 0.37304, 0.276688, -35.0737, 0.87304, -9.96488, -35.0736, 0.37304, -8.45501, -36.5735, 0.87304, -6.99514, -36.6086, 0.37304, -8.466, -36.5884, 0.37304, -5.47428, -33.5886, 0.37304, -8.49402, -38.1085, 0.37304, -6.95612, -36.6089, 0.37304, -11.4857, -35.0739, 0.37304, -11.4748, -24.8393, 0.37304, 1.02669, -38.0893, 0.37304, -20.9733, 49.4107, 0.37304, -19.9733, 49.4107, 0.37304, -21.7233, 46.9107, 0.37304, -21.9733, 46.9107, 0.37304, -10.7233, 49.4107, 0.37304, -10.7233, 9.66073, 0.37304, -10.7233, 9.91073, 0.37304, -9.47331, 46.6607, 0.37304, -9.47331, -52.0893, 3.87304, -15.4733, -52.5893, 3.87304, -13.2233, -48.5893, 4.12304, -12.2233, -52.5893, 3.87304, -17.2233, -52.0893, 3.87304, -16.9733, -47.3393, 4.12304, -20.4733, -46.8393, 4.12304, -19.7233, 53.6607, 0.37304, -10.4733, 53.6607, 0.37304, -9.22331, 54.6607, 0.37304, -8.97331, 55.6607, 0.37304, -10.4733, 53.4107, 0.37304, -21.4733, 51.9107, 0.37304, -21.2233, 51.9107, 0.37304, -20.2233, 55.4107, 0.37304, -21.7233, 53.6607, 0.37304, -21.9733, 56.1607, 0.37304, -20.4733, 51.1607, 0.37304, -19.4733, 55.9107, 0.37304, -17.9733, 49.9107, 0.37304, -19.4733, 58.9107, 0.37304, -11.2233, 59.6607, 0.37304, -17.2233, 57.1607, 0.37304, -17.2233, 56.1607, 0.37304, -10.9733, 53.6607, 0.37304, -4.22331, 54.6607, 0.37304, -4.22331, -51.0893, 0.37304, -13.9733, -50.8393, 0.37304, -13.2233, -49.5893, 0.37304, -13.2233, -49.3393, 0.37304, -13.9733, -49.3393, 0.37304, -15.9733, -50.5893, 0.37304, -15.9733, -56.0893, 0.37304, -6.97331, -57.3393, 0.37304, -6.22331, -57.0893, 0.37304, -4.97331, -58.3393, 0.37304, -4.22331, -58.3393, 0.37304, -2.22331, -54.0893, 0.37304, -12.2233, -54.8393, 0.37304, -11.4733, -55.0893, 0.37304, -8.72331, -54.5893, 0.37304, -2.47331, -54.8393, 0.37304, -1.97331, 5.66073, 5.12304, -9.47331, 5.66073, 5.12304, -8.22331, 6.91073, 5.12304, -8.22331, 6.91073, 5.12304, -9.47331, 46.4107, 0.37304, 0.276688, 46.9107, 0.37304, -1.72331, 8.91073, 0.37304, -7.22331, 10.9107, 0.37304, 3.77669, 13.1607, 0.37304, 3.77669, 48.4107, 3.62304, -1.97331, 47.9107, 3.62304, -1.22331, 47.9107, 3.62304, -0.223312, 51.1607, 3.62304, 0.526688, 51.6607, 3.87304, -2.47331, 52.4107, 3.87304, -2.97331, 52.4107, 3.87304, -5.22331, 53.1607, 3.62304, -7.22331, 52.6607, 3.87304, -9.47331, 48.1607, 4.12304, -9.47331, 48.3691, 4.12304, -3.22331, -15.8393, 0.37304, 1.52669, 3.91073, 0.37304, -6.47331, -10.8393, 0.37304, 12.7767, -10.5893, 0.37304, 13.5267, -9.33927, 0.37304, 13.5267, -13.8393, 0.37304, 13.5267, -12.8393, 0.62304, 13.7767, -11.5893, 0.37304, 12.7767, -16.8393, 0.37304, 5.02669, 8.41073, 0.37304, 9.77669, 9.16073, 0.37304, 9.27669, 9.16073, 0.37304, 7.27669, 10.4107, 0.87304, 6.77669, 10.4107, 0.37304, 4.02669, 8.41073, 0.37304, -6.47331, -15.5893, 0.37304, 3.02669, 8.16073, 0.37304, 10.7767, -54.5893, 0.37304, 10.0267, -53.3393, 0.37304, 9.52669, -53.3393, 0.37304, 8.02669, -54.8393, 0.37304, 4.02669, -55.8393, 0.37304, 4.77669, -56.0893, 0.37304, 9.52669, -55.0893, 0.37304, 2.02669, -58.3393, 0.37304, 0.776688, -48.0893, 4.12304, 8.27669, -46.8393, 3.62304, 7.52669, -46.8393, 3.62304, 6.02669, -52.0893, 3.87304, 9.27669, -48.0893, 4.12304, 9.27669, -48.0893, 3.62304, -1.97331, -48.8393, 3.62304, -1.97331, -53.3393, 3.62304, -1.22331, -48.5791, 4.12304, 5.02254, -48.5842, 3.62304, 3.53959, -50.1142, 4.12304, 5.01156, -51.6042, 3.62304, 3.51761, -48.3393, 0.37304, -1.97331, -48.3393, 0.37304, 0.276688, -47.8393, 0.37304, 1.02669, 13.6607, 0.37304, 4.02669, 13.4107, 0.37304, 7.02669, 15.6607, 0.37304, 7.77669, 46.9107, 0.37304, 8.77669, 46.9107, 0.37304, 7.02669, 47.4107, 0.37304, 6.27669, 51.4107, 0.37304, 6.27669, 51.9107, 0.37304, 5.02669, 51.9107, 0.37304, 2.02669, 47.1607, 0.37304, 1.02669, 54.9107, 0.37304, 4.52669, 55.6607, 0.37304, -1.22331, 52.9107, 0.37304, -1.47331, 15.9107, 0.37304, 8.77669, -50.0893, 0.37304, -0.473312, -50.8393, 0.37304, -0.973312, -51.5893, 0.37304, -0.223312, -50.3393, 0.37304, 1.77669, -50.5893, 0.37304, 4.02669, -49.8393, 0.37304, 4.02669, -47.8393, 0.37304, 3.02669, -47.3393, 0.37304, 4.02669, -23.0893, 0.37304, 1.77669, -22.3393, 0.37304, 3.77669, -21.3393, 0.62304, 3.02669, -23.5893, 0.37304, 5.77669, -22.3393, 0.37304, 5.02669, -24.3393, 0.37304, 1.77669, -19.8393, 5.12304, 2.52669, -19.8393, 5.12304, 3.77669, -18.5893, 5.12304, 3.77669, -18.5893, 5.12304, 2.52669, -14.8393, 0.37304, 15.0267, -14.3393, 0.37304, 15.7767, -14.3393, 0.37304, 13.7767, -21.0893, 0.37304, 5.77669, -16.5893, 0.37304, 15.2767, -17.0893, 0.37304, 5.52669, -27.8393, 0.37304, 18.0267, -16.3393, 0.37304, 17.2767, -28.5893, 0.37304, 17.2767, 11.6607, 2.62304, 5.02669, 11.6607, 2.62304, 5.77669, 12.4107, 2.62304, 5.77669, 12.4107, 2.62304, 5.02669, -37.5893, 0.37304, 7.52669, -37.8393, 0.87304, 8.52669, -31.3393, 0.37304, 16.2767, -30.0893, 0.37304, 16.2767, 52.9107, 0.37304, 16.0267, 53.9107, 0.37304, 18.2767, 54.4107, 0.37304, 17.7767, 53.4107, 0.37304, 15.7767, 55.6607, 0.37304, 11.7767, 54.4107, 0.37304, 10.7767, 54.6607, 0.37304, 8.27669, 53.9107, 0.37304, 7.27669, 57.1607, 0.37304, 17.7767, 58.1607, 0.37304, 11.5267, 47.9107, 3.62304, 12.7767, 47.9107, 3.62304, 14.5267, 52.1607, 3.62304, 14.5267, 52.4107, 3.62304, 10.5267, 52.4107, 3.62304, 7.77669, 48.1607, 3.62304, 7.52669, -40.3393, 0.87304, 8.77669, -38.8393, 0.62304, 9.02669, -45.5893, 0.37304, 8.52669, -45.3393, 0.87304, 13.5267, -45.0893, 0.37304, 14.7767, -41.0893, 0.37304, 8.77669, -39.9643, 0.87304, 8.90169, -44.1092, 0.37304, 12.5347, -38.0893, 0.37304, 9.02669, -46.8393, 0.37304, 19.7767, -33.3393, 0.37304, 20.0267, -33.3393, 0.37304, 18.7767, -46.5893, 0.37304, 8.77669, -46.5893, 0.37304, 13.2767, 50.4107, 0.37304, 9.27669, 50.4107, 0.37304, 8.27669, 49.6607, 0.37304, 8.02669, 49.6607, 0.37304, 14.0267, 50.1607, 0.37304, 14.0267, 10.9107, 5.12304, 8.52669, 10.9107, 5.12304, 9.77669, 12.1607, 5.12304, 9.77669, 12.1607, 5.12304, 8.52669, 15.9107, 0.37304, 9.27669, 19.6607, 0.37304, 14.7767, 46.6607, 0.37304, 15.5267, 50.1607, 0.37304, 22.0267, 49.9107, 0.37304, 15.7767, 46.6607, 0.37304, 22.5267, 46.4107, 0.37304, 23.0267, -46.5893, 3.62304, 15.0267, -46.5893, 3.62304, 14.5267, -47.3393, 3.62304, 14.5267, -51.0893, 3.62304, 18.0267, -51.3393, 3.62304, 19.5267, -48.3393, 3.62304, 19.7767, -52.0893, 3.87304, 10.2767, -53.5893, 3.62304, 11.0267, -53.5893, 3.62304, 11.7767, -48.0893, 3.62304, 13.5267, -49.0893, 4.12304, 12.7142, -48.0893, 4.12304, 12.11, 15.4107, 0.37304, 10.0267, 14.1607, 0.37304, 10.0267, 13.9107, 0.37304, 11.0267, 13.6607, 0.37304, 46.0267, 14.1607, 0.37304, 47.0267, 17.6607, 0.37304, 47.0267, 20.4107, 0.37304, 46.2767, 17.2062, 0.37304, 27.5608, 16.9335, 0.87304, 28.9812, 16.6607, 0.37304, 30.4017, 17.4108, 0.37304, 32.0066, 13.1607, 0.37304, 11.5267, 13.1607, 0.37304, 46.0267, 15.921, 0.37304, 24.5324, -8.83927, 0.37304, 14.0267, 8.91073, 0.37304, 11.5267, -4.40177, 0.37304, 13.4017, -2.9226, 0.87304, 13.1934, -1.44344, 0.37304, 12.985, 4.47323, 0.37304, 12.1517, 5.95239, 0.87304, 11.9434, 3.91073, 0.37304, 47.5267, 6.66073, 0.37304, 47.2767, 7.41073, 0.37304, 46.2767, -9.58927, 0.37304, 17.2767, -8.58927, 0.37304, 15.5267, -5.6726, 0.37304, 14.86, -4.21427, 0.87304, 14.5267, -2.75594, 0.37304, 14.1934, 4.53573, 0.37304, 12.5267, 5.99406, 0.87304, 12.1934, 10.6607, 0.37304, 46.2767, 10.6607, 0.37304, 11.5267, 3.41073, 0.37304, 48.0267, 43.6607, 0.37304, 46.5267, 44.1607, 0.37304, 48.0267, 44.6607, 0.37304, 46.2767, 30.9107, 0.37304, 49.2767, 31.9107, 0.37304, 49.2767, 31.9107, 0.37304, 47.2767, 30.4107, 0.37304, 50.0267, 27.6607, 0.37304, 50.7767, 25.1607, 0.37304, 47.2767, 25.4107, 0.37304, 49.7767, 45.4107, 0.37304, 36.2767, 47.4107, 0.37304, 35.5267, 47.6607, 0.37304, 32.2767, 46.1607, 0.37304, 46.2767, 46.6607, 0.37304, 42.0267, 45.4107, 0.37304, 38.0267, 38.1607, 0.37304, 46.2767, 24.4107, 0.62304, 45.5267, 32.4107, 0.37304, 46.7767, 23.4158, 0.87304, 20.0396, 23.4161, 0.37304, 23.0093, 41.3862, 0.87304, 23.0412, 32.406, 0.37304, 21.5434, 41.4114, 0.37304, 24.506, 24.9008, 0.37304, 20.0506, 36.911, 0.37304, 21.5264, 42.9212, 0.37304, 23.0022, -12.8393, 5.12304, 15.0267, -12.8393, 5.12304, 16.2767, -11.5893, 5.12304, 16.2767, -11.5893, 5.12304, 15.0267, 56.4107, 0.37304, 25.7767, 56.9107, 0.37304, 25.2767, 56.9107, 0.37304, 23.2767, 54.4107, 0.37304, 23.2767, 52.4107, 0.37304, 22.0267, 52.9107, 0.37304, 22.5267, 53.1607, 0.37304, 22.5267, 53.6607, 0.37304, 29.5267, 55.9107, 0.37304, 29.2767, -53.0893, 0.37304, 16.7767, -53.5893, 0.37304, 16.7767, -53.5893, 0.37304, 18.2767, -52.5893, 0.37304, 19.2767, -52.0893, 0.37304, 24.5267, -52.0893, 0.37304, 23.5267, -53.5893, 0.37304, 22.7767, -54.8393, 0.37304, 23.5267, -54.5893, 0.37304, 18.7767, -54.3393, 0.37304, 20.0267, -54.8393, 0.37304, 20.5267, -14.8393, 0.37304, 18.0267, -15.3393, 0.37304, 17.2767, -25.8393, 0.37304, 23.7767, -19.3393, 0.37304, 45.5267, -19.0893, 0.37304, 46.0267, -16.6287, 0.37304, 29.8162, -16.4051, 0.87304, 28.3425, -16.1814, 0.37304, 26.8688, -27.8393, 0.37304, 19.2767, -28.8393, 0.37304, 21.0267, -20.8393, 0.37304, 45.7767, -21.5893, 0.37304, 52.2767, -21.8393, 0.37304, 52.7767, -21.5893, 0.37304, 53.2767, -18.8393, 0.37304, 53.0267, -18.0893, 0.37304, 51.2767, -14.5893, 0.37304, 51.2767, -13.0893, 0.37304, 51.2767, -12.0893, 0.37304, 50.2767, -1.58927, 0.37304, 53.5267, 0.910728, 0.37304, 54.5267, 2.66073, 0.37304, 52.2767, -5.08927, 0.37304, 51.7767, -8.83927, 0.37304, 50.2767, -10.0893, 0.37304, 18.0267, -9.58909, 0.87304, 29.0165, -9.61403, 0.37304, 30.5214, -5.07866, 0.87304, 36.512, -6.58885, 0.37304, 33.5362, -5.11355, 0.37304, 38.0109, -5.10875, 0.37304, 35.0241, -11.099, 0.37304, 30.5104, -9.56919, 0.37304, 26.0286, 2.66073, 0.37304, 53.5267, -17.0893, 0.37304, 54.0267, -15.0893, 0.37304, 53.0267, -15.0893, 0.37304, 51.7767, -14.0892, 0.37304, 24.5066, -18.0893, 0.37304, 49.5267, 3.16073, 0.37304, 51.7767, 3.91073, 0.37304, 51.7767, -3.83927, 0.37304, 53.7767, -31.3393, 3.87304, 19.0267, -31.8393, 3.62304, 19.2767, -31.8393, 3.62304, 20.0267, -30.3393, 3.62304, 20.5267, -29.3393, 3.37304, 19.0267, -29.3393, 3.62304, 18.2767, -31.0893, 3.62304, 17.7767, -47.8393, 3.62304, 24.2767, -47.8393, 3.62304, 23.5267, -49.0893, 3.62304, 23.0267, -50.0893, 3.62304, 30.2767, -52.3393, 3.62304, 31.2767, -52.3393, 3.62304, 31.7767, -50.8393, 3.62304, 37.0267, -48.0893, 3.62304, 36.2767, -49.5893, 3.62304, 31.5267, -52.0893, 3.62304, 22.0267, -50.5893, 3.62304, 22.7767, -49.5893, 3.62304, 30.0267, -50.5893, 3.62304, 29.0267, -48.5893, 3.62304, 29.0267, -50.5893, 3.62304, 23.7767, -51.5893, 3.62304, 29.0267, -47.5893, 0.62304, 22.2767, -46.8393, 0.37304, 22.2767, -46.5893, 0.37304, 22.7767, -33.3393, 0.37304, 20.7767, -32.8393, 0.37304, 21.0267, -46.8393, 0.37304, 37.0267, -49.0893, 0.37304, 37.7767, -49.3393, 0.37304, 38.2767, -46.3393, 0.37304, 38.5267, -45.5893, 0.37304, 39.0267, -36.5893, 0.62304, 45.2767, -35.5893, 0.37304, 45.2767, -46.8393, 0.37304, 35.5267, -35.3393, 0.37304, 45.7767, -30.8393, 0.37304, 22.0267, -31.433, 0.37304, 24.9329, -31.7299, 0.87304, 26.3861, -32.0268, 0.37304, 27.8392, -31.8981, 0.37304, 27.6149, -31.6334, 0.87304, 26.2179, -31.3687, 0.37304, 24.8208, -47.5893, 0.37304, 30.5267, -48.3393, 0.37304, 30.7767, -47.3393, 0.37304, 29.5267, -46.3393, 0.62304, 45.7767, -42.0893, 0.37304, 47.0267, -41.0733, 0.87304, 38.0246, -41.0683, 0.37304, 36.5477, -33.574, 0.37304, 26.0154, -41.1132, 0.37304, 39.5405, -33.8393, 0.37304, 50.2767, -33.8393, 0.37304, 52.0267, -31.5893, 0.37304, 51.2767, -31.5893, 0.37304, 49.7767, -31.0893, 0.37304, 49.2767, -28.8393, 0.37304, 47.7767, -28.8393, 0.37304, 49.2767, -29.8393, 0.37304, 22.0267, -25.5893, 0.37304, 47.0267, 52.1607, 3.62304, 27.2767, 51.6607, 3.62304, 23.2767, 47.6607, 3.62304, 23.7767, 48.1607, 3.62304, 28.5267, 48.9107, 3.62304, 29.7767, 51.6607, 3.62304, 35.0267, 48.6607, 3.62304, 34.5267, 49.1607, 0.37304, 24.0267, 49.9107, 0.37304, 26.7767, 49.9107, 0.37304, 24.0267, 49.9107, 0.37304, 27.2767, 49.9107, 0.37304, 28.5267, 50.4107, 0.37304, 28.5267, 49.9107, 0.37304, 30.5267, 49.9107, 0.37304, 31.7767, 50.4107, 0.37304, 31.2767, 50.4107, 0.37304, 30.2767, 53.1607, 3.62304, 40.7767, 53.4107, 3.62304, 39.0267, 51.4107, 3.62304, 38.2767, 48.4107, 3.62304, 36.5267, 47.9107, 3.62304, 41.5267, 47.6607, 3.62304, 44.7767, 53.1607, 3.62304, 45.5267, 54.1607, 3.62304, 41.2767, 46.6607, 3.62304, 37.2767, 52.6607, 0.37304, 37.5267, 53.1607, 0.37304, 37.7767, 54.1607, 0.37304, 37.7767, 53.6607, 0.37304, 34.7767, 52.9107, 0.37304, 34.7767, 52.6607, 0.37304, 36.0267, 54.4107, 0.37304, 36.0267, 55.1607, 0.37304, 39.2767, 54.6607, 0.37304, 38.7767, 54.6607, 0.37304, 40.0267, 55.6607, 0.37304, 40.5267, 54.6607, 0.37304, 45.2767, 56.9107, 0.37304, 45.2767, 57.1607, 0.37304, 42.0267, 57.9107, 0.37304, 40.7767, 57.6607, 0.37304, 39.0267, -52.3393, 4.12304, 45.2767, -52.3393, 4.12304, 47.2767, -51.8393, 4.12304, 47.7767, -47.5893, 3.87304, 46.2767, -47.5893, 3.87304, 44.2767, -46.8393, 3.87304, 47.0267, -46.8393, 3.62304, 39.7767, -51.3393, 3.62304, 39.2767, -51.7393, 4.12304, 41.6767, -50.1143, 3.62304, 41.0017, -50.0893, 0.37304, 41.0267, -49.0893, 0.37304, 41.0267, -48.8393, 0.37304, 40.5267, -50.0893, 0.37304, 40.0267, -49.5893, 0.37304, 44.0267, -49.5893, 0.37304, 42.7767, -50.0893, 0.37304, 42.7767, -50.5893, 0.37304, 46.5267, -49.8393, 0.37304, 46.7767, 47.9107, 3.62304, 52.2767, 48.1607, 3.62304, 52.7767, 49.6607, 3.62304, 52.5267, 46.9107, 3.62304, 47.7767, 45.6607, 3.62304, 47.7767, 45.4107, 3.62304, 49.0267, 44.9107, 3.62304, 51.7767, 52.4107, 3.62304, 53.0267, 52.4107, 3.62304, 52.2767, 44.6607, 3.62304, 49.5267, 53.1607, 3.62304, 51.5267, 53.6607, 3.62304, 46.7767, 47.4107, 3.62304, 47.2767, 55.9107, 3.62304, 51.0267, 54.9107, 3.62304, 46.7767, -38.0893, 3.62304, 52.0267, -35.3393, 3.62304, 51.0267, -36.3393, 3.62304, 46.7767, -40.8393, 3.62304, 48.2767, -38.5893, 3.62304, 54.5267, -45.0893, 3.62304, 47.5267, -45.8393, 3.62304, 52.7767, -52.5893, 3.62304, 51.5267, -26.8393, 3.62304, 52.2767, -19.3393, 3.62304, 50.5267, -20.3393, 3.62304, 47.0267, -27.5893, 3.62304, 49.0267, 17.4107, 3.62304, 48.2767, 17.6607, 3.62304, 50.5267, 18.6607, 3.62304, 51.0267, 24.4107, 3.62304, 51.5267, 23.6607, 3.62304, 47.0267, 18.9107, 3.62304, 52.7767, 14.1607, 3.62304, 53.2767, 14.6607, 3.62304, 53.2767, 15.4107, 3.62304, 52.0267, 13.4107, 3.62304, 48.2767, 9.91073, 3.62304, 48.0267, 9.66073, 3.62304, 51.7767, 16.9107, 3.62304, 52.0267, 16.9107, 3.62304, 51.0267, 7.16073, 3.62304, 48.5267, 4.91073, 3.62304, 48.7767, 4.91073, 3.62304, 50.0267, 8.16073, 3.62304, 47.7767, 12.9107, 3.62304, 47.2767, 5.41073, 3.62304, 52.2767, 42.6607, 3.62304, 53.2767, 43.4107, 3.62304, 53.2767, 43.6607, 3.62304, 52.2767, 41.1607, 3.62304, 52.5267, 34.9107, 3.62304, 48.2767, 34.9107, 3.62304, 51.7767, 35.4107, 3.62304, 52.5267, 42.6607, 3.62304, 48.5267, 38.6607, 3.62304, 47.5267, 43.4107, 3.62304, 49.2767, 35.1607, 3.62304, 47.7767, 42.6607, 3.62304, 47.7767, 33.1607, 3.62304, 48.0267, 32.9107, 3.62304, 51.7767, -20.5893, 0.37304, 49.0267, -20.5893, 0.62304, 48.5267, -21.3393, 0.37304, 48.5267, -22.5893, 0.37304, 49.0267, 21.4107, 0.37304, 49.2767, 23.6607, 0.37304, 49.7767, 23.4107, 0.37304, 48.7767)
polygons = [PackedInt32Array(1, 0, 2), PackedInt32Array(2, 0, 3), PackedInt32Array(5, 4, 6), PackedInt32Array(6, 4, 8), PackedInt32Array(6, 8, 7), PackedInt32Array(10, 9, 11), PackedInt32Array(13, 12, 14), PackedInt32Array(14, 12, 15), PackedInt32Array(15, 12, 17), PackedInt32Array(15, 17, 16), PackedInt32Array(19, 18, 11), PackedInt32Array(12, 20, 17), PackedInt32Array(22, 21, 23), PackedInt32Array(23, 21, 7), PackedInt32Array(23, 7, 8), PackedInt32Array(25, 24, 26), PackedInt32Array(26, 24, 27), PackedInt32Array(21, 28, 7), PackedInt32Array(0, 29, 3), PackedInt32Array(3, 29, 19), PackedInt32Array(9, 30, 11), PackedInt32Array(11, 30, 31), PackedInt32Array(33, 32, 34), PackedInt32Array(34, 32, 35), PackedInt32Array(27, 15, 36), PackedInt32Array(36, 15, 35), PackedInt32Array(35, 15, 16), PackedInt32Array(35, 16, 34), PackedInt32Array(20, 38, 37), PackedInt32Array(26, 27, 39), PackedInt32Array(39, 27, 36), PackedInt32Array(41, 40, 37), PackedInt32Array(37, 40, 42), PackedInt32Array(37, 42, 20), PackedInt32Array(20, 42, 17), PackedInt32Array(3, 19, 31), PackedInt32Array(31, 19, 11), PackedInt32Array(44, 49, 45), PackedInt32Array(45, 49, 35), PackedInt32Array(35, 31, 30), PackedInt32Array(31, 47, 43), PackedInt32Array(43, 48, 44), PackedInt32Array(44, 46, 49), PackedInt32Array(49, 31, 35), PackedInt32Array(47, 48, 43), PackedInt32Array(31, 49, 47), PackedInt32Array(48, 46, 44), PackedInt32Array(46, 47, 49), PackedInt32Array(47, 46, 48), PackedInt32Array(8, 43, 23), PackedInt32Array(23, 43, 44), PackedInt32Array(35, 32, 45), PackedInt32Array(45, 50, 44), PackedInt32Array(52, 51, 53), PackedInt32Array(53, 51, 54), PackedInt32Array(56, 55, 57), PackedInt32Array(57, 55, 53), PackedInt32Array(57, 53, 54), PackedInt32Array(57, 54, 58), PackedInt32Array(60, 59, 61), PackedInt32Array(61, 59, 62), PackedInt32Array(63, 62, 64), PackedInt32Array(64, 62, 59), PackedInt32Array(64, 59, 65), PackedInt32Array(54, 66, 58), PackedInt32Array(69, 68, 67), PackedInt32Array(71, 70, 72), PackedInt32Array(72, 70, 73), PackedInt32Array(62, 74, 61), PackedInt32Array(76, 75, 73), PackedInt32Array(77, 69, 70), PackedInt32Array(70, 69, 73), PackedInt32Array(73, 69, 67), PackedInt32Array(73, 67, 76), PackedInt32Array(78, 63, 79), PackedInt32Array(79, 63, 64), PackedInt32Array(58, 72, 73), PackedInt32Array(73, 79, 58), PackedInt32Array(58, 79, 57), PackedInt32Array(57, 79, 64), PackedInt32Array(81, 80, 82), PackedInt32Array(82, 80, 85), PackedInt32Array(82, 85, 84), PackedInt32Array(82, 84, 83), PackedInt32Array(80, 86, 85), PackedInt32Array(89, 88, 87), PackedInt32Array(87, 90, 89), PackedInt32Array(89, 90, 93), PackedInt32Array(89, 93, 92), PackedInt32Array(89, 92, 91), PackedInt32Array(96, 95, 94), PackedInt32Array(98, 97, 74), PackedInt32Array(100, 99, 101), PackedInt32Array(101, 99, 102), PackedInt32Array(104, 103, 105), PackedInt32Array(105, 103, 107), PackedInt32Array(105, 107, 106), PackedInt32Array(109, 108, 94), PackedInt32Array(94, 108, 96), PackedInt32Array(96, 108, 110), PackedInt32Array(96, 110, 111), PackedInt32Array(113, 112, 114), PackedInt32Array(114, 112, 115), PackedInt32Array(74, 62, 116), PackedInt32Array(111, 117, 96), PackedInt32Array(118, 105, 119), PackedInt32Array(119, 105, 106), PackedInt32Array(119, 106, 120), PackedInt32Array(122, 121, 116), PackedInt32Array(116, 121, 102), PackedInt32Array(116, 102, 98), PackedInt32Array(116, 98, 74), PackedInt32Array(123, 111, 120), PackedInt32Array(120, 111, 110), PackedInt32Array(120, 110, 121), PackedInt32Array(119, 120, 124), PackedInt32Array(124, 120, 121), PackedInt32Array(115, 112, 121), PackedInt32Array(121, 112, 125), PackedInt32Array(121, 125, 101), PackedInt32Array(121, 126, 115), PackedInt32Array(102, 121, 101), PackedInt32Array(125, 127, 101), PackedInt32Array(128, 124, 122), PackedInt32Array(122, 124, 121), PackedInt32Array(116, 129, 122), PackedInt32Array(131, 130, 132), PackedInt32Array(132, 130, 133), PackedInt32Array(135, 134, 136), PackedInt32Array(136, 134, 137), PackedInt32Array(139, 138, 140), PackedInt32Array(140, 138, 141), PackedInt32Array(137, 134, 142), PackedInt32Array(137, 142, 143), PackedInt32Array(143, 142, 141), PackedInt32Array(141, 142, 140), PackedInt32Array(140, 142, 144), PackedInt32Array(147, 146, 145), PackedInt32Array(149, 148, 150), PackedInt32Array(150, 148, 153), PackedInt32Array(150, 153, 152), PackedInt32Array(150, 152, 151), PackedInt32Array(155, 154, 156), PackedInt32Array(156, 154, 158), PackedInt32Array(156, 158, 157), PackedInt32Array(159, 157, 160), PackedInt32Array(162, 161, 159), PackedInt32Array(159, 161, 157), PackedInt32Array(157, 161, 163), PackedInt32Array(157, 163, 156), PackedInt32Array(164, 153, 161), PackedInt32Array(161, 153, 148), PackedInt32Array(161, 148, 163), PackedInt32Array(164, 161, 165), PackedInt32Array(168, 167, 166), PackedInt32Array(171, 170, 169), PackedInt32Array(173, 172, 166), PackedInt32Array(166, 172, 174), PackedInt32Array(177, 176, 175), PackedInt32Array(166, 174, 177), PackedInt32Array(166, 177, 168), PackedInt32Array(168, 177, 169), PackedInt32Array(169, 177, 171), PackedInt32Array(171, 177, 175), PackedInt32Array(179, 178, 180), PackedInt32Array(180, 178, 182), PackedInt32Array(180, 182, 181), PackedInt32Array(180, 181, 175), PackedInt32Array(175, 181, 171), PackedInt32Array(185, 184, 183), PackedInt32Array(183, 184, 178), PackedInt32Array(178, 184, 182), PackedInt32Array(187, 186, 188), PackedInt32Array(188, 186, 189), PackedInt32Array(189, 186, 185), PackedInt32Array(189, 185, 183), PackedInt32Array(192, 191, 190), PackedInt32Array(192, 190, 193), PackedInt32Array(193, 190, 194), PackedInt32Array(196, 195, 197), PackedInt32Array(197, 195, 198), PackedInt32Array(197, 198, 194), PackedInt32Array(200, 199, 201), PackedInt32Array(201, 199, 202), PackedInt32Array(202, 199, 203), PackedInt32Array(203, 199, 193), PackedInt32Array(203, 193, 194), PackedInt32Array(203, 194, 198), PackedInt32Array(207, 206, 204), PackedInt32Array(204, 206, 205), PackedInt32Array(210, 209, 208), PackedInt32Array(213, 212, 211), PackedInt32Array(216, 215, 214), PackedInt32Array(220, 219, 217), PackedInt32Array(217, 219, 218), PackedInt32Array(224, 223, 221), PackedInt32Array(221, 223, 222), PackedInt32Array(227, 226, 225), PackedInt32Array(228, 227, 229), PackedInt32Array(229, 227, 225), PackedInt32Array(231, 230, 232), PackedInt32Array(234, 233, 235), PackedInt32Array(235, 233, 230), PackedInt32Array(230, 233, 232), PackedInt32Array(238, 237, 236), PackedInt32Array(240, 239, 241), PackedInt32Array(241, 239, 244), PackedInt32Array(244, 239, 245), PackedInt32Array(245, 239, 246), PackedInt32Array(246, 239, 242), PackedInt32Array(242, 239, 243), PackedInt32Array(243, 239, 65), PackedInt32Array(249, 248, 252), PackedInt32Array(252, 248, 250), PackedInt32Array(250, 248, 247), PackedInt32Array(250, 247, 251), PackedInt32Array(242, 253, 246), PackedInt32Array(246, 253, 256), PackedInt32Array(246, 256, 245), PackedInt32Array(245, 256, 255), PackedInt32Array(245, 255, 244), PackedInt32Array(244, 255, 254), PackedInt32Array(244, 254, 241), PackedInt32Array(65, 269, 258), PackedInt32Array(258, 260, 45), PackedInt32Array(45, 271, 50), PackedInt32Array(50, 271, 257), PackedInt32Array(257, 271, 239), PackedInt32Array(239, 268, 65), PackedInt32Array(269, 260, 258), PackedInt32Array(65, 268, 269), PackedInt32Array(260, 271, 45), PackedInt32Array(271, 267, 239), PackedInt32Array(239, 270, 268), PackedInt32Array(269, 259, 260), PackedInt32Array(268, 272, 269), PackedInt32Array(260, 261, 271), PackedInt32Array(267, 270, 239), PackedInt32Array(271, 262, 267), PackedInt32Array(270, 265, 268), PackedInt32Array(259, 261, 260), PackedInt32Array(269, 264, 259), PackedInt32Array(272, 264, 269), PackedInt32Array(268, 265, 272), PackedInt32Array(261, 262, 271), PackedInt32Array(267, 266, 270), PackedInt32Array(262, 263, 267), PackedInt32Array(270, 266, 265), PackedInt32Array(259, 264, 261), PackedInt32Array(272, 266, 264), PackedInt32Array(265, 266, 272), PackedInt32Array(261, 263, 262), PackedInt32Array(267, 263, 266), PackedInt32Array(264, 263, 261), PackedInt32Array(266, 263, 264), PackedInt32Array(65, 258, 64), PackedInt32Array(251, 247, 239), PackedInt32Array(239, 247, 257), PackedInt32Array(257, 273, 50), PackedInt32Array(45, 274, 258), PackedInt32Array(247, 275, 257), PackedInt32Array(277, 276, 278), PackedInt32Array(278, 276, 281), PackedInt32Array(278, 281, 280), PackedInt32Array(278, 280, 279), PackedInt32Array(282, 280, 283), PackedInt32Array(283, 280, 281), PackedInt32Array(283, 281, 284), PackedInt32Array(284, 287, 283), PackedInt32Array(283, 287, 285), PackedInt32Array(285, 287, 286), PackedInt32Array(288, 278, 289), PackedInt32Array(289, 278, 279), PackedInt32Array(290, 286, 291), PackedInt32Array(291, 286, 287), PackedInt32Array(293, 292, 294), PackedInt32Array(294, 292, 296), PackedInt32Array(294, 296, 295), PackedInt32Array(298, 297, 299), PackedInt32Array(299, 297, 302), PackedInt32Array(299, 302, 300), PackedInt32Array(300, 302, 301), PackedInt32Array(297, 303, 302), PackedInt32Array(306, 305, 304), PackedInt32Array(309, 308, 307), PackedInt32Array(311, 310, 312), PackedInt32Array(312, 310, 313), PackedInt32Array(313, 310, 307), PackedInt32Array(307, 310, 309), PackedInt32Array(314, 309, 315), PackedInt32Array(315, 309, 310), PackedInt32Array(318, 317, 316), PackedInt32Array(319, 303, 320), PackedInt32Array(320, 303, 297), PackedInt32Array(320, 297, 322), PackedInt32Array(320, 322, 321), PackedInt32Array(323, 318, 322), PackedInt32Array(322, 318, 316), PackedInt32Array(322, 316, 321), PackedInt32Array(321, 316, 324), PackedInt32Array(326, 325, 327), PackedInt32Array(327, 325, 328), PackedInt32Array(329, 328, 325), PackedInt32Array(331, 330, 329), PackedInt32Array(329, 330, 328), PackedInt32Array(333, 332, 334), PackedInt32Array(334, 332, 337), PackedInt32Array(334, 337, 335), PackedInt32Array(335, 337, 336), PackedInt32Array(340, 339, 338), PackedInt32Array(342, 341, 343), PackedInt32Array(343, 341, 344), PackedInt32Array(347, 346, 345), PackedInt32Array(350, 349, 348), PackedInt32Array(343, 344, 351), PackedInt32Array(351, 344, 352), PackedInt32Array(350, 348, 21), PackedInt32Array(347, 345, 353), PackedInt32Array(353, 345, 354), PackedInt32Array(350, 21, 355), PackedInt32Array(355, 21, 356), PackedInt32Array(356, 21, 22), PackedInt32Array(344, 357, 352), PackedInt32Array(352, 357, 353), PackedInt32Array(359, 358, 357), PackedInt32Array(357, 358, 353), PackedInt32Array(352, 365, 360), PackedInt32Array(360, 366, 257), PackedInt32Array(257, 368, 355), PackedInt32Array(355, 368, 354), PackedInt32Array(354, 367, 353), PackedInt32Array(353, 365, 352), PackedInt32Array(365, 366, 360), PackedInt32Array(366, 369, 257), PackedInt32Array(257, 369, 368), PackedInt32Array(368, 367, 354), PackedInt32Array(367, 365, 353), PackedInt32Array(365, 362, 366), PackedInt32Array(366, 361, 369), PackedInt32Array(369, 361, 368), PackedInt32Array(368, 364, 367), PackedInt32Array(367, 363, 365), PackedInt32Array(362, 361, 366), PackedInt32Array(365, 363, 362), PackedInt32Array(361, 364, 368), PackedInt32Array(364, 363, 367), PackedInt32Array(362, 364, 361), PackedInt32Array(363, 364, 362), PackedInt32Array(360, 370, 352), PackedInt32Array(371, 273, 257), PackedInt32Array(356, 371, 355), PackedInt32Array(355, 371, 257), PackedInt32Array(372, 374, 373), PackedInt32Array(376, 375, 372), PackedInt32Array(372, 375, 374), PackedInt32Array(379, 378, 377), PackedInt32Array(374, 375, 102), PackedInt32Array(102, 375, 379), PackedInt32Array(102, 379, 377), PackedInt32Array(102, 377, 98), PackedInt32Array(382, 381, 380), PackedInt32Array(383, 324, 384), PackedInt32Array(384, 324, 385), PackedInt32Array(385, 324, 316), PackedInt32Array(380, 384, 382), PackedInt32Array(382, 384, 386), PackedInt32Array(386, 384, 385), PackedInt32Array(388, 387, 389), PackedInt32Array(389, 387, 390), PackedInt32Array(393, 392, 391), PackedInt32Array(391, 395, 394), PackedInt32Array(393, 391, 397), PackedInt32Array(397, 391, 394), PackedInt32Array(397, 394, 396), PackedInt32Array(397, 396, 398), PackedInt32Array(399, 397, 398), PackedInt32Array(401, 400, 402), PackedInt32Array(402, 400, 403), PackedInt32Array(404, 389, 405), PackedInt32Array(405, 389, 390), PackedInt32Array(387, 376, 403), PackedInt32Array(403, 376, 402), PackedInt32Array(402, 376, 398), PackedInt32Array(398, 376, 399), PackedInt32Array(399, 376, 372), PackedInt32Array(403, 390, 387), PackedInt32Array(360, 257, 275), PackedInt32Array(407, 406, 408), PackedInt32Array(408, 406, 409), PackedInt32Array(409, 406, 411), PackedInt32Array(409, 411, 410), PackedInt32Array(414, 413, 412), PackedInt32Array(416, 415, 414), PackedInt32Array(418, 417, 419), PackedInt32Array(419, 417, 346), PackedInt32Array(419, 346, 347), PackedInt32Array(419, 347, 420), PackedInt32Array(421, 416, 420), PackedInt32Array(420, 416, 414), PackedInt32Array(420, 414, 412), PackedInt32Array(420, 412, 419), PackedInt32Array(425, 424, 422), PackedInt32Array(422, 424, 423), PackedInt32Array(379, 427, 426), PackedInt32Array(429, 428, 430), PackedInt32Array(430, 428, 378), PackedInt32Array(430, 378, 426), PackedInt32Array(426, 378, 379), PackedInt32Array(432, 431, 433), PackedInt32Array(433, 431, 434), PackedInt32Array(434, 431, 435), PackedInt32Array(437, 436, 435), PackedInt32Array(439, 438, 437), PackedInt32Array(431, 441, 435), PackedInt32Array(435, 441, 437), PackedInt32Array(437, 441, 439), PackedInt32Array(439, 441, 440), PackedInt32Array(253, 443, 256), PackedInt32Array(256, 443, 255), PackedInt32Array(255, 443, 254), PackedInt32Array(254, 443, 442), PackedInt32Array(254, 442, 241), PackedInt32Array(446, 445, 444), PackedInt32Array(448, 447, 449), PackedInt32Array(449, 447, 450), PackedInt32Array(452, 451, 453), PackedInt32Array(453, 451, 443), PackedInt32Array(454, 453, 455), PackedInt32Array(455, 453, 456), PackedInt32Array(456, 453, 443), PackedInt32Array(444, 450, 446), PackedInt32Array(446, 450, 457), PackedInt32Array(444, 449, 450), PackedInt32Array(455, 456, 429), PackedInt32Array(429, 456, 428), PackedInt32Array(457, 442, 446), PackedInt32Array(446, 442, 458), PackedInt32Array(458, 442, 451), PackedInt32Array(451, 442, 443), PackedInt32Array(460, 459, 461), PackedInt32Array(461, 459, 464), PackedInt32Array(461, 464, 463), PackedInt32Array(461, 463, 462), PackedInt32Array(465, 421, 462), PackedInt32Array(462, 421, 461), PackedInt32Array(416, 421, 466), PackedInt32Array(466, 421, 465), PackedInt32Array(469, 468, 467), PackedInt32Array(467, 471, 470), PackedInt32Array(467, 475, 469), PackedInt32Array(469, 476, 472), PackedInt32Array(472, 476, 473), PackedInt32Array(473, 478, 474), PackedInt32Array(474, 478, 470), PackedInt32Array(470, 477, 467), PackedInt32Array(475, 476, 469), PackedInt32Array(467, 477, 475), PackedInt32Array(476, 478, 473), PackedInt32Array(478, 477, 470), PackedInt32Array(475, 477, 476), PackedInt32Array(476, 477, 478), PackedInt32Array(481, 480, 479), PackedInt32Array(483, 482, 484), PackedInt32Array(484, 482, 426), PackedInt32Array(484, 426, 486), PackedInt32Array(484, 486, 485), PackedInt32Array(489, 488, 490), PackedInt32Array(490, 488, 487), PackedInt32Array(490, 487, 491), PackedInt32Array(489, 490, 492), PackedInt32Array(492, 490, 494), PackedInt32Array(492, 494, 493), PackedInt32Array(487, 486, 491), PackedInt32Array(491, 486, 426), PackedInt32Array(482, 430, 426), PackedInt32Array(485, 495, 484), PackedInt32Array(497, 496, 498), PackedInt32Array(498, 496, 499), PackedInt32Array(501, 500, 499), PackedInt32Array(499, 500, 498), PackedInt32Array(503, 502, 481), PackedInt32Array(248, 249, 504), PackedInt32Array(504, 249, 506), PackedInt32Array(504, 506, 505), PackedInt32Array(508, 507, 505), PackedInt32Array(505, 507, 504), PackedInt32Array(504, 507, 509), PackedInt32Array(509, 507, 370), PackedInt32Array(370, 507, 352), PackedInt32Array(513, 512, 510), PackedInt32Array(510, 512, 511), PackedInt32Array(516, 515, 514), PackedInt32Array(507, 508, 517), PackedInt32Array(514, 518, 516), PackedInt32Array(516, 518, 519), PackedInt32Array(519, 518, 517), PackedInt32Array(516, 519, 447), PackedInt32Array(447, 519, 450), PackedInt32Array(517, 518, 507), PackedInt32Array(507, 518, 521), PackedInt32Array(507, 521, 520), PackedInt32Array(507, 520, 522), PackedInt32Array(526, 525, 523), PackedInt32Array(523, 525, 524), PackedInt32Array(352, 527, 351), PackedInt32Array(529, 528, 527), PackedInt32Array(527, 352, 529), PackedInt32Array(529, 352, 530), PackedInt32Array(522, 530, 507), PackedInt32Array(507, 530, 352), PackedInt32Array(534, 533, 531), PackedInt32Array(531, 533, 532), PackedInt32Array(535, 534, 536), PackedInt32Array(537, 536, 538), PackedInt32Array(538, 536, 534), PackedInt32Array(533, 534, 539), PackedInt32Array(539, 534, 535), PackedInt32Array(539, 535, 540), PackedInt32Array(542, 541, 543), PackedInt32Array(543, 541, 544), PackedInt32Array(544, 541, 545), PackedInt32Array(545, 541, 546), PackedInt32Array(341, 342, 547), PackedInt32Array(547, 342, 548), PackedInt32Array(553, 554, 552), PackedInt32Array(552, 554, 549), PackedInt32Array(549, 554, 550), PackedInt32Array(550, 554, 551), PackedInt32Array(551, 554, 548), PackedInt32Array(548, 554, 553), PackedInt32Array(552, 547, 553), PackedInt32Array(553, 547, 548), PackedInt32Array(358, 359, 549), PackedInt32Array(549, 359, 552), PackedInt32Array(548, 555, 551), PackedInt32Array(551, 555, 556), PackedInt32Array(556, 555, 558), PackedInt32Array(556, 558, 557), PackedInt32Array(559, 549, 560), PackedInt32Array(560, 549, 550), PackedInt32Array(528, 529, 555), PackedInt32Array(555, 529, 558), PackedInt32Array(562, 561, 563), PackedInt32Array(563, 561, 565), PackedInt32Array(563, 565, 564), PackedInt32Array(569, 568, 566), PackedInt32Array(566, 568, 567), PackedInt32Array(570, 495, 571), PackedInt32Array(571, 495, 572), PackedInt32Array(572, 495, 485), PackedInt32Array(574, 573, 572), PackedInt32Array(572, 573, 575), PackedInt32Array(575, 576, 572), PackedInt32Array(572, 576, 571), PackedInt32Array(578, 577, 579), PackedInt32Array(579, 577, 582), PackedInt32Array(579, 582, 580), PackedInt32Array(580, 582, 581), PackedInt32Array(584, 583, 585), PackedInt32Array(585, 583, 587), PackedInt32Array(585, 587, 586), PackedInt32Array(585, 586, 579), PackedInt32Array(585, 579, 580), PackedInt32Array(586, 587, 588), PackedInt32Array(588, 587, 471), PackedInt32Array(471, 587, 583), PackedInt32Array(471, 583, 470), PackedInt32Array(590, 589, 591), PackedInt32Array(591, 589, 571), PackedInt32Array(595, 599, 571), PackedInt32Array(571, 599, 596), PackedInt32Array(596, 599, 597), PackedInt32Array(597, 599, 598), PackedInt32Array(598, 599, 592), PackedInt32Array(592, 594, 593), PackedInt32Array(594, 592, 595), PackedInt32Array(595, 592, 599), PackedInt32Array(589, 570, 571), PackedInt32Array(601, 598, 592), PackedInt32Array(598, 602, 597), PackedInt32Array(597, 602, 596), PackedInt32Array(596, 602, 571), PackedInt32Array(571, 600, 591), PackedInt32Array(600, 602, 601), PackedInt32Array(601, 602, 598), PackedInt32Array(602, 600, 571), PackedInt32Array(604, 609, 458), PackedInt32Array(458, 609, 608), PackedInt32Array(458, 608, 607), PackedInt32Array(458, 607, 606), PackedInt32Array(458, 606, 605), PackedInt32Array(458, 605, 603), PackedInt32Array(458, 603, 446), PackedInt32Array(611, 610, 612), PackedInt32Array(612, 610, 613), PackedInt32Array(604, 619, 609), PackedInt32Array(609, 619, 608), PackedInt32Array(608, 619, 618), PackedInt32Array(608, 618, 607), PackedInt32Array(607, 618, 606), PackedInt32Array(606, 618, 617), PackedInt32Array(606, 617, 605), PackedInt32Array(605, 617, 616), PackedInt32Array(605, 616, 615), PackedInt32Array(605, 615, 614), PackedInt32Array(605, 614, 603), PackedInt32Array(604, 621, 619), PackedInt32Array(619, 621, 618), PackedInt32Array(618, 621, 617), PackedInt32Array(617, 621, 616), PackedInt32Array(616, 621, 615), PackedInt32Array(615, 621, 614), PackedInt32Array(614, 621, 613), PackedInt32Array(613, 621, 612), PackedInt32Array(612, 621, 620), PackedInt32Array(610, 622, 613), PackedInt32Array(600, 601, 621), PackedInt32Array(621, 601, 620), PackedInt32Array(623, 625, 624), PackedInt32Array(628, 627, 626), PackedInt32Array(626, 629, 628), PackedInt32Array(628, 629, 630), PackedInt32Array(630, 632, 631), PackedInt32Array(633, 635, 634), PackedInt32Array(637, 636, 625), PackedInt32Array(625, 623, 637), PackedInt32Array(637, 623, 638), PackedInt32Array(638, 623, 633), PackedInt32Array(633, 623, 639), PackedInt32Array(631, 640, 630), PackedInt32Array(630, 640, 628), PackedInt32Array(628, 640, 641), PackedInt32Array(641, 640, 639), PackedInt32Array(639, 640, 633), PackedInt32Array(595, 643, 640), PackedInt32Array(640, 645, 633), PackedInt32Array(633, 646, 635), PackedInt32Array(635, 646, 576), PackedInt32Array(576, 648, 571), PackedInt32Array(571, 643, 595), PackedInt32Array(643, 645, 640), PackedInt32Array(645, 646, 633), PackedInt32Array(646, 649, 576), PackedInt32Array(648, 645, 571), PackedInt32Array(576, 649, 648), PackedInt32Array(571, 642, 643), PackedInt32Array(643, 647, 645), PackedInt32Array(645, 648, 646), PackedInt32Array(646, 644, 649), PackedInt32Array(645, 647, 571), PackedInt32Array(649, 644, 648), PackedInt32Array(642, 647, 643), PackedInt32Array(571, 647, 642), PackedInt32Array(648, 644, 646), PackedInt32Array(653, 652, 650), PackedInt32Array(650, 652, 651), PackedInt32Array(655, 654, 656), PackedInt32Array(656, 654, 657), PackedInt32Array(659, 658, 660), PackedInt32Array(660, 658, 532), PackedInt32Array(660, 657, 659), PackedInt32Array(659, 657, 654), PackedInt32Array(659, 654, 662), PackedInt32Array(659, 662, 661), PackedInt32Array(531, 532, 574), PackedInt32Array(574, 532, 658), PackedInt32Array(574, 658, 573), PackedInt32Array(664, 663, 665), PackedInt32Array(665, 663, 666), PackedInt32Array(668, 667, 669), PackedInt32Array(669, 667, 670), PackedInt32Array(671, 665, 672), PackedInt32Array(672, 665, 666), PackedInt32Array(672, 666, 673), PackedInt32Array(673, 666, 669), PackedInt32Array(673, 669, 670), PackedInt32Array(675, 674, 521), PackedInt32Array(521, 674, 681), PackedInt32Array(521, 681, 680), PackedInt32Array(521, 680, 679), PackedInt32Array(521, 679, 676), PackedInt32Array(676, 679, 677), PackedInt32Array(677, 679, 678), PackedInt32Array(676, 683, 682), PackedInt32Array(682, 520, 676), PackedInt32Array(676, 520, 521), PackedInt32Array(677, 684, 676), PackedInt32Array(686, 685, 687), PackedInt32Array(687, 685, 688), PackedInt32Array(688, 685, 689), PackedInt32Array(692, 691, 690), PackedInt32Array(694, 693, 695), PackedInt32Array(695, 693, 696), PackedInt32Array(695, 696, 622), PackedInt32Array(698, 705, 678), PackedInt32Array(678, 703, 697), PackedInt32Array(697, 703, 696), PackedInt32Array(696, 703, 622), PackedInt32Array(622, 704, 613), PackedInt32Array(613, 706, 698), PackedInt32Array(705, 703, 678), PackedInt32Array(698, 706, 705), PackedInt32Array(703, 701, 622), PackedInt32Array(704, 706, 613), PackedInt32Array(622, 701, 704), PackedInt32Array(705, 702, 703), PackedInt32Array(706, 699, 705), PackedInt32Array(703, 702, 701), PackedInt32Array(704, 702, 706), PackedInt32Array(701, 702, 704), PackedInt32Array(705, 700, 702), PackedInt32Array(699, 700, 705), PackedInt32Array(706, 702, 699), PackedInt32Array(700, 699, 702), PackedInt32Array(695, 707, 694), PackedInt32Array(709, 708, 710), PackedInt32Array(710, 708, 689), PackedInt32Array(689, 708, 688), PackedInt32Array(679, 711, 678), PackedInt32Array(678, 711, 698), PackedInt32Array(698, 711, 674), PackedInt32Array(674, 711, 681), PackedInt32Array(681, 711, 680), PackedInt32Array(680, 711, 679), PackedInt32Array(712, 678, 692), PackedInt32Array(692, 678, 697), PackedInt32Array(690, 710, 689), PackedInt32Array(622, 714, 713), PackedInt32Array(689, 712, 690), PackedInt32Array(690, 712, 692), PackedInt32Array(696, 693, 715), PackedInt32Array(695, 622, 713), PackedInt32Array(717, 716, 718), PackedInt32Array(718, 716, 719), PackedInt32Array(721, 720, 722), PackedInt32Array(722, 720, 716), PackedInt32Array(716, 720, 719), PackedInt32Array(725, 724, 723), PackedInt32Array(727, 726, 728), PackedInt32Array(728, 726, 731), PackedInt32Array(728, 731, 729), PackedInt32Array(729, 731, 730), PackedInt32Array(733, 732, 725), PackedInt32Array(725, 732, 581), PackedInt32Array(725, 581, 582), PackedInt32Array(731, 726, 734), PackedInt32Array(734, 735, 736), PackedInt32Array(736, 735, 723), PackedInt32Array(723, 735, 725), PackedInt32Array(725, 735, 737), PackedInt32Array(737, 733, 725), PackedInt32Array(735, 734, 726), PackedInt32Array(735, 738, 737), PackedInt32Array(740, 739, 556), PackedInt32Array(740, 556, 741), PackedInt32Array(741, 556, 742), PackedInt32Array(742, 556, 557), PackedInt32Array(742, 743, 741), PackedInt32Array(745, 744, 746), PackedInt32Array(746, 744, 747), PackedInt32Array(750, 749, 748), PackedInt32Array(747, 744, 748), PackedInt32Array(748, 744, 751), PackedInt32Array(753, 759, 754), PackedInt32Array(754, 759, 758), PackedInt32Array(754, 758, 755), PackedInt32Array(755, 758, 757), PackedInt32Array(755, 757, 756), PackedInt32Array(756, 757, 750), PackedInt32Array(750, 757, 752), PackedInt32Array(751, 761, 760), PackedInt32Array(760, 762, 751), PackedInt32Array(751, 762, 748), PackedInt32Array(763, 748, 764), PackedInt32Array(764, 748, 749), PackedInt32Array(748, 768, 750), PackedInt32Array(750, 766, 756), PackedInt32Array(756, 767, 755), PackedInt32Array(755, 767, 754), PackedInt32Array(754, 743, 753), PackedInt32Array(743, 767, 741), PackedInt32Array(741, 767, 762), PackedInt32Array(762, 766, 748), PackedInt32Array(768, 765, 750), PackedInt32Array(748, 765, 768), PackedInt32Array(766, 767, 756), PackedInt32Array(750, 765, 766), PackedInt32Array(767, 743, 754), PackedInt32Array(767, 766, 762), PackedInt32Array(766, 765, 748), PackedInt32Array(772, 771, 769), PackedInt32Array(769, 771, 770), PackedInt32Array(773, 769, 774), PackedInt32Array(774, 769, 752), PackedInt32Array(774, 775, 773), PackedInt32Array(773, 772, 769), PackedInt32Array(676, 776, 683), PackedInt32Array(753, 776, 759), PackedInt32Array(759, 776, 758), PackedInt32Array(758, 776, 757), PackedInt32Array(757, 776, 676), PackedInt32Array(757, 676, 752), PackedInt32Array(752, 676, 774), PackedInt32Array(774, 676, 777), PackedInt32Array(782, 781, 778), PackedInt32Array(778, 781, 779), PackedInt32Array(779, 781, 780), PackedInt32Array(784, 782, 783), PackedInt32Array(783, 782, 778), PackedInt32Array(676, 684, 777), PackedInt32Array(787, 786, 785), PackedInt32Array(790, 789, 788), PackedInt32Array(794, 793, 791), PackedInt32Array(791, 793, 792), PackedInt32Array(795, 797, 796), PackedInt32Array(784, 783, 798), PackedInt32Array(798, 783, 797), PackedInt32Array(797, 795, 799), PackedInt32Array(799, 795, 800), PackedInt32Array(800, 795, 801), PackedInt32Array(795, 802, 801), PackedInt32Array(798, 797, 803), PackedInt32Array(803, 797, 799), PackedInt32Array(806, 805, 804), PackedInt32Array(808, 807, 809), PackedInt32Array(809, 807, 810), PackedInt32Array(809, 810, 804), PackedInt32Array(804, 810, 806), PackedInt32Array(812, 811, 813), PackedInt32Array(813, 811, 814), PackedInt32Array(816, 815, 817), PackedInt32Array(817, 815, 814), PackedInt32Array(817, 814, 818), PackedInt32Array(818, 814, 819), PackedInt32Array(819, 814, 811), PackedInt32Array(821, 820, 822), PackedInt32Array(822, 820, 823), PackedInt32Array(823, 820, 824), PackedInt32Array(822, 823, 825), PackedInt32Array(828, 824, 820), PackedInt32Array(824, 829, 826), PackedInt32Array(826, 829, 827), PackedInt32Array(827, 829, 828), PackedInt32Array(828, 829, 824), PackedInt32Array(831, 830, 832), PackedInt32Array(832, 830, 833), PackedInt32Array(835, 834, 836), PackedInt32Array(836, 834, 837), PackedInt32Array(837, 834, 838), PackedInt32Array(841, 840, 839), PackedInt32Array(843, 842, 844), PackedInt32Array(844, 842, 845), PackedInt32Array(845, 842, 839), PackedInt32Array(847, 846, 841), PackedInt32Array(845, 848, 844), PackedInt32Array(847, 841, 849), PackedInt32Array(849, 841, 839), PackedInt32Array(850, 849, 801), PackedInt32Array(801, 849, 839), PackedInt32Array(801, 839, 851), PackedInt32Array(801, 851, 800), PackedInt32Array(851, 839, 842), PackedInt32Array(853, 852, 850), PackedInt32Array(850, 852, 849), PackedInt32Array(855, 854, 856), PackedInt32Array(856, 854, 857), PackedInt32Array(854, 858, 857), PackedInt32Array(857, 858, 859), PackedInt32Array(859, 858, 860), PackedInt32Array(859, 860, 825), PackedInt32Array(825, 860, 822), PackedInt32Array(822, 860, 861), PackedInt32Array(865, 864, 862), PackedInt32Array(862, 864, 863), PackedInt32Array(867, 866, 868), PackedInt32Array(868, 866, 870), PackedInt32Array(868, 870, 869), PackedInt32Array(869, 871, 868), PackedInt32Array(873, 872, 874), PackedInt32Array(874, 872, 875), PackedInt32Array(875, 872, 877), PackedInt32Array(875, 877, 876), PackedInt32Array(879, 878, 874), PackedInt32Array(882, 881, 880), PackedInt32Array(880, 883, 876), PackedInt32Array(866, 867, 879), PackedInt32Array(876, 884, 875), PackedInt32Array(882, 880, 885), PackedInt32Array(885, 880, 877), PackedInt32Array(877, 880, 876), PackedInt32Array(879, 874, 866), PackedInt32Array(866, 874, 875), PackedInt32Array(887, 886, 888), PackedInt32Array(888, 886, 889), PackedInt32Array(891, 890, 892), PackedInt32Array(892, 890, 894), PackedInt32Array(892, 894, 889), PackedInt32Array(889, 894, 893), PackedInt32Array(895, 848, 845), PackedInt32Array(845, 888, 895), PackedInt32Array(895, 888, 893), PackedInt32Array(893, 888, 889), PackedInt32Array(890, 896, 894), PackedInt32Array(894, 897, 893), PackedInt32Array(890, 891, 898), PackedInt32Array(898, 891, 899), PackedInt32Array(901, 900, 902), PackedInt32Array(902, 900, 903), PackedInt32Array(906, 905, 904)]
agent_height = 1.75
agent_radius = 0.375
agent_max_climb = 0.5
edge_max_length = 12.0
filter_low_hanging_obstacles = true
filter_ledge_spans = true
filter_walkable_low_height_spans = true

[sub_resource type="StandardMaterial3D" id="StandardMaterial3D_cin4e"]
albedo_texture = ExtResource("15_df8b0")
metallic = 0.2
metallic_texture = ExtResource("16_003du")
roughness = 0.8
uv1_scale = Vector3(10, 10, 1)

[sub_resource type="StandardMaterial3D" id="StandardMaterial3D_01ds2"]
transparency = 1
albedo_color = Color(1, 1, 0, 0.3)
emission_enabled = true
emission = Color(1, 1, 0, 1)

[node name="level01" type="Node3D"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 0, 0, 1.94383)
script = ExtResource("1_hk2b6")
minimap_scene = ExtResource("2_j32d5")
player_avatar_scene = ExtResource("3_0f42i")
item_boxes_scene = ExtResource("4_swvig")
skill_box_ui_scene = ExtResource("5_dbohx")

[node name="WorldEnvironment" type="WorldEnvironment" parent="."]
environment = SubResource("Environment_j4i7h")

[node name="DirectionalLight3D" type="DirectionalLight3D" parent="."]
transform = Transform3D(0.707107, -0.5, 0.5, 0, 0.707107, 0.707107, -0.707107, -0.5, 0.5, 0, 0, 0)
light_color = Color(1, 0.95, 0.8, 1)
light_energy = 1.2
shadow_enabled = true
shadow_opacity = 0.6
shadow_blur = 1.5

[node name="ModularLevelGenerator" type="Node3D" parent="."]
script = ExtResource("6_6ly8b")
level_config = ExtResource("7_r5102")

[node name="FogOfWar" parent="." instance=ExtResource("9_003du")]
player_vision_radius = 8.0
enabled = false

[node name="Camera3D" type="Camera3D" parent="."]
unique_name_in_owner = true
transform = Transform3D(1, 0, 0, 0, 0.866025, 0.5, 0, -0.5, 0.866025, 0, 30, 50)
v_offset = 20.0
projection = 1
current = true
size = 44.805
near = 0.022
script = ExtResource("26_camera_follower")
target_path = NodePath("../Player")
follow_speed = 10.0
position_offset = Vector3(0, 10, 55)

[node name="UIManager" type="Node" parent="."]
script = ExtResource("25_ui_manager")
border_width = 100.0

[node name="Player" parent="." instance=ExtResource("11_82jtv")]

[node name="Chest" parent="." instance=ExtResource("14_1ks1q")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, 4.57862, 0, -14.4123)
interaction_distance = 3.0
item_resource = ExtResource("17_j32d5")

[node name="Chest2" parent="." instance=ExtResource("14_1ks1q")]
transform = Transform3D(1.5, 0, 0, 0, 1.5, 0, 0, 0, 1.5, 20, 0.165, -3.779)
interaction_distance = 3.0
item_resource = ExtResource("18_j32d5")

[node name="EnemyBoy01" parent="." instance=ExtResource("19_0f42i")]
transform = Transform3D(0.8, 0, 0, 0, 0.751754, 0.273616, 0, -0.273616, 0.751754, -10.0014, 0, -8.65824)

[node name="Enemy02" parent="." instance=ExtResource("21_dbohx")]
transform = Transform3D(2.1, 0, 0, 0, 1.97335, 0.718242, 0, -0.718242, 1.97335, 7.86917, 0, -7.768)

[node name="NavigationRegion3D" type="NavigationRegion3D" parent="."]
navigation_mesh = SubResource("NavigationMesh_xcdtp")

[node name="Floor" type="Node3D" parent="NavigationRegion3D"]

[node name="@CSGBox3D@165127" type="CSGBox3D" parent="NavigationRegion3D/Floor"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 0, -0.05, 0)
use_collision = true
size = Vector3(100, 0.1, 100)
material = SubResource("StandardMaterial3D_cin4e")

[node name="FloorTiles" type="Node3D" parent="NavigationRegion3D/Floor"]

[node name="Floor01_0" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 32.5642, 0.1, -11.7878)

[node name="Floor01_1" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 35.2157, 0.1, 22.7461)

[node name="Floor01_2" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 7.70434, 0.1, -23.5862)

[node name="Floor01_3" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -47.4153, 0.1, -2.52745)

[node name="Floor01_4" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -34.4488, 0.1, 14.1259)

[node name="Floor01_5" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -15.3499, 0.1, 46.7165)

[node name="Floor01_6" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 17.0838, 0.1, -19.6133)

[node name="Floor01_7" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -26.2498, 0.1, -29.0656)

[node name="Floor01_8" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 44.9801, 0.1, 45.6277)

[node name="Floor01_9" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -33.5896, 0.1, 34.4435)

[node name="Floor01_10" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 7.65988, 0.1, 33.7801)

[node name="Floor01_11" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 2.29943, 0.1, 47.283)

[node name="Floor01_12" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -25.4422, 0.1, -15.4853)

[node name="Floor01_13" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -33.8899, 0.1, -17.9011)

[node name="Floor01_14" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -43.7655, 0.1, 20.4414)

[node name="Floor01_15" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -13.4869, 0.1, -21.052)

[node name="Floor01_16" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -30.182, 0.1, 1.41046)

[node name="Floor01_17" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -29.994, 0.1, -8.7434)

[node name="Floor01_18" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 25.7276, 0.1, -1.30017)

[node name="Floor01_19" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 43.8241, 0.1, -26.481)

[node name="Floor01_20" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 21.1269, 0.1, 8.29344)

[node name="Floor01_21" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -16.2151, 0.1, -34.6889)

[node name="Floor01_22" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -1.62649, 0.1, 0.424629)

[node name="Floor01_23" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -42.4528, 0.1, -36.9987)

[node name="Floor01_24" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 34.0131, 0.1, -43.5388)

[node name="Floor01_25" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 16.2472, 0.1, -35.9811)

[node name="Floor01_26" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 35.6538, 0.1, 37.7556)

[node name="Floor01_27" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -1.5198, 0.1, -43.3285)

[node name="Floor01_28" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -4.77441, 0.1, 26.9204)

[node name="Floor01_29" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 32.0385, 0.1, -32.7006)

[node name="Floor01_30" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -44.3543, 0.1, -24.4107)

[node name="Floor01_31" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -31.8899, 0.1, 44.9101)

[node name="Floor01_32" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -44.117, 0.1, -9.92924)

[node name="Floor01_33" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 25.3663, 0.1, -27.4604)

[node name="Floor01_34" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 11.4831, 0.1, 14.2687)

[node name="Floor01_35" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 35.7052, 0.1, 9.54796)

[node name="Floor01_36" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 42.1683, 0.1, -44.5581)

[node name="Floor01_37" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -7.14254, 0.1, 41.1286)

[node name="Floor01_38" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 27.6203, 0.1, 33.4732)

[node name="Floor01_39" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 28.2148, 0.1, 12.9659)

[node name="Floor01_40" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 17.453, 0.1, -11.2684)

[node name="Floor01_41" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -20.8235, 0.1, -2.75383)

[node name="Floor01_42" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 12.1955, 0.1, -44.3395)

[node name="Floor01_43" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -12.0149, 0.1, 21.9335)

[node name="Floor01_44" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 13.056, 0.1, 23.1902)

[node name="Floor01_45" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -15.0155, 0.1, 38.182)

[node name="Floor01_46" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -20.1246, 0.1, 14.6863)

[node name="Floor01_47" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 1.8282, 0.1, 9.34425)

[node name="Floor01_48" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -41.2437, 0.1, 45.0153)

[node name="Floor01_49" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -0.594637, 0.1, 19.2503)

[node name="Floor01_50" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 17.2248, 0.1, 42.1491)

[node name="Floor01_51" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 42.8303, 0.1, -3.80938)

[node name="Floor01_52" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -11.2415, 0.1, -2.57112)

[node name="Floor01_53" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -31.2748, 0.1, -36.1429)

[node name="Floor01_54" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 45.2925, 0.1, 18.1353)

[node name="Floor01_55" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 7.67714, 0.1, -31.6965)

[node name="Floor01_56" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 13.1789, 0.1, -2.46651)

[node name="Floor01_57" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -5.90314, 0.1, -12.1326)

[node name="Floor01_58" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -9.23885, 0.1, -45.8848)

[node name="Floor01_59" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -11.0664, 0.1, 7.83624)

[node name="Floor01_60" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 29.6015, 0.1, 43.6877)

[node name="Floor01_61" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -19.9384, 0.1, -44.8893)

[node name="Floor01_62" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -37.4266, 0.1, -45.8952)

[node name="Floor01_63" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 46.9489, 0.1, -10.7025)

[node name="Floor01_64" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -38.1504, 0.1, -0.729678)

[node name="Floor01_65" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -24.4521, 0.1, 26.1227)

[node name="Floor01_66" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 7.59291, 0.1, -15.3947)

[node name="Floor01_67" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 44.1831, 0.1, 29.7481)

[node name="Floor01_68" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 1.04562, 0.1, 38.7042)

[node name="Floor01_69" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 34.8146, 0.1, -24.9214)

[node name="Floor01_70" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 42.9309, 0.1, 5.21817)

[node name="Floor01_71" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 25.6534, 0.1, -18.1116)

[node name="Floor01_72" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 23.2317, 0.1, 26.0521)

[node name="Floor01_73" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -45.1211, 0.1, 6.27057)

[node name="Floor01_74" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 40.4103, 0.1, -34.8838)

[node name="Floor01_75" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -3.27805, 0.1, -26.4983)

[node name="Floor01_76" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -22.5418, 0.1, 34.7561)

[node name="wall_0" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(0.94927, 0, 0.254104, 0, 1, 0, -0.267002, 0, 0.903415, -50, 0, -50)

[node name="wall_1" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1.03894, 0, 0.0349085, 0, 1, 0, -0.0384218, 0, 0.943937, -47.5, 0, -50)

[node name="wall_2" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(0.812148, 0, 0.329624, 0, 1, 0, -0.253714, 0, 1.05514, -45, 0, -50)

[node name="wall_3" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(0.952013, 0, 0.0372928, 0, 1, 0, -0.0406775, 0, 0.872798, -27.5, 0, -50)

[node name="wall_4" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1.18893, 0, 0.224147, 0, 1, 0, -0.200194, 0, 1.33118, -25, 0, -50)

[node name="wall_5" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1.34003, 0, 0.0263523, 0, 1, 0, -0.0279824, 0, 1.26197, -22.5, 0, -50)

[node name="wall_6" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1.06189, 0, 0.0273135, 0, 1, 0, -0.0350897, 0, 0.826567, 0, 0, -50)

[node name="wall_7" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1.31718, 0, -0.203169, 0, 1, 0, 0.206254, 0, 1.29748, 5, 0, -50)

[node name="wall_8" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1.00632, 0, -0.214726, 0, 1, 0, 0.22324, 0, 0.967941, 15, 0, -50)

[node name="wall_9" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(0.999145, 0, 0.171208, 0, 1, 0, -0.171335, 0, 0.998408, 20, 0, -50)

[node name="wall_10" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(0.989262, 0.000609736, 0.169651, 0, 0.999993, -0.00381856, -0.165073, 0.00365407, 1.0167, 22.5, 0.307615, -50)

[node name="wall_11" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(0.872392, 0, -0.19189, 0, 1, 0, 0.15306, 0, 1.09371, 45, 0, -50)

[node name="wall_12" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(0.845162, 0, 0.172604, 0, 1, 0, -0.123307, 0, 1.18305, 47.5, 0, -50)

[node name="wall_13" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1.08201, 0, 0.11173, 0, 1, 0, -0.132933, 0, 0.909424, 50, 0, -50)

[node name="wall_14" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1.02909, 0, -0.209063, 0, 1, 0, 0.225347, 0, 0.954723, -50, 0, 50)

[node name="wall_15" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(0.882712, 0, -0.0105308, 0, 1, 0, 0.00867805, 0, 1.07117, -47.5, 0, 50)

[node name="wall_16" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1.3314, 0, -0.366728, 0, 1, 0, 0.397004, 0, 1.22986, -45, 0, 50)

[node name="wall_17" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(0.833925, 0, 0.328034, 0, 1, 0, -0.256722, 0, 1.06557, -40, 0, 50)

[node name="wall_18" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1.00567, 0, 0.245283, 0, 1, 0, -0.265948, 0, 0.927528, -25, 0, 50)

[node name="wall_19" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1.09508, 0, 0.171917, 0, 1, 0, -0.199288, 0, 0.944677, 7.5, 0, 50)

[node name="wall_20" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1.0021, 0, -0.267456, 0, 1, 0, 0.284488, 0, 0.942108, 10, 0, 50)

[node name="wall_21" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(0.902958, 0, -0.0163447, 0, 1, 0, 0.0146525, 0, 1.00724, 12.5, 0, 50)

[node name="wall_22" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(0.815999, 0, 0.243127, 0, 1, 0, -0.172065, 0, 1.15301, 20, 0, 50)

[node name="wall_23" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(0.932579, 0, -0.103831, 0, 1, 0, 0.0997097, 0, 0.971128, 35, 0, 50)

[node name="wall_24" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1.02012, 0, -0.00501793, 0, 1, 0, 0.004276, 0, 1.19713, 37.5, 0, 50)

[node name="wall_25" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(0.801793, 0, -0.334821, 0, 1, 0, 0.266004, 0, 1.00922, 40, 0, 50)

[node name="wall_26" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1.03143, 0, -0.198232, 0, 1, 0, 0.189263, 0, 1.0803, 47.5, 0, 50)

[node name="wall_27" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1.0392, 0, 0.278885, 0, 1, 0, -0.264163, 0, 1.09711, 50, 0, 50)

[node name="wall_28" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(0.173081, 0, 0.827513, 0, 1, 0, -0.890853, 0, 0.160775, -50, 0, -47.5)

[node name="wall_29" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-0.249943, 0, 1.10461, 0, 1, 0, -1.16707, 0, -0.236566, -50, 0, -32.5)

[node name="wall_30" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-0.455219, 0, 1.19483, 0, 1, 0, -1.25123, 0, -0.434701, -50, 0, -25)

[node name="wall_31" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-0.0927666, 0, 1.14567, 0, 1, 0, -0.924282, 0, -0.114986, -50, 0, -22.5)

[node name="wall_32" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(0.0476576, 0, 1.26495, 0, 1, 0, -1.2137, 0, 0.04967, -50, 0, -20)

[node name="wall_33" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(0.207478, -0.0792379, 1.09446, 0, 0.996732, 0.0904124, -1.04873, -0.0156762, 0.216525, -50, 0.487552, -15)

[node name="wall_34" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-0.178601, 0, 1.33063, 0, 1, 0, -1.25597, 0, -0.189218, -50, 0, 5)

[node name="wall_35" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(0.0242103, -0.0570789, 1.06175, 0, 0.998369, 0.0607449, -0.915897, -0.00150879, 0.0280656, -50, 0.476274, 10)

[node name="wall_36" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-0.334668, 0, 1.11052, 0, 1, 0, -0.924572, 0, -0.401974, -50, 0, 15)

[node name="wall_37" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(0.359795, 0, 0.826493, 0, 1, 0, -1.1191, 0, 0.26572, -50, 0, 20)

[node name="wall_38" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(0.13744, 0, 0.810721, 0, 1, 0, -0.842683, 0, 0.132227, -50, 0, 27.5)

[node name="wall_39" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-0.228817, 0, 0.775594, 0, 1, 0, -0.814946, 0, -0.217768, -50, 0, 35)

[node name="wall_40" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(0.158022, 0, 1.15171, 0, 1, 0, -1.05902, 0, 0.171852, -50, 0, 45)

[node name="wall_41" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(0.0339113, 0.0280181, 1.09209, 0, 0.999607, -0.0306449, -1.00902, 0.000941635, 0.036703, -50, 0.466638, 47.5)

[node name="wall_42" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-0.254915, 0, 0.885493, 0, 1, 0, -0.839651, 0, -0.268833, 50, 0, -47.5)

[node name="wall_43" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(0.0158265, 0, 1.06796, 0, 1, 0, -1.02734, 0, 0.0164522, 50, 0, -37.5)

[node name="wall_44" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(0.209293, 0, 0.915163, 0, 1, 0, -1.14562, 0, 0.167192, 50, 0, -32.5)

[node name="wall_45" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(0.261329, 0.0167306, 0.907635, 0, 0.999847, -0.0165637, -0.868183, 0.00503605, 0.273205, 50, 0.303883, -30)

[node name="wall_46" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(0.17355, 0.0393163, 0.946077, 0, 0.999202, -0.0384015, -0.976621, 0.00698669, 0.168122, 50, 0.408762, -25)

[node name="wall_47" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(0.00483289, 0.0921358, 1.10078, 0, 0.995746, -0.101858, -0.897742, 0.000496002, 0.00592593, 50, 0.424355, -5)

[node name="wall_48" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(0.295244, 0, 0.895321, 0, 1, 0, -1.16117, 0, 0.227647, 50, 0, -2.5)

[node name="wall_49" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(0.0447147, 0, 1.08287, 0, 1, 0, -0.964115, 0, 0.0502223, 50, 0, 12.5)

[node name="wall_50" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-0.0954684, 0, 1.0218, 0, 1, 0, -0.803301, 0, -0.121436, 50, 0, 27.5)

[node name="wall_51" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(0.0786443, 0, 0.847954, 0, 1, 0, -1.02273, 0, 0.0652047, 50, 0, 35)

[node name="wall_52" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-0.272541, 0, 1.04337, 0, 1, 0, -1.10434, 0, -0.257495, 50, 0, 42.5)

[node name="wall_53" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(0.147538, 0, 1.25369, 0, 1, 0, -1.21593, 0, 0.15212, 50, 0, 45)

[node name="wall_54" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(0.328561, 0, 1.22049, 0, 1, 0, -1.29218, 0, 0.310332, 50, 0, 47.5)

[node name="tree_0" type="Node3D" parent="NavigationRegion3D" instance=ExtResource("15_003du")]
transform = Transform3D(2.5, -0.00145593, -0.00167976, 0.00145725, 2.5, 0.00196474, 0.00167861, -0.00196571, 2.5, 11.437, 0, 9.18963)
script = ExtResource("18_oh2bt")
highlight_material = SubResource("StandardMaterial3D_01ds2")
progress_bar_scene = ExtResource("19_fcnrs")

[node name="PatrolPoint_0" type="Node3D" parent="NavigationRegion3D"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 14.8414, 1, 8.80366)
script = ExtResource("15_6ly8b")

[node name="tree_1" type="Node3D" parent="NavigationRegion3D" instance=ExtResource("15_003du")]
transform = Transform3D(2.5, -0.00145593, -0.00167976, 0.00145725, 2.5, 0.00196474, 0.00167861, -0.00196571, 2.5, -19.1313, 0, 3.21191)
script = ExtResource("18_oh2bt")
highlight_material = SubResource("StandardMaterial3D_01ds2")
progress_bar_scene = ExtResource("19_fcnrs")

[node name="PatrolPoint_1" type="Node3D" parent="NavigationRegion3D"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -23.6845, 1, 0.589623)
script = ExtResource("15_6ly8b")
point_id = 1

[node name="tree_2" type="Node3D" parent="NavigationRegion3D" instance=ExtResource("15_003du")]
transform = Transform3D(2.5, -0.00145593, -0.00167976, 0.00145725, 2.5, 0.00196474, 0.00167861, -0.00196571, 2.5, -12.1093, 0, 15.7309)
script = ExtResource("18_oh2bt")
highlight_material = SubResource("StandardMaterial3D_01ds2")
progress_bar_scene = ExtResource("19_fcnrs")

[node name="PatrolPoint_2" type="Node3D" parent="NavigationRegion3D"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -15.5786, 1, 16.1409)
script = ExtResource("15_6ly8b")
point_id = 2

[node name="tree_3" type="Node3D" parent="NavigationRegion3D" instance=ExtResource("15_003du")]
transform = Transform3D(2.5, -0.00145593, -0.00167976, 0.00145725, 2.5, 0.00196474, 0.00167861, -0.00196571, 2.5, 6.41053, 0, -8.81821)
script = ExtResource("18_oh2bt")
highlight_material = SubResource("StandardMaterial3D_01ds2")
preset_burnt_out_type = 1
progress_bar_scene = ExtResource("19_fcnrs")

[node name="PatrolPoint_3" type="Node3D" parent="NavigationRegion3D"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 7.98075, 1, -9.66025)
script = ExtResource("15_6ly8b")
point_id = 3

[node name="chest_0" type="Area3D" parent="NavigationRegion3D" instance=ExtResource("14_1ks1q")]
transform = Transform3D(0.935379, 0, 1.76779, 0, 2, 0, -1.76779, 0, 0.935379, -30.6336, 0, 19.2048)
collision_layer = 8
collision_mask = 16
script = ExtResource("21_1ks1q")
interaction_distance = 1.5
item_resource = ExtResource("22_xcdtp")
unlock_sound = ExtResource("23_cin4e")

[node name="rock_0" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("21_xcdtp")]
transform = Transform3D(-1.17823, 0, 0.12269, 0, 1.1846, 0, -0.12269, 0, -1.17823, -12.9354, 0.01, -15.2565)
collision_layer = 2
collision_mask = 0

[node name="rock_1" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("21_xcdtp")]
transform = Transform3D(0.225714, 0, 0.788008, 0, 0.819697, 0, -0.788008, 0, 0.225714, -4.58266, 0.01, 36.2076)
collision_layer = 2
collision_mask = 0

[node name="rock_2" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("21_xcdtp")]
transform = Transform3D(-0.980423, 0, -0.345627, 0, 1.03956, 0, 0.345627, 0, -0.980423, -31.7696, 0.01, 26.1547)
collision_layer = 2
collision_mask = 0

[node name="rock_3" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("21_xcdtp")]
transform = Transform3D(1.15579, 0, 0.246612, 0, 1.1818, 0, -0.246612, 0, 1.15579, -19.0229, 0.01, -13.172)
collision_layer = 2
collision_mask = 0

[node name="rock_4" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("21_xcdtp")]
transform = Transform3D(1.0146, 0, -0.41207, 0, 1.09509, 0, 0.41207, 0, 1.0146, -40.6739, 0.01, 38.2256)
collision_layer = 2
collision_mask = 0

[node name="rock_5" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("21_xcdtp")]
transform = Transform3D(-0.66335, 0, 0.473156, 0, 0.814807, 0, -0.473156, 0, -0.66335, -29.8445, 0.01, 6.58043)
collision_layer = 2
collision_mask = 0

[node name="rock_6" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("21_xcdtp")]
transform = Transform3D(-0.049666, 0, 1.11334, 0, 1.11445, 0, -1.11334, 0, -0.049666, 6.61836, 0.01, 12.5132)
collision_layer = 2
collision_mask = 0

[node name="rock_7" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("21_xcdtp")]
transform = Transform3D(1.06301, 0, 0.283668, 0, 1.10021, 0, -0.283668, 0, 1.06301, -41.6515, 0.01, 31.2144)
collision_layer = 2
collision_mask = 0

[node name="rock_8" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("21_xcdtp")]
transform = Transform3D(-0.76194, 0, -0.476306, 0, 0.898565, 0, 0.476306, 0, -0.76194, 41.2752, 0.01, 23.1475)
collision_layer = 2
collision_mask = 0

[node name="rock_9" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("21_xcdtp")]
transform = Transform3D(0.600721, 0, -0.569154, 0, 0.827527, 0, 0.569154, 0, 0.600721, -36.28, 0.01, -29.8737)
collision_layer = 2
collision_mask = 0

[node name="rock_10" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("21_xcdtp")]
transform = Transform3D(-0.560932, 0, -0.889068, 0, 1.05123, 0, 0.889068, 0, -0.560932, 23.9242, 0.01, 38.9433)
collision_layer = 2
collision_mask = 0

[node name="rock_11" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("21_xcdtp")]
transform = Transform3D(0.522107, 0, -0.857511, 0, 1.00395, 0, 0.857511, 0, 0.522107, -44.5803, 0.01, 13.3461)
collision_layer = 2
collision_mask = 0

[node name="rock_12" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("21_xcdtp")]
transform = Transform3D(0.94997, 0, -0.530422, 0, 1.08802, 0, 0.530422, 0, 0.94997, -16.6745, 0.01, 28.8223)
collision_layer = 2
collision_mask = 0

[node name="rock_13" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("21_xcdtp")]
transform = Transform3D(0.599919, 0, -0.911007, 0, 1.0908, 0, 0.911007, 0, 0.599919, 17.2368, 0.01, 29.1131)
collision_layer = 2
collision_mask = 0

[node name="rock_14" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("21_xcdtp")]
transform = Transform3D(-0.504294, 0, -0.807476, 0, 0.952014, 0, 0.807476, 0, -0.504294, -2.02652, 0.01, -5.84279)
collision_layer = 2
collision_mask = 0

[node name="barrel_0" type="StaticBody3D" parent="NavigationRegion3D" groups=["destructible"] instance=ExtResource("22_cin4e")]
transform = Transform3D(-0.145783, 0, -0.895537, 0, 0.907326, 0, 0.895537, 0, -0.145783, -39.1127, 0, -22.5536)
collision_layer = 3
collision_mask = 3

[node name="barrel_1" type="StaticBody3D" parent="NavigationRegion3D" groups=["destructible"] instance=ExtResource("22_cin4e")]
transform = Transform3D(1.16631, 0, 0.0122822, 0, 1.16638, 0, -0.0122822, 0, 1.16631, 12.0803, 0, 5.31842)
collision_layer = 3
collision_mask = 3

[node name="decoration_0" type="Node3D" parent="NavigationRegion3D" instance=ExtResource("23_6j2fk")]
transform = Transform3D(-1.15186, 0, 0.0957198, 0, 1.15583, 0, -0.0957198, 0, -1.15186, -7.6138, 0, -31.0434)

[node name="decoration_1" type="Node3D" parent="NavigationRegion3D" instance=ExtResource("23_6j2fk")]
transform = Transform3D(-0.955337, 0, 0.0553602, 0, 0.956939, 0, -0.0553602, 0, -0.955337, -35.815, 0, -6.73177)

[node name="decoration_2" type="Node3D" parent="NavigationRegion3D" instance=ExtResource("23_6j2fk")]
transform = Transform3D(-0.561152, 0, -1.11811, 0, 1.25102, 0, 1.11811, 0, -0.561152, -3.06651, 0, 13.8158)

[node name="decoration_3" type="Node3D" parent="NavigationRegion3D" instance=ExtResource("23_6j2fk")]
transform = Transform3D(0.139263, 0, 0.676601, 0, 0.690784, 0, -0.676601, 0, 0.139263, -38.4164, 0, -6.83489)

[node name="decoration_4" type="Node3D" parent="NavigationRegion3D" instance=ExtResource("23_6j2fk")]
transform = Transform3D(1.10643, 0, -0.351894, 0, 1.16104, 0, 0.351894, 0, 1.10643, -13.5127, 0, -7.77028)

[node name="decoration_5" type="Node3D" parent="NavigationRegion3D" instance=ExtResource("23_6j2fk")]
transform = Transform3D(0.28667, 0, 0.623047, 0, 0.685834, 0, -0.623047, 0, 0.28667, 1.68931, 0, -34.5577)

[node name="decoration_6" type="Node3D" parent="NavigationRegion3D" instance=ExtResource("23_6j2fk")]
transform = Transform3D(0.474664, 0, -0.626114, 0, 0.7857, 0, 0.626114, 0, 0.474664, -25.8889, 0, 15.5143)

[node name="decoration_7" type="Node3D" parent="NavigationRegion3D" instance=ExtResource("23_6j2fk")]
transform = Transform3D(0.639987, 0, 0.527364, 0, 0.829275, 0, -0.527364, 0, 0.639987, -10.2424, 0, 29.5169)

[node name="decoration_8" type="Node3D" parent="NavigationRegion3D" instance=ExtResource("23_6j2fk")]
transform = Transform3D(-0.107441, 0, -1.1525, 0, 1.1575, 0, 1.1525, 0, -0.107441, 21.658, 0, -40.9723)

[node name="decoration_9" type="Node3D" parent="NavigationRegion3D" instance=ExtResource("23_6j2fk")]
transform = Transform3D(0.388197, 0, 0.636272, 0, 0.745345, 0, -0.636272, 0, 0.388197, -1.54774, 0, -15.8209)

[node name="decoration_10" type="Node3D" parent="NavigationRegion3D" instance=ExtResource("23_6j2fk")]
transform = Transform3D(0.757451, 0, -0.522634, 0, 0.92026, 0, 0.522634, 0, 0.757451, -24.0733, 0, 41.2241)

[node name="decoration_11" type="Node3D" parent="NavigationRegion3D" instance=ExtResource("23_6j2fk")]
transform = Transform3D(1.25201, 0, 0.157313, 0, 1.26185, 0, -0.157313, 0, 1.25201, 24.3419, 0, 20.4073)

[node name="decoration_12" type="Node3D" parent="NavigationRegion3D" instance=ExtResource("23_6j2fk")]
transform = Transform3D(1.35091, 0, 0.348128, 0, 1.39505, 0, -0.348128, 0, 1.35091, -39.6225, 0, 7.82778)

[node name="decoration_13" type="Node3D" parent="NavigationRegion3D" instance=ExtResource("23_6j2fk")]
transform = Transform3D(-1.02805, 0, 0.478863, 0, 1.13411, 0, -0.478863, 0, -1.02805, -35.6667, 0, -10.0782)

[node name="decoration_14" type="Node3D" parent="NavigationRegion3D" instance=ExtResource("23_6j2fk")]
transform = Transform3D(1.09204, 0, 0.0875489, 0, 1.09554, 0, -0.0875489, 0, 1.09204, -17.7088, 0, 25.2378)

[node name="decoration_15" type="Node3D" parent="NavigationRegion3D" instance=ExtResource("23_6j2fk")]
transform = Transform3D(1.32197, 0, -0.18161, 0, 1.33439, 0, 0.18161, 0, 1.32197, 22.4259, 0, 13.3468)

[node name="decoration_16" type="Node3D" parent="NavigationRegion3D" instance=ExtResource("23_6j2fk")]
transform = Transform3D(-0.885831, 0, -0.288781, 0, 0.931714, 0, 0.288781, 0, -0.885831, 6.35722, 0, 27.0222)

[node name="decoration_17" type="Node3D" parent="NavigationRegion3D" instance=ExtResource("23_6j2fk")]
transform = Transform3D(0.195431, 0, -0.890798, 0, 0.911984, 0, 0.890798, 0, 0.195431, 31.0555, 0, 25.8386)

[node name="decoration_18" type="Node3D" parent="NavigationRegion3D" instance=ExtResource("23_6j2fk")]
transform = Transform3D(-1.03566, 0, -0.323838, 0, 1.08511, 0, 0.323838, 0, -1.03566, -27.2215, 0, 37.1403)

[node name="decoration_19" type="Node3D" parent="NavigationRegion3D" instance=ExtResource("23_6j2fk")]
transform = Transform3D(-0.650124, 0, -0.0863928, 0, 0.655839, 0, 0.0863928, 0, -0.650124, 30.1698, 0, 5.20921)

[node name="PatrolPointManager" type="Node" parent="."]
script = ExtResource("24_j2uky")
