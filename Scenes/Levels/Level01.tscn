[gd_scene load_steps=30 format=3 uid="uid://c0rj4dwj18nv8"]

[ext_resource type="Script" uid="uid://bo0h8t35yls55" path="res://Scripts/Levels/Level01.gd" id="1_hk2b6"]
[ext_resource type="PackedScene" uid="uid://d2xyw4d37ymv4" path="res://Scenes/Minimap.tscn" id="2_j32d5"]
[ext_resource type="PackedScene" uid="uid://ctxvyh1qr52ue" path="res://Scenes/PlayerAvatar.tscn" id="3_0f42i"]
[ext_resource type="PackedScene" uid="uid://do1sypgxbecbd" path="res://Scenes/ItemBoxes.tscn" id="4_swvig"]
[ext_resource type="PackedScene" uid="uid://b4l4phqqbvprm" path="res://Scenes/SkillBoxUI.tscn" id="5_dbohx"]
[ext_resource type="Script" uid="uid://f54r1khulcvh" path="res://Scripts/ModularLevelGenerator.gd" id="6_6ly8b"]
[ext_resource type="Resource" uid="uid://cvji54t3pjwfe" path="res://Resources/LevelConfigs/AllModulesConfig.tres" id="7_r5102"]
[ext_resource type="PackedScene" uid="uid://d2b5h1mlxxy7d" path="res://Scenes/FogOfWar.tscn" id="9_003du"]
[ext_resource type="PackedScene" uid="uid://b88l8pk1ebe1x" path="res://Scenes/player.tscn" id="11_82jtv"]
[ext_resource type="PackedScene" uid="uid://c3h8fj2xsp5oy" path="res://Scenes/Prefabs/Chest.tscn" id="14_1ks1q"]
[ext_resource type="PackedScene" uid="uid://dwusy8dd8usvo" path="res://Scenes/Prefabs/Wall.tscn" id="14_df8b0"]
[ext_resource type="PackedScene" uid="uid://crtnthqkksmri" path="res://Scenes/Prefabs/Tree.tscn" id="15_003du"]
[ext_resource type="Script" uid="uid://c3tr23vwvnmwf" path="res://Scripts/PatrolPoint.gd" id="15_6ly8b"]
[ext_resource type="Texture2D" uid="uid://c2ny0yi07rvcf" path="res://Environment/Floor/Floor01_Rocks_BaseColor.png" id="15_df8b0"]
[ext_resource type="Texture2D" uid="uid://dhfikoo16s5n0" path="res://Environment/Floor/Rocks_Metallic.png" id="16_003du"]
[ext_resource type="PackedScene" uid="uid://ddttv643pel23" path="res://Environment/Floor/Floor01_Custom.tscn" id="17_2bvpm"]
[ext_resource type="Resource" path="res://Resources/Items/Trap.tres" id="17_j32d5"]
[ext_resource type="Resource" path="res://Resources/Items/Torch.tres" id="18_j32d5"]
[ext_resource type="PackedScene" uid="uid://rvgn0irsuwao" path="res://Scenes/EnemyBoy01.tscn" id="19_0f42i"]
[ext_resource type="PackedScene" uid="uid://ervckea7fk57" path="res://Scenes/Enemy02.tscn" id="21_dbohx"]
[ext_resource type="PackedScene" uid="uid://b5dqjsb63wbhl" path="res://Scenes/Prefabs/Rock.tscn" id="21_xcdtp"]
[ext_resource type="PackedScene" uid="uid://bdq3b4e0mlgo4" path="res://Scenes/Prefabs/Barrel.tscn" id="22_cin4e"]
[ext_resource type="PackedScene" uid="uid://c6k7j3t3flhst" path="res://Scenes/Prefabs/Decoration.tscn" id="23_6j2fk"]
[ext_resource type="Script" uid="uid://de18ote8otj6u" path="res://Scripts/PatrolPointManager.gd" id="24_j2uky"]

[sub_resource type="ProceduralSkyMaterial" id="ProceduralSkyMaterial_gbvua"]
sky_top_color = Color(0.2, 0.4, 0.8, 1)
sky_horizon_color = Color(0.5, 0.7, 0.9, 1)
ground_bottom_color = Color(0.3, 0.5, 0.7, 1)
ground_horizon_color = Color(0.5, 0.7, 0.9, 1)

[sub_resource type="Sky" id="Sky_m0r78"]
sky_material = SubResource("ProceduralSkyMaterial_gbvua")

[sub_resource type="Environment" id="Environment_j4i7h"]
background_mode = 2
sky = SubResource("Sky_m0r78")
ambient_light_source = 3
ambient_light_color = Color(0.85, 0.85, 0.9, 1)
ambient_light_energy = 0.4
tonemap_mode = 2
ssao_enabled = true
glow_enabled = true
fog_enabled = true
fog_light_color = Color(0.85, 0.9, 1, 1)
fog_density = 0.001

[sub_resource type="NavigationMesh" id="NavigationMesh_fcnrs"]
vertices = PackedVector3Array(-17.1434, 0.340398, -53.5682, -17.8934, 0.590398, -53.0682, -17.8934, 0.590398, -52.3182, -11.1434, 0.340398, -52.3182, -15.8934, 0.340398, -53.3182, -10.8934, 0.340398, -53.8182, -11.6434, 0.340398, -55.5682, -15.3934, 0.340398, -53.8182, -13.6434, 0.590398, -56.0682, -15.3934, 0.340398, -55.0682, 11.1066, 0.340398, -53.3182, 10.6066, 0.590398, -52.8182, 10.6066, 0.590398, -52.3182, 12.6066, 0.340398, -53.0682, 17.3566, 0.340398, -52.3182, 13.1066, 0.340398, -53.5682, 16.8566, 0.340398, -55.3182, 14.8566, 0.590398, -55.8182, 14.6066, 0.590398, -55.3182, 13.1066, 0.340398, -55.0682, 17.6066, 0.340398, -53.8182, -19.8934, 0.590398, -52.3182, -20.3934, 0.340398, -53.3182, -21.8934, 0.590398, -53.8182, -23.6434, 0.340398, -52.8182, -27.3934, 0.590398, -55.0682, -27.6434, 0.590398, -55.5682, -29.8934, 0.340398, -54.5682, -29.8934, 0.340398, -53.3182, -26.3934, 0.340398, -52.3182, -24.8934, 0.340398, -52.8182, -26.1434, 0.340398, -55.0682, -30.3934, 0.340398, -52.8182, -32.3934, 0.590398, -52.5682, 8.35655, 0.590398, -52.3182, 8.10655, 0.340398, -53.0682, 7.10655, 0.340398, -53.0682, 1.60655, 0.340398, -52.8182, -0.393448, 0.590398, -52.5682, 6.10655, 0.590398, -54.0682, 5.85655, 0.340398, -55.0682, 4.35655, 0.590398, -55.0682, 4.10655, 0.590398, -55.5682, 2.10655, 0.340398, -54.5682, 2.10655, 0.340398, -53.3182, 47.1066, 0.340398, -52.3182, 46.1066, 0.340398, -54.0682, 45.1066, 0.590398, -54.3182, 42.6066, 0.340398, -53.3182, 42.6066, 0.340398, -52.3182, 22.3566, 0.340398, -52.3182, 26.8566, 0.340398, -52.3182, 26.1066, 0.340398, -53.5682, 24.6066, 0.590398, -54.0682, 22.3566, 0.340398, -53.3182, -48.8934, 4.3404, -51.5682, -48.8934, 4.3404, -53.8182, -51.1434, 4.3404, -53.8182, -51.1434, 4.3404, -48.8182, -48.6434, 4.3404, -48.5682, 34.6066, 4.3404, -48.8182, 46.6066, 4.3404, -48.8182, 46.6066, 4.3404, -51.0682, 34.6066, 4.3404, -51.0682, -48.3934, 4.3404, -51.0682, -37.1434, 4.3404, -48.8182, -36.6434, 4.3404, -51.0682, -0.893448, 4.3404, -51.0682, -1.14345, 4.3404, -48.8182, 10.6066, 4.3404, -48.8182, 10.8566, 4.3404, -51.0682, -25.1434, 4.3404, -48.8182, -24.6434, 4.3404, -51.0682, 22.8566, 4.3404, -51.0682, 22.6066, 4.3404, -48.8182, -13.1434, 4.3404, -48.8182, -12.8934, 4.3404, -51.0682, 48.3566, 4.3404, -48.8182, 48.3566, 4.3404, -51.0682, 51.1066, 4.3404, -48.3182, 51.6066, 4.3404, -51.0682, 51.1066, 4.3404, -51.5682, 48.8566, 4.3404, -51.5682, 51.6066, 4.3404, -48.8182, 48.8566, 4.3404, -48.3182, 48.8566, 4.3404, -46.5682, 51.1066, 4.3404, -46.5682, 51.1066, 4.3404, -53.8182, 48.8566, 4.3404, -53.8182, 53.8566, 4.3404, -48.8182, 53.8566, 4.3404, -51.0682, -38.1434, 0.590398, -52.5682, -34.8934, 0.340398, -52.3182, -36.6434, 0.590398, -53.3182, -2.89345, 0.340398, -52.3182, -3.14345, 0.340398, -52.8182, -4.39345, 0.590398, -53.0682, -6.14345, 0.340398, -52.5682, -51.1434, 4.3404, -42.8182, -48.8934, 4.3404, -42.5682, -48.8934, 4.3404, 36.1818, -51.1434, 4.3404, 31.1818, -51.1434, 4.3404, 37.4318, -51.1434, 4.3404, 43.6818, -51.1434, 4.3404, 49.9318, -50.3934, 4.3404, 49.9318, -48.8934, 4.3404, 48.4318, -48.8934, 4.3404, 42.1818, -51.1434, 4.3404, -36.5682, -48.8934, 4.3404, -36.5682, -19.3934, 4.3404, 51.1818, -13.1434, 4.3404, 51.1818, -12.1434, 4.3404, 48.9318, -24.1434, 4.3404, 48.9318, -48.8934, 4.3404, -30.5682, -51.1434, 4.3404, -30.5682, -25.3934, 4.3404, 51.1818, 42.6066, 4.3404, 51.1818, 48.8566, 4.3404, 51.1818, 48.6066, 4.3404, 48.6818, 42.6066, 4.3404, 48.9318, -36.3934, 4.3404, 48.9318, -48.3934, 4.3404, 48.9318, -43.8934, 4.3404, 51.1818, -37.6434, 4.3404, 51.1818, 36.6066, 4.3404, 48.9318, 36.3566, 4.3404, 51.1818, 30.3566, 4.3404, 48.9318, 30.1066, 4.3404, 51.1818, 24.3566, 4.3404, 48.9318, 24.1066, 4.3404, 51.1818, -31.6434, 4.3404, 51.1818, -30.3934, 4.3404, 48.9318, 18.1066, 4.3404, 48.9318, 17.8566, 4.3404, 51.1818, -0.643448, 4.3404, 51.1818, 5.35655, 4.3404, 51.1818, 0.106552, 4.3404, 48.9318, 12.1066, 4.3404, 48.9318, 11.6066, 4.3404, 51.1818, -48.8934, 4.3404, 24.1818, -51.1434, 4.3404, 25.1818, -49.8934, 4.3404, 51.1818, -48.8934, 4.3404, -24.3182, -51.1434, 4.3404, -24.3182, -51.1434, 4.3404, -18.3182, -48.8934, 4.3404, -12.3182, -48.8934, 4.3404, 17.9318, -51.1434, 4.3404, 18.9318, -51.1434, 4.3404, -12.0682, -48.8934, 4.3404, -6.31822, -51.1434, 4.3404, -5.81822, -6.89345, 4.3404, 51.1818, -6.14345, 4.3404, 48.9318, -48.8934, 4.3404, -0.0682182, -51.1434, 4.3404, 0.431782, -51.1434, 4.3404, 6.43178, -48.8934, 4.3404, 11.9318, -51.1434, 4.3404, 12.6818, -40.1434, 0.590398, -47.5682, -47.6434, 0.590398, -47.5682, -47.6434, 0.590398, -35.8182, -47.6434, 0.340398, -23.8182, -33.3934, 0.590398, -23.8182, -33.3934, 0.590398, -24.3182, -32.6434, 0.840398, -24.5682, -32.6434, 0.340398, -47.5682, -32.6434, 0.340398, -28.8807, -33.9071, 0.840398, -25.3187, -35.4072, 0.340398, -26.8286, -44.373, 0.840398, -37.3367, -42.888, 0.340398, -37.3257, -44.4031, 0.340398, -38.8246, -21.3934, 0.340398, -15.8182, -19.3934, 0.590398, -15.8182, -18.3934, 0.340398, -16.5682, -31.8934, 0.840398, -24.3182, -31.6434, 0.590398, -23.5682, -18.3934, 0.340398, -47.5682, -25.6434, 0.590398, -47.5682, -31.1582, 0.340398, -27.0535, -31.5258, 0.840398, -25.6859, -20.3975, 0.840398, -25.3423, -23.4175, 0.340398, -25.3143, -20.3925, 0.340398, -26.8192, -20.4024, 0.340398, -23.8154, -29.4075, 0.340398, -25.3082, -17.8934, 0.590398, -16.0682, -16.6434, 0.340398, -16.0682, 3.10655, 0.590398, -29.8182, 3.85655, 0.590398, -30.0682, 3.85655, 0.590398, -47.5682, -7.39345, 0.340398, -47.5682, 3.85655, 0.840398, -31.5266, 3.85655, 0.340398, -35.9016, 3.10655, 1.0904, -29.3182, 1.87218, 0.340398, -28.4901, -11.3833, 0.840398, -44.8224, -9.91821, 0.340398, -43.3235, -11.4032, 0.840398, -43.3345, -11.3731, 0.340398, -41.7966, -12.8865, 0.840398, -23.8073, -12.9017, 0.340398, -25.3061, -12.9183, 0.340398, -44.8333, -8.39661, 0.340398, -25.3232, -14.3814, 0.340398, -20.8144, 4.35655, 0.590398, -29.3182, 7.10655, 0.590398, -26.5682, 14.6066, 0.590398, -47.5682, 47.6066, 0.340398, -36.3182, 47.6066, 0.340398, -47.5682, 36.6066, 0.590398, -47.5682, 25.6066, 0.590398, -47.5682, 19.6066, 0.340398, -3.81822, 47.6066, 0.590398, -13.3182, 47.6066, 0.590398, -24.8182, 20.1204, 0.840398, -19.3164, 20.1151, 0.340398, -20.8191, 21.6054, 0.340398, -19.3054, 15.5892, 0.840398, -25.3431, 18.6301, 0.340398, -20.8301, 15.5937, 0.340398, -28.3095, 14.1042, 0.340398, -25.3041, 20.111, 0.340398, -16.3329, 20.8566, 0.340398, -1.81822, 47.6066, 0.340398, -1.81822, 25.3149, 0.340398, -1.81822, 26.801, 0.840398, -1.81822, 29.7732, 0.340398, -1.81822, 18.8566, 0.590398, -3.81822, -53.8934, 0.340398, -44.8182, -54.3934, 0.340398, -44.8182, -54.1434, 0.340398, -43.5682, -52.3934, 0.340398, -46.8182, -55.3934, 0.340398, -42.8182, -55.3934, 0.340398, -37.8182, -52.3934, 0.590398, -36.8182, 53.8566, 4.3404, 51.1818, 53.8566, 4.3404, 48.9318, 51.6066, 4.3404, 48.9318, 48.8566, 4.3404, -10.8182, 48.8566, 4.3404, 1.18178, 51.1066, 4.3404, 0.931782, 51.1066, 4.3404, -11.0682, 51.1066, 4.3404, 48.4318, 48.8566, 4.3404, 37.1818, 51.1066, 4.3404, 36.4318, 51.1066, 4.3404, -22.8182, 51.1066, 4.3404, -34.8182, 48.8566, 4.3404, -34.8182, 48.8566, 4.3404, -22.8182, 48.8566, 4.3404, 25.1818, 51.1066, 4.3404, 24.6818, 48.8566, 4.3404, 13.1818, 51.1066, 4.3404, 12.6818, 57.3566, 0.340398, -40.0682, 57.8566, 0.340398, -40.3182, 57.8566, 0.340398, -42.0682, 55.3566, 0.340398, -41.8182, 54.3566, 0.340398, -45.3182, 53.3566, 0.340398, -46.5682, 52.3566, 0.590398, -46.5682, 52.3566, 0.340398, -40.3182, 54.1066, 0.340398, -42.8182, 52.3566, 0.340398, -34.0682, 53.3566, 0.340398, -33.8182, 54.1066, 0.340398, -35.8182, 56.8566, 0.340398, -35.8182, -53.6434, 0.340398, -33.5682, -54.1434, 0.340398, -33.5682, -53.8934, 0.340398, -32.3182, -54.8934, 0.340398, -31.5682, -55.1434, 0.340398, -30.3182, -55.1434, 0.340398, -26.8182, -52.3934, 0.340398, -25.8182, -52.3934, 0.340398, -35.3182, 3.60655, 0.590398, -28.5682, 3.60655, 0.840398, -28.8182, -16.1434, 0.340398, -15.5682, -13.6747, 0.840398, -17.1932, -12.4403, 0.340398, -18.0057, 5.35655, 0.340398, 1.68178, 5.60655, 0.590398, 1.18178, 6.85655, 0.590398, 1.43178, 7.60655, 0.590398, 0.431782, -2.39345, 0.840398, 4.43178, -2.89345, 0.840398, 4.93178, -2.39345, 0.590398, 5.43178, 4.35655, 0.590398, 4.93178, 4.60655, 0.590398, 3.93178, -1.04345, 0.840398, 5.33178, 0.306553, 0.340398, 5.23178, 5.35655, 0.340398, 3.68178, 5.85655, 0.840398, 2.68178, -2.64345, 0.590398, 3.43178, -4.89345, 0.590398, 3.43178, -4.64345, 0.340398, 3.93178, -8.14345, 0.340398, 4.18178, -6.39345, 0.590398, 4.18178, -5.89345, 0.340398, 3.43178, -14.8934, 0.340398, -2.31822, -14.6434, 0.590398, -1.06822, -1.96488, 0.840398, 2.93178, -16.1434, 0.340398, -13.3182, -8.40165, 0.840398, -2.80614, -6.90693, 0.340398, -5.80885, -12.9131, 0.340398, -16.3306, -8.41652, 0.340398, -1.33529, -17.8934, 0.340398, -11.8182, -16.3934, 0.590398, -3.06822, 52.3566, 0.340398, -11.8182, 53.3566, 0.340398, -11.8182, 53.3566, 0.340398, -13.0682, 53.3566, 0.340398, -25.8182, 53.8566, 0.340398, -26.0682, 53.6066, 0.340398, -27.8182, 52.3566, 0.340398, -27.8182, 57.1066, 0.340398, -17.8182, 57.6066, 0.340398, -18.0682, 57.6066, 0.340398, -19.8182, 55.1066, 0.340398, -19.8182, 54.1066, 0.340398, -13.8182, 56.6066, 0.340398, -13.8182, 53.8566, 0.340398, -20.8182, 54.1066, 0.340398, -23.3182, 53.3566, 0.340398, -24.3182, 52.3566, 0.590398, -19.8182, 11.1066, 0.590398, 1.68178, 18.6066, 0.340398, -0.0682182, 17.1066, 0.340398, -1.31822, 17.1066, 0.340398, -2.56822, 9.10655, 0.590398, 0.431782, -22.1434, 1.0904, -12.3182, -21.6434, 0.840398, -13.0682, -22.3934, 0.590398, -13.5682, -47.6434, 0.340398, -2.81822, -41.6434, 0.590398, -2.81822, -41.1434, 0.340398, -4.31822, -47.6434, 0.590398, -13.3182, -33.1434, 0.340398, -23.0682, -39.3934, 0.590398, -4.31822, -40.2684, 0.840398, -4.31822, -22.3934, 0.590398, -15.0682, -31.8934, 0.590398, -23.0682, -23.1434, 0.340398, -13.2955, -21.8934, 0.340398, -11.3182, -38.4172, 0.840398, -10.2945, -39.9023, 0.340398, -10.3055, -36.8821, 0.340398, -8.82365, -36.9126, 0.340398, -13.3412, -52.8934, 0.340398, -21.3182, -52.8934, 0.340398, -18.0682, -52.3934, 0.340398, -17.8182, -52.3934, 0.340398, -22.8182, -19.8934, 5.0904, -14.3182, -19.8934, 5.0904, -13.0682, -18.6434, 5.0904, -13.0682, -18.6434, 5.0904, -14.3182, -52.3934, 0.340398, -12.8182, -53.1434, 0.340398, -12.8182, -52.8934, 0.340398, -11.3182, -53.8934, 0.340398, -10.8182, -53.6434, 0.340398, -9.56822, -54.1434, 0.340398, -9.06822, -54.1434, 0.340398, -5.81822, -52.3934, 0.340398, -5.31822, -18.8934, 0.340398, -11.0682, -17.6434, 0.340398, -2.31822, -21.6434, 0.340398, -11.3182, -39.1434, 0.590398, -4.31822, -38.6434, 0.590398, -2.56822, -17.6434, 0.340398, -1.31822, -39.3934, 0.590398, -1.56822, -13.1434, 0.590398, 15.4318, -12.8934, 0.340398, 15.9318, -11.6434, 0.590398, 15.6818, -9.39345, 0.590398, 7.93178, -17.6434, 0.340398, 18.9318, -16.6434, 0.840398, 18.6818, -16.8934, 0.590398, 17.4318, -9.39345, 0.340398, 6.68178, -8.14345, 0.590398, 5.93178, -8.64345, 0.340398, 4.43178, -15.3934, 0.340398, -0.0682182, -16.3934, 0.340398, -0.0682182, -16.3934, 0.590398, 16.1818, -14.6434, 0.590398, 16.1818, -13.8934, 0.340398, 15.4318, -15.5184, 0.840398, 16.1818, -19.3934, 0.340398, 22.4318, -18.3934, 0.340398, 22.4318, -17.6434, 0.590398, 20.4318, -34.1434, 0.340398, 22.1818, -47.6434, 0.340398, 10.1818, -26.7684, 0.340398, 22.3068, -23.8184, 0.840398, 22.3568, -22.3434, 0.340398, 22.3818, -36.8911, 0.840398, 18.1993, -36.8963, 0.340398, 16.7064, -18.871, 0.340398, 18.1811, -41.1434, 0.340398, -1.56822, -47.6434, 0.590398, 3.68178, -33.6434, 0.590398, 23.9318, -19.8934, 0.340398, 23.6818, -47.6434, 0.590398, 23.4318, -36.6434, 0.590398, 23.4318, -36.1434, 0.590398, 22.1818, -47.6434, 0.590398, 16.6818, 18.8566, 2.3404, -1.31822, 19.3566, 2.3404, -1.56822, 19.1066, 2.3404, -2.31822, 18.3566, 2.3404, -2.06822, 54.6066, 0.340398, -0.318218, 54.3566, 0.340398, -2.06822, 52.3566, 0.340398, -2.06822, 53.8566, 0.340398, 0.931782, 52.3566, 0.340398, 3.93178, 53.6066, 0.340398, 3.93178, -54.1434, 0.340398, 0.431782, -54.6434, 0.340398, 0.431782, -54.3934, 0.340398, 1.68178, -52.3934, 0.340398, -1.81822, -53.1434, 0.340398, -1.56822, -52.3934, 0.590398, 8.43178, -55.6434, 0.340398, 7.18178, -55.6434, 0.340398, 2.43178, -14.1434, 0.590398, 24.4318, -14.3934, 0.840398, 24.9318, -13.8934, 0.840398, 25.1818, -13.3934, 0.840398, 23.9318, 47.6066, 0.340398, 37.6818, 43.6066, 0.590398, 28.6818, 42.1066, 0.590398, 28.1818, 31.8566, 0.340398, 47.6818, 39.6066, 0.340398, 47.6818, 10.8566, 0.590398, 3.68178, 9.85655, 0.840398, 3.93178, 9.85655, 0.590398, 4.68178, -2.39345, 0.340398, 5.93178, -3.39345, 0.590398, 6.18178, -3.39345, 0.590398, 6.93178, -13.8934, 1.0904, 25.9318, -3.89345, 0.590398, 12.4318, -5.14345, 0.340398, 12.9318, -10.8934, 0.340398, 17.9318, -13.8934, 0.340398, 20.6818, -13.8934, 0.340398, 22.6818, -13.3934, 0.840398, 22.9318, -5.39345, 0.340398, 8.68178, -5.14345, 0.590398, 10.6818, -3.89345, 0.590398, 10.9318, 16.3566, 0.340398, 47.6818, 24.1066, 0.590398, 47.6818, 42.1066, 0.590398, 24.9318, 47.6066, 0.340398, 27.9318, 44.1066, 0.340398, 27.9318, 8.60655, 0.340398, 5.68178, 5.85655, 0.590398, 5.68178, 42.3566, 0.590398, 27.1818, 43.6066, 0.840398, 27.1818, 47.6066, 0.340398, 26.6818, 43.8566, 0.590398, 26.1818, 42.6066, 0.590398, 26.6818, 44.9399, 0.340398, 27.0151, 42.1066, 0.590398, 26.1818, 47.6066, 0.590398, 12.4318, 47.6066, 0.340398, 5.18178, 4.60655, 0.590398, 5.43178, 43.1066, 0.590398, 24.6818, 47.6066, 0.590398, 19.4318, 43.8566, 0.590398, 25.4318, 19.6066, 0.340398, -0.0682182, -14.6434, 0.590398, 26.4318, -14.6434, 0.340398, 47.6818, -6.89345, 0.340398, 47.6818, -12.9184, 0.340398, 33.1568, 8.60655, 0.590398, 47.6818, -4.25059, 0.340398, 35.2532, 2.17798, 0.840398, 41.4675, -11.3696, 0.340398, 25.6897, 3.60341, 0.340398, 40.6584, 17.123, 0.840398, 15.1692, 15.588, 0.340398, 15.1582, 17.0834, 0.340398, 16.7048, 18.6081, 0.340398, 15.1802, 23.0991, 0.840398, 19.7031, 24.5841, 0.340398, 19.6641, 21.5848, 0.340398, 22.6733, -11.3951, 0.340398, 24.2051, 0.856552, 0.340398, 47.6818, 2.09666, 0.340398, 43.7057, -6.91845, 0.340398, 34.6568, -10.8934, 0.590398, 16.4318, 47.6066, 0.340398, 47.6818, -8.41341, 0.340398, 39.1897, 7.10655, 5.0904, 2.68178, 7.10655, 5.0904, 3.93178, 8.35655, 5.0904, 3.93178, 8.35655, 5.0904, 2.68178, -6.89345, 5.0904, 5.68178, -6.89345, 5.0904, 6.93178, -5.64345, 5.0904, 6.93178, -5.64345, 5.0904, 5.68178, -6.14345, 0.590398, 11.1818, -8.14345, 0.590398, 8.68178, -6.14345, 0.340398, 12.1818, 56.1066, 0.340398, 13.9318, 55.8566, 0.340398, 11.9318, 53.3566, 0.340398, 12.1818, 55.3566, 0.340398, 15.1818, 52.3566, 0.340398, 18.1818, 55.1066, 0.340398, 18.1818, 52.3566, 0.340398, 11.1818, -52.6434, 0.340398, 13.6818, -53.6434, 0.340398, 14.1818, -53.3934, 0.340398, 15.4318, -53.8934, 0.340398, 16.1818, -53.8934, 0.340398, 18.9318, -52.3934, 0.340398, 19.4318, -52.3934, 0.340398, 12.1818, -15.1434, 5.0904, 17.6818, -15.1434, 5.0904, 18.9318, -13.8934, 5.0904, 18.9318, -13.8934, 5.0904, 17.6818, -14.8934, 0.590398, 24.1818, -15.3934, 0.590398, 25.4318, -47.6434, 0.340398, 35.4318, -46.1434, 0.590398, 39.4318, -43.6434, 0.340398, 38.9318, -33.8934, 0.590398, 25.1818, -36.1434, 0.340398, 25.1818, -47.6434, 0.590398, 41.4318, -46.1434, 0.340398, 41.1818, -45.6434, 0.340398, 41.6818, -47.6434, 0.340398, 47.6818, -14.8934, 0.590398, 23.4318, -17.6434, 0.340398, 22.9318, -17.8934, 0.340398, 24.6818, -15.3934, 0.590398, 26.1818, -22.8934, 0.590398, 47.6818, -30.9117, 0.840398, 40.6779, -29.3767, 0.340398, 40.6889, -29.4066, 0.340398, 42.1708, -32.3719, 0.340398, 39.2021, -32.3967, 0.340398, 40.667, -19.6434, 0.340398, 24.6818, -31.1434, 0.340398, 47.6818, -43.3934, 0.340398, 41.4318, -39.3934, 0.340398, 47.6818, 53.6066, 0.340398, 22.4318, 52.8566, 0.340398, 21.1818, 52.3566, 0.340398, 21.1818, 52.3566, 0.340398, 27.4318, 53.3566, 0.340398, 24.6818, 56.3566, 0.340398, 28.6818, 57.1066, 0.340398, 27.4318, 56.8566, 0.340398, 25.6818, 54.3566, 0.340398, 25.6818, 53.3566, 0.590398, 31.9318, 56.1066, 0.340398, 31.6818, 52.3566, 0.340398, 33.6818, -52.3934, 0.340398, 24.9318, -53.3934, 0.340398, 25.6818, -53.6434, 0.340398, 26.9318, -53.6434, 0.340398, 30.4318, -52.3934, 0.340398, 30.9318)
polygons = [PackedInt32Array(1, 0, 2), PackedInt32Array(2, 0, 4), PackedInt32Array(2, 4, 3), PackedInt32Array(5, 3, 6), PackedInt32Array(6, 3, 7), PackedInt32Array(7, 3, 4), PackedInt32Array(9, 8, 7), PackedInt32Array(7, 8, 6), PackedInt32Array(11, 10, 12), PackedInt32Array(12, 10, 13), PackedInt32Array(13, 15, 14), PackedInt32Array(18, 17, 16), PackedInt32Array(19, 18, 15), PackedInt32Array(15, 18, 16), PackedInt32Array(15, 16, 20), PackedInt32Array(15, 20, 14), PackedInt32Array(14, 12, 13), PackedInt32Array(22, 21, 23), PackedInt32Array(23, 21, 24), PackedInt32Array(26, 25, 27), PackedInt32Array(27, 25, 28), PackedInt32Array(30, 24, 29), PackedInt32Array(29, 24, 21), PackedInt32Array(30, 29, 31), PackedInt32Array(31, 29, 25), PackedInt32Array(25, 29, 28), PackedInt32Array(28, 29, 32), PackedInt32Array(32, 29, 33), PackedInt32Array(35, 34, 36), PackedInt32Array(36, 34, 37), PackedInt32Array(37, 34, 38), PackedInt32Array(41, 40, 39), PackedInt32Array(42, 41, 43), PackedInt32Array(43, 41, 44), PackedInt32Array(39, 36, 41), PackedInt32Array(41, 36, 44), PackedInt32Array(44, 36, 37), PackedInt32Array(46, 45, 47), PackedInt32Array(47, 45, 48), PackedInt32Array(48, 45, 49), PackedInt32Array(52, 51, 53), PackedInt32Array(53, 51, 54), PackedInt32Array(54, 51, 50), PackedInt32Array(57, 56, 55), PackedInt32Array(59, 58, 55), PackedInt32Array(55, 58, 57), PackedInt32Array(63, 62, 60), PackedInt32Array(60, 62, 61), PackedInt32Array(59, 55, 64), PackedInt32Array(59, 64, 65), PackedInt32Array(65, 64, 66), PackedInt32Array(70, 69, 67), PackedInt32Array(67, 69, 68), PackedInt32Array(72, 71, 66), PackedInt32Array(66, 71, 65), PackedInt32Array(63, 60, 73), PackedInt32Array(73, 60, 74), PackedInt32Array(76, 75, 72), PackedInt32Array(72, 75, 71), PackedInt32Array(73, 74, 70), PackedInt32Array(70, 74, 69), PackedInt32Array(67, 68, 76), PackedInt32Array(76, 68, 75), PackedInt32Array(78, 77, 62), PackedInt32Array(62, 77, 61), PackedInt32Array(78, 82, 77), PackedInt32Array(77, 82, 81), PackedInt32Array(77, 81, 80), PackedInt32Array(77, 80, 79), PackedInt32Array(80, 83, 79), PackedInt32Array(79, 86, 84), PackedInt32Array(84, 86, 85), PackedInt32Array(82, 88, 81), PackedInt32Array(81, 88, 87), PackedInt32Array(79, 84, 77), PackedInt32Array(80, 90, 83), PackedInt32Array(83, 90, 89), PackedInt32Array(93, 92, 91), PackedInt32Array(95, 94, 96), PackedInt32Array(96, 94, 97), PackedInt32Array(99, 98, 59), PackedInt32Array(59, 98, 58), PackedInt32Array(102, 101, 100), PackedInt32Array(105, 104, 106), PackedInt32Array(106, 104, 103), PackedInt32Array(106, 103, 107), PackedInt32Array(109, 108, 99), PackedInt32Array(99, 108, 98), PackedInt32Array(111, 110, 112), PackedInt32Array(112, 110, 113), PackedInt32Array(115, 108, 114), PackedInt32Array(114, 108, 109), PackedInt32Array(113, 110, 116), PackedInt32Array(120, 119, 117), PackedInt32Array(117, 119, 118), PackedInt32Array(124, 123, 121), PackedInt32Array(121, 123, 122), PackedInt32Array(120, 117, 125), PackedInt32Array(125, 117, 126), PackedInt32Array(105, 106, 122), PackedInt32Array(128, 127, 126), PackedInt32Array(126, 127, 125), PackedInt32Array(100, 107, 102), PackedInt32Array(102, 107, 103), PackedInt32Array(130, 129, 128), PackedInt32Array(128, 129, 127), PackedInt32Array(132, 131, 121), PackedInt32Array(121, 131, 124), PackedInt32Array(134, 133, 130), PackedInt32Array(130, 133, 129), PackedInt32Array(137, 136, 135), PackedInt32Array(133, 134, 138), PackedInt32Array(138, 134, 139), PackedInt32Array(141, 140, 101), PackedInt32Array(101, 140, 100), PackedInt32Array(105, 122, 142), PackedInt32Array(142, 122, 123), PackedInt32Array(144, 115, 143), PackedInt32Array(143, 115, 114), PackedInt32Array(131, 132, 116), PackedInt32Array(116, 132, 113), PackedInt32Array(144, 143, 145), PackedInt32Array(145, 143, 146), PackedInt32Array(141, 148, 140), PackedInt32Array(140, 148, 147), PackedInt32Array(149, 145, 146), PackedInt32Array(146, 150, 149), PackedInt32Array(149, 150, 151), PackedInt32Array(153, 152, 112), PackedInt32Array(112, 152, 111), PackedInt32Array(155, 151, 154), PackedInt32Array(154, 151, 150), PackedInt32Array(152, 153, 135), PackedInt32Array(135, 153, 137), PackedInt32Array(155, 154, 156), PackedInt32Array(156, 154, 157), PackedInt32Array(139, 136, 138), PackedInt32Array(138, 136, 137), PackedInt32Array(158, 156, 157), PackedInt32Array(157, 147, 158), PackedInt32Array(158, 147, 148), PackedInt32Array(161, 160, 159), PackedInt32Array(164, 163, 162), PackedInt32Array(159, 172, 161), PackedInt32Array(161, 171, 162), PackedInt32Array(162, 169, 164), PackedInt32Array(164, 168, 165), PackedInt32Array(165, 168, 167), PackedInt32Array(167, 171, 166), PackedInt32Array(166, 171, 159), PackedInt32Array(172, 170, 161), PackedInt32Array(159, 171, 172), PackedInt32Array(171, 169, 162), PackedInt32Array(161, 170, 171), PackedInt32Array(169, 168, 164), PackedInt32Array(168, 169, 167), PackedInt32Array(167, 169, 171), PackedInt32Array(172, 171, 170), PackedInt32Array(175, 174, 173), PackedInt32Array(178, 184, 179), PackedInt32Array(179, 184, 180), PackedInt32Array(180, 186, 181), PackedInt32Array(181, 186, 176), PackedInt32Array(176, 186, 177), PackedInt32Array(177, 186, 173), PackedInt32Array(173, 185, 175), PackedInt32Array(175, 184, 178), PackedInt32Array(184, 183, 180), PackedInt32Array(180, 183, 186), PackedInt32Array(186, 183, 173), PackedInt32Array(185, 182, 175), PackedInt32Array(173, 183, 185), PackedInt32Array(175, 182, 184), PackedInt32Array(184, 182, 183), PackedInt32Array(185, 183, 182), PackedInt32Array(176, 165, 181), PackedInt32Array(181, 165, 180), PackedInt32Array(180, 165, 167), PackedInt32Array(180, 167, 166), PackedInt32Array(180, 166, 179), PackedInt32Array(188, 187, 175), PackedInt32Array(190, 189, 193), PackedInt32Array(193, 189, 194), PackedInt32Array(194, 189, 191), PackedInt32Array(191, 189, 192), PackedInt32Array(175, 205, 188), PackedInt32Array(188, 204, 196), PackedInt32Array(196, 189, 195), PackedInt32Array(189, 198, 192), PackedInt32Array(192, 203, 178), PackedInt32Array(178, 202, 175), PackedInt32Array(205, 204, 188), PackedInt32Array(175, 202, 205), PackedInt32Array(204, 200, 196), PackedInt32Array(196, 200, 189), PackedInt32Array(198, 197, 192), PackedInt32Array(189, 200, 198), PackedInt32Array(203, 200, 178), PackedInt32Array(192, 197, 203), PackedInt32Array(178, 200, 202), PackedInt32Array(205, 201, 204), PackedInt32Array(202, 201, 205), PackedInt32Array(204, 202, 200), PackedInt32Array(198, 199, 197), PackedInt32Array(200, 199, 198), PackedInt32Array(203, 199, 200), PackedInt32Array(197, 199, 203), PackedInt32Array(201, 202, 204), PackedInt32Array(190, 193, 206), PackedInt32Array(206, 193, 207), PackedInt32Array(207, 193, 194), PackedInt32Array(207, 194, 191), PackedInt32Array(207, 191, 208), PackedInt32Array(211, 210, 209), PackedInt32Array(214, 218, 215), PackedInt32Array(215, 217, 212), PackedInt32Array(212, 221, 208), PackedInt32Array(208, 221, 207), PackedInt32Array(207, 223, 213), PackedInt32Array(213, 223, 214), PackedInt32Array(218, 217, 215), PackedInt32Array(214, 223, 218), PackedInt32Array(217, 221, 212), PackedInt32Array(221, 222, 207), PackedInt32Array(207, 220, 223), PackedInt32Array(218, 216, 217), PackedInt32Array(223, 216, 218), PackedInt32Array(217, 219, 221), PackedInt32Array(222, 220, 207), PackedInt32Array(221, 219, 222), PackedInt32Array(220, 216, 223), PackedInt32Array(216, 220, 217), PackedInt32Array(217, 220, 219), PackedInt32Array(222, 219, 220), PackedInt32Array(224, 213, 226), PackedInt32Array(226, 213, 227), PackedInt32Array(227, 213, 228), PackedInt32Array(228, 213, 225), PackedInt32Array(225, 213, 214), PackedInt32Array(211, 209, 212), PackedInt32Array(212, 209, 215), PackedInt32Array(213, 229, 207), PackedInt32Array(232, 231, 230), PackedInt32Array(232, 230, 233), PackedInt32Array(235, 234, 232), PackedInt32Array(235, 232, 236), PackedInt32Array(236, 232, 233), PackedInt32Array(239, 238, 237), PackedInt32Array(119, 239, 118), PackedInt32Array(118, 239, 237), PackedInt32Array(243, 242, 240), PackedInt32Array(240, 242, 241), PackedInt32Array(119, 244, 239), PackedInt32Array(119, 245, 244), PackedInt32Array(244, 245, 246), PackedInt32Array(250, 249, 247), PackedInt32Array(247, 249, 248), PackedInt32Array(252, 246, 251), PackedInt32Array(251, 246, 245), PackedInt32Array(249, 85, 248), PackedInt32Array(248, 85, 86), PackedInt32Array(254, 252, 253), PackedInt32Array(253, 252, 251), PackedInt32Array(247, 243, 250), PackedInt32Array(250, 243, 240), PackedInt32Array(242, 254, 241), PackedInt32Array(241, 254, 253), PackedInt32Array(256, 255, 257), PackedInt32Array(257, 255, 258), PackedInt32Array(260, 259, 261), PackedInt32Array(261, 259, 263), PackedInt32Array(261, 263, 262), PackedInt32Array(265, 264, 266), PackedInt32Array(266, 264, 262), PackedInt32Array(263, 258, 262), PackedInt32Array(262, 258, 255), PackedInt32Array(262, 255, 266), PackedInt32Array(266, 255, 267), PackedInt32Array(270, 269, 268), PackedInt32Array(271, 270, 272), PackedInt32Array(272, 270, 273), PackedInt32Array(273, 270, 274), PackedInt32Array(268, 275, 270), PackedInt32Array(270, 275, 274), PackedInt32Array(277, 276, 195), PackedInt32Array(195, 276, 196), PackedInt32Array(196, 276, 280), PackedInt32Array(196, 280, 279), PackedInt32Array(196, 279, 278), PackedInt32Array(196, 278, 188), PackedInt32Array(277, 206, 276), PackedInt32Array(276, 206, 207), PackedInt32Array(276, 207, 282), PackedInt32Array(276, 282, 281), PackedInt32Array(283, 282, 284), PackedInt32Array(284, 282, 207), PackedInt32Array(286, 285, 287), PackedInt32Array(287, 285, 290), PackedInt32Array(290, 285, 291), PackedInt32Array(291, 285, 288), PackedInt32Array(288, 285, 289), PackedInt32Array(292, 289, 293), PackedInt32Array(293, 289, 281), PackedInt32Array(281, 289, 285), PackedInt32Array(281, 285, 294), PackedInt32Array(295, 294, 296), PackedInt32Array(298, 297, 299), PackedInt32Array(299, 297, 301), PackedInt32Array(299, 301, 300), PackedInt32Array(295, 302, 294), PackedInt32Array(294, 302, 281), PackedInt32Array(281, 305, 276), PackedInt32Array(276, 305, 280), PackedInt32Array(280, 306, 279), PackedInt32Array(279, 306, 278), PackedInt32Array(278, 306, 303), PackedInt32Array(303, 305, 300), PackedInt32Array(300, 307, 299), PackedInt32Array(299, 305, 281), PackedInt32Array(305, 306, 280), PackedInt32Array(306, 305, 303), PackedInt32Array(305, 304, 300), PackedInt32Array(307, 305, 299), PackedInt32Array(300, 304, 307), PackedInt32Array(305, 307, 304), PackedInt32Array(295, 299, 302), PackedInt32Array(302, 299, 281), PackedInt32Array(309, 308, 300), PackedInt32Array(300, 308, 303), PackedInt32Array(312, 311, 310), PackedInt32Array(314, 313, 315), PackedInt32Array(315, 313, 316), PackedInt32Array(318, 317, 319), PackedInt32Array(319, 317, 320), PackedInt32Array(317, 321, 312), PackedInt32Array(317, 322, 321), PackedInt32Array(325, 324, 323), PackedInt32Array(325, 316, 313), PackedInt32Array(323, 320, 326), PackedInt32Array(326, 320, 317), PackedInt32Array(326, 317, 312), PackedInt32Array(326, 312, 310), PackedInt32Array(323, 326, 325), PackedInt32Array(325, 326, 316), PackedInt32Array(327, 329, 328), PackedInt32Array(329, 327, 330), PackedInt32Array(330, 327, 331), PackedInt32Array(331, 284, 330), PackedInt32Array(330, 284, 229), PackedInt32Array(229, 284, 207), PackedInt32Array(334, 333, 332), PackedInt32Array(336, 335, 337), PackedInt32Array(337, 335, 338), PackedInt32Array(162, 163, 339), PackedInt32Array(337, 338, 341), PackedInt32Array(341, 338, 340), PackedInt32Array(343, 177, 342), PackedInt32Array(342, 177, 173), PackedInt32Array(332, 344, 334), PackedInt32Array(334, 344, 339), PackedInt32Array(343, 342, 339), PackedInt32Array(339, 342, 334), PackedInt32Array(338, 347, 340), PackedInt32Array(340, 348, 345), PackedInt32Array(345, 344, 332), PackedInt32Array(344, 349, 339), PackedInt32Array(339, 349, 162), PackedInt32Array(162, 349, 338), PackedInt32Array(347, 348, 340), PackedInt32Array(338, 349, 347), PackedInt32Array(348, 344, 345), PackedInt32Array(344, 348, 349), PackedInt32Array(347, 346, 348), PackedInt32Array(349, 346, 347), PackedInt32Array(348, 346, 349), PackedInt32Array(351, 350, 352), PackedInt32Array(352, 350, 353), PackedInt32Array(357, 356, 354), PackedInt32Array(354, 356, 355), PackedInt32Array(360, 359, 358), PackedInt32Array(362, 361, 360), PackedInt32Array(364, 363, 362), PackedInt32Array(364, 362, 365), PackedInt32Array(365, 362, 360), PackedInt32Array(360, 358, 365), PackedInt32Array(308, 309, 366), PackedInt32Array(366, 309, 367), PackedInt32Array(368, 370, 369), PackedInt32Array(367, 371, 366), PackedInt32Array(366, 371, 368), PackedInt32Array(368, 371, 370), PackedInt32Array(370, 371, 372), PackedInt32Array(340, 345, 369), PackedInt32Array(369, 345, 368), PackedInt32Array(374, 373, 375), PackedInt32Array(375, 373, 376), PackedInt32Array(379, 378, 377), PackedInt32Array(381, 380, 382), PackedInt32Array(382, 380, 383), PackedInt32Array(383, 380, 384), PackedInt32Array(386, 388, 387), PackedInt32Array(387, 388, 385), PackedInt32Array(387, 385, 376), PackedInt32Array(376, 385, 380), PackedInt32Array(380, 385, 384), PackedInt32Array(390, 389, 391), PackedInt32Array(391, 389, 377), PackedInt32Array(385, 379, 377), PackedInt32Array(372, 398, 393), PackedInt32Array(393, 397, 392), PackedInt32Array(392, 398, 394), PackedInt32Array(394, 399, 395), PackedInt32Array(395, 399, 396), PackedInt32Array(396, 399, 389), PackedInt32Array(389, 399, 385), PackedInt32Array(385, 399, 371), PackedInt32Array(371, 398, 372), PackedInt32Array(398, 397, 393), PackedInt32Array(397, 398, 392), PackedInt32Array(398, 399, 394), PackedInt32Array(399, 398, 371), PackedInt32Array(336, 400, 335), PackedInt32Array(335, 400, 401), PackedInt32Array(389, 385, 377), PackedInt32Array(297, 382, 301), PackedInt32Array(301, 382, 383), PackedInt32Array(389, 403, 396), PackedInt32Array(396, 403, 395), PackedInt32Array(395, 403, 394), PackedInt32Array(394, 403, 402), PackedInt32Array(394, 402, 392), PackedInt32Array(371, 384, 385), PackedInt32Array(400, 372, 401), PackedInt32Array(401, 372, 393), PackedInt32Array(376, 373, 387), PackedInt32Array(405, 404, 406), PackedInt32Array(406, 404, 407), PackedInt32Array(406, 407, 392), PackedInt32Array(392, 407, 393), PackedInt32Array(409, 408, 410), PackedInt32Array(410, 408, 411), PackedInt32Array(412, 415, 413), PackedInt32Array(413, 415, 414), PackedInt32Array(417, 416, 415), PackedInt32Array(415, 416, 414), PackedInt32Array(420, 419, 418), PackedInt32Array(422, 421, 418), PackedInt32Array(418, 421, 420), PackedInt32Array(424, 420, 423), PackedInt32Array(423, 420, 421), PackedInt32Array(424, 425, 420), PackedInt32Array(427, 426, 428), PackedInt32Array(428, 426, 429), PackedInt32Array(431, 430, 432), PackedInt32Array(432, 430, 434), PackedInt32Array(432, 434, 433), PackedInt32Array(435, 437, 436), PackedInt32Array(438, 440, 439), PackedInt32Array(428, 429, 441), PackedInt32Array(441, 429, 444), PackedInt32Array(441, 444, 443), PackedInt32Array(441, 443, 442), PackedInt32Array(446, 445, 447), PackedInt32Array(447, 445, 444), PackedInt32Array(449, 448, 450), PackedInt32Array(450, 448, 440), PackedInt32Array(453, 452, 451), PackedInt32Array(455, 454, 431), PackedInt32Array(431, 454, 430), PackedInt32Array(440, 438, 450), PackedInt32Array(450, 438, 442), PackedInt32Array(442, 438, 457), PackedInt32Array(442, 457, 456), PackedInt32Array(458, 462, 459), PackedInt32Array(459, 462, 461), PackedInt32Array(459, 461, 463), PackedInt32Array(463, 461, 460), PackedInt32Array(458, 432, 464), PackedInt32Array(464, 432, 433), PackedInt32Array(429, 447, 444), PackedInt32Array(466, 465, 453), PackedInt32Array(287, 290, 438), PackedInt32Array(438, 290, 291), PackedInt32Array(438, 291, 288), PackedInt32Array(438, 288, 467), PackedInt32Array(468, 453, 469), PackedInt32Array(469, 453, 465), PackedInt32Array(470, 468, 460), PackedInt32Array(460, 468, 469), PackedInt32Array(224, 226, 471), PackedInt32Array(471, 226, 227), PackedInt32Array(471, 227, 228), PackedInt32Array(471, 228, 225), PackedInt32Array(471, 225, 466), PackedInt32Array(471, 466, 453), PackedInt32Array(461, 470, 460), PackedInt32Array(473, 475, 474), PackedInt32Array(474, 475, 441), PackedInt32Array(441, 475, 472), PackedInt32Array(472, 475, 473), PackedInt32Array(471, 482, 442), PackedInt32Array(442, 488, 441), PackedInt32Array(441, 479, 477), PackedInt32Array(477, 480, 478), PackedInt32Array(478, 480, 476), PackedInt32Array(476, 480, 451), PackedInt32Array(451, 487, 453), PackedInt32Array(453, 486, 471), PackedInt32Array(482, 477, 442), PackedInt32Array(471, 481, 482), PackedInt32Array(488, 479, 441), PackedInt32Array(442, 477, 488), PackedInt32Array(479, 488, 477), PackedInt32Array(477, 483, 480), PackedInt32Array(480, 487, 451), PackedInt32Array(487, 486, 453), PackedInt32Array(486, 484, 471), PackedInt32Array(482, 483, 477), PackedInt32Array(481, 483, 482), PackedInt32Array(471, 484, 481), PackedInt32Array(483, 487, 480), PackedInt32Array(487, 485, 486), PackedInt32Array(486, 485, 484), PackedInt32Array(481, 484, 483), PackedInt32Array(483, 485, 487), PackedInt32Array(485, 483, 484), PackedInt32Array(464, 462, 458), PackedInt32Array(456, 437, 471), PackedInt32Array(489, 490, 476), PackedInt32Array(476, 490, 478), PackedInt32Array(478, 490, 477), PackedInt32Array(477, 491, 441), PackedInt32Array(441, 491, 489), PackedInt32Array(489, 477, 490), PackedInt32Array(477, 489, 491), PackedInt32Array(444, 492, 443), PackedInt32Array(457, 438, 467), PackedInt32Array(430, 493, 434), PackedInt32Array(328, 435, 327), PackedInt32Array(464, 433, 453), PackedInt32Array(453, 433, 452), PackedInt32Array(328, 471, 435), PackedInt32Array(435, 471, 437), PackedInt32Array(474, 494, 489), PackedInt32Array(489, 494, 441), PackedInt32Array(441, 494, 474), PackedInt32Array(442, 456, 471), PackedInt32Array(498, 497, 495), PackedInt32Array(495, 497, 496), PackedInt32Array(502, 501, 499), PackedInt32Array(499, 501, 500), PackedInt32Array(449, 503, 448), PackedInt32Array(448, 503, 504), PackedInt32Array(505, 443, 492), PackedInt32Array(503, 505, 504), PackedInt32Array(504, 505, 376), PackedInt32Array(376, 505, 375), PackedInt32Array(375, 505, 492), PackedInt32Array(506, 509, 507), PackedInt32Array(507, 509, 508), PackedInt32Array(511, 510, 509), PackedInt32Array(509, 510, 508), PackedInt32Array(508, 510, 512), PackedInt32Array(515, 514, 513), PackedInt32Array(517, 516, 515), PackedInt32Array(517, 515, 518), PackedInt32Array(518, 515, 513), PackedInt32Array(513, 519, 518), PackedInt32Array(523, 522, 520), PackedInt32Array(520, 522, 521), PackedInt32Array(426, 427, 524), PackedInt32Array(524, 427, 525), PackedInt32Array(527, 526, 528), PackedInt32Array(528, 526, 530), PackedInt32Array(528, 530, 529), PackedInt32Array(532, 531, 527), PackedInt32Array(527, 531, 526), PackedInt32Array(532, 533, 531), PackedInt32Array(531, 533, 534), PackedInt32Array(524, 525, 535), PackedInt32Array(535, 525, 537), PackedInt32Array(535, 537, 536), PackedInt32Array(390, 391, 536), PackedInt32Array(536, 391, 535), PackedInt32Array(535, 391, 446), PackedInt32Array(446, 391, 445), PackedInt32Array(539, 541, 473), PackedInt32Array(473, 538, 472), PackedInt32Array(538, 541, 529), PackedInt32Array(529, 543, 528), PackedInt32Array(528, 542, 539), PackedInt32Array(541, 538, 473), PackedInt32Array(539, 542, 541), PackedInt32Array(541, 543, 529), PackedInt32Array(543, 544, 528), PackedInt32Array(528, 544, 542), PackedInt32Array(542, 540, 541), PackedInt32Array(541, 540, 543), PackedInt32Array(543, 540, 544), PackedInt32Array(544, 540, 542), PackedInt32Array(402, 403, 545), PackedInt32Array(539, 546, 528), PackedInt32Array(533, 547, 534), PackedInt32Array(534, 547, 548), PackedInt32Array(402, 545, 529), PackedInt32Array(529, 545, 538), PackedInt32Array(525, 538, 537), PackedInt32Array(537, 538, 545), PackedInt32Array(547, 528, 548), PackedInt32Array(548, 528, 546), PackedInt32Array(405, 530, 404), PackedInt32Array(404, 530, 526), PackedInt32Array(550, 549, 551), PackedInt32Array(551, 549, 553), PackedInt32Array(551, 553, 552), PackedInt32Array(555, 554, 556), PackedInt32Array(556, 554, 557), PackedInt32Array(553, 557, 552), PackedInt32Array(552, 557, 554), PackedInt32Array(552, 554, 559), PackedInt32Array(552, 559, 558), PackedInt32Array(552, 558, 560), PackedInt32Array(562, 561, 563), PackedInt32Array(563, 561, 564), PackedInt32Array(564, 561, 565), PackedInt32Array(459, 463, 455), PackedInt32Array(455, 463, 454), PackedInt32Array(454, 463, 460)]
agent_height = 1.75
agent_radius = 0.375
agent_max_climb = 0.5
edge_max_length = 12.0
filter_low_hanging_obstacles = true
filter_ledge_spans = true
filter_walkable_low_height_spans = true

[sub_resource type="StandardMaterial3D" id="StandardMaterial3D_1ks1q"]
albedo_texture = ExtResource("15_df8b0")
metallic = 0.2
metallic_texture = ExtResource("16_003du")
roughness = 0.8
uv1_scale = Vector3(10, 10, 1)

[node name="level01" type="Node3D"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 0, 0, 1.94383)
script = ExtResource("1_hk2b6")
minimap_scene = ExtResource("2_j32d5")
player_avatar_scene = ExtResource("3_0f42i")
item_boxes_scene = ExtResource("4_swvig")
skill_box_ui_scene = ExtResource("5_dbohx")

[node name="WorldEnvironment" type="WorldEnvironment" parent="."]
environment = SubResource("Environment_j4i7h")

[node name="DirectionalLight3D" type="DirectionalLight3D" parent="."]
transform = Transform3D(0.999999, 0, 0, 0, 0.5, 0.866026, 0, -0.866025, 0.5, 0, 0, 0)
light_color = Color(1, 0.868175, 0.323518, 1)
light_energy = 1.5
shadow_enabled = true
shadow_opacity = 0.7
shadow_blur = 2.0

[node name="ModularLevelGenerator" type="Node3D" parent="."]
script = ExtResource("6_6ly8b")
level_config = ExtResource("7_r5102")
mesh_x_rotation_degrees = 0.0

[node name="FogOfWar" parent="." instance=ExtResource("9_003du")]
player_vision_radius = 8.0
enabled = false

[node name="Camera3D" type="Camera3D" parent="."]
unique_name_in_owner = true
transform = Transform3D(-4.37114e-08, 0.939693, -0.34202, 0, 0.34202, 0.939692, 1, 4.10753e-08, -1.49502e-08, 3, 22.312, 0)
v_offset = -10.0
current = true
fov = 112.1

[node name="Player" parent="." instance=ExtResource("11_82jtv")]

[node name="Chest" parent="." instance=ExtResource("14_1ks1q")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 4.57862, 0, 6.58062)
interaction_distance = 3.0
item_resource = ExtResource("17_j32d5")

[node name="Chest2" parent="." instance=ExtResource("14_1ks1q")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 9.81867, 0, 6.58062)
interaction_distance = 3.0
item_resource = ExtResource("18_j32d5")

[node name="EnemyBoy01" parent="." instance=ExtResource("19_0f42i")]
transform = Transform3D(0.8, 0, 0, 0, 0.8, 0, 0, 0, 0.8, -10.0014, 0, -8.65824)

[node name="Enemy02" parent="." instance=ExtResource("21_dbohx")]
transform = Transform3D(2.1, 0, 0, 0, 2.1, 0, 0, 0, 2.1, 7.86917, 0, -7.768)

[node name="NavigationRegion3D" type="NavigationRegion3D" parent="."]
navigation_mesh = SubResource("NavigationMesh_fcnrs")

[node name="Floor" type="Node3D" parent="NavigationRegion3D"]

[node name="@CSGBox3D@34768" type="CSGBox3D" parent="NavigationRegion3D/Floor"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 0, -0.05, 0)
use_collision = true
size = Vector3(100, 0.1, 100)
material = SubResource("StandardMaterial3D_1ks1q")

[node name="FloorTiles" type="Node3D" parent="NavigationRegion3D/Floor"]

[node name="Floor01_0" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 19.1016, 0.1, -14.2347)

[node name="Floor01_1" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 13.0685, 0.1, -34.2941)

[node name="Floor01_2" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -15.8672, 0.1, -31.9773)

[node name="Floor01_3" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -37.1648, 0.1, 7.30103)

[node name="Floor01_4" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 44.1887, 0.1, 32.1216)

[node name="Floor01_5" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 21.8083, 0.1, 36.9886)

[node name="Floor01_6" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 24.8491, 0.1, -25.5509)

[node name="Floor01_7" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -20.6651, 0.1, -7.42654)

[node name="Floor01_8" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -28.9349, 0.1, 8.30447)

[node name="Floor01_9" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 8.00766, 0.1, -17.0973)

[node name="Floor01_10" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -0.186272, 0.1, 9.62765)

[node name="Floor01_11" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 37.8287, 0.1, -42.9975)

[node name="Floor01_12" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -4.06662, 0.1, -30.7045)

[node name="Floor01_13" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 33.3727, 0.1, 3.60321)

[node name="Floor01_14" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -6.01478, 0.1, -7.71796)

[node name="Floor01_15" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 12.4304, 0.1, 1.00863)

[node name="Floor01_16" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 36.4998, 0.1, -28.9838)

[node name="Floor01_17" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -2.77706, 0.1, -44.1166)

[node name="Floor01_18" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 19.7221, 0.1, 9.95851)

[node name="Floor01_19" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 6.34433, 0.1, 30.6789)

[node name="Floor01_20" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 17.468, 0.1, -42.7469)

[node name="Floor01_21" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 21.8824, 0.1, 28.371)

[node name="Floor01_22" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 31.0388, 0.1, 31.3249)

[node name="Floor01_23" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 26.2039, 0.1, -40.5082)

[node name="Floor01_24" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -41.8074, 0.1, -21.1945)

[node name="Floor01_25" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 40.9301, 0.1, -21.381)

[node name="Floor01_26" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 34.2395, 0.1, -16.186)

[node name="Floor01_27" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -4.61233, 0.1, 32.8028)

[node name="Floor01_28" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -12.9917, 0.1, 4.06872)

[node name="Floor01_29" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 8.20506, 0.1, -44.4883)

[node name="Floor01_30" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 35.0095, 0.1, 13.0216)

[node name="Floor01_31" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -24.0013, 0.1, 31.9011)

[node name="Floor01_32" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -44.1753, 0.1, -29.9556)

[node name="Floor01_33" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 2.66576, 0.1, 18.6451)

[node name="Floor01_34" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -7.08762, 0.1, 19.3872)

[node name="Floor01_35" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -35.4141, 0.1, 31.1902)

[node name="Floor01_36" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -24.9737, 0.1, -17.7957)

[node name="Floor01_37" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -14.4168, 0.1, 12.004)

[node name="Floor01_38" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 41.7071, 0.1, 4.37741)

[node name="Floor01_39" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -1.81793, 0.1, -20.7254)

[node name="Floor01_40" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -10.6715, 0.1, 44.1427)

[node name="Floor01_41" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 14.2061, 0.1, 43.2272)

[node name="Floor01_42" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -14.0796, 0.1, 33.3567)

[node name="Floor01_43" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 38.7019, 0.1, -7.74623)

[node name="Floor01_44" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 1.53642, 0.1, 0.962147)

[node name="Floor01_45" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -44.4633, 0.1, -41.1836)

[node name="Floor01_46" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -30.6745, 0.1, -8.65722)

[node name="Floor01_47" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 13.9789, 0.1, 26.8487)

[node name="Floor01_48" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 43.1884, 0.1, 18.5462)

[node name="Floor01_49" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -20.2012, 0.1, -44.7401)

[node name="Floor01_50" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -32.3132, 0.1, -30.7871)

[node name="Floor01_51" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -23.3555, 0.1, 43.897)

[node name="Floor01_52" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -34.6345, 0.1, -17.3813)

[node name="Floor01_53" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 26.2056, 0.1, 16.0056)

[node name="Floor01_54" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 44.9872, 0.1, -35.5094)

[node name="Floor01_55" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -30.8826, 0.1, -0.263475)

[node name="Floor01_56" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 35.0292, 0.1, 21.5485)

[node name="Floor01_57" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 6.80969, 0.1, -6.45575)

[node name="Floor01_58" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -44.7195, 0.1, 3.9818)

[node name="Floor01_59" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 26.7947, 0.1, 43.849)

[node name="Floor01_60" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -34.7075, 0.1, -44.1348)

[node name="Floor01_61" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -39.2052, 0.1, 44.8085)

[node name="Floor01_62" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -43.0984, 0.1, -9.08777)

[node name="Floor01_63" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 35.7636, 0.1, 44.8056)

[node name="Floor01_64" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 6.10674, 0.1, 44.8558)

[node name="Floor01_65" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -42.7368, 0.1, 15.7241)

[node name="Floor01_66" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 6.85586, 0.1, -25.8251)

[node name="Floor01_67" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -42.6659, 0.1, 27.2443)

[node name="Floor01_68" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 30.4987, 0.1, -7.08249)

[node name="Floor01_69" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 44.8082, 0.1, -13.4315)

[node name="Floor01_70" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -29.5812, 0.1, 24.0296)

[node name="Floor01_71" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -24.9849, 0.1, 17.4496)

[node name="Floor01_72" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 3.11939, 0.1, -36.786)

[node name="wall_0" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -50, 0, -50)

[node name="wall_1" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -50, 0, 50)

[node name="wall_2" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -48, 0, -50)

[node name="wall_3" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -48, 0, 50)

[node name="wall_4" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -46, 0, -50)

[node name="wall_5" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -46, 0, 50)

[node name="wall_6" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -44, 0, -50)

[node name="wall_7" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -44, 0, 50)

[node name="wall_8" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -42, 0, -50)

[node name="wall_9" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -42, 0, 50)

[node name="wall_10" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -40, 0, -50)

[node name="wall_11" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -40, 0, 50)

[node name="wall_12" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -38, 0, -50)

[node name="wall_13" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -38, 0, 50)

[node name="wall_14" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -36, 0, -50)

[node name="wall_15" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -36, 0, 50)

[node name="wall_16" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -34, 0, -50)

[node name="wall_17" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -34, 0, 50)

[node name="wall_18" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -32, 0, -50)

[node name="wall_19" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -32, 0, 50)

[node name="wall_20" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -30, 0, -50)

[node name="wall_21" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -30, 0, 50)

[node name="wall_22" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -28, 0, -50)

[node name="wall_23" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -28, 0, 50)

[node name="wall_24" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -26, 0, -50)

[node name="wall_25" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -26, 0, 50)

[node name="wall_26" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -24, 0, -50)

[node name="wall_27" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -24, 0, 50)

[node name="wall_28" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -22, 0, -50)

[node name="wall_29" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -22, 0, 50)

[node name="wall_30" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -20, 0, -50)

[node name="wall_31" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -20, 0, 50)

[node name="wall_32" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -18, 0, -50)

[node name="wall_33" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -18, 0, 50)

[node name="wall_34" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -16, 0, -50)

[node name="wall_35" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -16, 0, 50)

[node name="wall_36" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -14, 0, -50)

[node name="wall_37" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -14, 0, 50)

[node name="wall_38" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -12, 0, -50)

[node name="wall_39" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -12, 0, 50)

[node name="wall_40" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -10, 0, -50)

[node name="wall_41" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -10, 0, 50)

[node name="wall_42" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -8, 0, -50)

[node name="wall_43" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -8, 0, 50)

[node name="wall_44" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -6, 0, -50)

[node name="wall_45" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -6, 0, 50)

[node name="wall_46" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -4, 0, -50)

[node name="wall_47" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -4, 0, 50)

[node name="wall_48" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -2, 0, -50)

[node name="wall_49" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -2, 0, 50)

[node name="wall_50" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 0, 0, -50)

[node name="wall_51" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 0, 0, 50)

[node name="wall_52" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 2, 0, -50)

[node name="wall_53" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 2, 0, 50)

[node name="wall_54" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 4, 0, -50)

[node name="wall_55" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 4, 0, 50)

[node name="wall_56" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 6, 0, -50)

[node name="wall_57" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 6, 0, 50)

[node name="wall_58" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 8, 0, -50)

[node name="wall_59" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 8, 0, 50)

[node name="wall_60" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 10, 0, -50)

[node name="wall_61" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 10, 0, 50)

[node name="wall_62" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 12, 0, -50)

[node name="wall_63" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 12, 0, 50)

[node name="wall_64" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 14, 0, -50)

[node name="wall_65" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 14, 0, 50)

[node name="wall_66" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 16, 0, -50)

[node name="wall_67" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 16, 0, 50)

[node name="wall_68" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 18, 0, -50)

[node name="wall_69" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 18, 0, 50)

[node name="wall_70" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 20, 0, -50)

[node name="wall_71" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 20, 0, 50)

[node name="wall_72" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 22, 0, -50)

[node name="wall_73" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 22, 0, 50)

[node name="wall_74" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 24, 0, -50)

[node name="wall_75" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 24, 0, 50)

[node name="wall_76" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 26, 0, -50)

[node name="wall_77" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 26, 0, 50)

[node name="wall_78" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 28, 0, -50)

[node name="wall_79" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 28, 0, 50)

[node name="wall_80" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 30, 0, -50)

[node name="wall_81" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 30, 0, 50)

[node name="wall_82" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 32, 0, -50)

[node name="wall_83" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 32, 0, 50)

[node name="wall_84" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 34, 0, -50)

[node name="wall_85" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 34, 0, 50)

[node name="wall_86" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 36, 0, -50)

[node name="wall_87" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 36, 0, 50)

[node name="wall_88" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 38, 0, -50)

[node name="wall_89" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 38, 0, 50)

[node name="wall_90" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 40, 0, -50)

[node name="wall_91" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 40, 0, 50)

[node name="wall_92" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 42, 0, -50)

[node name="wall_93" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 42, 0, 50)

[node name="wall_94" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 44, 0, -50)

[node name="wall_95" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 44, 0, 50)

[node name="wall_96" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 46, 0, -50)

[node name="wall_97" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 46, 0, 50)

[node name="wall_98" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 48, 0, -50)

[node name="wall_99" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 48, 0, 50)

[node name="wall_100" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 50, 0, -50)

[node name="wall_101" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 50, 0, 50)

[node name="wall_102" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, -50, 0, -50)

[node name="wall_103" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, 50, 0, -50)

[node name="wall_104" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, -50, 0, -48)

[node name="wall_105" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, 50, 0, -48)

[node name="wall_106" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, -50, 0, -46)

[node name="wall_107" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, 50, 0, -46)

[node name="wall_108" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, -50, 0, -44)

[node name="wall_109" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, 50, 0, -44)

[node name="wall_110" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, -50, 0, -42)

[node name="wall_111" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, 50, 0, -42)

[node name="wall_112" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, -50, 0, -40)

[node name="wall_113" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, 50, 0, -40)

[node name="wall_114" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, -50, 0, -38)

[node name="wall_115" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, 50, 0, -38)

[node name="wall_116" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, -50, 0, -36)

[node name="wall_117" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, 50, 0, -36)

[node name="wall_118" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, -50, 0, -34)

[node name="wall_119" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, 50, 0, -34)

[node name="wall_120" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, -50, 0, -32)

[node name="wall_121" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, 50, 0, -32)

[node name="wall_122" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, -50, 0, -30)

[node name="wall_123" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, 50, 0, -30)

[node name="wall_124" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, -50, 0, -28)

[node name="wall_125" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, 50, 0, -28)

[node name="wall_126" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, -50, 0, -26)

[node name="wall_127" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, 50, 0, -26)

[node name="wall_128" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, -50, 0, -24)

[node name="wall_129" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, 50, 0, -24)

[node name="wall_130" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, -50, 0, -22)

[node name="wall_131" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, 50, 0, -22)

[node name="wall_132" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, -50, 0, -20)

[node name="wall_133" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, 50, 0, -20)

[node name="wall_134" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, -50, 0, -18)

[node name="wall_135" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, 50, 0, -18)

[node name="wall_136" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, -50, 0, -16)

[node name="wall_137" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, 50, 0, -16)

[node name="wall_138" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, -50, 0, -14)

[node name="wall_139" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, 50, 0, -14)

[node name="wall_140" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, -50, 0, -12)

[node name="wall_141" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, 50, 0, -12)

[node name="wall_142" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, -50, 0, -10)

[node name="wall_143" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, 50, 0, -10)

[node name="wall_144" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, -50, 0, -8)

[node name="wall_145" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, 50, 0, -8)

[node name="wall_146" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, -50, 0, -6)

[node name="wall_147" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, 50, 0, -6)

[node name="wall_148" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, -50, 0, -4)

[node name="wall_149" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, 50, 0, -4)

[node name="wall_150" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, -50, 0, -2)

[node name="wall_151" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, 50, 0, -2)

[node name="wall_152" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, -50, 0, 0)

[node name="wall_153" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, 50, 0, 0)

[node name="wall_154" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, -50, 0, 2)

[node name="wall_155" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, 50, 0, 2)

[node name="wall_156" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, -50, 0, 4)

[node name="wall_157" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, 50, 0, 4)

[node name="wall_158" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, -50, 0, 6)

[node name="wall_159" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, 50, 0, 6)

[node name="wall_160" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, -50, 0, 8)

[node name="wall_161" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, 50, 0, 8)

[node name="wall_162" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, -50, 0, 10)

[node name="wall_163" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, 50, 0, 10)

[node name="wall_164" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, -50, 0, 12)

[node name="wall_165" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, 50, 0, 12)

[node name="wall_166" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, -50, 0, 14)

[node name="wall_167" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, 50, 0, 14)

[node name="wall_168" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, -50, 0, 16)

[node name="wall_169" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, 50, 0, 16)

[node name="wall_170" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, -50, 0, 18)

[node name="wall_171" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, 50, 0, 18)

[node name="wall_172" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, -50, 0, 20)

[node name="wall_173" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, 50, 0, 20)

[node name="wall_174" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, -50, 0, 22)

[node name="wall_175" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, 50, 0, 22)

[node name="wall_176" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, -50, 0, 24)

[node name="wall_177" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, 50, 0, 24)

[node name="wall_178" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, -50, 0, 26)

[node name="wall_179" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, 50, 0, 26)

[node name="wall_180" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, -50, 0, 28)

[node name="wall_181" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, 50, 0, 28)

[node name="wall_182" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, -50, 0, 30)

[node name="wall_183" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, 50, 0, 30)

[node name="wall_184" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, -50, 0, 32)

[node name="wall_185" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, 50, 0, 32)

[node name="wall_186" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, -50, 0, 34)

[node name="wall_187" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, 50, 0, 34)

[node name="wall_188" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, -50, 0, 36)

[node name="wall_189" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, 50, 0, 36)

[node name="wall_190" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, -50, 0, 38)

[node name="wall_191" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, 50, 0, 38)

[node name="wall_192" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, -50, 0, 40)

[node name="wall_193" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, 50, 0, 40)

[node name="wall_194" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, -50, 0, 42)

[node name="wall_195" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, 50, 0, 42)

[node name="wall_196" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, -50, 0, 44)

[node name="wall_197" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, 50, 0, 44)

[node name="wall_198" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, -50, 0, 46)

[node name="wall_199" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, 50, 0, 46)

[node name="wall_200" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, -50, 0, 48)

[node name="wall_201" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, 50, 0, 48)

[node name="wall_202" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, -50, 0, 50)

[node name="wall_203" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, 50, 0, 50)

[node name="tree_0" parent="NavigationRegion3D" instance=ExtResource("15_003du")]
transform = Transform3D(2.5, -0.00145593, -0.00167976, 0.00145725, 2.5, 0.00196474, 0.00167861, -0.00196571, 2.5, -19.3879, 0, -13.7547)

[node name="PatrolPoint_0" type="Node3D" parent="NavigationRegion3D"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -21.3589, 1, -14.2304)
script = ExtResource("15_6ly8b")

[node name="tree_1" parent="NavigationRegion3D" instance=ExtResource("15_003du")]
transform = Transform3D(2.5, -0.00145593, -0.00167976, 0.00145725, 2.5, 0.00196474, 0.00167861, -0.00196571, 2.5, 7.72008, 0, 3.32183)

[node name="PatrolPoint_1" type="Node3D" parent="NavigationRegion3D"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 8.14858, 1, 1.48882)
script = ExtResource("15_6ly8b")
point_id = 1

[node name="tree_2" parent="NavigationRegion3D" instance=ExtResource("15_003du")]
transform = Transform3D(2.5, -0.00145593, -0.00167976, 0.00145725, 2.5, 0.00196474, 0.00167861, -0.00196571, 2.5, -6.23076, 0, 6.28085)
preset_burnt_out_type = 1

[node name="PatrolPoint_2" type="Node3D" parent="NavigationRegion3D"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -4.90517, 1, 11.7116)
script = ExtResource("15_6ly8b")
point_id = 2

[node name="tree_3" parent="NavigationRegion3D" instance=ExtResource("15_003du")]
transform = Transform3D(2.5, -0.00145593, -0.00167976, 0.00145725, 2.5, 0.00196474, 0.00167861, -0.00196571, 2.5, -14.4379, 0, 18.2728)

[node name="PatrolPoint_3" type="Node3D" parent="NavigationRegion3D"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -18.7461, 1, 23.6515)
script = ExtResource("15_6ly8b")
point_id = 3

[node name="chest_0" parent="NavigationRegion3D" instance=ExtResource("14_1ks1q")]
transform = Transform3D(0.152136, 0, -0.98836, 0, 1, 0, 0.98836, 0, 0.152136, -35.1638, 0, 23.7023)

[node name="rock_0" parent="NavigationRegion3D" instance=ExtResource("21_xcdtp")]
transform = Transform3D(-0.356773, 0, 0.830046, 0, 0.903473, 0, -0.830046, 0, -0.356773, 15.6005, 0.01, -25.2271)

[node name="rock_1" parent="NavigationRegion3D" instance=ExtResource("21_xcdtp")]
transform = Transform3D(-0.876097, 0, 0.522905, 0, 1.02028, 0, -0.522905, 0, -0.876097, -36.3136, 0.01, 18.5789)

[node name="rock_2" parent="NavigationRegion3D" instance=ExtResource("21_xcdtp")]
transform = Transform3D(0.00204961, 0, 1.12063, 0, 1.12063, 0, -1.12063, 0, 0.00204961, -16.6967, 0.01, -37.8802)

[node name="rock_3" parent="NavigationRegion3D" instance=ExtResource("21_xcdtp")]
transform = Transform3D(-0.961372, 0, -0.165322, 0, 0.975484, 0, 0.165322, 0, -0.961372, 17.7985, 0.01, 15.4884)

[node name="rock_4" parent="NavigationRegion3D" instance=ExtResource("21_xcdtp")]
transform = Transform3D(1.12939, 0, -0.301798, 0, 1.16902, 0, 0.301798, 0, 1.12939, -40.2038, 0.01, -3.02585)

[node name="rock_5" parent="NavigationRegion3D" instance=ExtResource("21_xcdtp")]
transform = Transform3D(0.244023, 0, -1.0033, 0, 1.03255, 0, 1.0033, 0, 0.244023, -8.48378, 0.01, -2.99677)

[node name="rock_6" parent="NavigationRegion3D" instance=ExtResource("21_xcdtp")]
transform = Transform3D(0.869615, 0, -0.532805, 0, 1.01986, 0, 0.532805, 0, 0.869615, -13.9794, 0.01, -16.8249)

[node name="rock_7" parent="NavigationRegion3D" instance=ExtResource("21_xcdtp")]
transform = Transform3D(0.618564, 0, 0.854733, 0, 1.05508, 0, -0.854733, 0, 0.618564, -30.9132, 0.01, 40.9036)

[node name="rock_8" parent="NavigationRegion3D" instance=ExtResource("21_xcdtp")]
transform = Transform3D(-0.880743, 0, 0.198419, 0, 0.902817, 0, -0.198419, 0, -0.880743, -11.5936, 0.01, -34.9408)

[node name="rock_9" parent="NavigationRegion3D" instance=ExtResource("21_xcdtp")]
transform = Transform3D(0.291081, 0, 1.13275, 0, 1.16956, 0, -1.13275, 0, 0.291081, -16.1599, 0.01, -1.58702)

[node name="rock_10" parent="NavigationRegion3D" instance=ExtResource("21_xcdtp")]
transform = Transform3D(0.906987, 0, -0.676002, 0, 1.1312, 0, 0.676002, 0, 0.906987, 26.9739, 0.01, -2.20258)

[node name="rock_11" parent="NavigationRegion3D" instance=ExtResource("21_xcdtp")]
transform = Transform3D(0.985908, 0, -0.417847, 0, 1.0708, 0, 0.417847, 0, 0.985908, 22.842, 0.01, 19.8248)

[node name="rock_12" parent="NavigationRegion3D" instance=ExtResource("21_xcdtp")]
transform = Transform3D(0.703768, 0, -0.90909, 0, 1.14967, 0, 0.90909, 0, 0.703768, 20.1538, 0.01, -19.4108)

[node name="rock_13" parent="NavigationRegion3D" instance=ExtResource("21_xcdtp")]
transform = Transform3D(0.125251, 0, 0.802855, 0, 0.812566, 0, -0.802855, 0, 0.125251, 43.5139, 0.01, 38.3921)

[node name="rock_14" parent="NavigationRegion3D" instance=ExtResource("21_xcdtp")]
transform = Transform3D(-0.325616, 0, 0.855542, 0, 0.915411, 0, -0.855542, 0, -0.325616, 29.6745, 0.01, -18.6581)

[node name="barrel_0" parent="NavigationRegion3D" instance=ExtResource("22_cin4e")]
transform = Transform3D(0.800955, 0, 0.634217, 0, 1.02165, 0, -0.634217, 0, 0.800955, 18.9611, 0, -1.87692)

[node name="barrel_1" parent="NavigationRegion3D" instance=ExtResource("22_cin4e")]
transform = Transform3D(-0.810557, 0, -0.0576646, 0, 0.812605, 0, 0.0576646, 0, -0.810557, -44.6824, 0, 40.4018)

[node name="decoration_0" parent="NavigationRegion3D" instance=ExtResource("23_6j2fk")]
transform = Transform3D(0.141024, 0, -1.2145, 0, 1.22266, 0, 1.2145, 0, 0.141024, 12.4988, 0, 12.0373)

[node name="decoration_1" parent="NavigationRegion3D" instance=ExtResource("23_6j2fk")]
transform = Transform3D(-0.370637, 0, 0.841102, 0, 0.919144, 0, -0.841102, 0, -0.370637, 24.2956, 0, -31.7192)

[node name="decoration_2" parent="NavigationRegion3D" instance=ExtResource("23_6j2fk")]
transform = Transform3D(0.742194, 0, -0.855918, 0, 1.13289, 0, 0.855918, 0, 0.742194, -11.1701, 0, -44.2528)

[node name="decoration_3" parent="NavigationRegion3D" instance=ExtResource("23_6j2fk")]
transform = Transform3D(-1.26579, 0, -0.079115, 0, 1.26826, 0, 0.079115, 0, -1.26579, 1.48308, 0, 41.5637)

[node name="decoration_4" parent="NavigationRegion3D" instance=ExtResource("23_6j2fk")]
transform = Transform3D(1.09115, 0, 0.13687, 0, 1.0997, 0, -0.13687, 0, 1.09115, -39.4704, 0, -4.04753)

[node name="decoration_5" parent="NavigationRegion3D" instance=ExtResource("23_6j2fk")]
transform = Transform3D(-0.160752, 0, 1.36592, 0, 1.37534, 0, -1.36592, 0, -0.160752, -14.1951, 0, 24.7323)

[node name="decoration_6" parent="NavigationRegion3D" instance=ExtResource("23_6j2fk")]
transform = Transform3D(0.548699, 0, -0.66245, 0, 0.860181, 0, 0.66245, 0, 0.548699, 28.3386, 0, 22.0592)

[node name="decoration_7" parent="NavigationRegion3D" instance=ExtResource("23_6j2fk")]
transform = Transform3D(0.196466, 0, 1.0822, 0, 1.09989, 0, -1.0822, 0, 0.196466, -20.3745, 0, -25.7715)

[node name="decoration_8" parent="NavigationRegion3D" instance=ExtResource("23_6j2fk")]
transform = Transform3D(1.15609, 0, 0.127372, 0, 1.16309, 0, -0.127372, 0, 1.15609, -22.8967, 0, 22.572)

[node name="decoration_9" parent="NavigationRegion3D" instance=ExtResource("23_6j2fk")]
transform = Transform3D(-0.0738185, 0, 1.38913, 0, 1.39109, 0, -1.38913, 0, -0.0738185, -32.3729, 0, -24.8057)

[node name="decoration_10" parent="NavigationRegion3D" instance=ExtResource("23_6j2fk")]
transform = Transform3D(-0.794671, 0, 0.960424, 0, 1.24656, 0, -0.960424, 0, -0.794671, -43.9264, 0, -36.2058)

[node name="decoration_11" parent="NavigationRegion3D" instance=ExtResource("23_6j2fk")]
transform = Transform3D(0.160923, 0, -1.29102, 0, 1.30101, 0, 1.29102, 0, 0.160923, 3.71175, 0, -30.2859)

[node name="decoration_12" parent="NavigationRegion3D" instance=ExtResource("23_6j2fk")]
transform = Transform3D(-0.725381, 0, -0.257119, 0, 0.769602, 0, 0.257119, 0, -0.725381, -30.238, 0, 16.3175)

[node name="decoration_13" parent="NavigationRegion3D" instance=ExtResource("23_6j2fk")]
transform = Transform3D(1.30904, 0, -0.0666607, 0, 1.31074, 0, 0.0666607, 0, 1.30904, -2.24723, 0, 4.5343)

[node name="decoration_14" parent="NavigationRegion3D" instance=ExtResource("23_6j2fk")]
transform = Transform3D(-0.797151, 0, 0.292153, 0, 0.849001, 0, -0.292153, 0, -0.797151, -37.9371, 0, -9.50853)

[node name="decoration_15" parent="NavigationRegion3D" instance=ExtResource("23_6j2fk")]
transform = Transform3D(0.277323, 0, -0.790589, 0, 0.837818, 0, 0.790589, 0, 0.277323, -39.1772, 0, 1.26298)

[node name="decoration_16" parent="NavigationRegion3D" instance=ExtResource("23_6j2fk")]
transform = Transform3D(-0.514463, 0, 0.799415, 0, 0.95065, 0, -0.799415, 0, -0.514463, -26.0323, 0, -33.8132)

[node name="decoration_17" parent="NavigationRegion3D" instance=ExtResource("23_6j2fk")]
transform = Transform3D(0.925543, 0, 0.228443, 0, 0.953319, 0, -0.228443, 0, 0.925543, -42.2835, 0, 21.9538)

[node name="decoration_18" parent="NavigationRegion3D" instance=ExtResource("23_6j2fk")]
transform = Transform3D(-0.35756, 0, -0.895056, 0, 0.963834, 0, 0.895056, 0, -0.35756, -12.7987, 0, -23.3094)

[node name="decoration_19" parent="NavigationRegion3D" instance=ExtResource("23_6j2fk")]
transform = Transform3D(-0.59674, 0, 1.14919, 0, 1.29488, 0, -1.14919, 0, -0.59674, 42.9682, 0, 26.8174)

[node name="PatrolPointManager" type="Node" parent="."]
script = ExtResource("24_j2uky")
