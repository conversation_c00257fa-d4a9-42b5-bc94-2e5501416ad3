[gd_scene load_steps=38 format=3 uid="uid://c0rj4dwj18nv8"]

[ext_resource type="Script" uid="uid://bo0h8t35yls55" path="res://Scripts/Levels/Level01.gd" id="1_hk2b6"]
[ext_resource type="PackedScene" uid="uid://d2xyw4d37ymv4" path="res://Scenes/Minimap.tscn" id="2_j32d5"]
[ext_resource type="PackedScene" uid="uid://ctxvyh1qr52ue" path="res://Scenes/PlayerAvatar.tscn" id="3_0f42i"]
[ext_resource type="PackedScene" uid="uid://do1sypgxbecbd" path="res://Scenes/ItemBoxes.tscn" id="4_swvig"]
[ext_resource type="PackedScene" uid="uid://b4l4phqqbvprm" path="res://Scenes/SkillBoxUI.tscn" id="5_dbohx"]
[ext_resource type="Script" uid="uid://f54r1khulcvh" path="res://Scripts/ModularLevelGenerator.gd" id="6_6ly8b"]
[ext_resource type="Resource" uid="uid://cvji54t3pjwfe" path="res://Resources/LevelConfigs/AllModulesConfig.tres" id="7_r5102"]
[ext_resource type="PackedScene" uid="uid://d2b5h1mlxxy7d" path="res://Scenes/FogOfWar.tscn" id="9_003du"]
[ext_resource type="PackedScene" uid="uid://b88l8pk1ebe1x" path="res://Scenes/player.tscn" id="11_82jtv"]
[ext_resource type="PackedScene" uid="uid://c3h8fj2xsp5oy" path="res://Scenes/Prefabs/Chest.tscn" id="14_1ks1q"]
[ext_resource type="PackedScene" uid="uid://dwusy8dd8usvo" path="res://Scenes/Prefabs/Wall.tscn" id="14_df8b0"]
[ext_resource type="PackedScene" uid="uid://crtnthqkksmri" path="res://Scenes/Prefabs/Tree.tscn" id="15_003du"]
[ext_resource type="Script" uid="uid://c3tr23vwvnmwf" path="res://Scripts/PatrolPoint.gd" id="15_6ly8b"]
[ext_resource type="Texture2D" uid="uid://c2ny0yi07rvcf" path="res://Environment/Floor/Floor01_Rocks_BaseColor.png" id="15_df8b0"]
[ext_resource type="Texture2D" uid="uid://dhfikoo16s5n0" path="res://Environment/Floor/Rocks_Metallic.png" id="16_003du"]
[ext_resource type="PackedScene" uid="uid://ddttv643pel23" path="res://Environment/Floor/Floor01_Custom.tscn" id="17_2bvpm"]
[ext_resource type="Resource" path="res://Resources/Items/Trap.tres" id="17_j32d5"]
[ext_resource type="Resource" path="res://Resources/Items/Torch.tres" id="18_j32d5"]
[ext_resource type="Script" uid="uid://pfva6p6rllgp" path="res://Scripts/Tree.gd" id="18_oh2bt"]
[ext_resource type="PackedScene" uid="uid://rvgn0irsuwao" path="res://Scenes/EnemyBoy01.tscn" id="19_0f42i"]
[ext_resource type="PackedScene" uid="uid://brxqv3iv3op6u" path="res://Scenes/ProgressBar3D.tscn" id="19_fcnrs"]
[ext_resource type="Script" uid="uid://dhw1dgex7wmiy" path="res://Scripts/ChestInteractable.gd" id="21_1ks1q"]
[ext_resource type="PackedScene" uid="uid://ervckea7fk57" path="res://Scenes/Enemy02.tscn" id="21_dbohx"]
[ext_resource type="PackedScene" uid="uid://b5dqjsb63wbhl" path="res://Scenes/Prefabs/Rock.tscn" id="21_xcdtp"]
[ext_resource type="PackedScene" uid="uid://bdq3b4e0mlgo4" path="res://Scenes/Prefabs/Barrel.tscn" id="22_cin4e"]
[ext_resource type="Resource" uid="uid://bjih53ivkm0qi" path="res://Resources/GasItem.tres" id="22_xcdtp"]
[ext_resource type="PackedScene" uid="uid://c6k7j3t3flhst" path="res://Scenes/Prefabs/Decoration.tscn" id="23_6j2fk"]
[ext_resource type="AudioStream" uid="uid://dgskkp7epyn0x" path="res://ChestOpening.mp3" id="23_cin4e"]
[ext_resource type="Script" uid="uid://de18ote8otj6u" path="res://Scripts/PatrolPointManager.gd" id="24_j2uky"]
[ext_resource type="Script" uid="uid://dt3st7cac7lok" path="res://Scripts/UI/UIManager.gd" id="25_ui_manager"]
[ext_resource type="Script" path="res://Scripts/CameraFollower.gd" id="26_camera_follower"]

[sub_resource type="ProceduralSkyMaterial" id="ProceduralSkyMaterial_gbvua"]
sky_top_color = Color(0.2, 0.4, 0.8, 1)
sky_horizon_color = Color(0.5, 0.7, 0.9, 1)
ground_bottom_color = Color(0.3, 0.5, 0.7, 1)
ground_horizon_color = Color(0.5, 0.7, 0.9, 1)

[sub_resource type="Sky" id="Sky_m0r78"]
sky_material = SubResource("ProceduralSkyMaterial_gbvua")

[sub_resource type="Environment" id="Environment_j4i7h"]
background_mode = 2
sky = SubResource("Sky_m0r78")
ambient_light_source = 3
ambient_light_color = Color(0.9, 0.9, 0.95, 1)
ambient_light_energy = 0.5
tonemap_mode = 2
ssao_enabled = true
ssao_radius = 2.0
ssao_intensity = 1.5
glow_enabled = true
glow_strength = 1.2

[sub_resource type="NavigationMesh" id="NavigationMesh_xcdtp"]
vertices = PackedVector3Array(46.464, 0.37304, -55.7666, 45.714, 0.37304, -54.7666, 45.714, 0.37304, -54.2666, 50.214, 0.37304, -54.2666, 49.714, 0.37304, -55.7666, 48.214, 0.37304, -56.2666, -27.036, 0.37304, -53.7666, -27.536, 0.37304, -53.5166, -25.786, 0.37304, -53.0166, -25.286, 0.37304, -53.7666, -27.036, 0.37304, -55.0166, -25.286, 0.37304, -56.0166, -22.536, 0.37304, -53.7666, -23.286, 0.37304, -55.5166, -5.78604, 0.37304, -55.2666, -6.28604, 0.37304, -55.7666, -8.03604, 0.37304, -54.7666, -8.28604, 0.37304, -53.5166, -5.78604, 0.37304, -53.2666, -3.53604, 0.37304, -53.7666, -4.28604, 0.37304, -55.2666, 16.714, 0.37304, -55.2666, 15.964, 0.37304, -54.0166, 16.214, 0.37304, -53.0166, 20.464, 0.37304, -53.5166, 19.964, 0.37304, -55.2666, 18.464, 0.37304, -55.7666, -16.286, 0.37304, -55.5166, -18.036, 0.37304, -54.5166, -18.286, 0.37304, -53.2666, -13.536, 0.37304, -53.2666, -14.286, 0.37304, -55.0166, -32.286, 0.37304, -53.7666, -32.536, 0.37304, -54.5166, -33.786, 0.37304, -54.7666, -36.036, 0.37304, -54.0166, -36.036, 0.37304, -53.2666, -44.286, 3.62304, -52.2666, -44.786, 3.62304, -53.0166, -46.286, 3.62304, -52.7666, -46.786, 3.62304, -48.0166, -45.036, 3.62304, -47.7666, -51.036, 3.62304, -52.2666, -52.536, 3.62304, -51.7666, -52.036, 3.62304, -48.7666, -52.286, 3.62304, -46.5166, -47.536, 3.62304, -46.0166, -47.536, 3.62304, -47.0166, -50.786, 3.62304, -53.7666, 8.46396, 3.62304, -47.5166, 10.964, 3.62304, -46.7666, 10.964, 3.62304, -47.5166, 11.714, 3.62304, -48.0166, 11.714, 3.62304, -52.0166, 10.464, 3.62304, -52.0166, 9.71396, 3.62304, -53.5166, 5.96396, 3.62304, -52.5166, 5.96396, 3.62304, -47.5166, 2.96396, 3.62304, -47.5166, 3.21396, 3.62304, -47.0166, 3.96396, 3.62304, -47.0166, 0.463959, 3.62304, -52.2666, -0.0360413, 3.62304, -53.2666, -0.536041, 3.62304, -53.2666, -7.53604, 3.62304, -47.5166, -7.03604, 3.62304, -47.0166, -3.78604, 3.62304, -47.5166, -5.28604, 3.62304, -52.0166, -7.53604, 3.62304, -52.0166, 25.214, 3.62304, -48.0166, 25.714, 3.62304, -47.2666, 27.964, 3.62304, -47.7666, 27.964, 3.62304, -52.2666, 19.964, 3.62304, -48.0166, 20.964, 3.62304, -47.2666, 23.714, 3.62304, -48.0166, 19.964, 3.62304, -52.0166, 26.464, 3.62304, -53.2666, 32.964, 3.62304, -48.5166, 33.464, 3.62304, -47.5166, 35.214, 3.62304, -47.7666, 34.214, 3.62304, -52.0166, 32.714, 3.62304, -53.2666, 31.714, 3.62304, -53.2666, 30.214, 3.62304, -48.5166, 35.714, 3.62304, -47.0166, 38.464, 3.62304, -47.7666, 38.714, 3.62304, -52.7666, 44.464, 3.62304, -46.0166, 45.214, 3.62304, -46.0166, 45.214, 3.62304, -47.0166, 47.214, 3.62304, -52.0166, 46.714, 3.62304, -53.0166, 44.964, 3.62304, -52.5166, 43.214, 3.62304, -52.5166, 42.714, 3.62304, -53.2666, 46.464, 3.62304, -47.5166, -37.286, 3.62304, -52.0166, -37.536, 3.62304, -52.5166, -38.786, 3.62304, -52.2666, -38.786, 3.62304, -47.7666, -27.286, 3.62304, -48.0166, -27.036, 3.62304, -51.7666, -28.536, 3.62304, -51.7666, -31.536, 3.62304, -48.0166, -29.036, 3.62304, -53.0166, -35.036, 3.62304, -52.0166, -33.286, 3.62304, -47.2666, 55.964, 3.62304, -50.0166, 55.464, 3.62304, -52.5166, 53.214, 3.62304, -52.2666, 52.214, 3.62304, -48.7666, 48.464, 3.62304, -52.0166, 47.214, 3.62304, -46.7666, 52.214, 3.62304, -45.7666, 52.964, 3.62304, -52.7666, 48.964, 3.62304, -53.0166, 47.464, 3.62304, -45.2666, -18.286, 3.62304, -46.7666, -17.286, 3.62304, -46.7666, -16.786, 3.62304, -47.7666, -19.286, 3.62304, -51.5166, -20.786, 3.62304, -47.5166, -12.786, 3.62304, -52.2666, -25.036, 3.62304, -51.7666, -23.036, 3.62304, -47.0166, -24.536, 3.62304, -52.5166, 17.714, 3.62304, -48.0166, 17.964, 3.62304, -47.5166, 15.714, 3.62304, -51.7666, 14.714, 3.62304, -52.5166, -40.536, 3.62304, -46.2666, -39.786, 3.62304, -46.2666, -39.786, 3.62304, -47.0166, 18.964, 0.37304, -17.2666, 18.464, 0.37304, -17.0166, 18.214, 0.37304, -15.5166, 22.214, 0.37304, -12.5166, 22.964, 0.37304, -12.5166, 34.714, 0.87304, -45.7666, 34.464, 0.87304, -46.2666, 32.714, 0.37304, -46.0166, 46.714, 0.37304, -42.5166, 46.214, 0.37304, -44.7666, 44.964, 0.37304, -44.5166, 46.214, 0.37304, -33.2666, 46.714, 0.37304, -35.7666, 17.464, 0.37304, -14.7666, 15.964, 0.87304, -14.7666, 15.964, 0.37304, -12.5166, 17.214, 0.37304, -12.0166, 38.964, 0.37304, -46.5166, 36.464, 0.62304, -45.7666, 45.964, 0.37304, -30.5166, 46.464, 0.37304, -30.7666, 35.5793, 0.37304, -44.5936, 19.964, 0.62304, -20.0166, 18.714, 0.37304, -18.7666, 24.464, 0.37304, -11.0166, 17.214, 0.37304, -11.0166, 20.714, 0.37304, -10.7666, 24.964, 0.37304, -45.7666, 33.3211, 0.37304, -45.7666, 45.964, 0.37304, -21.5166, 46.714, 0.37304, -21.7666, 45.964, 0.37304, -25.7666, 45.714, 0.37304, -13.0166, 30.7161, 0.87304, -22.5189, 30.7059, 0.37304, -23.9947, 32.2011, 0.37304, -22.5079, 29.1912, 0.37304, -21.0041, 45.964, 0.37304, -19.0166, 46.964, 0.37304, -13.5166, 24.464, 0.37304, -10.2666, 45.714, 0.37304, -10.2666, 41.464, 0.37304, -10.2666, 42.8806, 0.87304, -10.2666, 32.214, 0.37304, -47.2666, -46.036, 0.37304, -37.7666, -46.286, 0.37304, -35.7666, -45.786, 0.37304, -35.5166, -46.286, 0.37304, -42.0166, -46.786, 0.37304, -41.7666, -46.786, 0.37304, -38.2666, -17.286, 0.37304, -28.0166, -16.286, 0.37304, -29.0166, -16.536, 0.37304, -45.2666, -17.036, 0.37304, -33.7666, -17.0985, 0.87304, -32.3291, -17.161, 0.37304, -30.8916, -20.036, 0.37304, -46.0166, -22.286, 0.37304, -45.5166, -27.036, 0.37304, -46.7666, -31.036, 0.37304, -46.7666, -32.786, 0.37304, -46.0166, -46.286, 0.37304, -30.2666, -45.036, 0.37304, -29.7666, -38.536, 0.37304, -46.5166, -39.036, 0.37304, -44.7666, -40.286, 0.37304, -44.7666, -46.286, 0.37304, -46.5166, -45.036, 0.37304, -28.5166, 16.964, 0.37304, -20.0166, 17.214, 0.37304, -19.0166, 18.464, 0.37304, -19.2666, 13.964, 0.87304, -19.2666, 14.964, 0.62304, -19.0166, 15.964, 0.37304, -20.0166, 3.96396, 0.37304, -30.0166, 12.964, 0.37304, -20.3416, 20.464, 0.37304, -45.7666, 19.714, 0.37304, -46.5166, 16.214, 0.37304, -46.2666, 11.714, 0.37304, -45.5166, 12.214, 0.37304, -46.7666, 5.21396, 0.37304, -46.2666, 3.21396, 0.37304, -45.5166, 3.21396, 0.37304, -32.5166, 3.96396, 0.37304, -32.2666, 18.7001, 0.87304, -25.5369, 18.7251, 0.37304, -26.9918, 18.6902, 0.37304, -23.9929, 15.7301, 0.37304, -25.5088, 24.214, 0.37304, -46.7666, 21.6891, 0.87304, -34.5318, 21.7041, 0.37304, -35.9927, -52.286, 3.62304, -42.7666, -51.786, 3.62304, -42.5166, -52.286, 3.62304, -39.2666, -52.036, 3.62304, -37.7666, -48.036, 3.62304, -37.7666, -0.0360413, 0.37304, -29.5166, 0.713959, 0.37304, -29.7666, 0.713959, 0.37304, -32.0166, -9.53604, 0.37304, -46.2666, -16.036, 0.37304, -46.5166, -16.036, 0.37304, -45.5166, -3.53604, 0.37304, -45.7666, -13.036, 0.37304, -29.0166, -12.286, 0.37304, -28.2666, -3.76596, 0.87304, -43.5147, -3.78586, 0.37304, -42.0268, -5.30097, 0.37304, -43.5257, -0.0360413, 0.37304, -46.2666, -3.03604, 0.37304, -46.2666, 0.963959, 0.37304, -32.5166, 47.714, 3.62304, -28.5166, 51.714, 3.62304, -28.5166, 51.714, 3.62304, -30.2666, 47.964, 3.62304, -43.0166, 52.214, 3.62304, -42.7666, 52.464, 3.62304, -30.7666, 52.714, 3.62304, -42.5166, 47.714, 3.62304, -35.2666, 54.214, 0.37304, -43.2666, 53.464, 0.37304, -43.7666, 54.214, 0.37304, -43.0166, 56.214, 0.37304, -41.2666, 56.714, 0.37304, -41.5166, 56.714, 0.37304, -43.5166, 53.714, 0.37304, -37.2666, 55.714, 0.37304, -37.2666, -47.536, 3.62304, -33.5166, -47.036, 3.62304, -35.0166, -47.536, 3.62304, -35.2666, -47.286, 3.62304, -37.0166, -52.286, 3.62304, -30.7666, -51.536, 3.62304, -29.7666, -47.536, 3.62304, -29.7666, 54.964, 0.37304, -31.5166, 53.714, 0.37304, -32.5166, 53.714, 0.37304, -29.7666, 56.964, 0.37304, -28.7666, 57.464, 0.37304, -29.5166, 57.464, 0.37304, -31.5166, 52.964, 0.37304, -29.5166, 53.714, 0.37304, -25.0166, 56.714, 0.37304, -25.5166, 1.96396, 2.62304, -31.2666, 1.96396, 2.62304, -30.5166, 2.71396, 2.62304, -30.5166, 2.71396, 2.62304, -31.2666, 0.963959, 0.37304, -29.2666, -0.536041, 0.37304, -15.0166, 12.464, 0.37304, -16.5166, 13.464, 0.62304, -17.2666, 13.214, 0.37304, -18.2666, 1.71396, 0.62304, -13.7666, 12.964, 0.37304, -14.7666, 12.464, 0.37304, -15.0166, 3.46396, 0.37304, -29.2666, 12.914, 0.37304, -20.2666, -47.286, 3.62304, -26.2666, -46.286, 3.62304, -29.0166, -47.036, 3.62304, -29.0166, -52.036, 3.62304, -28.2666, -51.786, 3.62304, -26.0166, -52.536, 3.62304, -20.2666, -53.036, 3.62304, -20.0166, -52.786, 3.62304, -16.7666, -47.786, 3.62304, -16.0166, -48.036, 3.62304, -20.2666, -52.786, 3.62304, -23.7666, -47.036, 3.62304, -24.5166, -47.286, 3.62304, -12.0166, -46.786, 3.62304, -15.2666, -51.536, 3.62304, -11.7666, -11.286, 0.62304, -17.2666, -11.036, 0.62304, -16.7666, -9.28604, 0.37304, -16.5166, -4.53604, 0.37304, -11.2666, -3.78604, 0.37304, -12.0166, -3.78604, 0.37304, -14.0166, -9.03604, 0.37304, -14.7666, -10.536, 0.37304, -12.2666, -13.286, 0.37304, -24.2666, -13.036, 0.37304, -17.0166, -12.036, 0.37304, -25.0166, -3.28604, 0.37304, -14.5166, -2.53604, 0.37304, -15.0166, -45.786, 0.37304, -26.5166, -45.536, 0.37304, -24.7666, -17.036, 0.37304, -24.7666, -36.986, 0.37304, -24.7666, -35.561, 0.87304, -24.7666, -34.136, 0.37304, -24.7666, 47.964, 3.62304, -21.0166, 47.214, 3.62304, -20.5166, 47.714, 3.62304, -17.7666, 52.214, 3.62304, -17.7666, 52.964, 3.62304, -21.7666, 52.464, 3.62304, -22.0166, 47.714, 3.62304, -27.7666, 51.964, 3.62304, -26.2666, 47.964, 3.62304, -23.0166, 52.464, 3.62304, -26.0166, 47.214, 3.62304, -27.5166, -15.786, 3.62304, -25.7666, -13.536, 3.62304, -25.7666, -13.786, 3.37304, -27.7666, -16.036, 3.62304, -27.5166, -14.286, 0.37304, -16.7666, -13.286, 0.62304, -16.5166, -15.536, 0.37304, -14.0166, -14.786, 0.37304, -14.2666, -14.786, 0.37304, -16.2666, -46.786, 0.37304, -19.7666, -46.536, 0.37304, -16.5166, -45.286, 0.37304, -16.0166, -38.271, 0.37304, -22.4977, -16.286, 0.37304, -24.2666, -45.536, 0.37304, -14.2666, 14.964, 5.12304, -17.7666, 14.964, 5.12304, -16.5166, 16.214, 5.12304, -16.5166, 16.214, 5.12304, -17.7666, 48.214, 3.62304, -12.7666, 47.214, 3.62304, -12.2666, 47.464, 3.62304, -9.76663, 51.714, 3.62304, -9.76663, 51.964, 3.62304, -12.5166, 52.464, 3.62304, -12.7666, -13.286, 5.12304, -15.0166, -13.286, 5.12304, -13.7666, -12.036, 5.12304, -13.7666, -12.036, 5.12304, -15.0166, -5.53604, 0.37304, 45.9834, -5.53604, 0.37304, 47.2334, -4.78604, 0.37304, 46.7334, -15.036, 0.37304, -12.2666, -15.286, 0.37304, -11.0166, -14.786, 0.37304, -10.7666, -16.036, 0.37304, 0.233368, -15.536, 0.37304, 0.983368, -13.786, 0.37304, 1.23337, -14.786, 0.37304, -9.01663, -16.036, 0.37304, -8.76663, 14.964, 0.37304, -12.0166, -6.03604, 0.37304, 45.4834, 6.96396, 0.37304, 45.9834, 15.464, 0.37304, 45.7334, 14.964, 0.37304, -10.7666, 1.96396, 0.62304, -12.2666, 15.464, 0.37304, -10.2666, 0.463959, 0.37304, -9.76663, -18.036, 0.37304, 5.48337, -18.036, 0.37304, 9.73337, -17.036, 0.37304, 9.98337, -15.036, 0.37304, 5.48337, -16.786, 0.37304, 46.7334, -14.536, 0.37304, 46.9834, -17.036, 0.37304, 11.4834, -16.9631, 0.37304, 21.7646, -16.9527, 0.87304, 23.2334, -16.9319, 0.37304, 26.1709, -8.31044, 0.87304, 42.0077, -9.79557, 0.37304, 38.9869, -9.77063, 0.87304, 37.4819, -11.2857, 0.37304, 34.483, -15.786, 0.37304, 25.4633, -9.78538, 0.37304, 43.4627, -11.2758, 0.37304, 32.9891, -4.53604, 0.37304, -9.76663, 1.46396, 0.37304, 47.2334, 6.46396, 0.37304, 46.9834, -13.536, 0.37304, 2.98337, 11.1985, 0.87304, 22.5066, 11.2182, 0.37304, 20.9588, 9.71344, 0.37304, 22.4956, 11.2287, 0.37304, 24.0043, 11.964, 0.37304, 46.7334, -15.786, 0.37304, -12.5166, -16.286, 0.37304, -11.0166, -45.536, 0.62304, -11.5166, -2.03604, 5.12304, -12.7666, -2.03604, 5.12304, -11.5166, -0.786041, 5.12304, -11.5166, -0.786041, 5.12304, -12.7666, 54.714, 0.37304, -12.0166, 54.214, 0.37304, -12.7666, 52.964, 0.37304, -10.7666, 54.464, 0.37304, -7.51663, 54.464, 0.37304, -6.01663, 56.464, 0.37304, -6.01663, 56.714, 0.37304, -9.26663, 57.464, 0.37304, -10.2666, 57.214, 0.37304, -12.0166, 53.464, 0.37304, -7.76663, -47.036, 3.62304, -6.01663, -47.036, 3.62304, -8.01663, -47.536, 3.62304, -8.26663, -47.036, 3.62304, -10.5166, -51.536, 3.62304, -11.0166, -52.036, 3.62304, -3.76663, -52.286, 3.62304, -0.766632, -47.786, 3.62304, -0.766632, -52.536, 3.62304, -10.5166, -46.036, 0.37304, -10.0166, -45.536, 0.37304, -8.51663, -16.786, 0.37304, -9.01663, -17.036, 0.37304, -10.5166, 42.464, 0.62304, 28.9834, 42.964, 0.37304, 29.7334, 43.464, 0.87304, 28.9834, 18.214, 0.37304, 45.2334, 18.714, 0.37304, 46.9834, 19.464, 0.37304, 45.9834, 46.714, 0.37304, 41.9834, 46.714, 0.37304, 40.4834, 46.214, 0.37304, 39.9834, 45.214, 0.37304, 42.4834, 43.214, 0.62304, 30.9834, 42.714, 0.37304, 29.9834, 41.214, 0.37304, 30.2334, 25.214, 0.37304, 46.9834, 20.714, 0.37304, -10.0166, 16.714, 0.37304, -10.2666, 45.964, 0.37304, 15.4834, 46.714, 0.37304, 15.2334, 46.714, 0.37304, 4.48337, 43.464, 0.37304, 46.2334, 46.214, 0.37304, 46.4834, 39.214, 0.37304, 45.7334, 39.464, 0.37304, 46.9834, 40.964, 0.37304, 29.2334, 46.964, 0.37304, 27.7334, 47.214, 0.37304, 21.9834, 45.964, 0.37304, 17.2334, 41.714, 0.87304, 29.1084, 45.464, 0.37304, 28.4834, 44.214, 0.62304, 28.9834, 44.714, 0.37304, 30.4834, 46.714, 0.37304, 32.7334, 37.214, 0.37304, 45.7334, 46.214, 0.37304, 33.4834, 45.464, 0.37304, -3.76663, 23.464, 0.62304, -9.26663, 21.964, 0.37304, -8.76663, 38.9984, 0.37304, 25.3023, 39.6536, 0.87304, 26.6127, 41.221, 0.37304, 24.0042, 18.7348, 0.87304, 8.99451, 18.6999, 0.37304, 10.4934, 17.1908, 0.87304, 21.0064, 33.7264, 0.37304, 25.4668, 17.1959, 0.37304, 22.4993, 17.2206, 0.37304, 19.4746, 18.7047, 0.37304, 7.50661, 24.705, 0.87304, 11.9862, 18.7056, 0.37304, 19.4856, 21.7199, 0.37304, 10.4654, 24.6899, 0.37304, 10.4873, 26.1902, 0.37304, 13.5071, 23.2201, 0.37304, 13.4851, 30.464, 0.37304, 46.9834, 22.214, 2.12304, -10.7666, 22.214, 2.12304, -10.2666, 22.964, 2.37304, -10.5166, 22.714, 2.37304, -11.0166, 45.714, 0.37304, -4.26663, 46.714, 0.37304, -4.26663, 44.1891, 0.37304, -9.03176, 53.214, 3.62304, -4.76663, 53.214, 3.62304, -6.51663, 52.214, 3.62304, -7.26663, 48.214, 3.62304, -4.76663, 46.964, 3.62304, -9.01663, -20.286, 0.37304, 3.73337, -19.286, 0.62304, 2.98337, -19.286, 0.37304, 1.23337, -17.786, 0.62304, 1.23337, -18.536, 0.87304, 1.23337, -46.036, 0.37304, -1.76663, -46.786, 0.37304, 1.23337, -46.536, 0.37304, 4.73337, -20.286, 0.37304, 4.73337, -39.7956, 0.87304, -4.51313, -38.3106, 0.37304, -4.50215, -41.2806, 0.37304, -4.52412, 47.964, 3.62304, -3.51663, 52.464, 3.62304, -1.01663, 46.964, 3.62304, -3.01663, 47.714, 3.62304, 2.98337, 52.464, 3.62304, 2.73337, -53.536, 0.37304, -1.01663, -53.786, 0.37304, -2.76663, -54.536, 0.37304, -2.26663, -54.286, 0.37304, -1.01663, -55.536, 0.37304, 4.73337, -54.036, 0.37304, 5.23337, -54.286, 0.37304, 0.733368, -55.536, 0.37304, -0.266632, -53.786, 0.37304, 0.483368, -52.036, 3.62304, 0.983368, -52.786, 3.62304, 1.48337, -52.536, 3.62304, 2.98337, -47.536, 3.62304, 10.9834, -47.036, 3.62304, 8.48337, -47.536, 3.62304, 8.23337, -53.036, 3.62304, 3.73337, -52.286, 3.62304, 5.73337, -48.036, 3.62304, 3.48337, -52.036, 3.62304, 10.9834, -17.786, 5.12304, 2.48337, -17.786, 5.12304, 3.73337, -16.536, 5.12304, 3.73337, -16.536, 5.12304, 2.48337, 52.714, 3.62304, 9.23337, 52.714, 3.62304, 8.48337, 52.214, 3.62304, 8.23337, 48.714, 3.62304, 14.4834, 48.714, 3.62304, 15.2334, 51.714, 3.62304, 15.2334, 47.964, 3.62304, 13.9834, 52.714, 3.62304, 4.98337, -20.036, 0.37304, 12.9834, -18.536, 0.37304, 11.9834, -19.036, 0.37304, 11.7334, -46.286, 0.37304, 7.48337, -45.786, 0.37304, 7.73337, -19.036, 0.37304, 9.98337, -20.036, 0.37304, 5.48337, -45.286, 0.37304, 13.4834, -45.036, 0.37304, 13.9834, -46.286, 0.37304, 13.4834, 55.214, 0.37304, 7.73337, 55.714, 0.37304, 7.48337, 55.714, 0.37304, 5.73337, 53.714, 0.37304, 5.98337, 53.714, 0.37304, 11.9834, 54.714, 0.37304, 11.9834, -48.036, 3.62304, 13.9834, -47.536, 3.62304, 19.7334, -46.536, 3.62304, 14.7334, -52.036, 3.62304, 19.7334, -17.286, 0.37304, 11.9834, -20.786, 0.62304, 46.2334, -19.536, 0.37304, 46.7334, -17.0985, 0.37304, 25.0146, -17.1194, 0.87304, 23.5667, -17.1402, 0.37304, 22.1188, -43.036, 0.37304, 46.9834, -39.786, 0.37304, 46.9834, -39.536, 0.37304, 46.2334, -43.286, 0.37304, 45.9834, -45.536, 0.37304, 27.2334, -45.536, 0.37304, 28.4834, -44.786, 0.62304, 39.2334, -29.536, 0.37304, 46.9834, -23.286, 0.37304, 45.7334, -44.786, 0.37304, 40.7334, -46.286, 0.37304, 46.2334, -46.786, 0.37304, 33.4834, -46.786, 0.37304, 38.4834, -46.286, 0.37304, 19.7334, -45.786, 0.37304, 20.2334, -46.286, 0.37304, 26.7334, -34.536, 0.37304, 46.9834, 53.214, 0.37304, 14.4834, 52.964, 0.37304, 15.4834, 53.464, 0.37304, 16.2334, 54.714, 0.37304, 16.4834, 55.214, 0.37304, 16.2334, 54.964, 0.37304, 14.4834, 53.214, 0.37304, 18.9834, 53.714, 0.37304, 20.4834, 54.214, 0.37304, 20.4834, 47.964, 3.62304, 16.4834, 51.964, 3.62304, 19.4834, 51.964, 3.62304, 25.9834, 52.964, 3.62304, 21.7334, 52.464, 3.62304, 21.4834, 48.464, 3.62304, 21.4834, 47.964, 3.62304, 28.7334, 51.964, 3.62304, 28.7334, 47.214, 3.62304, 16.4834, -52.536, 3.62304, 21.2334, -52.536, 3.62304, 22.2334, -52.036, 3.62304, 22.4834, -47.036, 3.62304, 21.7334, -51.786, 3.62304, 26.9834, -47.786, 3.62304, 27.2334, -51.536, 3.62304, 28.2334, -52.286, 3.62304, 26.7334, -51.286, 3.62304, 32.2334, -51.786, 3.62304, 32.4834, -52.036, 3.62304, 34.4834, -53.286, 3.62304, 35.2334, -52.786, 3.62304, 36.9834, -53.286, 3.62304, 38.4834, -52.536, 3.62304, 40.4834, -47.036, 3.62304, 27.9834, -48.036, 3.62304, 32.9834, -47.536, 3.62304, 39.7334, -48.036, 3.62304, 39.2334, -53.536, 0.37304, 30.2334, -53.536, 0.37304, 31.9834, -52.536, 0.37304, 31.4834, -53.036, 0.37304, 28.4834, 52.714, 3.62304, 32.4834, 53.214, 3.62304, 29.4834, 46.964, 3.62304, 29.2334, 48.214, 3.62304, 33.2334, 47.464, 3.62304, 35.9834, 47.714, 3.62304, 39.4834, 51.964, 3.62304, 39.4834, 53.464, 3.62304, 32.9834, 52.214, 3.62304, 42.2334, 52.714, 3.62304, 40.7334, 51.964, 3.62304, 40.2334, 47.964, 3.62304, 42.4834, 51.964, 3.62304, 45.4834, 52.464, 3.62304, 45.2334, 46.714, 3.62304, 43.2334, 47.464, 3.62304, 46.2334, 51.964, 3.62304, 46.2334, -52.286, 3.62304, 45.4834, -52.786, 3.62304, 46.7334, -52.286, 3.62304, 46.9834, -47.786, 3.62304, 46.9834, -46.036, 3.62304, 40.2334, 46.214, 3.62304, 47.7334, 46.214, 3.62304, 51.7334, 46.964, 3.62304, 51.7334, 47.464, 3.62304, 46.9834, 52.714, 3.62304, 47.7334, 47.464, 3.62304, 52.4834, 55.964, 3.62304, 52.2334, 55.964, 3.62304, 47.4834, 10.214, 3.62304, 52.4834, 10.714, 3.62304, 52.9834, 11.964, 3.62304, 52.7334, 12.464, 3.62304, 47.9834, 10.714, 3.62304, 47.9834, 15.714, 3.62304, 53.4834, 15.714, 3.62304, 52.2334, 16.214, 3.62304, 51.7334, 18.714, 3.62304, 51.9834, 18.714, 3.62304, 48.2334, 17.464, 3.62304, 47.4834, 17.464, 3.62304, 46.4834, -43.536, 3.62304, 52.2334, -43.036, 3.62304, 51.7334, -43.036, 3.62304, 48.2334, -47.286, 3.62304, 47.7334, -52.036, 3.62304, 47.9834, -49.786, 3.62304, 51.9834, -44.036, 3.62304, 47.4834, -52.786, 3.62304, 48.4834, -52.286, 3.62304, 52.4834, -5.53604, 3.62304, 51.9834, -5.53604, 3.62304, 48.4834, -6.78604, 3.62304, 47.7334, -9.78604, 3.62304, 51.7334, -6.78604, 3.62304, 46.9834, -15.036, 3.62304, 48.2334, -15.036, 3.62304, 52.7334, 29.714, 3.62304, 48.2334, 29.464, 3.62304, 51.9834, 30.214, 3.62304, 52.7334, 35.714, 3.62304, 51.9834, 38.464, 3.62304, 47.9834, 38.214, 3.62304, 46.9834, 39.464, 3.62304, 52.4834, 39.464, 3.62304, 48.4834, 35.464, 3.62304, 52.4834, -16.786, 3.62304, 53.4834, -15.786, 3.62304, 53.4834, -22.786, 3.62304, 47.9834, -23.286, 3.62304, 47.2334, -25.286, 3.62304, 47.7334, -25.286, 3.62304, 51.7334, -20.036, 3.62304, 47.9834, -21.036, 3.62304, 47.4834, -24.786, 3.62304, 52.2334, -20.036, 3.62304, 52.2334, -16.786, 3.62304, 48.4834, -17.036, 3.62304, 47.9834, -33.036, 3.62304, 52.4834, -30.036, 3.62304, 52.9834, -29.286, 3.62304, 52.2334, -29.036, 3.62304, 48.2334, -33.036, 3.62304, 48.4834, -26.536, 3.62304, 47.4834, 7.71396, 3.62304, 47.4834, 7.21396, 3.62304, 48.2334, 6.46396, 3.62304, 51.7334, 1.46396, 3.62304, 48.4834, 1.71396, 3.62304, 51.9834, 26.464, 3.62304, 53.2334, 28.214, 3.62304, 53.2334, 28.214, 3.62304, 52.4834, 19.714, 3.62304, 48.2334, 20.214, 3.62304, 47.4834, 24.714, 3.62304, 48.2334, -40.286, 3.62304, 51.7334, -34.286, 3.62304, 53.2334, -36.286, 3.62304, 47.7334, 42.964, 3.62304, 52.2334, 44.214, 3.62304, 52.4834, 42.464, 3.62304, 52.9834, 42.714, 3.62304, 47.7334, -0.0360413, 3.62304, 52.2334, 1.21396, 3.62304, 52.7334, -4.53604, 3.62304, 48.4834, -4.28604, 3.62304, 47.9834)
polygons = [PackedInt32Array(1, 0, 2), PackedInt32Array(2, 0, 5), PackedInt32Array(2, 5, 4), PackedInt32Array(2, 4, 3), PackedInt32Array(8, 7, 6), PackedInt32Array(8, 6, 9), PackedInt32Array(9, 6, 10), PackedInt32Array(10, 11, 9), PackedInt32Array(9, 11, 13), PackedInt32Array(9, 13, 12), PackedInt32Array(15, 14, 16), PackedInt32Array(16, 14, 17), PackedInt32Array(17, 14, 18), PackedInt32Array(20, 19, 14), PackedInt32Array(14, 19, 18), PackedInt32Array(22, 21, 23), PackedInt32Array(23, 21, 26), PackedInt32Array(23, 26, 25), PackedInt32Array(23, 25, 24), PackedInt32Array(28, 27, 29), PackedInt32Array(29, 27, 31), PackedInt32Array(29, 31, 30), PackedInt32Array(33, 32, 34), PackedInt32Array(34, 32, 35), PackedInt32Array(35, 32, 36), PackedInt32Array(38, 37, 39), PackedInt32Array(39, 37, 40), PackedInt32Array(40, 37, 41), PackedInt32Array(44, 43, 42), PackedInt32Array(47, 46, 45), PackedInt32Array(42, 48, 39), PackedInt32Array(47, 45, 40), PackedInt32Array(40, 45, 44), PackedInt32Array(40, 44, 42), PackedInt32Array(40, 42, 39), PackedInt32Array(51, 50, 49), PackedInt32Array(52, 51, 49), PackedInt32Array(54, 53, 52), PackedInt32Array(55, 54, 56), PackedInt32Array(56, 54, 57), PackedInt32Array(57, 54, 49), PackedInt32Array(49, 54, 52), PackedInt32Array(60, 59, 58), PackedInt32Array(63, 62, 61), PackedInt32Array(60, 58, 57), PackedInt32Array(57, 58, 56), PackedInt32Array(56, 58, 61), PackedInt32Array(65, 64, 66), PackedInt32Array(66, 64, 67), PackedInt32Array(67, 64, 68), PackedInt32Array(63, 61, 67), PackedInt32Array(67, 61, 66), PackedInt32Array(66, 61, 58), PackedInt32Array(70, 69, 71), PackedInt32Array(71, 69, 72), PackedInt32Array(74, 73, 75), PackedInt32Array(75, 73, 76), PackedInt32Array(72, 69, 77), PackedInt32Array(77, 69, 75), PackedInt32Array(77, 75, 76), PackedInt32Array(80, 79, 78), PackedInt32Array(82, 81, 83), PackedInt32Array(83, 81, 78), PackedInt32Array(83, 78, 84), PackedInt32Array(83, 84, 72), PackedInt32Array(86, 85, 80), PackedInt32Array(84, 71, 72), PackedInt32Array(78, 81, 80), PackedInt32Array(80, 81, 86), PackedInt32Array(86, 81, 87), PackedInt32Array(90, 89, 88), PackedInt32Array(93, 92, 91), PackedInt32Array(94, 93, 91), PackedInt32Array(94, 87, 95), PackedInt32Array(90, 91, 96), PackedInt32Array(90, 88, 91), PackedInt32Array(91, 88, 94), PackedInt32Array(94, 88, 86), PackedInt32Array(94, 86, 87), PackedInt32Array(98, 97, 99), PackedInt32Array(99, 97, 100), PackedInt32Array(102, 101, 103), PackedInt32Array(103, 101, 104), PackedInt32Array(103, 104, 105), PackedInt32Array(105, 104, 107), PackedInt32Array(105, 107, 106), PackedInt32Array(97, 106, 100), PackedInt32Array(100, 106, 107), PackedInt32Array(109, 108, 110), PackedInt32Array(110, 108, 111), PackedInt32Array(91, 112, 96), PackedInt32Array(96, 112, 113), PackedInt32Array(113, 112, 111), PackedInt32Array(113, 111, 114), PackedInt32Array(110, 111, 115), PackedInt32Array(115, 111, 116), PackedInt32Array(116, 111, 112), PackedInt32Array(113, 114, 117), PackedInt32Array(119, 118, 120), PackedInt32Array(120, 118, 122), PackedInt32Array(120, 122, 121), PackedInt32Array(68, 64, 123), PackedInt32Array(123, 64, 120), PackedInt32Array(123, 120, 121), PackedInt32Array(101, 102, 124), PackedInt32Array(124, 126, 101), PackedInt32Array(101, 126, 125), PackedInt32Array(125, 126, 122), PackedInt32Array(122, 126, 121), PackedInt32Array(127, 73, 128), PackedInt32Array(130, 129, 53), PackedInt32Array(53, 129, 52), PackedInt32Array(52, 129, 127), PackedInt32Array(73, 127, 76), PackedInt32Array(76, 127, 129), PackedInt32Array(132, 131, 133), PackedInt32Array(133, 131, 41), PackedInt32Array(100, 133, 99), PackedInt32Array(99, 133, 37), PackedInt32Array(37, 133, 41), PackedInt32Array(135, 134, 136), PackedInt32Array(136, 134, 137), PackedInt32Array(137, 134, 138), PackedInt32Array(139, 141, 140), PackedInt32Array(143, 142, 144), PackedInt32Array(144, 142, 146), PackedInt32Array(144, 146, 145), PackedInt32Array(148, 147, 149), PackedInt32Array(149, 147, 150), PackedInt32Array(152, 151, 144), PackedInt32Array(139, 152, 155), PackedInt32Array(155, 152, 144), PackedInt32Array(155, 144, 145), PackedInt32Array(155, 145, 154), PackedInt32Array(155, 154, 153), PackedInt32Array(157, 156, 134), PackedInt32Array(134, 156, 138), PackedInt32Array(138, 156, 158), PackedInt32Array(147, 136, 150), PackedInt32Array(150, 136, 159), PackedInt32Array(159, 136, 160), PackedInt32Array(160, 136, 137), PackedInt32Array(139, 162, 141), PackedInt32Array(141, 162, 161), PackedInt32Array(165, 164, 163), PackedInt32Array(158, 170, 166), PackedInt32Array(166, 169, 153), PackedInt32Array(153, 168, 155), PackedInt32Array(155, 162, 139), PackedInt32Array(162, 168, 161), PackedInt32Array(161, 168, 156), PackedInt32Array(156, 170, 158), PackedInt32Array(170, 169, 166), PackedInt32Array(169, 168, 153), PackedInt32Array(168, 162, 155), PackedInt32Array(168, 170, 156), PackedInt32Array(170, 167, 169), PackedInt32Array(169, 167, 168), PackedInt32Array(168, 167, 170), PackedInt32Array(165, 163, 166), PackedInt32Array(163, 171, 166), PackedInt32Array(171, 172, 166), PackedInt32Array(174, 176, 166), PackedInt32Array(166, 176, 175), PackedInt32Array(166, 175, 158), PackedInt32Array(158, 175, 173), PackedInt32Array(161, 177, 141), PackedInt32Array(153, 165, 166), PackedInt32Array(180, 179, 178), PackedInt32Array(182, 181, 183), PackedInt32Array(183, 181, 178), PackedInt32Array(184, 189, 185), PackedInt32Array(185, 189, 188), PackedInt32Array(185, 188, 187), PackedInt32Array(185, 187, 186), PackedInt32Array(191, 190, 186), PackedInt32Array(192, 194, 193), PackedInt32Array(196, 195, 180), PackedInt32Array(198, 197, 194), PackedInt32Array(178, 181, 180), PackedInt32Array(180, 181, 199), PackedInt32Array(180, 199, 198), PackedInt32Array(180, 198, 196), PackedInt32Array(181, 200, 199), PackedInt32Array(186, 187, 191), PackedInt32Array(191, 187, 188), PackedInt32Array(191, 188, 189), PackedInt32Array(191, 189, 184), PackedInt32Array(191, 184, 198), PackedInt32Array(198, 184, 196), PackedInt32Array(196, 184, 201), PackedInt32Array(192, 191, 194), PackedInt32Array(194, 191, 198), PackedInt32Array(203, 202, 204), PackedInt32Array(204, 202, 156), PackedInt32Array(204, 156, 157), PackedInt32Array(206, 205, 207), PackedInt32Array(207, 205, 209), PackedInt32Array(207, 209, 208), PackedInt32Array(202, 207, 208), PackedInt32Array(211, 210, 212), PackedInt32Array(212, 210, 213), PackedInt32Array(213, 214, 212), PackedInt32Array(215, 213, 216), PackedInt32Array(216, 213, 217), PackedInt32Array(217, 213, 218), PackedInt32Array(156, 220, 210), PackedInt32Array(210, 220, 213), PackedInt32Array(213, 220, 218), PackedInt32Array(218, 222, 208), PackedInt32Array(208, 222, 202), PackedInt32Array(202, 221, 156), PackedInt32Array(156, 219, 220), PackedInt32Array(220, 222, 218), PackedInt32Array(222, 221, 202), PackedInt32Array(221, 219, 156), PackedInt32Array(219, 222, 220), PackedInt32Array(222, 219, 221), PackedInt32Array(161, 210, 223), PackedInt32Array(210, 225, 156), PackedInt32Array(156, 224, 161), PackedInt32Array(161, 225, 210), PackedInt32Array(225, 224, 156), PackedInt32Array(224, 225, 161), PackedInt32Array(226, 45, 227), PackedInt32Array(227, 45, 46), PackedInt32Array(228, 227, 229), PackedInt32Array(229, 227, 230), PackedInt32Array(230, 227, 46), PackedInt32Array(233, 232, 231), PackedInt32Array(236, 235, 234), PackedInt32Array(236, 234, 186), PackedInt32Array(186, 234, 237), PackedInt32Array(231, 239, 238), PackedInt32Array(186, 238, 185), PackedInt32Array(238, 233, 231), PackedInt32Array(233, 240, 237), PackedInt32Array(237, 242, 186), PackedInt32Array(186, 242, 238), PackedInt32Array(238, 241, 233), PackedInt32Array(240, 242, 237), PackedInt32Array(233, 241, 240), PackedInt32Array(242, 241, 238), PackedInt32Array(240, 241, 242), PackedInt32Array(244, 243, 237), PackedInt32Array(237, 243, 216), PackedInt32Array(237, 216, 245), PackedInt32Array(237, 245, 233), PackedInt32Array(216, 217, 245), PackedInt32Array(248, 247, 246), PackedInt32Array(249, 117, 250), PackedInt32Array(250, 117, 114), PackedInt32Array(248, 246, 251), PackedInt32Array(251, 246, 253), PackedInt32Array(251, 253, 252), PackedInt32Array(252, 253, 249), PackedInt32Array(249, 250, 252), PackedInt32Array(256, 255, 254), PackedInt32Array(258, 257, 259), PackedInt32Array(259, 257, 256), PackedInt32Array(259, 256, 254), PackedInt32Array(261, 260, 257), PackedInt32Array(257, 260, 256), PackedInt32Array(264, 263, 262), PackedInt32Array(230, 265, 264), PackedInt32Array(230, 264, 262), PackedInt32Array(267, 266, 268), PackedInt32Array(268, 266, 262), PackedInt32Array(262, 266, 230), PackedInt32Array(230, 266, 229), PackedInt32Array(271, 270, 269), PackedInt32Array(273, 272, 274), PackedInt32Array(274, 272, 269), PackedInt32Array(269, 272, 271), PackedInt32Array(271, 272, 275), PackedInt32Array(275, 272, 277), PackedInt32Array(275, 277, 276), PackedInt32Array(281, 280, 278), PackedInt32Array(278, 280, 279), PackedInt32Array(232, 282, 231), PackedInt32Array(231, 282, 283), PackedInt32Array(285, 284, 286), PackedInt32Array(286, 284, 287), PackedInt32Array(289, 288, 287), PackedInt32Array(284, 289, 287), PackedInt32Array(205, 291, 209), PackedInt32Array(209, 291, 290), PackedInt32Array(209, 290, 208), PackedInt32Array(205, 286, 291), PackedInt32Array(291, 286, 287), PackedInt32Array(291, 287, 283), PackedInt32Array(291, 283, 290), PackedInt32Array(290, 283, 282), PackedInt32Array(294, 293, 292), PackedInt32Array(294, 292, 268), PackedInt32Array(268, 292, 267), PackedInt32Array(267, 292, 295), PackedInt32Array(295, 292, 296), PackedInt32Array(298, 297, 299), PackedInt32Array(299, 297, 301), PackedInt32Array(299, 301, 300), PackedInt32Array(292, 303, 296), PackedInt32Array(296, 303, 302), PackedInt32Array(302, 303, 301), PackedInt32Array(302, 301, 297), PackedInt32Array(305, 304, 300), PackedInt32Array(300, 304, 306), PackedInt32Array(300, 306, 299), PackedInt32Array(309, 308, 307), PackedInt32Array(311, 310, 312), PackedInt32Array(312, 310, 313), PackedInt32Array(313, 310, 314), PackedInt32Array(316, 315, 307), PackedInt32Array(307, 315, 317), PackedInt32Array(307, 317, 309), PackedInt32Array(309, 317, 239), PackedInt32Array(309, 239, 283), PackedInt32Array(283, 239, 231), PackedInt32Array(318, 312, 313), PackedInt32Array(318, 313, 319), PackedInt32Array(319, 313, 309), PackedInt32Array(283, 319, 309), PackedInt32Array(320, 201, 321), PackedInt32Array(321, 201, 323), PackedInt32Array(323, 201, 324), PackedInt32Array(324, 201, 325), PackedInt32Array(325, 201, 184), PackedInt32Array(325, 184, 322), PackedInt32Array(327, 326, 328), PackedInt32Array(328, 326, 329), PackedInt32Array(329, 326, 331), PackedInt32Array(329, 331, 330), PackedInt32Array(246, 247, 332), PackedInt32Array(332, 247, 333), PackedInt32Array(326, 334, 331), PackedInt32Array(335, 331, 333), PackedInt32Array(333, 331, 334), PackedInt32Array(333, 334, 332), PackedInt32Array(332, 334, 336), PackedInt32Array(340, 339, 337), PackedInt32Array(337, 339, 338), PackedInt32Array(342, 341, 316), PackedInt32Array(316, 341, 315), PackedInt32Array(345, 344, 343), PackedInt32Array(323, 349, 321), PackedInt32Array(321, 349, 346), PackedInt32Array(346, 348, 347), PackedInt32Array(348, 349, 322), PackedInt32Array(322, 349, 325), PackedInt32Array(325, 349, 324), PackedInt32Array(324, 349, 323), PackedInt32Array(349, 348, 346), PackedInt32Array(350, 345, 322), PackedInt32Array(322, 345, 343), PackedInt32Array(322, 343, 348), PackedInt32Array(348, 343, 351), PackedInt32Array(341, 345, 315), PackedInt32Array(315, 345, 350), PackedInt32Array(355, 354, 352), PackedInt32Array(352, 354, 353), PackedInt32Array(357, 356, 358), PackedInt32Array(358, 356, 359), PackedInt32Array(359, 356, 360), PackedInt32Array(360, 356, 361), PackedInt32Array(361, 356, 329), PackedInt32Array(329, 356, 328), PackedInt32Array(365, 364, 362), PackedInt32Array(362, 364, 363), PackedInt32Array(367, 366, 368), PackedInt32Array(370, 369, 371), PackedInt32Array(371, 369, 314), PackedInt32Array(373, 372, 374), PackedInt32Array(374, 372, 376), PackedInt32Array(374, 376, 375), PackedInt32Array(149, 377, 148), PackedInt32Array(148, 377, 288), PackedInt32Array(380, 379, 378), PackedInt32Array(377, 381, 288), PackedInt32Array(288, 381, 382), PackedInt32Array(288, 382, 287), PackedInt32Array(381, 383, 382), PackedInt32Array(382, 383, 384), PackedInt32Array(386, 385, 387), PackedInt32Array(387, 385, 388), PackedInt32Array(378, 401, 391), PackedInt32Array(391, 399, 392), PackedInt32Array(392, 399, 393), PackedInt32Array(393, 399, 394), PackedInt32Array(394, 398, 389), PackedInt32Array(389, 400, 390), PackedInt32Array(390, 400, 378), PackedInt32Array(401, 399, 391), PackedInt32Array(378, 397, 401), PackedInt32Array(399, 401, 394), PackedInt32Array(398, 396, 389), PackedInt32Array(394, 401, 398), PackedInt32Array(389, 396, 400), PackedInt32Array(400, 395, 378), PackedInt32Array(397, 398, 401), PackedInt32Array(378, 395, 397), PackedInt32Array(398, 397, 396), PackedInt32Array(396, 395, 400), PackedInt32Array(395, 396, 397), PackedInt32Array(371, 314, 375), PackedInt32Array(375, 314, 310), PackedInt32Array(375, 310, 402), PackedInt32Array(404, 403, 379), PackedInt32Array(379, 403, 366), PackedInt32Array(379, 366, 378), PackedInt32Array(387, 388, 391), PackedInt32Array(391, 388, 405), PackedInt32Array(380, 407, 383), PackedInt32Array(383, 407, 384), PackedInt32Array(384, 407, 405), PackedInt32Array(405, 408, 391), PackedInt32Array(391, 408, 378), PackedInt32Array(378, 409, 380), PackedInt32Array(380, 409, 407), PackedInt32Array(407, 408, 405), PackedInt32Array(408, 409, 378), PackedInt32Array(409, 406, 407), PackedInt32Array(407, 406, 408), PackedInt32Array(408, 406, 409), PackedInt32Array(403, 368, 366), PackedInt32Array(374, 375, 405), PackedInt32Array(405, 375, 402), PackedInt32Array(405, 402, 384), PackedInt32Array(380, 410, 379), PackedInt32Array(369, 370, 411), PackedInt32Array(411, 370, 412), PackedInt32Array(411, 412, 343), PackedInt32Array(343, 412, 413), PackedInt32Array(343, 413, 351), PackedInt32Array(417, 416, 414), PackedInt32Array(414, 416, 415), PackedInt32Array(420, 419, 418), PackedInt32Array(422, 421, 423), PackedInt32Array(423, 421, 424), PackedInt32Array(426, 425, 424), PackedInt32Array(421, 427, 424), PackedInt32Array(424, 427, 420), PackedInt32Array(424, 420, 418), PackedInt32Array(424, 418, 426), PackedInt32Array(430, 429, 428), PackedInt32Array(431, 430, 304), PackedInt32Array(304, 430, 432), PackedInt32Array(304, 432, 306), PackedInt32Array(433, 435, 434), PackedInt32Array(432, 430, 436), PackedInt32Array(436, 430, 428), PackedInt32Array(436, 428, 433), PackedInt32Array(433, 428, 435), PackedInt32Array(437, 413, 438), PackedInt32Array(438, 413, 440), PackedInt32Array(438, 440, 439), PackedInt32Array(413, 412, 440), PackedInt32Array(443, 442, 441), PackedInt32Array(446, 445, 444), PackedInt32Array(448, 447, 449), PackedInt32Array(449, 447, 450), PackedInt32Array(453, 452, 451), PackedInt32Array(444, 454, 446), PackedInt32Array(160, 455, 159), PackedInt32Array(159, 455, 456), PackedInt32Array(459, 458, 457), PackedInt32Array(450, 461, 460), PackedInt32Array(463, 462, 460), PackedInt32Array(460, 462, 450), PackedInt32Array(441, 468, 465), PackedInt32Array(465, 468, 464), PackedInt32Array(465, 464, 466), PackedInt32Array(466, 464, 467), PackedInt32Array(443, 441, 469), PackedInt32Array(469, 441, 465), PackedInt32Array(457, 467, 464), PackedInt32Array(470, 443, 469), PackedInt32Array(470, 469, 471), PackedInt32Array(471, 469, 472), PackedInt32Array(462, 473, 450), PackedInt32Array(450, 473, 449), PackedInt32Array(449, 473, 474), PackedInt32Array(474, 473, 453), PackedInt32Array(472, 474, 471), PackedInt32Array(471, 474, 451), PackedInt32Array(479, 480, 464), PackedInt32Array(464, 480, 457), PackedInt32Array(457, 477, 459), PackedInt32Array(459, 476, 475), PackedInt32Array(476, 459, 477), PackedInt32Array(477, 457, 478), PackedInt32Array(478, 480, 479), PackedInt32Array(480, 478, 457), PackedInt32Array(383, 456, 455), PackedInt32Array(453, 451, 474), PackedInt32Array(444, 484, 464), PackedInt32Array(464, 484, 479), PackedInt32Array(479, 484, 478), PackedInt32Array(478, 491, 477), PackedInt32Array(477, 383, 455), PackedInt32Array(383, 486, 380), PackedInt32Array(380, 485, 444), PackedInt32Array(444, 485, 484), PackedInt32Array(484, 492, 478), PackedInt32Array(491, 487, 477), PackedInt32Array(478, 492, 491), PackedInt32Array(477, 487, 383), PackedInt32Array(486, 485, 380), PackedInt32Array(383, 487, 486), PackedInt32Array(485, 489, 484), PackedInt32Array(484, 489, 492), PackedInt32Array(491, 490, 487), PackedInt32Array(492, 488, 491), PackedInt32Array(486, 489, 485), PackedInt32Array(487, 482, 486), PackedInt32Array(489, 493, 492), PackedInt32Array(490, 481, 487), PackedInt32Array(491, 488, 490), PackedInt32Array(492, 493, 488), PackedInt32Array(486, 482, 489), PackedInt32Array(487, 481, 482), PackedInt32Array(489, 482, 493), PackedInt32Array(490, 482, 481), PackedInt32Array(488, 493, 490), PackedInt32Array(482, 490, 493), PackedInt32Array(494, 454, 473), PackedInt32Array(473, 454, 444), PackedInt32Array(473, 444, 453), PackedInt32Array(453, 444, 464), PackedInt32Array(498, 497, 495), PackedInt32Array(495, 497, 496), PackedInt32Array(174, 500, 499), PackedInt32Array(173, 175, 476), PackedInt32Array(476, 175, 475), PackedInt32Array(475, 501, 499), PackedInt32Array(499, 501, 174), PackedInt32Array(174, 501, 176), PackedInt32Array(176, 501, 175), PackedInt32Array(175, 501, 475), PackedInt32Array(503, 502, 504), PackedInt32Array(504, 502, 505), PackedInt32Array(358, 359, 506), PackedInt32Array(506, 359, 504), PackedInt32Array(506, 504, 505), PackedInt32Array(509, 508, 507), PackedInt32Array(510, 511, 372), PackedInt32Array(372, 511, 509), PackedInt32Array(372, 509, 376), PackedInt32Array(376, 509, 439), PackedInt32Array(513, 512, 514), PackedInt32Array(514, 512, 507), PackedInt32Array(514, 507, 515), PackedInt32Array(438, 518, 512), PackedInt32Array(512, 517, 507), PackedInt32Array(507, 517, 509), PackedInt32Array(509, 517, 439), PackedInt32Array(439, 517, 438), PackedInt32Array(518, 516, 512), PackedInt32Array(438, 516, 518), PackedInt32Array(512, 516, 517), PackedInt32Array(517, 516, 438), PackedInt32Array(505, 502, 519), PackedInt32Array(519, 502, 520), PackedInt32Array(519, 520, 521), PackedInt32Array(521, 520, 523), PackedInt32Array(521, 523, 522), PackedInt32Array(527, 526, 524), PackedInt32Array(524, 526, 525), PackedInt32Array(529, 528, 530), PackedInt32Array(530, 528, 531), PackedInt32Array(524, 532, 527), PackedInt32Array(527, 532, 530), PackedInt32Array(527, 530, 531), PackedInt32Array(535, 534, 533), PackedInt32Array(538, 537, 536), PackedInt32Array(540, 539, 535), PackedInt32Array(533, 434, 435), PackedInt32Array(535, 533, 540), PackedInt32Array(540, 533, 541), PackedInt32Array(541, 533, 435), PackedInt32Array(536, 542, 538), PackedInt32Array(538, 542, 540), PackedInt32Array(538, 540, 541), PackedInt32Array(546, 545, 543), PackedInt32Array(543, 545, 544), PackedInt32Array(549, 548, 547), PackedInt32Array(552, 551, 550), PackedInt32Array(550, 553, 552), PackedInt32Array(552, 553, 547), PackedInt32Array(547, 553, 549), PackedInt32Array(549, 553, 522), PackedInt32Array(554, 549, 523), PackedInt32Array(523, 549, 522), PackedInt32Array(556, 555, 557), PackedInt32Array(558, 514, 559), PackedInt32Array(559, 514, 515), PackedInt32Array(560, 557, 555), PackedInt32Array(386, 560, 385), PackedInt32Array(385, 560, 561), PackedInt32Array(562, 559, 563), PackedInt32Array(563, 559, 555), PackedInt32Array(555, 559, 560), PackedInt32Array(560, 559, 515), PackedInt32Array(515, 561, 560), PackedInt32Array(562, 564, 559), PackedInt32Array(566, 565, 567), PackedInt32Array(567, 565, 568), PackedInt32Array(570, 569, 565), PackedInt32Array(565, 569, 568), PackedInt32Array(542, 536, 571), PackedInt32Array(571, 573, 572), PackedInt32Array(572, 574, 571), PackedInt32Array(571, 574, 542), PackedInt32Array(556, 575, 555), PackedInt32Array(555, 575, 580), PackedInt32Array(555, 580, 579), PackedInt32Array(555, 579, 578), PackedInt32Array(555, 578, 576), PackedInt32Array(576, 578, 577), PackedInt32Array(577, 578, 389), PackedInt32Array(391, 392, 575), PackedInt32Array(575, 392, 580), PackedInt32Array(580, 392, 393), PackedInt32Array(580, 393, 579), PackedInt32Array(579, 393, 578), PackedInt32Array(578, 393, 394), PackedInt32Array(578, 394, 389), PackedInt32Array(582, 581, 583), PackedInt32Array(583, 581, 584), PackedInt32Array(586, 585, 587), PackedInt32Array(587, 585, 583), PackedInt32Array(583, 585, 588), PackedInt32Array(588, 585, 589), PackedInt32Array(584, 591, 590), PackedInt32Array(593, 592, 587), PackedInt32Array(587, 592, 586), PackedInt32Array(563, 595, 594), PackedInt32Array(590, 587, 584), PackedInt32Array(584, 587, 583), PackedInt32Array(585, 596, 595), PackedInt32Array(595, 563, 585), PackedInt32Array(585, 563, 555), PackedInt32Array(585, 555, 589), PackedInt32Array(589, 555, 576), PackedInt32Array(588, 597, 583), PackedInt32Array(601, 600, 602), PackedInt32Array(602, 600, 599), PackedInt32Array(602, 599, 598), PackedInt32Array(602, 598, 603), PackedInt32Array(605, 604, 606), PackedInt32Array(606, 604, 601), PackedInt32Array(601, 604, 600), PackedInt32Array(551, 552, 607), PackedInt32Array(607, 552, 608), PackedInt32Array(611, 610, 612), PackedInt32Array(612, 610, 609), PackedInt32Array(612, 609, 613), PackedInt32Array(609, 614, 613), PackedInt32Array(611, 612, 608), PackedInt32Array(608, 612, 607), PackedInt32Array(607, 612, 615), PackedInt32Array(617, 616, 618), PackedInt32Array(618, 616, 574), PackedInt32Array(572, 619, 574), PackedInt32Array(574, 619, 618), PackedInt32Array(618, 619, 620), PackedInt32Array(620, 619, 621), PackedInt32Array(621, 622, 620), PackedInt32Array(620, 623, 618), PackedInt32Array(624, 626, 625), PackedInt32Array(628, 627, 626), PackedInt32Array(630, 629, 628), PackedInt32Array(621, 631, 622), PackedInt32Array(622, 631, 624), PackedInt32Array(624, 631, 632), PackedInt32Array(630, 634, 633), PackedInt32Array(626, 624, 628), PackedInt32Array(628, 624, 632), PackedInt32Array(628, 632, 634), PackedInt32Array(628, 634, 630), PackedInt32Array(636, 635, 637), PackedInt32Array(637, 635, 638), PackedInt32Array(640, 639, 614), PackedInt32Array(614, 639, 613), PackedInt32Array(613, 639, 641), PackedInt32Array(641, 639, 642), PackedInt32Array(639, 646, 642), PackedInt32Array(642, 646, 643), PackedInt32Array(643, 646, 644), PackedInt32Array(644, 646, 645), PackedInt32Array(648, 647, 649), PackedInt32Array(649, 647, 650), PackedInt32Array(649, 650, 644), PackedInt32Array(652, 651, 647), PackedInt32Array(647, 651, 650), PackedInt32Array(650, 651, 653), PackedInt32Array(653, 651, 654), PackedInt32Array(651, 655, 654), PackedInt32Array(644, 645, 649), PackedInt32Array(657, 656, 658), PackedInt32Array(658, 656, 659), PackedInt32Array(633, 660, 630), PackedInt32Array(630, 660, 656), PackedInt32Array(656, 660, 659), PackedInt32Array(663, 662, 661), PackedInt32Array(654, 655, 664), PackedInt32Array(664, 655, 665), PackedInt32Array(663, 661, 666), PackedInt32Array(666, 661, 664), PackedInt32Array(666, 664, 665), PackedInt32Array(666, 665, 667), PackedInt32Array(665, 668, 667), PackedInt32Array(670, 669, 671), PackedInt32Array(671, 669, 673), PackedInt32Array(671, 673, 672), PackedInt32Array(675, 674, 671), PackedInt32Array(678, 677, 679), PackedInt32Array(679, 677, 676), PackedInt32Array(675, 671, 676), PackedInt32Array(676, 671, 672), PackedInt32Array(676, 672, 679), PackedInt32Array(679, 672, 680), PackedInt32Array(682, 681, 683), PackedInt32Array(683, 681, 684), PackedInt32Array(684, 681, 686), PackedInt32Array(684, 686, 685), PackedInt32Array(684, 687, 683), PackedInt32Array(658, 659, 685), PackedInt32Array(685, 659, 684), PackedInt32Array(688, 685, 689), PackedInt32Array(689, 685, 686), PackedInt32Array(691, 690, 692), PackedInt32Array(692, 690, 693), PackedInt32Array(692, 693, 694), PackedInt32Array(694, 693, 695), PackedInt32Array(695, 693, 696), PackedInt32Array(698, 697, 699), PackedInt32Array(699, 697, 700), PackedInt32Array(700, 697, 701), PackedInt32Array(701, 697, 702), PackedInt32Array(704, 703, 701), PackedInt32Array(701, 703, 700), PackedInt32Array(700, 705, 699), PackedInt32Array(696, 707, 706), PackedInt32Array(709, 708, 710), PackedInt32Array(710, 708, 711), PackedInt32Array(713, 712, 708), PackedInt32Array(708, 712, 715), PackedInt32Array(708, 715, 714), PackedInt32Array(708, 714, 711), PackedInt32Array(717, 716, 712), PackedInt32Array(712, 716, 715), PackedInt32Array(715, 716, 706), PackedInt32Array(706, 716, 696), PackedInt32Array(716, 695, 696), PackedInt32Array(719, 718, 720), PackedInt32Array(720, 718, 722), PackedInt32Array(720, 722, 721), PackedInt32Array(723, 710, 721), PackedInt32Array(721, 710, 711), PackedInt32Array(721, 711, 720), PackedInt32Array(724, 673, 725), PackedInt32Array(725, 673, 726), PackedInt32Array(726, 673, 669), PackedInt32Array(728, 727, 726), PackedInt32Array(726, 727, 725), PackedInt32Array(731, 730, 729), PackedInt32Array(677, 678, 732), PackedInt32Array(697, 698, 731), PackedInt32Array(732, 733, 677), PackedInt32Array(677, 733, 734), PackedInt32Array(677, 734, 729), PackedInt32Array(731, 729, 697), PackedInt32Array(697, 729, 734), PackedInt32Array(735, 682, 683), PackedInt32Array(718, 736, 722), PackedInt32Array(722, 736, 737), PackedInt32Array(737, 736, 735), PackedInt32Array(737, 735, 683), PackedInt32Array(662, 739, 738), PackedInt32Array(740, 703, 738), PackedInt32Array(703, 704, 738), PackedInt32Array(738, 704, 741), PackedInt32Array(738, 741, 662), PackedInt32Array(662, 741, 661), PackedInt32Array(728, 743, 742), PackedInt32Array(690, 691, 744), PackedInt32Array(744, 745, 690), PackedInt32Array(690, 745, 742), PackedInt32Array(742, 745, 728), PackedInt32Array(728, 745, 727)]
agent_height = 1.75
agent_radius = 0.375
agent_max_climb = 0.5
edge_max_length = 12.0
filter_low_hanging_obstacles = true
filter_ledge_spans = true
filter_walkable_low_height_spans = true

[sub_resource type="StandardMaterial3D" id="StandardMaterial3D_cin4e"]
albedo_texture = ExtResource("15_df8b0")
metallic = 0.2
metallic_texture = ExtResource("16_003du")
roughness = 0.8
uv1_scale = Vector3(10, 10, 1)

[sub_resource type="StandardMaterial3D" id="StandardMaterial3D_01ds2"]
transparency = 1
albedo_color = Color(1, 1, 0, 0.3)
emission_enabled = true
emission = Color(1, 1, 0, 1)

[node name="level01" type="Node3D"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 0, 0, 1.94383)
script = ExtResource("1_hk2b6")
minimap_scene = ExtResource("2_j32d5")
player_avatar_scene = ExtResource("3_0f42i")
item_boxes_scene = ExtResource("4_swvig")
skill_box_ui_scene = ExtResource("5_dbohx")

[node name="WorldEnvironment" type="WorldEnvironment" parent="."]
environment = SubResource("Environment_j4i7h")

[node name="DirectionalLight3D" type="DirectionalLight3D" parent="."]
transform = Transform3D(0.707107, -0.5, 0.5, 0, 0.707107, 0.707107, -0.707107, -0.5, 0.5, 0, 0, 0)
light_color = Color(1, 0.95, 0.8, 1)
light_energy = 1.2
shadow_enabled = true
shadow_opacity = 0.6
shadow_blur = 1.5

[node name="ModularLevelGenerator" type="Node3D" parent="."]
script = ExtResource("6_6ly8b")
level_config = ExtResource("7_r5102")

[node name="FogOfWar" parent="." instance=ExtResource("9_003du")]
player_vision_radius = 8.0
enabled = false

[node name="Camera3D" type="Camera3D" parent="."]
unique_name_in_owner = true
transform = Transform3D(1, 0, 0, 0, 0.866025, 0.5, 0, -0.5, 0.866025, 0, 30, 50)
v_offset = 20.0
projection = 1
current = true
size = 44.805
near = 0.022
script = ExtResource("26_camera_follower")
target_path = NodePath("../Player")
follow_speed = 10.0
position_offset = Vector3(0, 10, 55)

[node name="UIManager" type="Node" parent="."]
script = ExtResource("25_ui_manager")
border_width = 100.0

[node name="Player" parent="." instance=ExtResource("11_82jtv")]

[node name="Chest" parent="." instance=ExtResource("14_1ks1q")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, 4.57862, 0, -14.4123)
interaction_distance = 3.0
item_resource = ExtResource("17_j32d5")

[node name="Chest2" parent="." instance=ExtResource("14_1ks1q")]
transform = Transform3D(1.5, 0, 0, 0, 1.5, 0, 0, 0, 1.5, 20, 0.165, -3.779)
interaction_distance = 3.0
item_resource = ExtResource("18_j32d5")

[node name="EnemyBoy01" parent="." instance=ExtResource("19_0f42i")]
transform = Transform3D(0.8, 0, 0, 0, 0.751754, 0.273616, 0, -0.273616, 0.751754, -10.0014, 0, -8.65824)

[node name="Enemy02" parent="." instance=ExtResource("21_dbohx")]
transform = Transform3D(2.1, 0, 0, 0, 1.97335, 0.718242, 0, -0.718242, 1.97335, 7.86917, 0, -7.768)

[node name="NavigationRegion3D" type="NavigationRegion3D" parent="."]
navigation_mesh = SubResource("NavigationMesh_xcdtp")

[node name="Floor" type="Node3D" parent="NavigationRegion3D"]

[node name="@CSGBox3D@126433" type="CSGBox3D" parent="NavigationRegion3D/Floor"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 0, -0.05, 0)
use_collision = true
size = Vector3(100, 0.1, 100)
material = SubResource("StandardMaterial3D_cin4e")

[node name="FloorTiles" type="Node3D" parent="NavigationRegion3D/Floor"]

[node name="Floor01_0" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 1.1686, 0.1, 2.24103)

[node name="Floor01_1" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -19.3375, 0.1, 29.9449)

[node name="Floor01_2" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -0.758396, 0.1, -22.9856)

[node name="Floor01_3" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -5.78815, 0.1, 15.811)

[node name="Floor01_4" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 40.9666, 0.1, -44.9385)

[node name="Floor01_5" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 26.2013, 0.1, -40.0238)

[node name="Floor01_6" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -11.6086, 0.1, -34.4308)

[node name="Floor01_7" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -2.93244, 0.1, -33.3433)

[node name="Floor01_8" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -0.550253, 0.1, 31.6066)

[node name="Floor01_9" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 43.8834, 0.1, -36.9336)

[node name="Floor01_10" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -11.9354, 0.1, 2.17938)

[node name="Floor01_11" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 20.7183, 0.1, 1.03739)

[node name="Floor01_12" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 26.7137, 0.1, 40.9694)

[node name="Floor01_13" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 33.7459, 0.1, -29.8447)

[node name="Floor01_14" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 5.26391, 0.1, 40.307)

[node name="Floor01_15" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 12.049, 0.1, -7.20805)

[node name="Floor01_16" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -44.6121, 0.1, 1.43272)

[node name="Floor01_17" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 39.3712, 0.1, 4.6304)

[node name="Floor01_18" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 12.8458, 0.1, 11.1812)

[node name="Floor01_19" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 18.1613, 0.1, -38.4286)

[node name="Floor01_20" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -18.6808, 0.1, -6.10519)

[node name="Floor01_21" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 2.34638, 0.1, 20.8064)

[node name="Floor01_22" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -40.4269, 0.1, -13.3421)

[node name="Floor01_23" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -22.8438, 0.1, -44.1888)

[node name="Floor01_24" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -24.6986, 0.1, -21.5261)

[node name="Floor01_25" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -32.985, 0.1, 13.0467)

[node name="Floor01_26" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -15.8372, 0.1, 14.2817)

[node name="Floor01_27" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 30.3459, 0.1, 24.6424)

[node name="Floor01_28" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -2.36785, 0.1, -5.69757)

[node name="Floor01_29" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 10.2896, 0.1, -35.4757)

[node name="Floor01_30" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -33.1288, 0.1, 40.807)

[node name="Floor01_31" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 1.4409, 0.1, -41.973)

[node name="Floor01_32" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 12.4227, 0.1, -24.3456)

[node name="Floor01_33" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -31.1351, 0.1, -10.3289)

[node name="Floor01_34" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 42.3314, 0.1, 20.9651)

[node name="Floor01_35" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 38.0801, 0.1, 37.7582)

[node name="Floor01_36" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 35.6239, 0.1, -9.2476)

[node name="Floor01_37" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -30.3145, 0.1, -35.9324)

[node name="Floor01_38" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -39.7823, 0.1, -35.5757)

[node name="Floor01_39" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -29.4689, 0.1, 25.7604)

[node name="Floor01_40" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -40.5084, 0.1, -21.6499)

[node name="Floor01_41" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 44.7667, 0.1, -25.0483)

[node name="Floor01_42" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -6.93412, 0.1, 25.9688)

[node name="Floor01_43" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 24.9625, 0.1, -20.7055)

[node name="Floor01_44" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -15.6638, 0.1, 44.6506)

[node name="Floor01_45" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -40.5313, 0.1, 16.3868)

[node name="Floor01_46" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 44.5319, 0.1, -5.60713)

[node name="Floor01_47" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 14.9193, 0.1, 26.5532)

[node name="Floor01_48" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -41.0529, 0.1, 38.0953)

[node name="Floor01_49" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -42.3681, 0.1, 28.8482)

[node name="Floor01_50" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -12.9067, 0.1, -44.3086)

[node name="Floor01_51" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 34.4631, 0.1, 16.0123)

[node name="Floor01_52" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -6.77873, 0.1, -17.5704)

[node name="Floor01_53" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -36.9403, 0.1, 5.32264)

[node name="Floor01_54" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 22.13, 0.1, 31.6806)

[node name="Floor01_55" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 14.5436, 0.1, 42.0557)

[node name="Floor01_56" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -25.006, 0.1, 36.6392)

[node name="Floor01_57" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -22.5438, 0.1, 20.137)

[node name="Floor01_58" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -24.2401, 0.1, 9.74109)

[node name="Floor01_59" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -24.2174, 0.1, 0.900865)

[node name="Floor01_60" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 35.975, 0.1, -21.8477)

[node name="Floor01_61" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 11.2292, 0.1, -44.4394)

[node name="Floor01_62" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -24.2296, 0.1, -29.7761)

[node name="Floor01_63" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 42.8531, 0.1, 12.2884)

[node name="Floor01_64" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 12.7072, 0.1, 3.06112)

[node name="Floor01_65" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -41.0086, 0.1, -43.4982)

[node name="Floor01_66" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -23.6778, 0.1, -13.3223)

[node name="Floor01_67" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 27.5825, 0.1, -8.73023)

[node name="Floor01_68" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -31.8426, 0.1, -44.5748)

[node name="Floor01_69" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 7.09323, 0.1, 28.4314)

[node name="Floor01_70" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 6.25718, 0.1, -14.0429)

[node name="Floor01_71" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 19.6527, 0.1, 16.1499)

[node name="wall_0" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1.05764, 0, 0.182128, 0, 1, 0, -0.169825, 0, 1.13426, -50, 0, -50)

[node name="wall_1" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(0.779631, 0, 0.20133, 0, 1, 0, -0.188017, 0, 0.834834, -47.5, 0, -50)

[node name="wall_2" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1.11036, 0, -0.310985, 0, 1, 0, 0.333757, 0, 1.0346, -45, 0, -50)

[node name="wall_3" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(0.955491, 0, 0.0984934, 0, 1, 0, -0.0852857, 0, 1.10346, -42.5, 0, -50)

[node name="wall_4" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1.12687, 0, -0.146684, 0, 1, 0, 0.194009, 0, 0.85199, -40, 0, -50)

[node name="wall_5" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1.06822, 0, 0.13112, 0, 1, 0, -0.136573, 0, 1.02557, -37.5, 0, -50)

[node name="wall_6" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1.0318, 0, -0.0782298, 0, 1, 0, 0.096575, 0, 0.835802, -35, 0, -50)

[node name="wall_7" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(0.788176, 0, 0.309288, 0, 1, 0, -0.222728, 0, 1.09449, -32.5, 0, -50)

[node name="wall_8" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(0.94551, 0, 0.00965082, 0, 1, 0, -0.00888268, 0, 1.02728, -30, 0, -50)

[node name="wall_9" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1.07156, 0, -0.198996, 0, 1, 0, 0.227644, 0, 0.936706, -27.5, 0, -50)

[node name="wall_10" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(0.958061, 0, -0.119707, 0, 1, 0, 0.124219, 0, 0.923266, -25, 0, -50)

[node name="wall_11" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1.10174, 0, -0.226053, 0, 1, 0, 0.234744, 0, 1.06095, -22.5, 0, -50)

[node name="wall_12" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(0.884057, 0, 0.0264743, 0, 1, 0, -0.0282883, 0, 0.827365, -20, 0, -50)

[node name="wall_13" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1.12169, 0, -0.0721297, 0, 1, 0, 0.0911357, 0, 0.88777, -17.5, 0, -50)

[node name="wall_14" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(0.96944, 0, 0.0646028, 0, 1, 0, -0.0566728, 0, 1.10509, -15, 0, -50)

[node name="wall_15" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1.05482, 0, -0.0271511, 0, 1, 0, 0.0252902, 0, 1.13243, -12.5, 0, -50)

[node name="wall_16" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1.1374, 0, -0.0960378, 0, 1, 0, 0.0962636, 0, 1.13473, -10, 0, -50)

[node name="wall_17" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(0.955805, 0, -0.0635989, 0, 1, 0, 0.0558574, 0, 1.08827, -7.5, 0, -50)

[node name="wall_18" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1.03953, 0, 0.245623, 0, 1, 0, -0.221261, 0, 1.15399, -5, 0, -50)

[node name="wall_19" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(0.887228, 0, -0.104232, 0, 1, 0, 0.0812459, 0, 1.13824, -2.5, 0, -50)

[node name="wall_20" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1.17071, 0, 0.0178178, 0, 1, 0, -0.0174152, 0, 1.19778, 0, 0, -50)

[node name="wall_21" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1.10533, 0, 0.220489, 0, 1, 0, -0.31469, 0, 0.774452, 2.5, 0, -50)

[node name="wall_22" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1.02324, 0, 0.331973, 0, 1, 0, -0.298116, 0, 1.13946, 5, 0, -50)

[node name="wall_23" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(0.785281, 0, -0.246174, 0, 1, 0, 0.171182, 0, 1.1293, 7.5, 0, -50)

[node name="wall_24" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1.03872, 0, 0.131467, 0, 1, 0, -0.137459, 0, 0.993443, 10, 0, -50)

[node name="wall_25" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(0.815797, 0, -0.0886628, 0, 1, 0, 0.0724084, 0, 0.998929, 12.5, 0, -50)

[node name="wall_26" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(0.933836, 0, 0.0172551, 0, 1, 0, -0.0158473, 0, 1.01679, 15, 0, -50)

[node name="wall_27" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(0.799517, 0, 0.190388, 0, 1, 0, -0.172752, 0, 0.881136, 17.5, 0, -50)

[node name="wall_28" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1.02816, 0, 0.254286, 0, 1, 0, -0.285813, 0, 0.914746, 20, 0, -50)

[node name="wall_29" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(0.984517, 0, 0.289613, 0, 1, 0, -0.265288, 0, 1.07479, 22.5, 0, -50)

[node name="wall_30" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1.05816, 0, 0.11267, 0, 1, 0, -0.121384, 0, 0.982194, 25, 0, -50)

[node name="wall_31" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1.10356, 0, 0.279029, 0, 1, 0, -0.288974, 0, 1.06558, 27.5, 0, -50)

[node name="wall_32" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(0.822122, 0, 0.159221, 0, 1, 0, -0.162605, 0, 0.805015, 30, 0, -50)

[node name="wall_33" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(0.995841, 0, -0.141465, 0, 1, 0, 0.173656, 0, 0.811236, 32.5, 0, -50)

[node name="wall_34" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(0.92207, 0, 0.258625, 0, 1, 0, -0.229592, 0, 1.03867, 35, 0, -50)

[node name="wall_35" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1.11009, 0, 0.264084, 0, 1, 0, -0.255843, 0, 1.14585, 37.5, 0, -50)

[node name="wall_36" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1.10779, 0, -0.327624, 0, 1, 0, 0.319088, 0, 1.13743, 40, 0, -50)

[node name="wall_37" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(0.913793, 0, 0.244889, 0, 1, 0, -0.202291, 0, 1.10622, 42.5, 0, -50)

[node name="wall_38" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(0.995193, 0, -0.111248, 0, 1, 0, 0.0952134, 0, 1.16279, 45, 0, -50)

[node name="wall_39" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(0.811218, 0, -0.121165, 0, 1, 0, 0.0864608, 0, 1.13683, 47.5, 0, -50)

[node name="wall_40" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1.0883, 0, 0.209599, 0, 1, 0, -0.288177, 0, 0.791551, 50, 0, -50)

[node name="wall_41" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1.16896, 0, 0.136165, 0, 1, 0, -0.151669, 0, 1.04947, -50, 0, 50)

[node name="wall_42" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(0.810863, 0, -0.10515, 0, 1, 0, 0.0902818, 0, 0.944405, -47.5, 0, 50)

[node name="wall_43" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(0.802193, 0, 0.0520772, 0, 1, 0, -0.0422491, 0, 0.988802, -45, 0, 50)

[node name="wall_44" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1.1335, 0, 0.0887273, 0, 1, 0, -0.111243, 0, 0.904076, -42.5, 0, 50)

[node name="wall_45" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1.03631, 0, -0.148433, 0, 1, 0, 0.159829, 0, 0.962422, -40, 0, 50)

[node name="wall_46" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(0.787824, 0, -0.236666, 0, 1, 0, 0.161907, 0, 1.15159, -37.5, 0, 50)

[node name="wall_47" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(0.989681, 0, -0.19671, 0, 1, 0, 0.188657, 0, 1.03193, -35, 0, 50)

[node name="wall_48" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1.15699, 0, 0.0528474, 0, 1, 0, -0.063695, 0, 0.959946, -32.5, 0, 50)

[node name="wall_49" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(0.81238, 0, 0.196973, 0, 1, 0, -0.169849, 0, 0.942116, -30, 0, 50)

[node name="wall_50" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(0.901439, 0, 0.23784, 0, 1, 0, -0.231001, 0, 0.928126, -27.5, 0, 50)

[node name="wall_51" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(0.803376, 0, 0.166846, 0, 1, 0, -0.164594, 0, 0.814366, -25, 0, 50)

[node name="wall_52" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1.01292, 0, 0.00767085, 0, 1, 0, -0.00711443, 0, 1.09214, -22.5, 0, 50)

[node name="wall_53" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(0.908779, 0, -0.319306, 0, 1, 0, 0.271519, 0, 1.06872, -20, 0, 50)

[node name="wall_54" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1.16448, 0, 0.128453, 0, 1, 0, -0.180796, 0, 0.827347, -17.5, 0, 50)

[node name="wall_55" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1.10829, 0, 0.115969, 0, 1, 0, -0.135961, 0, 0.945321, -15, 0, 50)

[node name="wall_56" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1.16354, 0, 0.203898, 0, 1, 0, -0.212061, 0, 1.11876, -12.5, 0, 50)

[node name="wall_57" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1.07597, 0, -0.0985282, 0, 1, 0, 0.130386, 0, 0.813074, -10, 0, 50)

[node name="wall_58" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1.02699, 0, -0.0690077, 0, 1, 0, 0.0738371, 0, 0.959816, -7.5, 0, 50)

[node name="wall_59" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(0.876953, 0, -0.0149949, 0, 1, 0, 0.0140495, 0, 0.935963, -5, 0, 50)

[node name="wall_60" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(0.866384, 0, -0.180441, 0, 1, 0, 0.164375, 0, 0.951062, -2.5, 0, 50)

[node name="wall_61" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(0.907389, 0, -0.0353728, 0, 1, 0, 0.0350409, 0, 0.915984, 0, 0, 50)

[node name="wall_62" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(0.982286, 0, 0.0636808, 0, 1, 0, -0.0694412, 0, 0.900802, 2.5, 0, 50)

[node name="wall_63" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1.15643, 0, -0.0132104, 0, 1, 0, 0.0161801, 0, 0.944175, 5, 0, 50)

[node name="wall_64" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(0.823789, 0, -0.0198599, 0, 1, 0, 0.0187154, 0, 0.874167, 7.5, 0, 50)

[node name="wall_65" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1.16412, 0, -0.208652, 0, 1, 0, 0.206969, 0, 1.17358, 10, 0, 50)

[node name="wall_66" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1.0469, 0, 0.299309, 0, 1, 0, -0.284376, 0, 1.10187, 12.5, 0, 50)

[node name="wall_67" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1.13012, 0, -0.0380984, 0, 1, 0, 0.0468358, 0, 0.919292, 15, 0, 50)

[node name="wall_68" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(0.984902, 0, -0.17856, 0, 1, 0, 0.222352, 0, 0.790925, 17.5, 0, 50)

[node name="wall_69" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(0.960869, 0, -0.0405024, 0, 1, 0, 0.0385765, 0, 1.00884, 20, 0, 50)

[node name="wall_70" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1.14466, 0, -0.176899, 0, 1, 0, 0.171178, 0, 1.18291, 22.5, 0, 50)

[node name="wall_71" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1.06607, 0, 0.0285153, 0, 1, 0, -0.0311923, 0, 0.974578, 25, 0, 50)

[node name="wall_72" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(0.882559, 0, -0.0068535, 0, 1, 0, 0.00687234, 0, 0.88014, 27.5, 0, 50)

[node name="wall_73" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1.06316, 0, -0.0899027, 0, 1, 0, 0.0916112, 0, 1.04333, 30, 0, 50)

[node name="wall_74" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1.16919, 0, 0.168922, 0, 1, 0, -0.172878, 0, 1.14243, 32.5, 0, 50)

[node name="wall_75" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(0.902453, 0, -0.0185589, 0, 1, 0, 0.0201916, 0, 0.82948, 35, 0, 50)

[node name="wall_76" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1.01898, 0, -0.206851, 0, 1, 0, 0.204918, 0, 1.02859, 37.5, 0, 50)

[node name="wall_77" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(0.785409, 0, 0.193703, 0, 1, 0, -0.173517, 0, 0.876779, 40, 0, 50)

[node name="wall_78" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(0.979698, 0, 0.110141, 0, 1, 0, -0.101482, 0, 1.06329, 42.5, 0, 50)

[node name="wall_79" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(0.803281, 0, 0.286913, 0, 1, 0, -0.236429, 0, 0.974802, 45, 0, 50)

[node name="wall_80" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(0.821031, 0, -0.157073, 0, 1, 0, 0.146424, 0, 0.880744, 47.5, 0, 50)

[node name="wall_81" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1.14793, 0, 0.0542227, 0, 1, 0, -0.0519551, 0, 1.19803, 50, 0, 50)

[node name="wall_82" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(0.24606, 0, 1.09435, 0, 1, 0, -1.13997, 0, 0.236214, -50, 0, -47.5)

[node name="wall_83" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(0.0212208, 0, 1.17041, 0, 1, 0, -0.932445, 0, 0.0266365, -50, 0, -45)

[node name="wall_84" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(0.205582, 0, 0.886654, 0, 1, 0, -1.11681, 0, 0.163215, -50, 0, -42.5)

[node name="wall_85" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-0.0462426, 0, 0.972883, 0, 1, 0, -1.12717, 0, -0.0399128, -50, 0, -40)

[node name="wall_86" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(0.0636483, 0, 0.891329, 0, 1, 0, -0.865871, 0, 0.0655197, -50, 0, -37.5)

[node name="wall_87" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-0.111364, 0, 0.982952, 0, 1, 0, -0.836696, 0, -0.13083, -50, 0, -35)

[node name="wall_88" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(0.032386, 0, 1.18069, 0, 1, 0, -0.894017, 0, 0.042771, -50, 0, -32.5)

[node name="wall_89" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(0.26947, 0, 0.824361, 0, 1, 0, -0.998961, 0, 0.222372, -50, 0, -30)

[node name="wall_90" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-0.00449904, 0, 0.907167, 0, 1, 0, -0.839189, 0, -0.00486348, -50, 0, -27.5)

[node name="wall_91" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(0.267138, 0, 1.12075, 0, 1, 0, -0.912622, 0, 0.32806, -50, 0, -25)

[node name="wall_92" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(0.0681138, 0, 1.09794, 0, 1, 0, -1.04617, 0, 0.0714845, -50, 0, -22.5)

[node name="wall_93" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(0.290314, 0, 0.816765, 0, 1, 0, -1.06569, 0, 0.222501, -50, 0, -20)

[node name="wall_94" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-0.180891, 0, 1.03485, 0, 1, 0, -1.16371, 0, -0.160861, -50, 0, -17.5)

[node name="wall_95" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-0.194136, 0, 1.1706, 0, 1, 0, -1.03213, 0, -0.220181, -50, 0, -15)

[node name="wall_96" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-0.195093, 0, 0.96884, 0, 1, 0, -0.924072, 0, -0.204544, -50, 0, -12.5)

[node name="wall_97" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(0.245459, 0, 0.988708, 0, 1, 0, -1.08006, 0, 0.224698, -50, 0, -10)

[node name="wall_98" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(0.201368, 0, 1.01704, 0, 1, 0, -0.870803, 0, 0.235185, -50, 0, -7.5)

[node name="wall_99" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-0.077111, 0, 1.18253, 0, 1, 0, -1.09519, 0, -0.0832607, -50, 0, -5)

[node name="wall_100" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(0.111989, 0, 1.18763, 0, 1, 0, -1.07436, 0, 0.123796, -50, 0, -2.5)

[node name="wall_101" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-0.155165, 0, 0.89069, 0, 1, 0, -0.910279, 0, -0.151826, -50, 0, 0)

[node name="wall_102" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-0.0947774, 0, 0.998528, 0, 1, 0, -0.959937, 0, -0.0985876, -50, 0, 2.5)

[node name="wall_103" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-0.244631, 0, 0.961947, 0, 1, 0, -0.805981, 0, -0.291969, -50, 0, 5)

[node name="wall_104" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-0.222128, 0, 1.04652, 0, 1, 0, -0.893286, 0, -0.260233, -50, 0, 7.5)

[node name="wall_105" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-0.0223701, 0, 1.16737, 0, 1, 0, -1.07214, 0, -0.0243571, -50, 0, 10)

[node name="wall_106" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(0.18019, 0, 0.997578, 0, 1, 0, -0.865716, 0, 0.207636, -50, 0, 12.5)

[node name="wall_107" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-0.0886514, 0, 1.00661, 0, 1, 0, -0.980306, 0, -0.0910297, -50, 0, 15)

[node name="wall_108" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-0.112843, 0, 0.968272, 0, 1, 0, -1.02098, 0, -0.107017, -50, 0, 17.5)

[node name="wall_109" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(0.238748, 0, 1.13644, 0, 1, 0, -1.12199, 0, 0.241823, -50, 0, 20)

[node name="wall_110" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(0.0235042, 0, 0.91238, 0, 1, 0, -1.13222, 0, 0.0189405, -50, 0, 22.5)

[node name="wall_111" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(0.085104, 0, 1.19192, 0, 1, 0, -0.819629, 0, 0.12376, -50, 0, 25)

[node name="wall_112" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-0.125253, 0, 0.937176, 0, 1, 0, -0.794864, 0, -0.147678, -50, 0, 27.5)

[node name="wall_113" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-0.203815, 0, 0.810227, 0, 1, 0, -1.14061, 0, -0.144779, -50, 0, 30)

[node name="wall_114" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(0.297761, 0, 0.833899, 0, 1, 0, -0.975288, 0, 0.254594, -50, 0, 32.5)

[node name="wall_115" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(0.159612, 0, 0.867349, 0, 1, 0, -0.785348, 0, 0.176277, -50, 0, 35)

[node name="wall_116" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(0.00123219, 0, 0.983773, 0, 1, 0, -0.945975, 0, 0.00128142, -50, 0, 37.5)

[node name="wall_117" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-0.308834, 0, 1.03782, 0, 1, 0, -1.03754, 0, -0.308916, -50, 0, 40)

[node name="wall_118" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-0.26048, 0, 1.06166, 0, 1, 0, -0.966429, 0, -0.286147, -50, 0, 42.5)

[node name="wall_119" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(0.293895, 0, 1.15418, 0, 1, 0, -1.07636, 0, 0.315144, -50, 0, 45)

[node name="wall_120" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-0.186158, 0, 0.9633, 0, 1, 0, -1.09327, 0, -0.164027, -50, 0, 47.5)

[node name="wall_121" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(0.137217, 0, 0.989398, 0, 1, 0, -1.05727, 0, 0.128409, 50, 0, -47.5)

[node name="wall_122" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-0.16437, 0, 0.857732, 0, 1, 0, -1.05711, 0, -0.133368, 50, 0, -45)

[node name="wall_123" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-0.179472, 0, 1.08593, 0, 1, 0, -0.860023, 0, -0.226615, 50, 0, -42.5)

[node name="wall_124" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-0.0306967, 0, 1.13526, 0, 1, 0, -1.11715, 0, -0.0311943, 50, 0, -40)

[node name="wall_125" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(0.24679, 0, 0.852112, 0, 1, 0, -1.0048, 0, 0.209289, 50, 0, -37.5)

[node name="wall_126" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-0.0192613, 0, 0.85604, 0, 1, 0, -1.0832, 0, -0.015222, 50, 0, -35)

[node name="wall_127" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(0.0151556, 0, 1.19427, 0, 1, 0, -0.80353, 0, 0.0225255, 50, 0, -32.5)

[node name="wall_128" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(0.131904, 0, 0.910825, 0, 1, 0, -0.791809, 0, 0.151731, 50, 0, -30)

[node name="wall_129" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-0.15407, 0, 0.940699, 0, 1, 0, -1.01198, 0, -0.143218, 50, 0, -27.5)

[node name="wall_130" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-0.256997, 0, 0.802159, 0, 1, 0, -0.974213, 0, -0.211609, 50, 0, -25)

[node name="wall_131" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-0.149866, 0, 1.12225, 0, 1, 0, -1.00039, 0, -0.168121, 50, 0, -22.5)

[node name="wall_132" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(0.028442, 0, 1.11244, 0, 1, 0, -1.10582, 0, 0.028612, 50, 0, -20)

[node name="wall_133" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(0.164783, 0, 1.01681, 0, 1, 0, -0.876355, 0, 0.191193, 50, 0, -17.5)

[node name="wall_134" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-0.176437, 0, 1.06244, 0, 1, 0, -1.11421, 0, -0.168239, 50, 0, -15)

[node name="wall_135" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(0.13564, 0, 0.929974, 0, 1, 0, -0.851301, 0, 0.148175, 50, 0, -12.5)

[node name="wall_136" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(0.190655, 0, 0.837164, 0, 1, 0, -0.984828, 0, 0.162069, 50, 0, -10)

[node name="wall_137" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-0.182586, 0, 1.08558, 0, 1, 0, -0.980464, 0, -0.20216, 50, 0, -7.5)

[node name="wall_138" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-0.250428, 0, 0.985018, 0, 1, 0, -0.94478, 0, -0.261094, 50, 0, -5)

[node name="wall_139" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(0.154036, 0, 1.17924, 0, 1, 0, -0.895706, 0, 0.202796, 50, 0, -2.5)

[node name="wall_140" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(0.179477, 0, 1.15477, 0, 1, 0, -1.07508, 0, 0.192781, 50, 0, 0)

[node name="wall_141" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-0.198639, 0, 1.14751, 0, 1, 0, -1.1357, 0, -0.200705, 50, 0, 2.5)

[node name="wall_142" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-0.161782, 0, 1.08439, 0, 1, 0, -1.04426, 0, -0.167998, 50, 0, 5)

[node name="wall_143" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(0.0445504, 0, 1.13568, 0, 1, 0, -1.16987, 0, 0.0432487, 50, 0, 7.5)

[node name="wall_144" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(0.246722, 0, 0.784775, 0, 1, 0, -1.11431, 0, 0.17376, 50, 0, 10)

[node name="wall_145" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(0.0888578, 0, 1.09064, 0, 1, 0, -0.831966, 0, 0.116485, 50, 0, 12.5)

[node name="wall_146" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-0.166747, 0, 0.806114, 0, 1, 0, -1.08916, 0, -0.123413, 50, 0, 15)

[node name="wall_147" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(0.0891268, 0, 0.833079, 0, 1, 0, -1.01427, 0, 0.0732049, 50, 0, 17.5)

[node name="wall_148" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-0.167007, 0, 1.04925, 0, 1, 0, -0.793491, 0, -0.220838, 50, 0, 20)

[node name="wall_149" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(0.0859639, 0, 0.856288, 0, 1, 0, -1.06895, 0, 0.068862, 50, 0, 22.5)

[node name="wall_150" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(0.118235, 0, 0.802965, 0, 1, 0, -0.957131, 0, 0.0991905, 50, 0, 25)

[node name="wall_151" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(0.27829, 0, 0.866695, 0, 1, 0, -1.12913, 0, 0.213608, 50, 0, 27.5)

[node name="wall_152" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-0.00300112, 0, 0.999027, 0, 1, 0, -1.04347, 0, -0.0028733, 50, 0, 30)

[node name="wall_153" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-0.229321, 0, 1.06875, 0, 1, 0, -0.777181, 0, -0.315352, 50, 0, 32.5)

[node name="wall_154" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(0.278406, 0, 0.896592, 0, 1, 0, -1.15517, 0, 0.216087, 50, 0, 35)

[node name="wall_155" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(0.257743, 0, 1.05631, 0, 1, 0, -0.998327, 0, 0.272713, 50, 0, 37.5)

[node name="wall_156" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-0.170029, 0, 0.901601, 0, 1, 0, -1.10994, 0, -0.138115, 50, 0, 40)

[node name="wall_157" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-0.0970374, 0, 1.08207, 0, 1, 0, -1.18446, 0, -0.088649, 50, 0, 42.5)

[node name="wall_158" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(0.193525, 0, 0.887471, 0, 1, 0, -0.926866, 0, 0.185299, 50, 0, 45)

[node name="wall_159" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-0.288911, 0, 1.07296, 0, 1, 0, -0.941929, 0, -0.329103, 50, 0, 47.5)

[node name="tree_0" type="Node3D" parent="NavigationRegion3D" instance=ExtResource("15_003du")]
transform = Transform3D(2.5, -0.00145593, -0.00167976, 0.00145725, 2.5, 0.00196474, 0.00167861, -0.00196571, 2.5, 15.5375, 0, -17.0487)
script = ExtResource("18_oh2bt")
highlight_material = SubResource("StandardMaterial3D_01ds2")
progress_bar_scene = ExtResource("19_fcnrs")

[node name="PatrolPoint_0" type="Node3D" parent="NavigationRegion3D"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 16.0677, 1, -11.3873)
script = ExtResource("15_6ly8b")

[node name="tree_1" type="Node3D" parent="NavigationRegion3D" instance=ExtResource("15_003du")]
transform = Transform3D(2.5, -0.00145593, -0.00167976, 0.00145725, 2.5, 0.00196474, 0.00167861, -0.00196571, 2.5, -17.0707, 0, 3.15482)
script = ExtResource("18_oh2bt")
highlight_material = SubResource("StandardMaterial3D_01ds2")
preset_burnt_out_type = 1
progress_bar_scene = ExtResource("19_fcnrs")

[node name="PatrolPoint_1" type="Node3D" parent="NavigationRegion3D"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -18.0296, 1, 10.8432)
script = ExtResource("15_6ly8b")
point_id = 1

[node name="tree_2" type="Node3D" parent="NavigationRegion3D" instance=ExtResource("15_003du")]
transform = Transform3D(2.5, -0.00145593, -0.00167976, 0.00145725, 2.5, 0.00196474, 0.00167861, -0.00196571, 2.5, -12.5476, 0, -14.4899)
script = ExtResource("18_oh2bt")
highlight_material = SubResource("StandardMaterial3D_01ds2")
progress_bar_scene = ExtResource("19_fcnrs")

[node name="PatrolPoint_2" type="Node3D" parent="NavigationRegion3D"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -15.7996, 1, -9.93203)
script = ExtResource("15_6ly8b")
point_id = 2

[node name="tree_3" type="Node3D" parent="NavigationRegion3D" instance=ExtResource("15_003du")]
transform = Transform3D(2.5, -0.00145593, -0.00167976, 0.00145725, 2.5, 0.00196474, 0.00167861, -0.00196571, 2.5, -1.51592, 0, -12.0669)
script = ExtResource("18_oh2bt")
highlight_material = SubResource("StandardMaterial3D_01ds2")
progress_bar_scene = ExtResource("19_fcnrs")

[node name="PatrolPoint_3" type="Node3D" parent="NavigationRegion3D"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -1.87114, 1, -13.9856)
script = ExtResource("15_6ly8b")
point_id = 3

[node name="chest_0" type="Area3D" parent="NavigationRegion3D" instance=ExtResource("14_1ks1q")]
transform = Transform3D(-1.99997, 0, -0.0107876, 0, 2, 0, 0.0107876, 0, -1.99997, -14.6422, 0, -26.6943)
collision_layer = 8
collision_mask = 16
script = ExtResource("21_1ks1q")
interaction_distance = 1.5
item_resource = ExtResource("22_xcdtp")
unlock_sound = ExtResource("23_cin4e")

[node name="rock_0" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("21_xcdtp")]
transform = Transform3D(0.588202, 0, -0.892738, 0, 1.06909, 0, 0.892738, 0, 0.588202, -39.6265, 0.01, -4.41002)
collision_layer = 2
collision_mask = 0

[node name="rock_1" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("21_xcdtp")]
transform = Transform3D(1.15507, 0, -0.160634, 0, 1.16618, 0, 0.160634, 0, 1.15507, 21.2317, 0.01, -34.2725)
collision_layer = 2
collision_mask = 0

[node name="rock_2" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("21_xcdtp")]
transform = Transform3D(-0.16029, 0, -0.995349, 0, 1.00817, 0, 0.995349, 0, -0.16029, -37.4252, 0.01, -26.5528)
collision_layer = 2
collision_mask = 0

[node name="rock_3" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("21_xcdtp")]
transform = Transform3D(0.324009, 0, -1.02118, 0, 1.07135, 0, 1.02118, 0, 0.324009, -8.22735, 0.01, 41.7409)
collision_layer = 2
collision_mask = 0

[node name="rock_4" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("21_xcdtp")]
transform = Transform3D(-1.15688, 0, 0.0781867, 0, 1.15952, 0, -0.0781867, 0, -1.15688, 11.6813, 0.01, 22.4163)
collision_layer = 2
collision_mask = 0

[node name="rock_5" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("21_xcdtp")]
transform = Transform3D(-0.523068, 0, 0.799978, 0, 0.955806, 0, -0.799978, 0, -0.523068, -35.2839, 0.01, -24.3746)
collision_layer = 2
collision_mask = 0

[node name="rock_6" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("21_xcdtp")]
transform = Transform3D(-1.13458, 0, -0.0575275, 0, 1.13604, 0, 0.0575275, 0, -1.13458, 24.44, 0.01, 11.6856)
collision_layer = 2
collision_mask = 0

[node name="rock_7" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("21_xcdtp")]
transform = Transform3D(0.208351, 0, 0.827638, 0, 0.85346, 0, -0.827638, 0, 0.208351, 3.73845, 0.01, 14.3798)
collision_layer = 2
collision_mask = 0

[node name="rock_8" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("21_xcdtp")]
transform = Transform3D(-0.237327, 0, 1.03285, 0, 1.05976, 0, -1.03285, 0, -0.237327, -16.9609, 0.01, 24.0068)
collision_layer = 2
collision_mask = 0

[node name="rock_9" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("21_xcdtp")]
transform = Transform3D(0.853502, 0, -0.641452, 0, 1.06767, 0, 0.641452, 0, 0.853502, 43.2041, 0.01, -10.6745)
collision_layer = 2
collision_mask = 0

[node name="rock_10" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("21_xcdtp")]
transform = Transform3D(0.617665, 0, -0.969565, 0, 1.14959, 0, 0.969565, 0, 0.617665, -4.02959, 0.01, -43.0209)
collision_layer = 2
collision_mask = 0

[node name="rock_11" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("21_xcdtp")]
transform = Transform3D(-0.285087, 0, -0.857919, 0, 0.904047, 0, 0.857919, 0, -0.285087, 30.5201, 0.01, -22.1346)
collision_layer = 2
collision_mask = 0

[node name="rock_12" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("21_xcdtp")]
transform = Transform3D(0.26236, 0, 0.834277, 0, 0.874557, 0, -0.834277, 0, 0.26236, 18.9935, 0.01, 8.81352)
collision_layer = 2
collision_mask = 0

[node name="rock_13" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("21_xcdtp")]
transform = Transform3D(-0.958947, 0, 0.365594, 0, 1.02627, 0, -0.365594, 0, -0.958947, -10.3138, 0.01, 37.9598)
collision_layer = 2
collision_mask = 0

[node name="rock_14" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("21_xcdtp")]
transform = Transform3D(-0.190747, 0, 0.827041, 0, 0.848752, 0, -0.827041, 0, -0.190747, 33.3271, 0.01, 5.34343)
collision_layer = 2
collision_mask = 0

[node name="barrel_0" type="StaticBody3D" parent="NavigationRegion3D" groups=["destructible"] instance=ExtResource("22_cin4e")]
transform = Transform3D(0.668369, 0, -0.677365, 0, 0.951599, 0, 0.677365, 0, 0.668369, 22.5535, 0, -10.5808)
collision_layer = 3
collision_mask = 3

[node name="barrel_1" type="StaticBody3D" parent="NavigationRegion3D" groups=["destructible"] instance=ExtResource("22_cin4e")]
transform = Transform3D(1.11184, 0, 0.0259347, 0, 1.11214, 0, -0.0259347, 0, 1.11184, 2.35653, 0, -31.0017)
collision_layer = 3
collision_mask = 3

[node name="decoration_0" type="Node3D" parent="NavigationRegion3D" instance=ExtResource("23_6j2fk")]
transform = Transform3D(-0.681261, 0, 0.136543, 0, 0.69481, 0, -0.136543, 0, -0.681261, 22.4135, 0, -43.5649)

[node name="decoration_1" type="Node3D" parent="NavigationRegion3D" instance=ExtResource("23_6j2fk")]
transform = Transform3D(0.532776, 0, 0.938071, 0, 1.07881, 0, -0.938071, 0, 0.532776, -8.25782, 0, -27.0157)

[node name="decoration_2" type="Node3D" parent="NavigationRegion3D" instance=ExtResource("23_6j2fk")]
transform = Transform3D(0.605167, 0, -0.480126, 0, 0.772495, 0, 0.480126, 0, 0.605167, 0.876196, 0, -11.0496)

[node name="decoration_3" type="Node3D" parent="NavigationRegion3D" instance=ExtResource("23_6j2fk")]
transform = Transform3D(0.92468, 0, 0.891866, 0, 1.2847, 0, -0.891866, 0, 0.92468, 35.5954, 0, -44.8526)

[node name="decoration_4" type="Node3D" parent="NavigationRegion3D" instance=ExtResource("23_6j2fk")]
transform = Transform3D(0.458782, 0, 0.398913, 0, 0.607957, 0, -0.398913, 0, 0.458782, 19.9626, 0, 39.1462)

[node name="decoration_5" type="Node3D" parent="NavigationRegion3D" instance=ExtResource("23_6j2fk")]
transform = Transform3D(0.60395, 0, -0.538847, 0, 0.80939, 0, 0.538847, 0, 0.60395, -32.9252, 0, 33.0516)

[node name="decoration_6" type="Node3D" parent="NavigationRegion3D" instance=ExtResource("23_6j2fk")]
transform = Transform3D(0.993273, 0, 0.617296, 0, 1.16946, 0, -0.617296, 0, 0.993273, 16.9376, 0, 37.3298)

[node name="decoration_7" type="Node3D" parent="NavigationRegion3D" instance=ExtResource("23_6j2fk")]
transform = Transform3D(-0.0481707, 0, -0.647827, 0, 0.649616, 0, 0.647827, 0, -0.0481707, -15.9918, 0, 22.9206)

[node name="decoration_8" type="Node3D" parent="NavigationRegion3D" instance=ExtResource("23_6j2fk")]
transform = Transform3D(0.862078, 0, 0.941072, 0, 1.27624, 0, -0.941072, 0, 0.862078, 39.1574, 0, 25.6684)

[node name="decoration_9" type="Node3D" parent="NavigationRegion3D" instance=ExtResource("23_6j2fk")]
transform = Transform3D(-1.02625, 0, -0.106585, 0, 1.03177, 0, 0.106585, 0, -1.02625, 16.6901, 0, 21.2075)

[node name="decoration_10" type="Node3D" parent="NavigationRegion3D" instance=ExtResource("23_6j2fk")]
transform = Transform3D(-0.884004, 0, 0.017991, 0, 0.884187, 0, -0.017991, 0, -0.884004, 32.5623, 0, 36.9596)

[node name="decoration_11" type="Node3D" parent="NavigationRegion3D" instance=ExtResource("23_6j2fk")]
transform = Transform3D(0.453321, 0, -0.801386, 0, 0.920717, 0, 0.801386, 0, 0.453321, -2.57163, 0, 39.9241)

[node name="decoration_12" type="Node3D" parent="NavigationRegion3D" instance=ExtResource("23_6j2fk")]
transform = Transform3D(0.854486, 0, -0.23794, 0, 0.886995, 0, 0.23794, 0, 0.854486, -18.9124, 0, -20.056)

[node name="decoration_13" type="Node3D" parent="NavigationRegion3D" instance=ExtResource("23_6j2fk")]
transform = Transform3D(-1.12684, 0, -0.259609, 0, 1.15636, 0, 0.259609, 0, -1.12684, 17.8651, 0, -25.4713)

[node name="decoration_14" type="Node3D" parent="NavigationRegion3D" instance=ExtResource("23_6j2fk")]
transform = Transform3D(-0.642488, 0, 0.0807191, 0, 0.647539, 0, -0.0807191, 0, -0.642488, 30.7856, 0, 0.254235)

[node name="decoration_15" type="Node3D" parent="NavigationRegion3D" instance=ExtResource("23_6j2fk")]
transform = Transform3D(-0.187249, 0, -1.07696, 0, 1.09312, 0, 1.07696, 0, -0.187249, 28.4816, 0, 33.1243)

[node name="decoration_16" type="Node3D" parent="NavigationRegion3D" instance=ExtResource("23_6j2fk")]
transform = Transform3D(-1.00797, 0, -0.681737, 0, 1.21687, 0, 0.681737, 0, -1.00797, -16.2619, 0, -32.5544)

[node name="decoration_17" type="Node3D" parent="NavigationRegion3D" instance=ExtResource("23_6j2fk")]
transform = Transform3D(-1.35074, 0, 0.0392482, 0, 1.35131, 0, -0.0392482, 0, -1.35074, 42.8431, 0, 29.8726)

[node name="decoration_18" type="Node3D" parent="NavigationRegion3D" instance=ExtResource("23_6j2fk")]
transform = Transform3D(0.233496, 0, 0.713152, 0, 0.750404, 0, -0.713152, 0, 0.233496, 23.3312, 0, 11.7198)

[node name="decoration_19" type="Node3D" parent="NavigationRegion3D" instance=ExtResource("23_6j2fk")]
transform = Transform3D(0.24853, 0, 0.953646, 0, 0.985499, 0, -0.953646, 0, 0.24853, 28.4856, 0, 19.4022)

[node name="PatrolPointManager" type="Node" parent="."]
script = ExtResource("24_j2uky")
