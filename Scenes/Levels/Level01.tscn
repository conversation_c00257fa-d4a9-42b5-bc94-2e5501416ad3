[gd_scene load_steps=38 format=3 uid="uid://c0rj4dwj18nv8"]

[ext_resource type="Script" uid="uid://bo0h8t35yls55" path="res://Scripts/Levels/Level01.gd" id="1_hk2b6"]
[ext_resource type="PackedScene" uid="uid://d2xyw4d37ymv4" path="res://Scenes/Minimap.tscn" id="2_j32d5"]
[ext_resource type="PackedScene" uid="uid://ctxvyh1qr52ue" path="res://Scenes/PlayerAvatar.tscn" id="3_0f42i"]
[ext_resource type="PackedScene" uid="uid://do1sypgxbecbd" path="res://Scenes/ItemBoxes.tscn" id="4_swvig"]
[ext_resource type="PackedScene" uid="uid://b4l4phqqbvprm" path="res://Scenes/SkillBoxUI.tscn" id="5_dbohx"]
[ext_resource type="Script" uid="uid://f54r1khulcvh" path="res://Scripts/ModularLevelGenerator.gd" id="6_6ly8b"]
[ext_resource type="Resource" uid="uid://cvji54t3pjwfe" path="res://Resources/LevelConfigs/AllModulesConfig.tres" id="7_r5102"]
[ext_resource type="PackedScene" uid="uid://d2b5h1mlxxy7d" path="res://Scenes/FogOfWar.tscn" id="9_003du"]
[ext_resource type="PackedScene" uid="uid://b88l8pk1ebe1x" path="res://Scenes/player.tscn" id="11_82jtv"]
[ext_resource type="PackedScene" uid="uid://c3h8fj2xsp5oy" path="res://Scenes/Prefabs/Chest.tscn" id="14_1ks1q"]
[ext_resource type="PackedScene" uid="uid://dwusy8dd8usvo" path="res://Scenes/Prefabs/Wall.tscn" id="14_df8b0"]
[ext_resource type="PackedScene" uid="uid://crtnthqkksmri" path="res://Scenes/Prefabs/Tree.tscn" id="15_003du"]
[ext_resource type="Script" uid="uid://c3tr23vwvnmwf" path="res://Scripts/PatrolPoint.gd" id="15_6ly8b"]
[ext_resource type="Texture2D" uid="uid://c2ny0yi07rvcf" path="res://Environment/Floor/Floor01_Rocks_BaseColor.png" id="15_df8b0"]
[ext_resource type="Texture2D" uid="uid://dhfikoo16s5n0" path="res://Environment/Floor/Rocks_Metallic.png" id="16_003du"]
[ext_resource type="PackedScene" uid="uid://ddttv643pel23" path="res://Environment/Floor/Floor01_Custom.tscn" id="17_2bvpm"]
[ext_resource type="Resource" path="res://Resources/Items/Trap.tres" id="17_j32d5"]
[ext_resource type="Resource" path="res://Resources/Items/Torch.tres" id="18_j32d5"]
[ext_resource type="Script" uid="uid://pfva6p6rllgp" path="res://Scripts/Tree.gd" id="18_oh2bt"]
[ext_resource type="PackedScene" uid="uid://rvgn0irsuwao" path="res://Scenes/EnemyBoy01.tscn" id="19_0f42i"]
[ext_resource type="PackedScene" uid="uid://brxqv3iv3op6u" path="res://Scenes/ProgressBar3D.tscn" id="19_fcnrs"]
[ext_resource type="Script" uid="uid://dhw1dgex7wmiy" path="res://Scripts/ChestInteractable.gd" id="21_1ks1q"]
[ext_resource type="PackedScene" uid="uid://ervckea7fk57" path="res://Scenes/Enemy02.tscn" id="21_dbohx"]
[ext_resource type="PackedScene" uid="uid://b5dqjsb63wbhl" path="res://Scenes/Prefabs/Rock.tscn" id="21_xcdtp"]
[ext_resource type="PackedScene" uid="uid://bdq3b4e0mlgo4" path="res://Scenes/Prefabs/Barrel.tscn" id="22_cin4e"]
[ext_resource type="Resource" uid="uid://bjih53ivkm0qi" path="res://Resources/GasItem.tres" id="22_xcdtp"]
[ext_resource type="PackedScene" uid="uid://c6k7j3t3flhst" path="res://Scenes/Prefabs/Decoration.tscn" id="23_6j2fk"]
[ext_resource type="AudioStream" uid="uid://dgskkp7epyn0x" path="res://ChestOpening.mp3" id="23_cin4e"]
[ext_resource type="Script" uid="uid://de18ote8otj6u" path="res://Scripts/PatrolPointManager.gd" id="24_j2uky"]
[ext_resource type="Script" uid="uid://dt3st7cac7lok" path="res://Scripts/UI/UIManager.gd" id="25_ui_manager"]
[ext_resource type="Script" path="res://Scripts/CameraFollower.gd" id="26_camera_follower"]

[sub_resource type="ProceduralSkyMaterial" id="ProceduralSkyMaterial_gbvua"]
sky_top_color = Color(0.2, 0.4, 0.8, 1)
sky_horizon_color = Color(0.5, 0.7, 0.9, 1)
ground_bottom_color = Color(0.3, 0.5, 0.7, 1)
ground_horizon_color = Color(0.5, 0.7, 0.9, 1)

[sub_resource type="Sky" id="Sky_m0r78"]
sky_material = SubResource("ProceduralSkyMaterial_gbvua")

[sub_resource type="Environment" id="Environment_j4i7h"]
background_mode = 2
sky = SubResource("Sky_m0r78")
ambient_light_source = 3
ambient_light_color = Color(0.9, 0.9, 0.95, 1)
ambient_light_energy = 0.5
tonemap_mode = 2
ssao_enabled = true
ssao_radius = 2.0
ssao_intensity = 1.5
glow_enabled = true
glow_strength = 1.2

[sub_resource type="NavigationMesh" id="NavigationMesh_xcdtp"]
vertices = PackedVector3Array(-94.4663, 0.37304, -106.605, -95.2163, 0.37304, -106.105, -95.2163, 0.37304, -105.105, -87.9663, 0.37304, -104.605, -88.4663, 0.37304, -105.105, -93.2163, 0.37304, -106.355, -76.2163, 0.37304, -101.105, -75.7163, 0.37304, -101.355, -75.9663, 0.37304, -103.105, -78.4663, 0.37304, -103.105, -79.7163, 0.37304, -106.605, -79.7163, 0.37304, -107.355, -81.4663, 0.37304, -107.605, -81.7163, 0.37304, -108.105, -83.7163, 0.37304, -107.105, -83.7163, 0.37304, -105.855, -75.2163, 0.37304, -99.3546, -76.2163, 0.37304, -99.8546, -79.7163, 0.37304, -104.105, -79.2163, 0.37304, -106.355, -79.4663, 0.37304, -74.6046, -76.7163, 0.37304, -75.3546, -75.9663, 0.37304, -78.8546, -90.9663, 0.37304, -109.105, -92.7163, 0.37304, -108.105, -92.7163, 0.37304, -106.855, -88.2163, 0.37304, -107.605, -88.9663, 0.37304, -108.605, -79.2163, 0.37304, -103.605, -84.2163, 0.37304, -105.355, -102.216, 0.37304, -90.3546, -102.466, 0.37304, -85.8546, -99.4663, 0.37304, -84.6046, -96.2163, 0.37304, -104.605, -74.9663, 0.37304, -79.6046, -99.4663, 0.37304, -73.8546, -79.7163, 0.37304, -73.8546, -6.46626, 0.37304, -107.355, -6.46626, 0.37304, -108.105, -7.96626, 0.37304, -108.355, -6.21626, 0.37304, -104.855, -5.96626, 0.37304, -107.105, -8.21626, 0.37304, -108.855, -10.4663, 0.37304, -107.855, -10.4663, 0.37304, -106.605, -2.46626, 0.37304, -102.105, -2.71626, 0.37304, -103.855, -4.96626, 0.37304, -103.855, -13.7163, 0.37304, -104.605, -14.7163, 0.37304, -104.605, -14.4663, 0.37304, -79.3546, -8.96626, 0.37304, -80.3546, -10.9663, 0.37304, -106.105, -12.4663, 0.37304, -106.105, 45.5337, 0.37304, -108.355, 45.2837, 0.37304, -108.855, 43.2837, 0.37304, -107.855, 43.2837, 0.37304, -106.605, 48.5337, 0.37304, -103.855, 40.0337, 0.37304, -104.605, 39.2837, 0.37304, -104.605, 39.2837, 0.37304, -79.6046, 39.2837, 0.37304, -94.3105, 39.2837, 0.87304, -92.8399, 39.2837, 0.37304, -89.8988, 40.759, 0.37304, -94.8599, 42.7837, 0.37304, -106.105, 41.2837, 0.37304, -106.105, 47.5337, 0.37304, -104.855, 47.0337, 0.37304, -108.355, 40.0337, 0.37304, -78.6046, 46.7837, 0.37304, -77.3546, 49.2837, 0.37304, -77.8546, 49.2837, 0.37304, -103.855, 62.7837, 0.37304, -107.355, 62.7837, 0.37304, -108.105, 61.5337, 0.37304, -108.105, 61.2837, 0.37304, -108.605, 59.5337, 0.37304, -108.105, 58.2837, 0.37304, -105.855, 56.2837, 0.37304, -105.605, 56.2837, 0.37304, -104.355, 64.2837, 0.37304, -103.605, 63.0337, 0.37304, -104.605, 63.2837, 0.37304, -107.105, 67.0337, 0.37304, -101.855, 66.7837, 0.37304, -103.605, 55.2837, 0.37304, -103.855, 55.2837, 0.37304, -79.1046, 64.0337, 0.37304, -81.1046, 56.7837, 0.37304, -78.8546, -41.2163, 0.37304, -76.8546, -40.7163, 0.37304, -76.8546, -40.4663, 0.37304, -78.1046, -41.7163, 0.37304, -107.855, -41.9663, 0.37304, -108.355, -43.7163, 0.37304, -107.855, -39.7163, 0.37304, -106.855, -40.4663, 0.37304, -107.855, -44.7163, 0.37304, -105.605, -46.7163, 0.37304, -105.355, -47.2163, 0.37304, -104.355, -39.9663, 0.37304, -104.355, -47.2163, 0.37304, -78.3546, -38.9663, 0.37304, -103.355, -37.9663, 0.37304, -77.8546, -37.7163, 0.37304, -103.355, -46.7163, 0.37304, -77.8546, 94.2837, 0.37304, -105.355, 93.5337, 0.37304, -104.855, 93.5337, 0.37304, -103.605, 101.534, 0.37304, -102.855, 100.284, 0.37304, -104.105, 95.5337, 0.37304, -105.105, 97.7837, 0.37304, -107.355, 96.7837, 0.37304, -107.355, 96.2837, 0.37304, -105.855, 100.034, 0.37304, -107.355, 98.0337, 0.37304, -107.855, 100.034, 0.37304, -106.605, 105.784, 0.37304, -98.6046, 105.034, 0.37304, -99.8546, 104.034, 0.37304, -100.355, 105.534, 0.37304, -96.1046, 108.534, 0.37304, -89.3546, 109.284, 0.37304, -95.3546, 106.784, 0.37304, -95.1046, 105.784, 0.37304, -89.1046, 100.534, 0.37304, -106.355, 102.534, 0.37304, -87.3546, 105.034, 0.37304, -87.1046, 105.034, 0.37304, -88.3546, 99.7837, 0.37304, -85.1046, 102.034, 0.37304, -85.1046, 102.034, 0.37304, -86.8546, 104.034, 0.37304, -103.105, 92.7837, 0.37304, -103.105, 99.2837, 0.37304, -84.3546, -106.216, 0.37304, -102.105, -107.466, 0.37304, -101.355, -107.216, 0.37304, -100.105, -99.7163, 0.37304, -106.855, -101.466, 0.37304, -105.855, -101.466, 0.37304, -104.605, -97.7163, 0.37304, -106.355, -105.216, 0.37304, -93.3546, -103.966, 0.37304, -91.6046, -102.966, 0.37304, -91.3546, -101.966, 0.37304, -104.105, -105.716, 0.37304, -103.605, -108.466, 0.37304, -99.3546, -108.466, 0.37304, -94.6046, -16.9663, 0.37304, -106.105, -17.4663, 0.37304, -106.605, -19.2163, 0.37304, -105.605, -15.7163, 0.37304, -106.105, -19.2163, 0.37304, -104.355, -20.2163, 0.37304, -103.855, -19.2163, 0.37304, -78.1046, 85.0337, 0.37304, -103.355, 85.2837, 0.37304, -105.105, 84.7837, 0.37304, -105.855, 82.2837, 0.37304, -106.105, 80.2837, 0.37304, -104.105, 82.5337, 0.37304, -106.605, 81.2837, 0.37304, -106.355, 78.2837, 0.37304, -103.855, 77.7837, 0.37304, -102.355, 85.5337, 0.37304, -102.855, 91.7837, 0.37304, -103.605, 91.2837, 0.37304, -105.105, 89.2837, 0.37304, -105.605, 87.5337, 0.37304, -104.605, 86.7837, 0.37304, -102.855, 77.0337, 0.37304, -102.105, 81.0337, 0.37304, -76.1046, 99.2837, 0.37304, -80.3546, 76.5337, 0.37304, -77.8546, 29.5337, 0.37304, -101.605, 28.5337, 0.37304, -101.105, 28.7837, 0.37304, -100.105, 38.0337, 0.37304, -106.105, 36.2837, 0.37304, -106.355, 36.0337, 0.37304, -105.855, 38.2837, 0.37304, -105.105, 34.0337, 0.37304, -103.855, 30.0337, 0.37304, -103.105, 34.7837, 0.37304, -105.855, 33.7837, 0.37304, -78.3546, 36.2591, 0.37304, -94.85, 52.5337, 0.37304, -105.855, 52.0337, 0.37304, -106.355, 51.7837, 0.37304, -105.855, 54.0337, 0.37304, -104.855, 53.7837, 0.37304, -105.855, 50.7837, 0.37304, -105.855, 50.2837, 0.37304, -104.355, -50.7163, 0.37304, -105.605, -51.2163, 0.37304, -106.105, -52.9663, 0.37304, -105.105, -52.9663, 0.37304, -103.855, -57.9663, 0.37304, -101.105, -59.2163, 0.37304, -100.355, -59.7163, 0.37304, -99.3546, -48.7163, 0.37304, -104.105, -53.4663, 0.37304, -103.355, -49.2163, 0.37304, -105.605, -61.9663, 0.37304, -78.8546, -61.4663, 0.37304, -77.8546, -54.7163, 0.37304, -78.3546, -62.7163, 0.37304, -99.3546, -62.7163, 0.37304, -78.8546, -57.4663, 0.37304, -102.605, -28.9663, 0.37304, -103.605, -28.9663, 0.37304, -104.355, -30.4663, 0.37304, -104.855, -33.4663, 0.37304, -102.105, -34.7163, 0.37304, -102.355, -35.4663, 0.37304, -101.855, -28.7163, 0.37304, -101.105, -28.4663, 0.37304, -103.355, -32.4663, 0.37304, -104.355, -36.2163, 0.37304, -77.6046, -32.7163, 0.37304, -76.8546, -31.9663, 0.37304, -78.6046, -29.9663, 0.37304, -78.3546, 75.5337, 0.37304, -104.105, 73.7837, 0.37304, -104.355, 73.5337, 0.37304, -103.855, 72.0337, 0.37304, -103.605, 72.0337, 0.37304, -102.355, 71.5337, 0.37304, -101.855, 70.0337, 0.37304, -101.855, 69.0337, 0.37304, -101.105, 71.0337, 0.37304, -79.8546, 69.7837, 0.37304, -79.8546, 10.5337, 0.37304, -101.355, 10.5337, 0.37304, -102.605, 9.78374, 0.37304, -103.605, 7.78374, 0.37304, -104.105, 6.03374, 0.37304, -103.105, 6.03374, 0.37304, -101.855, 5.53374, 0.37304, -101.355, 3.53374, 0.37304, -101.105, 3.53374, 0.37304, -99.8546, 11.5337, 0.37304, -100.855, 2.53374, 0.37304, -78.3546, 7.78374, 0.37304, -78.3546, 10.2837, 0.37304, -78.8546, 2.53374, 0.37304, -99.3546, -20.7163, 0.37304, -77.3546, -19.7163, 0.37304, -77.3546, -23.7163, 0.37304, -103.105, -24.4663, 0.37304, -101.355, -24.4663, 0.37304, -78.3546, 19.0337, 0.37304, -103.605, 17.2837, 0.37304, -102.605, 17.2837, 0.37304, -101.355, 21.2837, 0.37304, -102.855, 16.7837, 0.37304, -100.855, 15.0337, 0.37304, -100.855, 13.7837, 0.37304, -99.3546, 22.0337, 0.37304, -99.3546, 21.5337, 0.37304, -99.8546, 14.5337, 0.37304, -78.8546, 18.5337, 0.37304, -79.6046, 19.0337, 0.37304, -77.8546, 25.2837, 0.37304, -78.6046, 24.7837, 0.37304, -99.3546, -36.4663, 0.37304, -102.605, -36.4663, 0.37304, -103.355, 1.03374, 0.37304, -101.355, -0.966255, 0.37304, -101.855, -1.21626, 0.37304, -101.355, 1.28374, 0.37304, -100.355, -1.96626, 0.37304, -101.355, -8.21626, 0.37304, -79.8546, -7.71626, 0.37304, -77.6046, -72.2163, 0.37304, -101.855, -73.9663, 0.37304, -100.855, -74.4663, 0.37304, -99.3546, -68.9663, 0.37304, -99.3546, -70.4663, 0.37304, -101.355, -67.7163, 0.37304, -77.6046, 65.2837, 0.37304, -78.6046, 69.0337, 0.37304, -78.6046, 64.7837, 0.37304, -80.6046, 67.5337, 0.37304, -101.105, -26.9663, 0.37304, -77.3546, -25.7163, 0.37304, -77.3546, -25.4663, 0.37304, -78.3546, -27.4663, 0.37304, -99.8546, -26.4663, 0.37304, -99.8546, 12.7837, 0.37304, -99.3546, 12.2837, 0.37304, -100.855, 10.7837, 0.37304, -78.6046, 10.7837, 0.37304, -77.8546, 30.7837, 0.37304, -76.6046, 31.2837, 0.37304, -76.6046, 31.5337, 0.37304, -77.8546, 26.5337, 0.37304, -78.6046, 26.7837, 0.37304, -77.8546, 28.2837, 0.37304, -99.3546, 103.284, 0.37304, -81.6046, 102.284, 0.37304, -81.6046, 101.784, 0.37304, -80.1046, 105.534, 0.37304, -81.6046, 103.534, 0.37304, -82.1046, 103.034, 0.37304, -69.3546, 105.284, 0.37304, -69.1046, 105.284, 0.37304, -70.1046, 109.784, 0.37304, -75.8546, 109.534, 0.37304, -77.3546, 107.034, 0.37304, -77.1046, 109.034, 0.37304, -74.3546, 106.284, 0.37304, -80.6046, 106.034, 0.37304, -78.1046, 100.784, 0.37304, -79.3546, 102.534, 0.37304, -68.6046, 106.034, 0.37304, -71.1046, 108.784, 0.37304, -71.1046, 81.5337, 0.37304, -75.6046, 100.034, 0.37304, -79.6046, 63.7837, 3.62304, -78.3546, 63.7837, 3.62304, -79.3546, 63.0337, 3.62304, -79.6046, 55.0337, 3.87304, -77.8546, 52.2837, 3.87304, -77.1046, 53.2837, 4.12304, -74.1046, 54.7837, 4.12304, -73.6046, 56.5337, 3.62304, -77.3546, 55.7837, 4.12304, -77.6046, 55.3671, 4.12304, -74.8546, 66.5337, 3.62304, -72.3546, 66.5337, 3.62304, -77.3546, 64.5337, 3.62304, -77.3546, 60.0337, 3.62304, -72.8546, 61.5337, 3.62304, -72.8546, 61.7837, 3.62304, -72.3546, 55.2837, 3.62304, -71.3546, 78.2837, 0.37304, -64.1046, 78.5337, 0.37304, -63.1046, 79.5337, 0.37304, -62.3546, 79.7837, 0.62304, -69.6046, 79.0337, 0.37304, -68.3546, 99.7837, 0.37304, -65.8546, 102.034, 0.37304, -66.1046, 78.2837, 0.37304, -68.3546, 99.2837, 0.37304, -65.3546, 99.2837, 0.37304, -62.6046, -9.21626, 3.62304, -77.3546, -9.46626, 3.62304, -79.1046, -9.96626, 3.62304, -79.1046, -9.96626, 3.62304, -73.3546, -8.21626, 3.62304, -73.3546, -8.21626, 3.62304, -76.3546, -18.7163, 3.62304, -75.8546, -19.7163, 3.62304, -75.8546, -20.4663, 3.62304, -72.8546, -17.9663, 3.62304, -77.1046, -15.9663, 3.62304, -71.6046, -65.2163, 3.62304, -74.1046, -63.9663, 3.62304, -74.3546, -64.4663, 3.62304, -77.1046, -68.2163, 3.62304, -76.3546, -65.9663, 3.62304, -73.3546, -72.2163, 3.62304, -71.8546, -66.7163, 3.62304, -70.3546, -74.7163, 3.62304, -78.3546, -75.4663, 3.62304, -74.8546, 10.2837, 3.62304, -76.6046, 9.53374, 3.62304, -77.6046, 8.03374, 3.62304, -77.1046, 8.53374, 3.62304, -74.3546, 9.78374, 3.62304, -74.6046, 11.5337, 3.62304, -76.6046, 10.7837, 3.62304, -72.3546, 17.0337, 3.62304, -73.8546, 17.7837, 3.62304, -77.3546, 17.2837, 3.62304, -78.3546, 39.2837, 3.62304, -73.6046, 39.7837, 3.62304, -76.1046, 39.0337, 3.62304, -76.6046, 32.2837, 3.62304, -75.1046, 30.5337, 3.62304, -75.3546, 30.0337, 3.62304, -72.6046, 33.0337, 3.62304, -71.8546, 38.5337, 3.62304, -78.3546, 33.0337, 3.62304, -76.8546, 73.0337, 3.62304, -77.6046, 70.2837, 3.62304, -78.3546, 69.7837, 3.62304, -77.3546, 72.2837, 3.62304, -72.1046, -62.2163, 3.62304, -76.6046, -62.9663, 3.62304, -77.6046, -62.2163, 3.62304, -74.6046, -61.7163, 3.62304, -73.1046, -54.7163, 3.62304, -73.1046, -55.2163, 3.62304, -76.8546, -47.9663, 3.62304, -72.8546, -47.4663, 3.62304, -76.1046, -47.9663, 3.62304, -77.1046, -54.4663, 3.62304, -72.6046, -47.4663, 3.62304, -73.8546, 73.0337, 3.62304, -67.6046, 77.0337, 3.62304, -67.3546, 77.0337, 3.62304, -69.3546, 73.2837, 3.62304, -71.3546, 78.5337, 3.62304, -70.1046, 80.0337, 3.62304, -75.1046, -26.9663, 3.62304, -71.8546, -26.4663, 3.62304, -71.8546, -25.7163, 3.62304, -73.8546, -31.9663, 3.62304, -75.6046, -33.2163, 3.62304, -75.6046, -31.7163, 3.62304, -73.1046, -26.4663, 3.62304, -75.8546, -31.2163, 3.62304, -77.3546, -24.7163, 3.62304, -76.8546, -25.2163, 3.62304, -75.8546, 18.2837, 3.62304, -76.6046, 27.5337, 3.62304, -73.6046, 26.2837, 3.62304, -76.6046, 17.7837, 3.62304, -72.8546, 25.7837, 3.62304, -77.3546, 7.03374, 3.62304, -74.1046, -2.96626, 3.62304, -73.6046, -2.46626, 3.62304, -72.8546, 0.283745, 3.62304, -72.8546, 6.53374, 3.62304, -73.3546, 0.0337448, 3.62304, -76.8546, 40.5337, 3.62304, -72.8546, 41.0337, 3.87304, -77.1046, 46.7837, 3.87304, -71.8546, 47.5337, 4.12304, -72.8546, 45.5337, 3.87304, -76.1046, 48.2837, 4.12304, -76.1046, -41.9663, 3.62304, -72.8546, -41.2163, 3.62304, -72.8546, -40.7163, 3.62304, -73.8546, -33.7163, 3.62304, -72.6046, -32.2163, 3.62304, -72.6046, -37.9663, 3.62304, -76.6046, -39.4663, 3.62304, -76.6046, -39.9663, 3.62304, -75.6046, -45.9663, 3.62304, -76.6046, -14.7163, 0.37304, -75.6046, -15.4663, 0.37304, -74.8546, -14.9663, 0.37304, -74.6046, -13.4663, 0.37304, -75.3546, -11.2163, 0.37304, -75.6046, -11.2163, 0.37304, -76.6046, -11.7163, 0.37304, -76.6046, 35.7837, 0.37304, -75.3546, 34.7837, 0.37304, -75.3546, 35.2837, 0.37304, -74.8546, 38.7837, 0.37304, -75.6046, 38.7837, 0.37304, -76.3546, 38.0337, 0.37304, -76.6046, 37.0337, 0.37304, -75.6046, 60.7837, 0.37304, -75.8546, 60.7837, 0.37304, -76.6046, 60.2837, 0.37304, -76.6046, 57.0337, 0.37304, -75.3546, 57.5337, 0.37304, -74.6046, 14.2837, 0.37304, -75.8546, 16.0337, 0.37304, -75.8546, 15.7837, 0.37304, -76.3546, 41.0337, 0.37304, -74.6046, 46.5337, 0.37304, -73.6046, 46.2837, 0.37304, -74.8546, 41.0337, 0.37304, -75.8546, 52.7837, 0.37304, -75.3546, 52.5337, 0.37304, -75.8546, 51.7837, 0.37304, -75.8546, 48.5337, 0.37304, -75.1046, 48.5337, 0.37304, -74.3546, 49.5337, 0.37304, -74.3546, 70.0337, 0.37304, -75.6046, 71.0337, 0.37304, -75.1046, 71.0337, 0.37304, -75.8546, -58.4663, 0.37304, -75.3546, -55.9663, 0.37304, -74.8546, -55.9663, 0.37304, -75.6046, -53.9663, 0.37304, -75.3546, -53.9663, 0.37304, -74.6046, -48.4663, 0.37304, -74.6046, -48.4663, 0.37304, -75.6046, 66.2837, 0.37304, -75.3546, 66.2837, 0.37304, -74.8546, 67.7837, 0.37304, -74.8546, 68.0337, 0.37304, -75.3546, -72.4663, 0.37304, -74.6046, -70.9663, 0.37304, -74.3546, -71.2163, 0.37304, -75.1046, -72.7163, 0.37304, -75.3546, -30.7163, 0.37304, -75.3546, -30.7163, 0.37304, -74.8546, -29.4663, 0.37304, -74.8546, 11.7837, 0.37304, -75.1046, 11.7837, 0.37304, -74.6046, 12.5337, 0.37304, -74.8546, -71.4663, 3.62304, -67.6046, -71.4663, 3.62304, -68.6046, -72.4663, 3.62304, -69.3546, -72.4663, 3.62304, -65.3546, -76.7163, 3.62304, -66.6046, -77.7163, 3.62304, -63.8546, -76.2163, 3.62304, -63.3546, -76.7163, 3.62304, -73.8546, -78.2163, 3.62304, -73.6046, -76.7163, 3.62304, -68.6046, -72.7163, 3.62304, -70.3546, -72.4663, 3.62304, -64.1046, -28.9663, 0.37304, -74.8546, -26.4663, 0.37304, -73.8546, -26.4663, 0.37304, -74.6046, 74.0337, 0.37304, -74.8546, 74.5337, 0.37304, -74.1046, 74.5337, 0.37304, -74.8546, 77.7837, 0.37304, -72.3546, 78.2837, 0.37304, -72.3546, 78.7837, 0.37304, -73.3546, 78.7837, 0.37304, -73.8546, 77.5337, 0.37304, -74.1046, -78.9663, 0.37304, -64.3546, -77.9663, 0.37304, -68.1046, -99.7163, 0.37304, -65.3546, -86.7157, 0.87304, -67.8352, -85.2307, 0.37304, -67.8742, -89.7357, 0.37304, -67.8572, -85.2359, 0.37304, -69.3671, -45.4663, 0.37304, -72.3546, -46.7163, 0.37304, -72.3546, -46.9663, 0.37304, -71.6046, -40.7163, 0.37304, -71.6046, -62.7163, 0.37304, -71.8546, -63.2163, 0.37304, -73.3546, -64.7163, 0.37304, -72.8546, -65.4663, 0.37304, -69.3546, -30.9663, 0.37304, -37.1046, -29.9663, 0.62304, -38.3546, -29.9663, 0.37304, -39.8546, -70.2163, 0.37304, -67.1046, -71.2163, 0.37304, -64.6046, -69.7163, 0.37304, -64.1046, -70.9663, 0.37304, -54.1046, -72.2163, 0.37304, -46.8546, -71.4663, 0.37304, -41.6046, -65.9663, 0.37304, -68.8546, -71.2163, 0.37304, -70.1046, -69.9663, 0.37304, -69.3546, -51.4663, 0.37304, -71.3546, -53.6329, 0.87304, -69.438, -54.7163, 0.37304, -68.4796, -71.7163, 0.37304, -37.1046, -69.7163, 0.37304, -62.3546, -71.4663, 0.37304, -56.3546, 21.2837, 0.37304, -29.3546, 22.2837, 0.37304, -29.6046, 22.2837, 0.37304, -30.3546, 9.03374, 0.37304, -73.1046, 7.53374, 0.37304, -72.8546, 7.28374, 0.37304, -72.1046, 9.78374, 0.37304, -70.8546, -9.21626, 0.37304, -27.6046, 21.2837, 0.37304, -27.6046, 4.78374, 0.37304, -72.1046, 3.53374, 0.37304, -71.6046, 12.5337, 0.37304, -71.3546, -3.21626, 0.37304, -71.6046, 10.7718, 0.87304, -39.3669, 10.7616, 0.37304, -40.8427, 9.28202, 0.37304, -37.8411, 12.302, 0.37304, -37.8692, -29.7163, 0.37304, -40.3546, -28.7163, 0.62304, -40.1046, -28.4663, 0.37304, -40.6046, -26.7163, 0.37304, -41.1046, -26.4663, 0.37304, -70.3546, -31.2163, 0.37304, -71.8546, -31.7163, 0.37304, -71.1046, -32.7163, 0.37304, -71.1046, -39.7163, 0.37304, -72.6046, 54.5337, 0.37304, -69.8546, 53.7837, 0.37304, -72.3546, 52.7837, 0.37304, -72.6046, 48.0337, 0.37304, -71.6046, 47.5337, 0.37304, -70.6046, 71.0337, 0.37304, -57.8546, 72.0337, 0.37304, -58.1046, 72.7837, 0.37304, -60.8546, 71.7837, 0.37304, -63.8546, 38.2837, 0.37304, -72.1046, 33.7837, 0.37304, -70.8546, 33.5337, 0.37304, -70.3546, 61.0337, 0.37304, -71.1046, 60.5337, 0.37304, -71.6046, 55.7837, 0.37304, -69.8546, 71.5337, 0.37304, -40.3546, 72.0337, 0.37304, -45.6046, 71.5337, 0.37304, -45.8546, 71.7837, 0.37304, -70.8546, 71.0337, 0.37304, -40.1046, 71.2837, 0.37304, -47.8546, 31.7837, 0.37304, -70.6046, 31.2837, 0.37304, -35.3546, 71.2837, 0.37304, -38.1046, 55.7679, 0.87304, -42.3611, 55.7776, 0.37304, -43.8748, 51.3031, 0.37304, -40.8303, 57.3029, 0.37304, -42.3501, -26.2163, 0.37304, -40.3546, -24.9663, 0.37304, -40.3546, -23.9663, 0.37304, -72.3546, -24.9663, 0.37304, -72.3546, -25.4663, 0.37304, -70.6046, -17.4663, 0.37304, -70.1046, -18.2163, 0.37304, -71.1046, -24.7163, 0.37304, -39.8546, -11.7163, 0.37304, -27.8546, -10.2163, 0.37304, -27.6046, -28.4663, 0.37304, -35.8546, -28.2163, 0.37304, -33.6046, -27.2163, 0.37304, -33.3546, -25.4663, 0.37304, -36.1046, -24.4663, 0.37304, -38.3546, -27.2163, 0.37304, -32.1046, -3.71626, 0.37304, -72.3546, -9.46626, 0.37304, -72.1046, 16.7837, 0.37304, -71.6046, 16.5337, 0.37304, -72.3546, 15.5337, 0.37304, -72.3546, 22.0337, 0.37304, -71.8546, 23.0337, 0.87304, -31.1046, 22.998, 0.37304, -32.56, 24.0337, 0.62304, -30.8546, 25.0337, 0.37304, -31.6046, 26.0337, 0.37304, -31.3546, 26.2837, 0.37304, -72.3546, 26.2837, 0.37304, -30.8546, 27.5337, 0.37304, -31.1046, 27.7837, 0.37304, -30.6046, -70.4663, 0.37304, -71.6046, -68.7163, 0.37304, -71.1046, -69.2163, 0.37304, -71.6046, 77.2837, 3.62304, -58.8546, 78.0337, 3.62304, -61.8546, 77.2837, 3.62304, -62.3546, 74.2837, 3.62304, -62.8546, 73.7837, 3.62304, -59.6046, 77.0337, 3.62304, -63.6046, 73.2837, 3.62304, -63.3546, -74.9663, 0.37304, -66.8546, -74.9663, 0.37304, -65.6046, -74.4663, 0.37304, -66.1046, -74.4663, 0.37304, -66.8546, 74.7837, 0.37304, -66.1046, 74.7837, 0.37304, -63.8546, 75.2837, 0.37304, -63.8546, -101.216, 0.37304, -63.6046, -102.216, 0.37304, -63.1046, -101.966, 0.37304, -61.8546, -77.4663, 0.37304, -61.8546, -77.4663, 0.37304, -62.6046, -78.9663, 0.37304, -63.1046, -79.2163, 0.37304, -54.6046, -100.716, 0.37304, -65.1046, -103.216, 0.37304, -61.1046, -103.466, 0.37304, -56.3546, -100.966, 0.37304, -55.6046, -99.4663, 0.37304, -54.3546, 104.034, 0.37304, -64.1046, 103.534, 0.37304, -64.6046, 103.284, 0.37304, -64.1046, 101.034, 0.37304, -49.6046, 102.284, 0.37304, -49.3546, 102.284, 0.37304, -50.6046, 106.284, 0.37304, -62.8546, 105.284, 0.37304, -64.1046, 103.034, 0.37304, -51.6046, 99.7837, 0.37304, -61.8546, 102.284, 0.37304, -64.1046, 101.284, 0.37304, -61.8546, 109.784, 0.37304, -58.3546, 109.534, 0.37304, -59.6046, 107.034, 0.37304, -59.6046, 109.034, 0.37304, -56.6046, 106.034, 0.37304, -60.3546, 105.284, 0.37304, -51.6046, 106.034, 0.37304, -53.3546, 108.784, 0.37304, -53.6046, 78.5337, 0.37304, -58.6046, 79.0337, 0.37304, -48.8546, 100.534, 0.37304, -48.8546, -77.7163, 3.62304, -56.3546, -77.7163, 3.62304, -55.3546, -76.7163, 3.62304, -55.1046, -75.9663, 3.62304, -62.3546, -72.9663, 3.62304, -55.1046, -70.9663, 3.62304, -62.8546, 75.2837, 0.37304, -60.8546, 75.5337, 0.37304, -59.6046, 76.2837, 0.37304, -61.3546, 75.5337, 0.37304, -61.8546, -75.7163, 0.37304, -56.3546, -75.7163, 0.37304, -55.8546, -74.9663, 0.37304, -55.8546, -74.4663, 0.37304, -60.1046, -74.4663, 0.37304, -61.6046, -73.9663, 0.37304, -60.6046, -73.9663, 0.37304, -61.3546, 73.2837, 3.62304, -57.3546, 72.2837, 3.62304, -56.8546, 72.5337, 3.62304, -48.3546, 73.2837, 3.62304, -44.8546, 77.5337, 3.62304, -44.1046, 77.7837, 3.62304, -51.1046, -72.4663, 3.62304, -52.6046, -72.4663, 3.62304, -54.3546, -76.2163, 3.62304, -54.6046, -77.2163, 3.87304, -47.8546, -73.7163, 3.87304, -47.3546, -73.2163, 3.87304, -47.6046, -76.7163, 3.62304, -34.3546, -73.2163, 3.62304, -34.3546, -72.7163, 3.87304, -41.1046, -76.4663, 3.87304, -41.8546, -78.4663, 0.37304, -45.8546, -77.7163, 0.37304, -53.8546, -78.9663, 0.37304, -54.1046, -99.4663, 0.37304, -45.8546, 74.5337, 0.37304, -47.8546, 75.5337, 0.37304, -47.8546, 75.0337, 0.37304, -54.1046, 74.2837, 0.37304, -54.1046, -74.9663, 0.37304, -53.1046, -74.9663, 0.37304, -50.8546, -74.4663, 0.37304, -52.8546, 100.784, 0.37304, -46.8546, 101.034, 0.37304, -47.6046, 100.534, 0.37304, -47.8546, 79.0337, 0.37304, -46.6046, -75.2163, 0.37304, -44.6046, -75.2163, 0.37304, -46.8546, -75.9663, 0.37304, -46.8546, -75.4663, 0.37304, -42.1046, -74.4663, 0.37304, -41.8546, 78.0337, 0.37304, -39.3546, 76.7837, 0.37304, -39.3546, 77.5337, 0.37304, -36.6046, 103.784, 0.37304, -41.3546, 104.534, 0.37304, -42.3546, 104.284, 0.37304, -44.3546, 101.784, 0.37304, -44.1046, 100.034, 0.37304, -36.6046, 100.784, 0.37304, -38.1046, 100.784, 0.37304, -45.1046, 78.5337, 0.37304, -40.3546, 103.534, 0.37304, -38.1046, -99.4663, 0.37304, -40.8546, -77.7163, 0.37304, -40.8546, 75.2837, 0.37304, -45.1046, 75.7837, 0.37304, -45.6046, 74.7837, 0.37304, -45.6046, 74.5337, 0.37304, -41.8546, 74.5337, 0.37304, -41.3546, 75.2837, 0.37304, -41.3546, 73.7837, 3.62304, -39.8546, 72.5337, 3.62304, -39.3546, 72.5337, 3.62304, -38.3546, 75.5337, 3.62304, -40.8546, 77.2837, 3.62304, -40.8546, 73.5337, 3.62304, -35.6046, 76.2837, 3.62304, -36.1046, 75.0337, 3.62304, -40.3546, 72.7837, 3.62304, -41.1046, 73.7837, 3.62304, -40.6046, -78.9663, 0.37304, -34.8546, -77.9663, 0.37304, -34.8546, -79.4663, 0.37304, -34.1046, -99.4663, 0.37304, -39.3546, -100.216, 0.37304, -38.8546, -75.2163, 0.37304, -39.1046, -75.2163, 0.37304, -36.8546, -74.7163, 0.37304, -38.1046, -74.7163, 0.37304, -39.1046, -102.466, 0.37304, -37.1046, -103.716, 0.37304, -36.3546, -103.466, 0.37304, -34.8546, -102.216, 0.37304, -38.1046, -103.466, 0.37304, -28.8546, -78.7163, 0.37304, -27.6046, -104.466, 0.37304, -34.3546, -104.716, 0.37304, -29.3546, -103.966, 0.37304, -27.6046, -28.4663, 5.12304, -38.8546, -28.4663, 5.12304, -37.6046, -27.2163, 5.12304, -37.6046, -27.2163, 5.12304, -38.8546, 71.5337, 0.37304, -34.6046, 72.2837, 0.37304, -34.8546, 72.2837, 0.37304, -35.3546, 28.0337, 0.37304, -29.6046, 71.7837, 0.37304, -29.6046, -29.4663, 0.37304, -33.1046, -30.7163, 0.37304, -36.1046, -71.9663, 0.37304, -32.8546, -71.4663, 0.37304, -31.1046, -29.2163, 0.37304, -31.6046, 100.534, 0.37304, -34.8546, 100.784, 0.37304, -35.3546, 100.034, 0.37304, -35.8546, 78.2837, 0.37304, -34.3546, 73.7837, 3.62304, -34.1046, 77.0337, 3.62304, -34.1046, 72.7837, 3.62304, -33.6046, 73.0337, 3.62304, -28.6046, 77.2837, 3.62304, -28.8546, 101.784, 0.37304, -35.3546, 104.034, 0.37304, -35.3546, 102.034, 0.37304, -35.8546, 107.784, 0.37304, -28.6046, 108.284, 0.37304, -28.8546, 108.284, 0.37304, -31.1046, 105.784, 0.37304, -30.8546, 105.784, 0.37304, -23.8546, 104.534, 0.37304, -31.8546, 79.5337, 0.37304, -27.6046, 79.2837, 0.37304, -23.8546, 104.784, 0.37304, -34.3546, 106.284, 0.37304, -24.8546, 107.284, 0.37304, -24.8546, 78.5337, 0.37304, -28.1046, 79.2837, 0.37304, -28.1046, -77.2163, 3.62304, -33.6046, -78.2163, 3.62304, -33.6046, -78.2163, 3.62304, -32.8546, -76.9663, 3.62304, -18.1046, -76.2163, 3.62304, -16.8546, -72.7163, 3.62304, -16.8546, -72.7163, 3.62304, -20.3546, -70.9663, 3.62304, -25.1046, -72.2163, 3.62304, -25.6046, -75.9663, 3.62304, -23.8546, -77.7163, 3.62304, -18.1046, -76.9663, 3.62304, -24.3546, -10.9663, 0.37304, -25.1046, -10.9663, 0.37304, -25.6046, -12.2163, 0.37304, -26.1046, -12.4663, 0.62304, -24.1046, -13.9663, 0.37304, -23.8546, -13.7163, 0.37304, -24.3546, -12.2163, 0.37304, -27.6046, -27.9663, 0.37304, -31.3546, -70.9663, 0.37304, -26.3546, -69.7163, 0.37304, -25.6046, -28.7163, 0.37304, -31.3546, 74.5337, 0.37304, -29.3546, 75.0337, 0.37304, -28.3546, 75.0337, 0.37304, -31.6046, 74.5337, 0.37304, -31.3546, -75.4663, 0.37304, -27.8546, -74.9663, 0.37304, -27.8546, -75.2163, 0.37304, -31.3546, -75.9663, 0.37304, -31.3546, -14.9663, 0.37304, -21.6046, -13.9663, 0.62304, -22.3546, -71.9663, 0.37304, -12.3546, -71.9663, 0.37304, -10.1046, -71.4663, 0.37304, -9.85464, -71.2163, 0.37304, -16.6046, -71.2163, 0.37304, -2.35464, -14.4663, 0.37304, -19.8546, -14.9663, 0.37304, -20.1046, -71.9663, 0.37304, -2.85464, -69.7163, 0.37304, -24.3546, -71.4663, 0.37304, -19.8546, 27.0337, 0.37304, -26.8546, 26.5337, 0.37304, -26.6046, 26.7837, 0.37304, -25.8546, 72.2837, 0.37304, -12.6046, 72.2837, 0.37304, -15.8546, 71.0337, 0.37304, -16.1046, 70.0337, 0.37304, -12.3546, 69.5337, 0.37304, -11.6046, 71.5337, 0.37304, -22.6046, 29.7837, 0.37304, -22.8546, 24.0337, 5.12304, -29.3546, 24.0337, 5.12304, -28.1046, 25.2837, 5.12304, -28.1046, 25.2837, 5.12304, -29.3546, 78.2837, 3.62304, -25.8546, 78.2837, 3.62304, -26.8546, 77.2837, 3.62304, -27.3546, 73.5337, 3.62304, -13.6046, 76.7837, 3.62304, -13.3546, 76.7837, 3.62304, -16.1046, 73.5337, 3.62304, -16.6046, 72.2837, 3.62304, -17.1046, 77.5337, 3.62304, -16.6046, -104.716, 0.37304, -25.8546, -105.716, 0.37304, -25.3546, -105.716, 0.37304, -23.8546, -104.466, 0.37304, -17.8546, -102.716, 0.37304, -16.6046, -106.716, 0.37304, -23.3546, -106.966, 0.37304, -18.6046, -79.2163, 0.37304, -17.6046, -77.4663, 0.37304, -22.8546, -78.2163, 0.37304, -23.6046, -9.96626, 0.37304, -26.1046, -8.71626, 0.37304, -23.8546, 21.7837, 0.37304, -26.6046, 21.2837, 0.37304, -26.8546, -8.46626, 0.37304, -22.3546, -8.96626, 0.37304, -22.1046, -9.21626, 0.37304, -20.8546, 27.0337, 0.37304, 34.3954, -4.22986, 0.87304, -15.3551, -2.6946, 0.37304, -13.8744, -1.19508, 0.37304, -16.8639, -1.22913, 0.37304, -10.8459, -4.21511, 0.37304, -16.8359, 74.7837, 0.37304, -26.3546, 74.7837, 0.37304, -25.3546, 75.2837, 0.37304, -25.3546, 75.2837, 0.37304, -26.3546, 29.2837, 0.37304, 35.6454, 30.5337, 0.62304, 35.6454, 31.0337, 0.37304, 35.1454, 29.0337, 0.37304, 36.1454, 108.784, 0.37304, -20.6046, 109.284, 0.37304, -20.8546, 109.284, 0.37304, -22.8546, 106.784, 0.37304, -22.8546, 105.034, 0.37304, -15.8546, 105.534, 0.37304, -16.6046, 78.7837, 0.37304, -16.1046, 108.284, 0.37304, -16.8546, -74.7163, 0.37304, -22.3546, -74.4663, 0.37304, -21.1046, -73.7163, 0.37304, -23.3546, -74.4663, 0.37304, -23.3546, 74.5337, 0.37304, -19.3546, 75.2837, 0.37304, -19.3546, 75.2837, 0.37304, -23.1046, 74.5337, 0.37304, -23.6046, 77.5337, 0.37304, -22.8546, 78.0337, 0.37304, -22.6046, 78.0337, 0.37304, -23.6046, 77.5337, 0.37304, -23.6046, -12.4663, 5.12304, -22.8546, -12.4663, 5.12304, -21.6046, -11.2163, 5.12304, -21.6046, -11.2163, 5.12304, -22.8546, 32.5337, 0.37304, 34.8954, 32.7837, 0.37304, 35.6454, 34.0337, 0.37304, 35.3954, 71.0337, 0.37304, 16.8954, 72.2837, 0.37304, 16.6454, 72.5337, 0.37304, 16.1454, 72.7837, 0.37304, 12.6454, 71.7837, 0.37304, 12.3954, 70.7837, 0.37304, 28.6454, 70.5337, 0.37304, 26.6454, 71.7837, 0.37304, 10.6454, 73.0337, 0.37304, 6.14536, 72.0337, 0.37304, 5.64536, 71.2837, 0.37304, 26.1454, 72.2837, 0.37304, -0.354637, 28.0337, 0.37304, 38.1454, 29.0337, 0.62304, 37.3954, -10.2163, 0.37304, -19.8546, 13.0337, 0.37304, 49.1454, 13.2837, 0.37304, 48.6454, 28.0337, 0.37304, 39.3954, 16.0337, 0.37304, 48.6454, -2.71626, 0.37304, 70.3954, -2.21626, 0.37304, 71.6454, -1.21626, 0.37304, 71.8954, 1.28374, 0.37304, 71.8954, 1.78374, 0.37304, 70.6454, -66.9663, 0.37304, 60.8954, -65.2163, 0.37304, 60.3954, -64.9663, 0.37304, 59.8954, -71.7163, 0.37304, 55.8954, -71.9663, 0.37304, 62.1454, -14.7163, 0.37304, 69.8954, -14.4663, 0.37304, 71.8954, -13.9663, 0.37304, 72.1454, -8.71626, 0.37304, 72.3954, -41.9663, 0.37304, 72.8954, -41.2163, 0.37304, 72.8954, -40.4663, 0.37304, 70.6454, -45.2163, 0.62304, 70.1454, -44.7163, 0.62304, 71.8954, -72.4663, 0.37304, 21.6454, -71.9663, 0.37304, 27.6454, -71.2163, 0.37304, 33.8954, -69.4663, 0.37304, 13.1454, -71.7163, 0.37304, 8.89536, -71.4663, 0.37304, 10.8954, -69.4663, 0.37304, 11.6454, -71.7163, 0.37304, 5.14536, -38.2163, 0.37304, 70.8954, -33.2163, 0.37304, 72.6454, -24.7163, 0.37304, 70.8954, -51.4743, 0.37304, 55.0486, -50.6921, 0.87304, 56.9357, -49.9098, 0.37304, 58.8228, -29.7102, 0.87304, -9.36277, -29.7299, 0.37304, -7.85513, -10.2275, 0.87304, 13.164, -46.2291, 0.37304, 52.1207, -11.7126, 0.37304, 13.153, -10.2031, 0.37304, 11.6696, -31.1952, 0.37304, -9.37376, -7.23089, 0.37304, 17.6693, -25.2007, 0.37304, -12.3462, -49.2184, 0.37304, 55.1458, -67.4663, 0.37304, 62.1454, -71.2163, 0.37304, 50.3954, -73.2163, 0.37304, 50.8954, -72.9663, 0.37304, 27.8954, 7.78374, 0.37304, 71.8954, 13.2837, 0.37304, 51.6454, -71.9663, 0.37304, 42.6454, -72.7163, 0.37304, 43.1454, -70.7163, 0.37304, 49.1454, -63.7163, 0.37304, 59.8954, -51.4743, 0.37304, 55.4357, -50.6921, 0.87304, 57.2744, -49.9098, 0.37304, 59.1131, -15.2163, 0.37304, 69.3954, -3.96626, 0.37304, 70.3954, -71.2163, 0.37304, 35.3954, -72.4663, 0.37304, 35.8954, -72.2163, 0.37304, 3.89536, -53.7163, 0.37304, 69.6454, -53.2163, 0.37304, 71.3954, -62.2163, 0.37304, 64.6454, -61.9663, 0.37304, 71.6454, -55.2163, 0.37304, 69.6454, -61.9663, 0.37304, 62.6454, -22.7163, 0.37304, 71.3954, -103.716, 0.37304, -13.3546, -104.716, 0.37304, -12.8546, -104.466, 0.37304, -11.8546, -102.716, 0.37304, -15.3546, -103.966, 0.37304, -14.8546, -104.966, 0.37304, -10.3546, -78.2163, 0.37304, -10.6046, -78.4663, 0.37304, -16.8546, -77.4663, 0.37304, -16.8546, -75.9663, 3.62304, -3.10464, -75.9663, 3.62304, -2.35464, -74.4663, 3.62304, -2.35464, -73.9663, 3.62304, -3.35464, -76.7163, 3.62304, -3.60464, -76.9663, 3.62304, -9.85464, -76.4663, 3.62304, -9.60464, -73.4663, 3.62304, -10.6046, -73.2163, 3.62304, -3.35464, -72.9663, 3.62304, -9.10464, -73.4663, 3.62304, -9.35464, 107.784, 0.37304, -12.1046, 108.284, 0.37304, -12.3546, 108.034, 0.37304, -14.1046, 105.784, 0.37304, -14.1046, 104.534, 0.37304, -7.85464, 105.034, 0.37304, -14.8546, 102.534, 0.37304, -5.60464, 103.784, 0.37304, -6.10464, 78.5337, 0.37304, -15.3546, 78.0337, 0.37304, -15.3546, 78.0337, 0.37304, -10.1046, 107.284, 0.37304, -8.10464, 102.034, 0.37304, -3.60464, 79.7837, 0.37304, -3.35464, 77.0337, 3.62304, -2.10464, 78.2837, 3.62304, -2.60464, 78.2837, 3.62304, -3.85464, 73.5337, 3.62304, -12.1046, 73.5337, 3.62304, -0.854637, 73.5337, 3.62304, 1.14536, 76.7837, 3.62304, 1.39536, 76.7837, 3.62304, -9.60464, 72.7837, 3.62304, -11.3546, 71.0337, 3.62304, -11.1046, -75.4663, 0.37304, -10.8546, -75.4663, 0.37304, -10.3546, -74.9663, 0.37304, -10.6046, -74.9663, 0.37304, -12.3546, -77.2163, 0.37304, -1.60464, -77.2163, 0.37304, -2.35464, -77.9663, 0.37304, -2.60464, -78.4663, 0.37304, 5.89536, -106.216, 0.37304, -5.60464, -107.216, 0.37304, -5.10464, -106.966, 0.37304, -3.85464, -104.966, 0.37304, -7.60464, -105.716, 0.37304, -7.10464, -104.716, 0.37304, 3.14536, -102.966, 0.37304, 5.89536, -108.216, 0.37304, -3.10464, -108.466, 0.37304, 1.89536, -77.7163, 0.37304, -8.85464, -78.2163, 0.37304, -9.10464, 74.5337, 0.37304, -4.85464, 74.7837, 0.37304, -4.35464, 75.5337, 0.37304, -4.35464, 74.5337, 0.37304, -7.10464, 73.5337, 0.37304, -8.85464, 74.5337, 0.37304, -8.85464, -75.2163, 0.37304, -8.60464, -75.2163, 0.37304, -6.10464, -74.7163, 0.37304, -7.35464, -74.7163, 0.37304, -8.60464, 100.034, 0.37304, 12.3954, 101.534, 0.37304, 12.1454, 101.534, 0.37304, 10.6454, 108.284, 0.37304, 4.64536, 108.784, 0.37304, 4.14536, 108.784, 0.37304, 2.14536, 106.284, 0.37304, 2.39536, 102.034, 0.37304, 10.1454, 104.534, 0.37304, 10.3954, 104.534, 0.37304, 8.89536, 79.7837, 0.37304, -1.85464, 78.2837, 0.37304, -1.35464, 78.0337, 0.37304, 0.395363, 79.2837, 0.37304, 5.14536, 78.7837, 0.37304, 7.64536, 105.284, 0.37304, -1.10464, 104.284, 0.37304, -2.35464, 102.534, 0.37304, -2.60464, 105.034, 0.37304, 1.39536, 105.034, 0.37304, 8.39536, 105.534, 0.37304, 1.89536, 107.784, 0.37304, 8.39536, 78.0337, 0.37304, 4.64536, -73.9663, 3.62304, -1.60464, -76.7163, 3.62304, 2.64536, -76.4663, 3.62304, 5.14536, -77.2163, 3.62304, 5.64536, -77.2163, 3.62304, 8.14536, -76.9663, 3.62304, 11.6454, -75.7163, 3.62304, 12.6454, -72.4663, 3.62304, 11.8954, -72.9663, 3.62304, 5.64536, -73.4663, 3.62304, 4.89536, -72.7163, 3.62304, -1.60464, -74.9663, 0.37304, -1.35464, -74.9663, 0.37304, 0.645363, -74.4663, 0.37304, -0.354637, -74.4663, 0.37304, -1.35464, 74.0337, 3.62304, 12.1454, 73.7837, 3.62304, 15.1454, 76.7837, 3.62304, 15.3954, 76.2837, 3.62304, 11.3954, 77.7837, 3.62304, 5.89536, 76.5337, 3.62304, 5.14536, 74.5337, 3.62304, 5.39536, 73.0337, 3.62304, 11.1454, 73.2837, 3.62304, 4.89536, -103.466, 0.37304, 7.64536, -104.716, 0.37304, 8.14536, -104.466, 0.37304, 9.64536, -103.716, 0.37304, 24.1454, -101.716, 0.37304, 26.3954, -100.216, 0.37304, 26.3954, -105.216, 0.37304, 17.6454, -106.466, 0.37304, 18.3954, -106.716, 0.37304, 23.1454, -105.466, 0.37304, 10.1454, -105.716, 0.37304, 15.3954, -79.2163, 0.37304, 18.6454, -77.2163, 0.37304, 13.3954, -78.2163, 0.37304, 12.1454, -79.2163, 0.37304, 19.8954, -75.7163, 0.37304, 6.14536, -74.9663, 0.37304, 9.64536, -74.9663, 0.37304, 6.14536, 75.5337, 0.37304, 6.14536, 75.5337, 0.37304, 7.14536, 76.0337, 0.37304, 6.64536, 76.0337, 0.37304, 6.14536, 77.5337, 0.37304, 11.8954, 78.2837, 0.37304, 12.3954, 78.2837, 0.37304, 13.1454, 99.2837, 0.37304, 13.1454, -77.7163, 3.62304, 18.3954, -77.7163, 3.62304, 19.3954, -76.7163, 3.62304, 19.8954, -70.9663, 3.62304, 13.3954, -70.9663, 3.62304, 12.3954, -76.4663, 3.62304, 23.1454, -73.7163, 3.62304, 23.1454, -73.7163, 3.62304, 21.1454, -75.7163, 0.37304, 18.3954, -75.7163, 0.37304, 19.1454, -75.2163, 0.37304, 19.1454, -74.9663, 0.37304, 17.1454, -73.2163, 0.37304, 13.3954, -73.2163, 0.37304, 12.8954, -74.2163, 0.37304, 13.1454, -74.2163, 0.37304, 14.8954, 78.0337, 0.37304, 16.3954, 78.5337, 0.37304, 16.6454, 99.2837, 0.37304, 20.6454, 78.7837, 0.37304, 20.6454, 77.5337, 3.62304, 19.3954, 77.2837, 3.62304, 17.6454, 76.5337, 3.62304, 17.1454, 73.5337, 3.62304, 17.3954, 72.7837, 3.62304, 26.8954, 76.5337, 3.62304, 28.1454, 77.7837, 3.62304, 28.1454, 72.2837, 3.62304, 17.8954, 71.7837, 3.62304, 27.6454, 72.7837, 3.62304, 30.3954, 76.0337, 3.62304, 29.6454, -79.2163, 0.37304, 25.8954, -77.4663, 0.37304, 25.6454, -77.9663, 0.37304, 20.6454, -79.4663, 0.37304, 27.1454, -99.4663, 0.37304, 27.1454, 105.784, 0.37304, 20.3954, 104.284, 0.37304, 20.1454, 104.034, 0.37304, 20.6454, 106.784, 0.37304, 21.6454, 105.284, 0.37304, 33.6454, 106.034, 0.37304, 33.1454, 106.034, 0.37304, 31.8954, 107.784, 0.37304, 25.1454, 106.534, 0.37304, 24.1454, 101.784, 0.37304, 22.8954, 99.7837, 0.37304, 22.6454, 106.784, 0.37304, 31.1454, 102.784, 0.37304, 20.6454, 99.2837, 0.37304, 22.1454, 79.0337, 0.37304, 29.1454, 101.784, 0.37304, 38.8954, 104.284, 0.37304, 38.6454, 104.784, 0.37304, 35.1454, 77.5337, 0.37304, 29.3954, 78.7837, 0.37304, 34.8954, 109.534, 0.37304, 30.8954, 110.284, 0.37304, 24.8954, 74.5337, 0.37304, 25.1454, 75.2837, 0.37304, 25.1454, 75.0337, 0.37304, 20.3954, 74.2837, 0.37304, 20.3954, 77.0337, 0.37304, 20.6454, 77.5337, 0.37304, 23.8954, 77.5337, 0.37304, 20.6454, -73.7163, 3.62304, 35.1454, -72.7163, 3.62304, 34.6454, -72.7163, 3.62304, 33.3954, -76.2163, 3.62304, 26.3954, -77.9663, 3.62304, 27.1454, -77.9663, 3.62304, 28.1454, -76.2163, 3.62304, 35.1454, -73.7163, 3.62304, 36.3954, -74.4663, 3.62304, 27.8954, -74.4663, 3.62304, 27.1454, -73.4663, 3.62304, 26.6454, -100.466, 0.37304, 28.3954, -100.716, 0.37304, 29.8954, -100.716, 0.37304, 33.3954, -99.4663, 0.37304, 34.3954, -77.7163, 0.37304, 34.3954, -76.4663, 0.37304, 28.3954, -75.9663, 0.37304, 29.3954, -75.9663, 0.37304, 28.1454, 70.5337, 0.37304, 35.3954, 72.5337, 0.37304, 34.8954, 70.2837, 0.37304, 36.1454, 34.5337, 0.37304, 36.1454, 76.0337, 3.87304, 35.1454, 77.2837, 3.62304, 34.3954, 77.2837, 3.62304, 33.3954, 74.0337, 3.62304, 34.3954, 74.2837, 4.12304, 42.6454, 73.5337, 3.87304, 45.8954, 76.7837, 4.12304, 46.6454, 77.7837, 4.12304, 43.1454, 77.0337, 3.87304, 41.3954, 76.0337, 3.87304, 36.1454, 73.5337, 4.12304, 35.8954, 72.0337, 4.12304, 36.3954, 77.7837, 3.87304, 40.6454, -78.2163, 0.37304, 34.8954, -77.4663, 0.37304, 34.6454, -79.4663, 0.37304, 41.3954, -77.7163, 0.37304, 40.8954, -79.9663, 0.37304, 42.1454, -99.4663, 0.37304, 37.8954, -100.216, 0.37304, 38.3954, 99.7837, 0.37304, 41.1454, 101.034, 0.37304, 40.6454, 101.034, 0.37304, 39.3954, 79.0337, 0.37304, 40.3954, 79.2837, 0.37304, 42.3954, 77.2837, 0.37304, 35.8954, 99.2837, 0.37304, 42.3954, -76.7163, 3.62304, 35.6454, -76.2163, 3.62304, 41.6454, -74.2163, 3.62304, 42.1454, -73.2163, 3.62304, 41.6454, -78.4663, 3.62304, 42.6454, -76.4663, 3.62304, 49.6454, -74.7163, 3.62304, 50.1454, -72.2163, 3.62304, 49.1454, -74.2163, 3.62304, 42.8954, 32.2837, 0.37304, 39.8954, 32.5337, 0.37304, 42.1454, 33.2837, 0.37304, 42.1454, 33.5337, 0.37304, 39.6454, 34.5337, 0.37304, 37.3954, 34.0337, 0.37304, 37.6454, 33.7837, 0.37304, 43.3954, 72.7837, 0.37304, 43.3954, 74.5337, 0.37304, 40.6454, 76.0337, 0.37304, 40.8954, 74.7837, 0.37304, 38.6454, 74.5337, 0.37304, 39.3954, 73.2837, 0.37304, 36.6454, 74.5337, 0.37304, 36.8954, 30.5337, 5.12304, 36.8954, 30.5337, 5.12304, 38.1454, 31.7837, 5.12304, 38.1454, 31.7837, 5.12304, 36.8954, -78.7163, 0.37304, 49.3954, -77.9663, 0.37304, 49.3954, -77.9663, 0.37304, 48.6454, -103.966, 0.37304, 40.6454, -105.216, 0.37304, 41.1454, -104.966, 0.37304, 42.6454, -101.966, 0.37304, 39.1454, -103.466, 0.37304, 39.1454, -105.966, 0.37304, 43.1454, -106.216, 0.37304, 48.1454, -103.466, 0.37304, 48.8954, -102.966, 0.37304, 50.1454, -78.9663, 0.37304, 50.1454, 31.2837, 0.37304, 45.6454, 32.0337, 0.37304, 44.3954, 31.5337, 0.37304, 44.1454, 31.5337, 0.37304, 42.3954, 29.2837, 0.37304, 40.1454, 16.5337, 0.37304, 49.1454, 105.784, 0.37304, 42.6454, 105.784, 0.37304, 41.8954, 104.284, 0.37304, 41.6454, 109.284, 0.37304, 48.3954, 109.784, 0.37304, 48.1454, 109.784, 0.37304, 46.1454, 106.034, 0.37304, 45.1454, 106.284, 0.37304, 42.8954, 104.034, 0.37304, 41.1454, 101.784, 0.37304, 42.1454, 103.034, 0.37304, 54.1454, 105.534, 0.37304, 54.1454, 105.534, 0.37304, 52.8954, 101.784, 0.37304, 43.3954, 106.034, 0.37304, 52.3954, 106.784, 0.37304, 45.8954, 108.784, 0.37304, 52.1454, 101.034, 0.37304, 43.8954, 102.534, 0.37304, 54.6454, 78.2837, 0.37304, 47.3954, 79.5337, 0.37304, 47.3954, 79.7837, 0.37304, 47.8954, 99.2837, 0.37304, 43.3954, 100.034, 0.37304, 43.8954, 76.2837, 0.37304, 43.3954, 76.2837, 0.37304, 42.8954, 75.2837, 0.37304, 43.1454, 75.2837, 0.37304, 44.8954, 75.5337, 0.37304, 47.1454, 74.5337, 0.37304, 46.8954, -76.7163, 0.37304, 43.1454, -75.7163, 0.37304, 45.6454, -75.9663, 0.37304, 43.1454, 33.5337, 0.62304, 43.8954, 35.7837, 0.37304, 45.8954, 72.0337, 0.37304, 46.8954, 25.5337, 0.37304, 69.6454, 24.7837, 0.37304, 70.1454, 25.0337, 0.37304, 71.3954, 30.0337, 0.37304, 72.1454, 26.7837, 0.37304, 69.6454, 33.2837, 0.62304, 44.3954, 28.0337, 0.37304, 67.6454, 28.0337, 0.37304, 68.3954, 38.7837, 0.37304, 71.1454, 39.5337, 0.37304, 70.6454, 38.7837, 0.37304, 71.8954, 27.2837, 0.37304, 66.8954, 38.2102, 0.37304, 61.9101, 37.9896, 0.87304, 60.4542, 37.769, 0.37304, 58.9983, 36.264, 0.37304, 56.6231, 36.2892, 0.37304, 61.1379, 79.0337, 0.37304, 55.3954, 102.534, 0.37304, 55.6454, 16.7837, 0.37304, 51.3954, 17.7837, 0.37304, 52.1454, 26.7837, 0.37304, 66.1454, 26.0337, 0.37304, 66.1454, 67.7837, 0.37304, 72.6454, 69.0337, 0.37304, 72.6454, 69.2837, 0.37304, 72.1454, 64.0337, 0.37304, 71.1454, 64.2837, 0.37304, 71.8954, 56.7837, 0.37304, 69.6454, 57.2837, 0.37304, 70.1454, 71.0337, 0.37304, 58.6454, 71.0337, 0.37304, 54.8954, 72.2837, 0.37304, 62.8954, 73.0337, 0.37304, 58.8954, 71.5337, 0.37304, 63.6454, 47.2837, 0.37304, 72.1454, 40.7614, 0.37304, 64.1375, 72.0337, 0.37304, 72.3954, 57.5337, 0.37304, 72.1454, 73.7837, 3.62304, 57.6454, 74.2837, 3.62304, 58.8954, 77.2837, 3.62304, 58.8954, 77.7837, 3.62304, 57.3954, 76.5337, 4.12304, 48.1454, 72.7837, 3.87304, 48.8954, 77.0337, 4.12304, 48.6454, 78.2837, 3.62304, 48.6454, 72.2837, 3.62304, 57.6454, 77.5337, 0.37304, 53.6454, 77.7837, 0.37304, 55.8954, 78.2837, 0.37304, 51.1454, 78.0337, 0.37304, 48.6454, -77.4663, 3.62304, 50.6454, -76.2163, 3.62304, 54.8954, -72.9663, 3.62304, 56.3954, -76.4663, 3.62304, 63.8954, -73.4663, 3.62304, 63.8954, 14.5337, 2.62304, 49.8954, 14.5337, 2.62304, 50.8954, 15.2837, 2.62304, 50.6454, 15.2837, 2.62304, 49.8954, -105.466, 0.37304, 52.6454, -106.716, 0.37304, 53.3954, -106.466, 0.37304, 54.6454, -103.716, 0.37304, 51.1454, -104.966, 0.37304, 51.1454, -107.466, 0.37304, 55.3954, -107.716, 0.37304, 56.6454, -107.716, 0.37304, 60.1454, -103.966, 0.37304, 61.6454, -79.2163, 0.37304, 63.6454, -77.7163, 0.37304, 63.3954, -77.4663, 0.37304, 59.8954, -77.4663, 0.37304, 55.3954, -103.216, 0.37304, 63.1454, 75.5337, 0.37304, 52.1454, 75.5337, 0.37304, 51.1454, 74.5337, 0.37304, 51.1454, 74.5337, 0.37304, 55.3954, 75.2837, 0.37304, 55.3954, 16.5337, 0.37304, 51.8954, 24.5337, 0.37304, 68.3954, 23.2837, 0.37304, 69.8954, 17.0337, 0.37304, 72.3954, 24.5337, 0.37304, 67.6454, 14.0337, 0.37304, 52.1454, 13.7837, 0.37304, 72.1454, 9.28374, 0.37304, 72.3954, 9.78374, 0.37304, 71.6454, 101.284, 0.37304, 66.3954, 102.534, 0.37304, 65.1454, 102.034, 0.37304, 64.8954, 100.534, 0.37304, 65.3954, 79.0337, 0.37304, 57.8954, 77.7837, 0.37304, 62.8954, 78.0337, 0.37304, 66.6454, 99.2837, 0.37304, 66.6454, 99.7837, 0.37304, 65.3954, 102.284, 0.37304, 63.8954, 102.784, 0.37304, 57.1454, 102.284, 0.37304, 56.6454, 103.284, 0.37304, 64.1454, 105.284, 0.37304, 62.8954, 106.034, 0.37304, 57.3954, 73.5337, 3.62304, 72.6454, 74.7837, 3.62304, 72.8954, 75.2837, 3.62304, 72.1454, 72.7837, 3.62304, 64.3954, 76.5337, 3.62304, 62.3954, 73.5337, 3.62304, 63.3954, 77.0337, 3.62304, 72.1454, -64.9663, 3.62304, 64.1454, -63.4663, 3.62304, 63.6454, -64.4663, 3.62304, 61.3954, -65.9663, 3.62304, 61.8954, -67.4663, 0.37304, 62.8954, -72.4663, 0.37304, 67.1454, -71.4663, 0.37304, 71.1454, -69.7163, 0.37304, 70.8954, -64.4663, 0.37304, 65.6454, -64.4663, 0.37304, 71.8954, -65.7163, 0.37304, 65.6454, -103.716, 0.37304, 64.3954, -104.966, 0.37304, 65.1454, -104.716, 0.37304, 66.3954, -105.716, 0.37304, 67.1454, -105.966, 0.37304, 71.8954, -102.716, 0.37304, 73.1454, -79.4663, 0.37304, 65.3954, -77.9663, 0.37304, 70.8954, -76.9663, 3.62304, 64.6454, -77.9663, 3.62304, 64.6454, -77.9663, 3.62304, 65.8954, -76.2163, 3.62304, 72.6454, -74.9663, 3.62304, 72.6454, -73.9663, 3.62304, 66.6454, -72.4663, 3.62304, 72.8954, -74.4663, 3.62304, 73.1454, -72.2163, 3.62304, 76.8954, -71.7163, 3.62304, 73.3954, -74.7163, 3.62304, 76.6454, 103.784, 0.37304, 65.6454, 103.284, 0.37304, 64.8954, 105.284, 0.37304, 66.6454, 105.284, 0.37304, 65.8954, 105.534, 0.37304, 69.1454, 105.784, 0.37304, 66.8954, 101.284, 0.37304, 67.3954, 106.284, 0.37304, 69.8954, 100.034, 0.37304, 67.8954, 108.284, 0.37304, 76.1454, 109.284, 0.37304, 70.1454, 105.534, 0.37304, 76.3954, 105.034, 0.37304, 77.6454, 79.7837, 0.37304, 73.3954, 79.5337, 0.37304, 77.6454, -76.4663, 0.37304, 65.8954, -75.7163, 0.37304, 67.6454, -75.7163, 0.37304, 66.1454, 74.2837, 0.37304, 66.1454, 74.7837, 0.37304, 68.1454, 74.7837, 0.37304, 66.1454, 78.2837, 0.37304, 72.8954, 79.5337, 0.37304, 72.8954, 99.2837, 0.37304, 67.3954, 26.0337, 2.12304, 67.6454, 26.0337, 2.12304, 68.3954, 26.5337, 2.12304, 68.1454, 74.7837, 0.37304, 68.8954, 74.7837, 0.37304, 71.6454, 75.2837, 0.37304, 71.8954, -75.9663, 0.37304, 75.1454, -75.7163, 0.37304, 74.1454, -77.2163, 0.37304, 73.8954, -101.466, 0.37304, 75.1454, -101.716, 0.37304, 73.8954, -27.2163, 3.62304, 72.8954, -27.2163, 3.62304, 76.3954, -25.7163, 3.62304, 76.3954, -23.2163, 3.62304, 72.8954, -25.7163, 3.62304, 72.3954, -13.9663, 3.87304, 76.8954, -13.7163, 3.87304, 73.1454, -14.9663, 3.87304, 73.1454, -18.2163, 3.62304, 76.3954, -15.7163, 3.87304, 72.3954, -24.9663, 3.62304, 78.3954, -15.9663, 3.62304, 70.8954, -54.4663, 3.62304, 71.8954, -54.4663, 3.62304, 71.1454, -55.7163, 3.62304, 71.1454, -53.7163, 3.62304, 72.6454, -62.9663, 3.62304, 78.3954, -60.7163, 3.62304, 78.3954, -59.9663, 3.62304, 77.6454, -70.7163, 3.62304, 73.3954, -44.7163, 3.62304, 77.1454, -44.2163, 3.62304, 73.3954, -45.9663, 3.62304, 72.6454, -48.2163, 3.62304, 76.8954, -56.4663, 3.62304, 76.6454, -55.7163, 3.62304, 76.6454, -70.2163, 3.62304, 72.3954, -62.4663, 3.62304, 73.3954, -54.9663, 3.62304, 78.1454, -45.9663, 3.62304, 71.6454, 41.2837, 3.62304, 72.1454, 40.0337, 3.62304, 72.1454, 39.2837, 3.62304, 73.1454, 58.2837, 3.62304, 76.3954, 58.2837, 3.62304, 73.3954, 57.0337, 3.62304, 73.3954, 54.7837, 3.62304, 76.6454, 55.5337, 3.62304, 77.1454, 56.2837, 3.62304, 72.6454, 53.2837, 3.62304, 76.6454, 56.0337, 3.62304, 71.1454, 47.7837, 3.62304, 73.3954, 49.2837, 3.62304, 77.6454, 46.0337, 3.62304, 73.3954, 39.2837, 3.62304, 77.1454, 48.7837, 3.62304, 79.1454, 18.2837, 3.62304, 76.3954, 18.7837, 3.62304, 77.6454, 19.5337, 3.62304, 77.8954, 24.5337, 3.62304, 76.1454, 25.0337, 3.62304, 73.8954, 24.0337, 3.62304, 72.8954, 13.2837, 3.62304, 77.1454, 17.0337, 3.62304, 77.6454, 17.0337, 3.62304, 76.8954, 16.7837, 3.62304, 73.8954, 13.5337, 3.62304, 73.3954, 23.7837, 3.62304, 71.3954, -9.21626, 3.87304, 76.6454, -8.71626, 3.87304, 77.3954, -7.96626, 3.87304, 77.3954, -2.71626, 3.87304, 75.6454, -2.21626, 3.62304, 77.1454, 0.0337448, 3.62304, 77.1454, 0.0337448, 3.62304, 73.1454, -2.71626, 3.87304, 72.8954, -10.2163, 3.87304, 76.6454, -8.71626, 3.87304, 73.8954, -10.4663, 3.87304, 77.1454, -3.71626, 3.87304, 71.8954, -41.7163, 3.62304, 78.1454, -41.4663, 3.62304, 77.1454, -40.2163, 3.62304, 76.6454, -40.7163, 3.62304, 74.3954, -31.7163, 3.62304, 79.3954, -31.4663, 3.62304, 77.8954, -33.9663, 3.62304, 73.8954, -39.7163, 3.62304, 72.1454, -39.9663, 3.62304, 73.6454, -30.4663, 3.62304, 76.8954, 3.53374, 3.62304, 72.1454, 2.53374, 3.62304, 72.1454, 2.03374, 3.62304, 73.1454, 10.7837, 3.62304, 72.8954, 10.2837, 3.62304, 73.6454, 12.0337, 3.62304, 76.8954, 8.78374, 3.62304, 73.6454, 4.53374, 3.62304, 77.3954, 10.7837, 3.62304, 79.1454, 70.2837, 3.62304, 77.8954, 70.7837, 3.62304, 77.8954, 71.2837, 3.62304, 76.3954, 73.0337, 3.62304, 73.6454, 75.2837, 3.62304, 73.8954, 78.2837, 3.62304, 76.8954, 78.2837, 3.62304, 74.1454, 69.7837, 3.62304, 73.6454, 69.5337, 3.62304, 74.1454, 63.0337, 3.62304, 72.3954, 61.5337, 3.62304, 75.8954, 63.5337, 3.62304, 73.1454, 61.7837, 3.62304, 76.3954, 30.2837, 3.62304, 77.8954, 31.5337, 3.62304, 77.8954, 32.0337, 3.62304, 77.1454, 26.0337, 3.62304, 73.3954, 25.5337, 3.62304, 77.1454, 31.0337, 3.62304, 73.6454, 26.0337, 3.62304, 72.6454, 31.7837, 3.62304, 73.6454, 32.0337, 3.62304, 73.1454, -22.9663, 0.37304, 74.8954, -22.7163, 0.37304, 75.3954, -20.4663, 0.37304, 74.3954, -18.2163, 0.37304, 73.1454, -17.9663, 0.37304, 74.1454, -3.21626, 0.37304, 73.8954, -3.46626, 0.37304, 73.1454, -3.96626, 0.37304, 73.1454, -7.21626, 0.37304, 74.6454, -5.46626, 0.37304, 74.3954, -8.96626, 0.37304, 74.8954, -8.96626, 0.37304, 75.6454, -8.21626, 0.37304, 75.8954, 18.5337, 0.37304, 75.1454, 18.5337, 0.37304, 75.6454, 19.2837, 0.37304, 75.6454, 20.0337, 0.37304, 74.8954, 21.2837, 0.37304, 74.3954, 23.0337, 0.37304, 73.1454, 23.7837, 0.37304, 74.1454, 23.7837, 0.37304, 73.1454, -56.9663, 0.37304, 74.3954, -56.9663, 0.37304, 73.6454, -57.4663, 0.37304, 73.6454, -59.2163, 0.37304, 74.3954, -59.2163, 0.37304, 74.8954, -47.7163, 0.37304, 74.1454, -47.7163, 0.37304, 73.6454, -48.4663, 0.37304, 73.6454, -52.2163, 0.37304, 74.6454, -51.4663, 0.37304, 75.1454, -47.9663, 0.37304, 74.6454, 50.7837, 0.37304, 74.6454, 53.5337, 0.37304, 74.3954, 53.0337, 0.37304, 73.6454, -34.9663, 0.37304, 75.6454, -33.4663, 0.37304, 76.3954, -33.4663, 0.37304, 75.6454, -39.2163, 0.37304, 74.1454, -39.2163, 0.37304, 74.8954, -74.2163, 0.37304, 74.6454, -74.2163, 0.37304, 75.1454, -71.7163, 0.37304, 75.1454, -13.4663, 0.37304, 75.3954, -10.9663, 0.37304, 75.6454, -11.2163, 0.37304, 74.6454, -13.4663, 0.37304, 74.6454, -0.966255, 0.37304, 74.6454, -0.966255, 0.37304, 75.1454, 0.533745, 0.37304, 75.1454, 2.78374, 0.37304, 74.6454, 4.53374, 0.37304, 75.1454, 4.53374, 0.37304, 74.6454, 29.0337, 0.37304, 75.1454, 31.0337, 0.37304, 75.8954, 31.0337, 0.37304, 74.8954, 26.2837, 0.37304, 74.6454, 26.2837, 0.37304, 75.1454, 33.5337, 0.37304, 74.6454, 33.5337, 0.37304, 75.1454, 38.2837, 0.37304, 74.8954, 42.5337, 0.37304, 75.3954, 45.7837, 0.37304, 75.8954, 45.0337, 0.37304, 74.8954, 42.2837, 0.37304, 74.6454, -66.2163, 0.37304, 75.3954, -63.9663, 0.37304, 75.6454, -64.2163, 0.37304, 74.8954, -66.2163, 0.37304, 74.8954, 6.78374, 0.37304, 75.6454, 9.03374, 0.37304, 76.1454, 9.03374, 0.37304, 75.3954, 6.28374, 0.37304, 74.8954, -105.716, 0.37304, 77.8954, -106.966, 0.37304, 78.3954, -106.716, 0.37304, 79.8954, -107.716, 0.37304, 80.3954, -107.966, 0.37304, 81.8954, -107.966, 0.37304, 85.3954, -104.216, 0.37304, 86.6454, -102.966, 0.37304, 76.1454, -105.216, 0.37304, 76.3954, -103.466, 0.37304, 91.6454, -103.466, 0.37304, 95.1454, -101.216, 0.37304, 95.6454, -99.4663, 0.37304, 96.8954, -92.9663, 0.37304, 99.3954, -86.4663, 0.37304, 99.3954, -102.716, 0.37304, 88.8954, -99.4663, 0.37304, 99.3954, -75.9663, 0.37304, 77.3954, -61.9663, 0.37304, 75.8954, -61.4663, 0.37304, 75.8954, -61.4663, 0.37304, 75.1454, -62.2163, 0.37304, 75.1454, 14.7837, 0.37304, 75.1454, 16.7837, 0.37304, 75.8954, 16.5337, 0.37304, 75.1454, 47.7837, 0.37304, 75.3954, 47.7837, 0.37304, 75.8954, 48.5337, 0.37304, 75.6454, -2.96626, 0.37304, 78.3954, -3.46626, 0.37304, 77.1454, -4.21626, 0.37304, 77.1454, -8.46626, 0.37304, 78.8954, -0.966255, 0.37304, 78.3954, -8.46626, 0.37304, 99.3954, -0.966255, 0.37304, 99.3954, -82.4663, 0.37304, 101.145, -82.2163, 0.37304, 101.645, -80.7163, 0.37304, 101.645, -80.4663, 0.37304, 102.145, -78.4663, 0.37304, 101.145, -78.4663, 0.37304, 99.8954, -85.2163, 0.37304, 100.395, -84.9663, 0.37304, 101.395, -73.7163, 0.37304, 99.3954, -73.4663, 0.37304, 78.1454, -75.7163, 0.37304, 77.8954, -77.9663, 0.37304, 99.3954, 57.5337, 0.37304, 99.3954, 57.7837, 0.37304, 99.8954, 58.5337, 0.37304, 99.8954, 71.0337, 0.37304, 102.145, 72.2837, 0.37304, 102.395, 72.5337, 0.37304, 101.895, 63.5337, 0.37304, 103.145, 63.7837, 0.37304, 104.145, 66.0337, 0.37304, 103.895, 68.0337, 0.37304, 104.895, 70.0337, 0.37304, 104.395, 70.5337, 0.37304, 102.645, 59.2837, 0.37304, 101.145, 62.0337, 0.37304, 101.895, 71.2837, 0.37304, 79.1454, 61.2837, 0.37304, 77.3954, 55.7837, 0.37304, 78.3954, 55.7837, 0.37304, 99.3954, -64.7163, 0.37304, 77.6454, -64.4663, 0.62304, 78.1454, -63.2163, 0.87304, 78.1454, -26.9663, 0.37304, 100.395, -24.9663, 0.37304, 100.395, -24.9663, 0.37304, 99.8954, -25.7163, 0.37304, 79.6454, -26.4663, 0.37304, 77.6454, -27.2163, 0.37304, 77.6454, -29.9663, 0.37304, 78.1454, -30.7163, 0.37304, 80.3954, -29.4663, 0.37304, 101.395, -28.2163, 0.37304, 101.395, -31.2163, 0.37304, 80.6454, -23.9663, 0.37304, 99.3954, -24.2163, 0.37304, 79.6454, -29.9663, 0.37304, 102.145, 19.5337, 0.37304, 79.3954, 19.5337, 0.37304, 99.8954, 24.5337, 0.37304, 78.1454, 23.5337, 0.37304, 77.6454, 73.2837, 0.37304, 101.895, 73.5337, 0.37304, 101.395, 75.5337, 0.37304, 101.395, 75.5337, 0.37304, 100.145, 76.2837, 0.37304, 99.3954, 77.7837, 0.37304, 78.1454, 72.0337, 0.37304, 78.6454, 77.5337, 0.37304, 99.3954, 72.0337, 0.37304, 77.6454, 86.2837, 0.37304, 103.895, 86.5337, 0.37304, 104.395, 88.0337, 0.37304, 104.395, 103.284, 0.37304, 91.3954, 105.284, 0.37304, 91.3954, 105.284, 0.37304, 90.6454, 88.2837, 0.37304, 104.895, 90.2837, 0.37304, 103.895, 90.2837, 0.37304, 102.645, 90.7837, 0.37304, 102.145, 92.2837, 0.37304, 102.145, 93.2837, 0.37304, 101.145, 79.2837, 0.37304, 78.1454, 78.5337, 0.37304, 99.3954, 102.784, 0.37304, 91.8954, 109.034, 0.37304, 86.3954, 109.784, 0.37304, 85.1454, 109.534, 0.37304, 83.3954, 107.284, 0.37304, 83.3954, 95.2837, 0.37304, 101.395, 96.0337, 0.37304, 99.3954, 105.034, 0.37304, 78.6454, 82.5337, 0.37304, 102.145, 99.2837, 0.37304, 99.3954, 99.2837, 0.37304, 97.8954, 106.284, 0.37304, 89.3954, 106.034, 0.37304, 82.3954, 106.284, 0.37304, 79.8954, 108.784, 0.37304, 89.3954, 83.7837, 0.37304, 104.145, 99.7837, 0.37304, 97.3954, 79.0337, 0.37304, 100.895, 101.784, 0.37304, 97.1454, -16.2163, 0.37304, 101.645, -15.9663, 0.37304, 102.145, -14.4663, 0.37304, 101.645, -17.9663, 0.37304, 101.145, -17.4663, 0.37304, 101.645, -13.2163, 0.37304, 99.3954, -21.4663, 0.37304, 99.3954, -20.2163, 0.37304, 101.395, -11.2163, 0.37304, 78.3954, -18.7163, 0.37304, 77.8954, -11.2163, 0.37304, 99.3954, 50.7837, 0.37304, 78.6454, 50.0337, 0.37304, 78.8954, 49.5337, 0.37304, 80.3954, 54.2837, 0.37304, 78.3954, 53.7837, 0.37304, 77.8954, 48.7837, 0.37304, 99.3954, 48.7837, 0.37304, 80.3954, -66.7163, 0.37304, 99.3954, -66.4663, 0.37304, 79.1454, -56.2163, 0.37304, 79.1454, -56.2163, 0.37304, 78.1454, -56.9663, 0.37304, 78.1454, -59.4663, 0.37304, 78.8954, -60.7163, 0.37304, 79.8954, -60.7163, 0.37304, 99.3954, -58.4663, 0.37304, 99.3954, -55.7163, 0.37304, 79.3954, -57.9663, 0.37304, 100.145, -48.9663, 0.37304, 104.645, -46.9663, 0.37304, 104.145, -46.2163, 0.37304, 102.395, -50.9663, 0.37304, 103.645, -54.2163, 0.37304, 102.145, -53.2163, 0.37304, 103.895, -43.9663, 0.37304, 78.8954, -45.7163, 0.37304, 78.1454, -47.7163, 0.37304, 78.1454, -57.7163, 0.37304, 100.895, -43.9663, 0.37304, 102.395, -39.9663, 0.37304, 78.1454, -40.4663, 0.37304, 78.1454, -40.9663, 0.37304, 79.6454, -34.2163, 0.37304, 80.1454, -31.4663, 0.37304, 103.395, -29.9663, 0.37304, 103.145, -36.9663, 0.37304, 102.395, -33.7163, 0.37304, 103.145, -33.4663, 0.37304, 103.645, -31.7163, 0.37304, 103.895, -40.9663, 0.37304, 102.895, -40.7163, 0.37304, 103.395, -38.9663, 0.37304, 102.395, -36.4663, 0.37304, 103.395, 19.2837, 0.37304, 100.145, 19.2837, 0.37304, 101.145, 23.0337, 0.37304, 102.395, 31.2837, 0.37304, 102.395, 32.7837, 0.37304, 102.395, 33.2837, 0.37304, 101.895, 32.2837, 0.37304, 79.3954, 26.5337, 0.37304, 104.145, 28.5337, 0.37304, 105.145, 30.0337, 0.37304, 104.645, 24.0337, 0.37304, 104.395, 6.28374, 0.37304, 79.3954, 2.53374, 0.37304, 78.3954, 6.28374, 0.37304, 99.3954, 18.7837, 0.37304, 99.3954, 10.7837, 0.37304, 100.395, 12.5337, 0.37304, 100.895, 12.7837, 0.37304, 100.395, 14.7837, 0.37304, 100.145, 15.2837, 0.37304, 99.3954, 11.0337, 0.37304, 80.3954, 12.2837, 0.37304, 80.1454, 12.5337, 0.37304, 78.3954, 33.7837, 0.37304, 101.395, 35.7837, 0.37304, 101.645, 35.7837, 0.37304, 100.395, 36.5337, 0.37304, 99.3954, 38.5337, 0.37304, 78.3954, 32.7837, 0.37304, 78.3954, 32.7837, 0.37304, 79.1454, 38.5337, 0.37304, 99.3954, 45.2837, 0.37304, 99.3954, 45.5337, 0.37304, 79.6454, 7.28374, 0.37304, 99.3954, 7.78374, 0.37304, 100.395)
polygons = [PackedInt32Array(1, 0, 2), PackedInt32Array(2, 0, 5), PackedInt32Array(2, 5, 4), PackedInt32Array(2, 4, 3), PackedInt32Array(7, 6, 8), PackedInt32Array(8, 6, 9), PackedInt32Array(12, 11, 10), PackedInt32Array(13, 12, 14), PackedInt32Array(14, 12, 15), PackedInt32Array(3, 17, 16), PackedInt32Array(10, 19, 18), PackedInt32Array(20, 22, 21), PackedInt32Array(24, 23, 25), PackedInt32Array(25, 23, 27), PackedInt32Array(25, 27, 26), PackedInt32Array(18, 28, 29), PackedInt32Array(29, 28, 3), PackedInt32Array(3, 28, 17), PackedInt32Array(32, 31, 30), PackedInt32Array(15, 12, 29), PackedInt32Array(29, 12, 10), PackedInt32Array(29, 10, 18), PackedInt32Array(9, 6, 28), PackedInt32Array(28, 6, 17), PackedInt32Array(2, 3, 33), PackedInt32Array(33, 3, 30), PackedInt32Array(16, 34, 22), PackedInt32Array(5, 25, 4), PackedInt32Array(4, 25, 26), PackedInt32Array(30, 3, 32), PackedInt32Array(32, 3, 16), PackedInt32Array(32, 16, 22), PackedInt32Array(32, 22, 20), PackedInt32Array(36, 35, 20), PackedInt32Array(20, 35, 32), PackedInt32Array(39, 38, 37), PackedInt32Array(37, 41, 40), PackedInt32Array(42, 39, 43), PackedInt32Array(43, 39, 44), PackedInt32Array(47, 46, 45), PackedInt32Array(48, 47, 49), PackedInt32Array(49, 47, 45), PackedInt32Array(49, 45, 51), PackedInt32Array(49, 51, 50), PackedInt32Array(53, 52, 48), PackedInt32Array(48, 52, 40), PackedInt32Array(48, 40, 47), PackedInt32Array(44, 39, 52), PackedInt32Array(52, 39, 37), PackedInt32Array(52, 37, 40), PackedInt32Array(55, 54, 56), PackedInt32Array(56, 54, 57), PackedInt32Array(64, 58, 61), PackedInt32Array(58, 65, 59), PackedInt32Array(59, 65, 60), PackedInt32Array(60, 65, 62), PackedInt32Array(62, 65, 63), PackedInt32Array(63, 65, 64), PackedInt32Array(64, 65, 58), PackedInt32Array(67, 66, 59), PackedInt32Array(59, 66, 68), PackedInt32Array(59, 68, 58), PackedInt32Array(57, 54, 66), PackedInt32Array(66, 54, 69), PackedInt32Array(66, 69, 68), PackedInt32Array(70, 61, 71), PackedInt32Array(71, 61, 72), PackedInt32Array(72, 61, 58), PackedInt32Array(72, 58, 73), PackedInt32Array(76, 75, 74), PackedInt32Array(78, 77, 76), PackedInt32Array(80, 79, 81), PackedInt32Array(81, 79, 83), PackedInt32Array(81, 83, 82), PackedInt32Array(74, 84, 83), PackedInt32Array(82, 86, 85), PackedInt32Array(76, 74, 78), PackedInt32Array(78, 74, 79), PackedInt32Array(79, 74, 83), PackedInt32Array(81, 82, 87), PackedInt32Array(87, 82, 85), PackedInt32Array(87, 85, 89), PackedInt32Array(87, 89, 88), PackedInt32Array(89, 90, 88), PackedInt32Array(93, 92, 91), PackedInt32Array(96, 95, 94), PackedInt32Array(94, 98, 97), PackedInt32Array(101, 100, 99), PackedInt32Array(96, 94, 99), PackedInt32Array(99, 94, 97), PackedInt32Array(99, 97, 102), PackedInt32Array(91, 103, 93), PackedInt32Array(93, 103, 104), PackedInt32Array(104, 103, 99), PackedInt32Array(99, 103, 101), PackedInt32Array(104, 106, 93), PackedInt32Array(93, 106, 105), PackedInt32Array(99, 102, 104), PackedInt32Array(91, 107, 103), PackedInt32Array(109, 108, 110), PackedInt32Array(110, 108, 113), PackedInt32Array(110, 113, 112), PackedInt32Array(110, 112, 111), PackedInt32Array(116, 115, 114), PackedInt32Array(114, 118, 117), PackedInt32Array(117, 119, 114), PackedInt32Array(114, 119, 116), PackedInt32Array(121, 120, 122), PackedInt32Array(122, 120, 123), PackedInt32Array(125, 124, 126), PackedInt32Array(126, 124, 127), PackedInt32Array(128, 112, 119), PackedInt32Array(119, 112, 116), PackedInt32Array(116, 112, 113), PackedInt32Array(131, 130, 129), PackedInt32Array(134, 133, 132), PackedInt32Array(111, 135, 122), PackedInt32Array(134, 132, 129), PackedInt32Array(129, 132, 123), PackedInt32Array(131, 129, 127), PackedInt32Array(127, 129, 126), PackedInt32Array(126, 129, 123), PackedInt32Array(111, 122, 123), PackedInt32Array(110, 111, 136), PackedInt32Array(136, 111, 123), PackedInt32Array(136, 123, 132), PackedInt32Array(136, 132, 137), PackedInt32Array(140, 139, 138), PackedInt32Array(142, 141, 143), PackedInt32Array(143, 141, 144), PackedInt32Array(146, 145, 147), PackedInt32Array(147, 145, 140), PackedInt32Array(138, 148, 140), PackedInt32Array(140, 148, 33), PackedInt32Array(140, 33, 147), PackedInt32Array(147, 33, 30), PackedInt32Array(148, 138, 149), PackedInt32Array(143, 144, 148), PackedInt32Array(148, 144, 33), PackedInt32Array(150, 140, 151), PackedInt32Array(151, 140, 145), PackedInt32Array(154, 153, 152), PackedInt32Array(155, 49, 152), PackedInt32Array(152, 49, 154), PackedInt32Array(154, 49, 156), PackedInt32Array(156, 49, 157), PackedInt32Array(157, 49, 50), PackedInt32Array(157, 50, 158), PackedInt32Array(160, 159, 161), PackedInt32Array(161, 159, 162), PackedInt32Array(162, 159, 163), PackedInt32Array(162, 164, 161), PackedInt32Array(162, 163, 165), PackedInt32Array(166, 163, 167), PackedInt32Array(167, 163, 159), PackedInt32Array(167, 159, 168), PackedInt32Array(170, 169, 171), PackedInt32Array(171, 169, 172), PackedInt32Array(172, 169, 173), PackedInt32Array(174, 173, 136), PackedInt32Array(136, 173, 169), PackedInt32Array(176, 175, 137), PackedInt32Array(137, 175, 177), PackedInt32Array(137, 177, 136), PackedInt32Array(136, 177, 174), PackedInt32Array(167, 168, 174), PackedInt32Array(174, 168, 173), PackedInt32Array(180, 179, 178), PackedInt32Array(182, 181, 183), PackedInt32Array(183, 181, 184), PackedInt32Array(178, 186, 185), PackedInt32Array(187, 183, 185), PackedInt32Array(185, 183, 184), PackedInt32Array(185, 184, 60), PackedInt32Array(62, 189, 60), PackedInt32Array(60, 189, 185), PackedInt32Array(185, 189, 178), PackedInt32Array(178, 189, 180), PackedInt32Array(180, 189, 188), PackedInt32Array(188, 64, 61), PackedInt32Array(64, 189, 63), PackedInt32Array(63, 189, 62), PackedInt32Array(189, 64, 188), PackedInt32Array(192, 191, 190), PackedInt32Array(190, 194, 193), PackedInt32Array(196, 195, 192), PackedInt32Array(190, 193, 192), PackedInt32Array(192, 193, 196), PackedInt32Array(196, 193, 73), PackedInt32Array(73, 193, 87), PackedInt32Array(73, 87, 88), PackedInt32Array(73, 88, 72), PackedInt32Array(198, 197, 199), PackedInt32Array(199, 197, 200), PackedInt32Array(202, 201, 203), PackedInt32Array(203, 201, 205), PackedInt32Array(203, 205, 204), PackedInt32Array(206, 204, 197), PackedInt32Array(197, 204, 200), PackedInt32Array(200, 204, 205), PackedInt32Array(207, 209, 208), PackedInt32Array(211, 210, 207), PackedInt32Array(207, 210, 203), PackedInt32Array(204, 101, 203), PackedInt32Array(203, 101, 207), PackedInt32Array(207, 101, 209), PackedInt32Array(209, 101, 103), PackedInt32Array(201, 212, 205), PackedInt32Array(213, 215, 214), PackedInt32Array(218, 217, 216), PackedInt32Array(213, 220, 219), PackedInt32Array(215, 213, 221), PackedInt32Array(221, 213, 216), PackedInt32Array(216, 213, 219), PackedInt32Array(223, 222, 224), PackedInt32Array(224, 222, 219), PackedInt32Array(219, 222, 216), PackedInt32Array(216, 222, 218), PackedInt32Array(219, 225, 224), PackedInt32Array(228, 227, 226), PackedInt32Array(229, 228, 230), PackedInt32Array(230, 228, 226), PackedInt32Array(230, 226, 174), PackedInt32Array(233, 232, 231), PackedInt32Array(231, 174, 233), PackedInt32Array(233, 174, 234), PackedInt32Array(234, 174, 177), PackedInt32Array(234, 235, 233), PackedInt32Array(231, 230, 174), PackedInt32Array(237, 236, 238), PackedInt32Array(238, 236, 239), PackedInt32Array(239, 236, 240), PackedInt32Array(240, 236, 241), PackedInt32Array(243, 242, 244), PackedInt32Array(244, 242, 236), PackedInt32Array(244, 236, 245), PackedInt32Array(247, 246, 248), PackedInt32Array(248, 246, 249), PackedInt32Array(248, 249, 244), PackedInt32Array(248, 244, 245), PackedInt32Array(242, 241, 236), PackedInt32Array(158, 251, 250), PackedInt32Array(252, 157, 253), PackedInt32Array(253, 157, 254), PackedInt32Array(254, 157, 250), PackedInt32Array(250, 157, 158), PackedInt32Array(256, 255, 257), PackedInt32Array(257, 255, 258), PackedInt32Array(261, 260, 259), PackedInt32Array(263, 262, 259), PackedInt32Array(259, 262, 261), PackedInt32Array(261, 262, 265), PackedInt32Array(261, 265, 264), PackedInt32Array(257, 258, 259), PackedInt32Array(259, 258, 263), PackedInt32Array(266, 265, 267), PackedInt32Array(267, 265, 262), PackedInt32Array(267, 262, 268), PackedInt32Array(106, 270, 269), PackedInt32Array(269, 218, 106), PackedInt32Array(106, 218, 105), PackedInt32Array(105, 218, 222), PackedInt32Array(272, 271, 273), PackedInt32Array(273, 271, 274), PackedInt32Array(51, 45, 275), PackedInt32Array(275, 273, 274), PackedInt32Array(274, 249, 275), PackedInt32Array(275, 249, 51), PackedInt32Array(51, 249, 276), PackedInt32Array(276, 249, 246), PackedInt32Array(246, 277, 276), PackedInt32Array(279, 278, 280), PackedInt32Array(280, 278, 282), PackedInt32Array(280, 282, 281), PackedInt32Array(280, 281, 34), PackedInt32Array(283, 34, 211), PackedInt32Array(211, 34, 281), PackedInt32Array(211, 281, 210), PackedInt32Array(34, 16, 280), PackedInt32Array(285, 284, 235), PackedInt32Array(235, 284, 286), PackedInt32Array(89, 85, 287), PackedInt32Array(286, 89, 235), PackedInt32Array(235, 89, 287), PackedInt32Array(235, 287, 233), PackedInt32Array(289, 288, 290), PackedInt32Array(290, 288, 225), PackedInt32Array(290, 225, 291), PackedInt32Array(291, 225, 219), PackedInt32Array(291, 292, 290), PackedInt32Array(290, 292, 254), PackedInt32Array(254, 292, 253), PackedInt32Array(294, 293, 245), PackedInt32Array(245, 293, 248), PackedInt32Array(248, 293, 295), PackedInt32Array(295, 293, 264), PackedInt32Array(264, 296, 295), PackedInt32Array(293, 261, 264), PackedInt32Array(298, 297, 299), PackedInt32Array(299, 297, 301), PackedInt32Array(299, 301, 300), PackedInt32Array(299, 300, 188), PackedInt32Array(188, 300, 302), PackedInt32Array(188, 302, 180), PackedInt32Array(267, 268, 300), PackedInt32Array(300, 268, 302), PackedInt32Array(305, 304, 303), PackedInt32Array(303, 307, 306), PackedInt32Array(310, 309, 308), PackedInt32Array(311, 314, 312), PackedInt32Array(312, 314, 313), PackedInt32Array(306, 315, 303), PackedInt32Array(303, 315, 305), PackedInt32Array(305, 315, 316), PackedInt32Array(305, 316, 317), PackedInt32Array(317, 316, 308), PackedInt32Array(317, 308, 318), PackedInt32Array(319, 310, 308), PackedInt32Array(314, 320, 319), PackedInt32Array(313, 314, 316), PackedInt32Array(316, 314, 319), PackedInt32Array(316, 319, 308), PackedInt32Array(175, 176, 321), PackedInt32Array(321, 176, 322), PackedInt32Array(325, 324, 323), PackedInt32Array(329, 328, 332), PackedInt32Array(332, 328, 327), PackedInt32Array(332, 327, 326), PackedInt32Array(332, 326, 331), PackedInt32Array(332, 331, 330), PackedInt32Array(335, 334, 333), PackedInt32Array(329, 332, 336), PackedInt32Array(336, 332, 337), PackedInt32Array(337, 332, 330), PackedInt32Array(337, 330, 323), PackedInt32Array(323, 330, 325), PackedInt32Array(338, 337, 333), PackedInt32Array(333, 337, 335), PackedInt32Array(335, 337, 323), PackedInt32Array(336, 339, 329), PackedInt32Array(341, 340, 342), PackedInt32Array(342, 340, 344), PackedInt32Array(342, 344, 343), PackedInt32Array(346, 345, 318), PackedInt32Array(318, 345, 317), PackedInt32Array(317, 345, 322), PackedInt32Array(340, 347, 344), PackedInt32Array(343, 321, 342), PackedInt32Array(342, 321, 348), PackedInt32Array(348, 321, 345), PackedInt32Array(345, 321, 322), PackedInt32Array(348, 349, 342), PackedInt32Array(352, 351, 350), PackedInt32Array(354, 353, 355), PackedInt32Array(355, 353, 350), PackedInt32Array(358, 357, 356), PackedInt32Array(350, 353, 352), PackedInt32Array(352, 353, 360), PackedInt32Array(352, 360, 359), PackedInt32Array(359, 360, 358), PackedInt32Array(358, 356, 359), PackedInt32Array(363, 362, 361), PackedInt32Array(361, 365, 363), PackedInt32Array(363, 365, 364), PackedInt32Array(365, 367, 364), PackedInt32Array(364, 367, 366), PackedInt32Array(364, 366, 369), PackedInt32Array(364, 369, 368), PackedInt32Array(371, 370, 372), PackedInt32Array(372, 370, 374), PackedInt32Array(372, 374, 373), PackedInt32Array(374, 370, 375), PackedInt32Array(378, 377, 379), PackedInt32Array(379, 377, 375), PackedInt32Array(375, 377, 374), PackedInt32Array(374, 377, 376), PackedInt32Array(382, 381, 380), PackedInt32Array(384, 383, 385), PackedInt32Array(385, 383, 386), PackedInt32Array(382, 380, 387), PackedInt32Array(387, 380, 388), PackedInt32Array(388, 380, 383), PackedInt32Array(383, 380, 386), PackedInt32Array(390, 389, 391), PackedInt32Array(391, 389, 392), PackedInt32Array(334, 391, 333), PackedInt32Array(333, 391, 392), PackedInt32Array(394, 393, 363), PackedInt32Array(363, 393, 395), PackedInt32Array(363, 395, 362), PackedInt32Array(395, 393, 396), PackedInt32Array(396, 393, 398), PackedInt32Array(396, 398, 397), PackedInt32Array(400, 399, 401), PackedInt32Array(401, 399, 402), PackedInt32Array(401, 402, 397), PackedInt32Array(401, 397, 398), PackedInt32Array(400, 403, 399), PackedInt32Array(405, 404, 406), PackedInt32Array(406, 404, 407), PackedInt32Array(407, 392, 406), PackedInt32Array(406, 392, 408), PackedInt32Array(408, 392, 409), PackedInt32Array(409, 392, 389), PackedInt32Array(412, 411, 410), PackedInt32Array(415, 414, 413), PackedInt32Array(412, 410, 416), PackedInt32Array(416, 410, 415), PackedInt32Array(416, 415, 413), PackedInt32Array(416, 413, 417), PackedInt32Array(419, 418, 412), PackedInt32Array(412, 418, 358), PackedInt32Array(358, 418, 357), PackedInt32Array(416, 419, 412), PackedInt32Array(377, 378, 420), PackedInt32Array(385, 421, 384), PackedInt32Array(384, 421, 422), PackedInt32Array(377, 420, 423), PackedInt32Array(423, 420, 424), PackedInt32Array(423, 424, 422), PackedInt32Array(423, 422, 421), PackedInt32Array(372, 373, 425), PackedInt32Array(428, 427, 426), PackedInt32Array(425, 429, 372), PackedInt32Array(372, 429, 428), PackedInt32Array(372, 428, 430), PackedInt32Array(430, 428, 426), PackedInt32Array(354, 355, 426), PackedInt32Array(426, 355, 430), PackedInt32Array(431, 380, 381), PackedInt32Array(381, 432, 431), PackedInt32Array(431, 432, 435), PackedInt32Array(431, 435, 433), PackedInt32Array(433, 435, 434), PackedInt32Array(327, 328, 436), PackedInt32Array(436, 328, 434), PackedInt32Array(435, 436, 434), PackedInt32Array(437, 439, 438), PackedInt32Array(415, 441, 440), PackedInt32Array(444, 443, 442), PackedInt32Array(415, 440, 414), PackedInt32Array(414, 440, 442), PackedInt32Array(442, 440, 444), PackedInt32Array(444, 440, 439), PackedInt32Array(439, 437, 444), PackedInt32Array(444, 437, 445), PackedInt32Array(445, 437, 400), PackedInt32Array(400, 437, 403), PackedInt32Array(447, 446, 448), PackedInt32Array(448, 446, 449), PackedInt32Array(451, 450, 452), PackedInt32Array(452, 450, 449), PackedInt32Array(452, 449, 446), PackedInt32Array(455, 454, 453), PackedInt32Array(457, 456, 458), PackedInt32Array(458, 456, 459), PackedInt32Array(455, 453, 459), PackedInt32Array(459, 453, 458), PackedInt32Array(461, 460, 462), PackedInt32Array(462, 460, 464), PackedInt32Array(462, 464, 463), PackedInt32Array(467, 466, 465), PackedInt32Array(471, 470, 468), PackedInt32Array(468, 470, 469), PackedInt32Array(473, 472, 474), PackedInt32Array(474, 472, 477), PackedInt32Array(474, 477, 476), PackedInt32Array(474, 476, 475), PackedInt32Array(480, 479, 478), PackedInt32Array(483, 482, 481), PackedInt32Array(485, 484, 486), PackedInt32Array(486, 484, 487), PackedInt32Array(489, 488, 490), PackedInt32Array(490, 488, 491), PackedInt32Array(495, 494, 492), PackedInt32Array(492, 494, 493), PackedInt32Array(498, 497, 496), PackedInt32Array(501, 500, 499), PackedInt32Array(503, 502, 504), PackedInt32Array(504, 502, 505), PackedInt32Array(508, 507, 506), PackedInt32Array(512, 511, 366), PackedInt32Array(366, 511, 509), PackedInt32Array(509, 511, 510), PackedInt32Array(505, 513, 508), PackedInt32Array(512, 504, 511), PackedInt32Array(511, 504, 506), PackedInt32Array(506, 504, 505), PackedInt32Array(506, 505, 508), PackedInt32Array(509, 369, 366), PackedInt32Array(516, 515, 514), PackedInt32Array(519, 518, 517), PackedInt32Array(521, 520, 522), PackedInt32Array(522, 520, 523), PackedInt32Array(523, 520, 524), PackedInt32Array(35, 530, 527), PackedInt32Array(527, 530, 525), PackedInt32Array(525, 529, 526), PackedInt32Array(526, 531, 36), PackedInt32Array(36, 531, 35), PackedInt32Array(35, 531, 530), PackedInt32Array(530, 528, 525), PackedInt32Array(529, 531, 526), PackedInt32Array(525, 528, 529), PackedInt32Array(531, 528, 530), PackedInt32Array(529, 528, 531), PackedInt32Array(533, 532, 534), PackedInt32Array(534, 532, 535), PackedInt32Array(537, 536, 538), PackedInt32Array(538, 536, 539), PackedInt32Array(541, 540, 542), PackedInt32Array(545, 544, 543), PackedInt32Array(548, 547, 546), PackedInt32Array(551, 550, 549), PackedInt32Array(549, 539, 536), PackedInt32Array(543, 551, 545), PackedInt32Array(545, 551, 549), PackedInt32Array(552, 535, 553), PackedInt32Array(553, 535, 554), PackedInt32Array(554, 535, 546), PackedInt32Array(546, 535, 548), PackedInt32Array(548, 535, 542), PackedInt32Array(548, 542, 540), PackedInt32Array(548, 540, 555), PackedInt32Array(556, 545, 549), PackedInt32Array(557, 556, 546), PackedInt32Array(546, 556, 549), PackedInt32Array(546, 549, 536), PackedInt32Array(546, 536, 554), PackedInt32Array(554, 536, 553), PackedInt32Array(553, 536, 552), PackedInt32Array(552, 534, 535), PackedInt32Array(558, 560, 559), PackedInt32Array(562, 561, 563), PackedInt32Array(563, 561, 564), PackedInt32Array(558, 566, 565), PackedInt32Array(567, 563, 568), PackedInt32Array(568, 563, 564), PackedInt32Array(564, 569, 560), PackedInt32Array(558, 574, 560), PackedInt32Array(560, 572, 564), PackedInt32Array(564, 572, 568), PackedInt32Array(568, 572, 570), PackedInt32Array(570, 572, 565), PackedInt32Array(565, 573, 558), PackedInt32Array(574, 572, 560), PackedInt32Array(558, 573, 574), PackedInt32Array(572, 573, 565), PackedInt32Array(574, 571, 572), PackedInt32Array(573, 571, 574), PackedInt32Array(572, 571, 573), PackedInt32Array(577, 576, 575), PackedInt32Array(577, 575, 578), PackedInt32Array(578, 575, 579), PackedInt32Array(581, 580, 579), PackedInt32Array(582, 581, 579), PackedInt32Array(535, 583, 582), PackedInt32Array(582, 579, 535), PackedInt32Array(535, 579, 575), PackedInt32Array(535, 575, 542), PackedInt32Array(585, 584, 586), PackedInt32Array(586, 584, 587), PackedInt32Array(587, 584, 588), PackedInt32Array(590, 589, 591), PackedInt32Array(591, 589, 592), PackedInt32Array(594, 593, 595), PackedInt32Array(595, 593, 588), PackedInt32Array(598, 597, 596), PackedInt32Array(601, 600, 599), PackedInt32Array(592, 589, 602), PackedInt32Array(602, 589, 596), PackedInt32Array(596, 589, 598), PackedInt32Array(599, 603, 601), PackedInt32Array(601, 603, 604), PackedInt32Array(606, 605, 595), PackedInt32Array(604, 603, 589), PackedInt32Array(589, 603, 598), PackedInt32Array(598, 603, 584), PackedInt32Array(607, 611, 603), PackedInt32Array(603, 609, 584), PackedInt32Array(584, 609, 588), PackedInt32Array(588, 610, 595), PackedInt32Array(595, 610, 606), PackedInt32Array(606, 610, 607), PackedInt32Array(611, 609, 603), PackedInt32Array(607, 610, 611), PackedInt32Array(609, 610, 588), PackedInt32Array(611, 608, 609), PackedInt32Array(610, 608, 611), PackedInt32Array(609, 608, 610), PackedInt32Array(612, 578, 613), PackedInt32Array(613, 578, 579), PackedInt32Array(615, 614, 616), PackedInt32Array(616, 614, 618), PackedInt32Array(616, 618, 617), PackedInt32Array(616, 617, 579), PackedInt32Array(579, 617, 613), PackedInt32Array(613, 617, 619), PackedInt32Array(565, 621, 620), PackedInt32Array(623, 622, 624), PackedInt32Array(624, 622, 625), PackedInt32Array(626, 619, 620), PackedInt32Array(620, 619, 565), PackedInt32Array(565, 619, 617), PackedInt32Array(565, 617, 570), PackedInt32Array(627, 624, 625), PackedInt32Array(628, 570, 629), PackedInt32Array(629, 570, 617), PackedInt32Array(625, 626, 627), PackedInt32Array(627, 626, 620), PackedInt32Array(631, 630, 632), PackedInt32Array(632, 630, 569), PackedInt32Array(634, 560, 635), PackedInt32Array(635, 560, 633), PackedInt32Array(633, 560, 630), PackedInt32Array(630, 560, 569), PackedInt32Array(636, 634, 637), PackedInt32Array(637, 634, 635), PackedInt32Array(637, 635, 633), PackedInt32Array(637, 633, 638), PackedInt32Array(638, 633, 639), PackedInt32Array(640, 638, 641), PackedInt32Array(641, 638, 606), PackedInt32Array(606, 638, 605), PackedInt32Array(605, 638, 639), PackedInt32Array(606, 642, 641), PackedInt32Array(643, 645, 644), PackedInt32Array(647, 646, 648), PackedInt32Array(648, 646, 650), PackedInt32Array(648, 650, 649), PackedInt32Array(651, 648, 649), PackedInt32Array(649, 652, 651), PackedInt32Array(651, 652, 405), PackedInt32Array(405, 652, 404), PackedInt32Array(656, 655, 653), PackedInt32Array(653, 655, 654), PackedInt32Array(659, 658, 657), PackedInt32Array(662, 661, 660), PackedInt32Array(664, 663, 665), PackedInt32Array(665, 663, 666), PackedInt32Array(660, 667, 527), PackedInt32Array(662, 660, 527), PackedInt32Array(668, 662, 669), PackedInt32Array(669, 662, 670), PackedInt32Array(665, 666, 525), PackedInt32Array(525, 666, 671), PackedInt32Array(525, 671, 662), PackedInt32Array(525, 662, 527), PackedInt32Array(671, 670, 662), PackedInt32Array(674, 673, 672), PackedInt32Array(677, 676, 675), PackedInt32Array(679, 678, 672), PackedInt32Array(677, 675, 680), PackedInt32Array(680, 675, 681), PackedInt32Array(681, 675, 349), PackedInt32Array(683, 682, 674), PackedInt32Array(684, 687, 685), PackedInt32Array(685, 687, 686), PackedInt32Array(672, 678, 674), PackedInt32Array(674, 678, 688), PackedInt32Array(674, 688, 683), PackedInt32Array(690, 689, 680), PackedInt32Array(683, 688, 681), PackedInt32Array(681, 688, 687), PackedInt32Array(681, 687, 690), PackedInt32Array(681, 690, 680), PackedInt32Array(687, 691, 690), PackedInt32Array(692, 342, 693), PackedInt32Array(693, 342, 349), PackedInt32Array(693, 349, 675), PackedInt32Array(693, 675, 694), PackedInt32Array(688, 686, 687), PackedInt32Array(697, 696, 695), PackedInt32Array(698, 508, 513), PackedInt32Array(697, 695, 699), PackedInt32Array(699, 695, 698), PackedInt32Array(699, 698, 513), PackedInt32Array(699, 513, 700), PackedInt32Array(704, 703, 701), PackedInt32Array(701, 703, 702), PackedInt32Array(706, 705, 707), PackedInt32Array(707, 705, 708), PackedInt32Array(708, 705, 709), PackedInt32Array(711, 710, 709), PackedInt32Array(709, 710, 708), PackedInt32Array(712, 650, 646), PackedInt32Array(713, 712, 646), PackedInt32Array(715, 714, 716), PackedInt32Array(716, 714, 717), PackedInt32Array(717, 714, 713), PackedInt32Array(717, 713, 646), PackedInt32Array(719, 718, 699), PackedInt32Array(720, 697, 699), PackedInt32Array(722, 721, 723), PackedInt32Array(723, 721, 718), PackedInt32Array(718, 721, 699), PackedInt32Array(699, 721, 720), PackedInt32Array(725, 724, 726), PackedInt32Array(726, 724, 727), PackedInt32Array(721, 722, 727), PackedInt32Array(727, 722, 726), PackedInt32Array(730, 729, 728), PackedInt32Array(730, 728, 666), PackedInt32Array(666, 728, 671), PackedInt32Array(671, 728, 731), PackedInt32Array(735, 734, 732), PackedInt32Array(732, 734, 733), PackedInt32Array(738, 737, 736), PackedInt32Array(740, 739, 741), PackedInt32Array(741, 739, 742), PackedInt32Array(741, 742, 693), PackedInt32Array(693, 694, 741), PackedInt32Array(745, 744, 743), PackedInt32Array(747, 746, 743), PackedInt32Array(743, 746, 745), PackedInt32Array(750, 749, 748), PackedInt32Array(752, 751, 753), PackedInt32Array(753, 751, 754), PackedInt32Array(757, 756, 755), PackedInt32Array(748, 758, 750), PackedInt32Array(750, 758, 755), PackedInt32Array(754, 751, 757), PackedInt32Array(757, 751, 759), PackedInt32Array(757, 759, 756), PackedInt32Array(757, 755, 739), PackedInt32Array(739, 755, 758), PackedInt32Array(739, 758, 742), PackedInt32Array(731, 728, 760), PackedInt32Array(760, 728, 761), PackedInt32Array(764, 763, 762), PackedInt32Array(766, 765, 767), PackedInt32Array(767, 765, 762), PackedInt32Array(762, 765, 764), PackedInt32Array(770, 769, 768), PackedInt32Array(716, 772, 771), PackedInt32Array(768, 775, 770), PackedInt32Array(770, 775, 773), PackedInt32Array(773, 775, 774), PackedInt32Array(775, 777, 771), PackedInt32Array(771, 777, 776), PackedInt32Array(771, 776, 715), PackedInt32Array(771, 715, 716), PackedInt32Array(775, 768, 777), PackedInt32Array(761, 779, 778), PackedInt32Array(778, 780, 761), PackedInt32Array(761, 780, 781), PackedInt32Array(761, 781, 760), PackedInt32Array(780, 782, 781), PackedInt32Array(786, 785, 783), PackedInt32Array(783, 785, 784), PackedInt32Array(789, 788, 787), PackedInt32Array(787, 790, 782), PackedInt32Array(787, 782, 789), PackedInt32Array(789, 782, 791), PackedInt32Array(791, 782, 780), PackedInt32Array(791, 780, 792), PackedInt32Array(793, 789, 794), PackedInt32Array(794, 789, 791), PackedInt32Array(791, 792, 795), PackedInt32Array(799, 798, 796), PackedInt32Array(796, 798, 797), PackedInt32Array(801, 800, 802), PackedInt32Array(802, 800, 607), PackedInt32Array(642, 606, 803), PackedInt32Array(803, 606, 607), PackedInt32Array(803, 607, 800), PackedInt32Array(803, 800, 804), PackedInt32Array(623, 805, 622), PackedInt32Array(622, 805, 806), PackedInt32Array(807, 555, 808), PackedInt32Array(808, 555, 540), PackedInt32Array(808, 540, 806), PackedInt32Array(805, 809, 806), PackedInt32Array(806, 809, 808), PackedInt32Array(811, 810, 812), PackedInt32Array(812, 810, 813), PackedInt32Array(812, 813, 755), PackedInt32Array(755, 813, 750), PackedInt32Array(773, 774, 814), PackedInt32Array(814, 774, 815), PackedInt32Array(814, 815, 816), PackedInt32Array(816, 815, 818), PackedInt32Array(816, 818, 817), PackedInt32Array(819, 810, 811), PackedInt32Array(819, 821, 820), PackedInt32Array(823, 822, 824), PackedInt32Array(824, 822, 825), PackedInt32Array(822, 826, 827), PackedInt32Array(827, 826, 810), PackedInt32Array(810, 826, 828), PackedInt32Array(828, 826, 829), PackedInt32Array(820, 830, 819), PackedInt32Array(819, 830, 810), PackedInt32Array(810, 830, 827), PackedInt32Array(822, 832, 831), PackedInt32Array(826, 822, 831), PackedInt32Array(827, 825, 822), PackedInt32Array(834, 833, 813), PackedInt32Array(834, 813, 828), PackedInt32Array(828, 813, 810), PackedInt32Array(837, 836, 835), PackedInt32Array(835, 724, 725), PackedInt32Array(839, 838, 840), PackedInt32Array(840, 838, 841), PackedInt32Array(843, 842, 844), PackedInt32Array(844, 842, 841), PackedInt32Array(844, 841, 838), PackedInt32Array(844, 838, 845), PackedInt32Array(844, 846, 843), PackedInt32Array(843, 846, 837), PackedInt32Array(843, 837, 835), PackedInt32Array(843, 835, 725), PackedInt32Array(848, 847, 849), PackedInt32Array(849, 847, 850), PackedInt32Array(852, 851, 853), PackedInt32Array(853, 851, 627), PackedInt32Array(627, 851, 854), PackedInt32Array(850, 852, 849), PackedInt32Array(849, 852, 853), PackedInt32Array(627, 620, 853), PackedInt32Array(855, 808, 856), PackedInt32Array(856, 808, 809), PackedInt32Array(856, 809, 857), PackedInt32Array(861, 860, 858), PackedInt32Array(858, 860, 859), PackedInt32Array(863, 862, 864), PackedInt32Array(864, 862, 865), PackedInt32Array(867, 866, 851), PackedInt32Array(851, 866, 854), PackedInt32Array(854, 866, 857), PackedInt32Array(869, 868, 870), PackedInt32Array(870, 868, 871), PackedInt32Array(872, 874, 873), PackedInt32Array(872, 875, 870), PackedInt32Array(871, 877, 876), PackedInt32Array(872, 870, 871), PackedInt32Array(866, 874, 857), PackedInt32Array(857, 874, 876), PackedInt32Array(876, 874, 871), PackedInt32Array(871, 874, 872), PackedInt32Array(876, 856, 857), PackedInt32Array(880, 879, 878), PackedInt32Array(882, 881, 883), PackedInt32Array(883, 881, 884), PackedInt32Array(883, 885, 886), PackedInt32Array(886, 885, 804), PackedInt32Array(804, 885, 887), PackedInt32Array(804, 887, 803), PackedInt32Array(878, 803, 880), PackedInt32Array(880, 803, 887), PackedInt32Array(885, 883, 884), PackedInt32Array(891, 890, 888), PackedInt32Array(888, 890, 889), PackedInt32Array(893, 892, 894), PackedInt32Array(896, 895, 897), PackedInt32Array(897, 895, 898), PackedInt32Array(898, 899, 900), PackedInt32Array(900, 899, 892), PackedInt32Array(892, 899, 894), PackedInt32Array(894, 899, 817), PackedInt32Array(900, 897, 898), PackedInt32Array(817, 818, 894), PackedInt32Array(902, 901, 903), PackedInt32Array(903, 901, 904), PackedInt32Array(904, 901, 905), PackedInt32Array(906, 903, 907), PackedInt32Array(907, 903, 904), PackedInt32Array(910, 909, 908), PackedInt32Array(910, 908, 792), PackedInt32Array(792, 908, 905), PackedInt32Array(792, 905, 901), PackedInt32Array(792, 901, 795), PackedInt32Array(848, 911, 847), PackedInt32Array(847, 911, 912), PackedInt32Array(911, 621, 565), PackedInt32Array(914, 913, 912), PackedInt32Array(911, 565, 912), PackedInt32Array(912, 565, 914), PackedInt32Array(914, 565, 566), PackedInt32Array(917, 916, 915), PackedInt32Array(917, 922, 918), PackedInt32Array(918, 922, 880), PackedInt32Array(880, 913, 879), PackedInt32Array(913, 921, 915), PackedInt32Array(915, 923, 917), PackedInt32Array(917, 919, 922), PackedInt32Array(922, 913, 880), PackedInt32Array(921, 923, 915), PackedInt32Array(913, 922, 921), PackedInt32Array(923, 919, 917), PackedInt32Array(919, 920, 922), PackedInt32Array(921, 919, 923), PackedInt32Array(922, 920, 921), PackedInt32Array(919, 921, 920), PackedInt32Array(915, 912, 913), PackedInt32Array(927, 926, 924), PackedInt32Array(924, 926, 925), PackedInt32Array(929, 928, 930), PackedInt32Array(930, 928, 918), PackedInt32Array(930, 918, 887), PackedInt32Array(887, 918, 880), PackedInt32Array(928, 931, 918), PackedInt32Array(933, 932, 934), PackedInt32Array(934, 932, 935), PackedInt32Array(937, 936, 826), PackedInt32Array(826, 936, 829), PackedInt32Array(829, 936, 938), PackedInt32Array(935, 932, 826), PackedInt32Array(826, 932, 939), PackedInt32Array(826, 939, 937), PackedInt32Array(943, 942, 940), PackedInt32Array(940, 942, 941), PackedInt32Array(945, 944, 946), PackedInt32Array(946, 944, 947), PackedInt32Array(951, 950, 948), PackedInt32Array(948, 950, 949), PackedInt32Array(955, 954, 952), PackedInt32Array(952, 954, 953), PackedInt32Array(958, 957, 956), PackedInt32Array(960, 959, 961), PackedInt32Array(961, 959, 962), PackedInt32Array(962, 959, 963), PackedInt32Array(964, 958, 965), PackedInt32Array(965, 958, 956), PackedInt32Array(959, 965, 885), PackedInt32Array(885, 965, 956), PackedInt32Array(885, 956, 930), PackedInt32Array(885, 930, 887), PackedInt32Array(968, 967, 966), PackedInt32Array(959, 966, 963), PackedInt32Array(959, 969, 965), PackedInt32Array(968, 966, 970), PackedInt32Array(970, 966, 959), PackedInt32Array(970, 959, 885), PackedInt32Array(972, 971, 931), PackedInt32Array(931, 971, 918), PackedInt32Array(975, 974, 973), PackedInt32Array(971, 976, 918), PackedInt32Array(977, 975, 976), PackedInt32Array(976, 975, 918), PackedInt32Array(918, 975, 973), PackedInt32Array(918, 973, 917), PackedInt32Array(979, 978, 980), PackedInt32Array(980, 978, 981), PackedInt32Array(981, 978, 982), PackedInt32Array(984, 983, 985), PackedInt32Array(985, 983, 987), PackedInt32Array(985, 987, 986), PackedInt32Array(989, 988, 990), PackedInt32Array(990, 988, 991), PackedInt32Array(993, 992, 994), PackedInt32Array(994, 992, 996), PackedInt32Array(994, 996, 995), PackedInt32Array(998, 997, 999), PackedInt32Array(999, 997, 1000), PackedInt32Array(1002, 1001, 1003), PackedInt32Array(1003, 1001, 1004), PackedInt32Array(1003, 1004, 872), PackedInt32Array(1007, 1006, 1005), PackedInt32Array(974, 1018, 973), PackedInt32Array(973, 1019, 873), PackedInt32Array(873, 1017, 872), PackedInt32Array(872, 1017, 1003), PackedInt32Array(1003, 1014, 1008), PackedInt32Array(1008, 1020, 1009), PackedInt32Array(1009, 1020, 1010), PackedInt32Array(1010, 1014, 995), PackedInt32Array(995, 1014, 974), PackedInt32Array(1018, 1016, 973), PackedInt32Array(974, 1014, 1018), PackedInt32Array(1019, 1017, 873), PackedInt32Array(973, 1016, 1019), PackedInt32Array(1017, 1012, 1003), PackedInt32Array(1014, 1020, 1008), PackedInt32Array(1003, 1015, 1014), PackedInt32Array(1020, 1014, 1010), PackedInt32Array(1018, 1013, 1016), PackedInt32Array(1014, 1015, 1018), PackedInt32Array(1019, 1011, 1017), PackedInt32Array(1016, 1012, 1019), PackedInt32Array(1012, 1015, 1003), PackedInt32Array(1017, 1011, 1012), PackedInt32Array(1013, 1015, 1016), PackedInt32Array(1018, 1015, 1013), PackedInt32Array(1019, 1012, 1011), PackedInt32Array(1016, 1015, 1012), PackedInt32Array(1007, 1005, 994), PackedInt32Array(983, 1021, 987), PackedInt32Array(1022, 986, 1023), PackedInt32Array(999, 1024, 998), PackedInt32Array(1026, 1025, 982), PackedInt32Array(1027, 1029, 1028), PackedInt32Array(1022, 1029, 986), PackedInt32Array(986, 1029, 985), PackedInt32Array(985, 1029, 1030), PackedInt32Array(995, 1033, 1010), PackedInt32Array(1010, 1033, 1032), PackedInt32Array(1010, 1032, 1009), PackedInt32Array(1009, 1032, 1031), PackedInt32Array(1009, 1031, 1008), PackedInt32Array(1008, 1031, 1000), PackedInt32Array(1008, 1000, 1003), PackedInt32Array(978, 1035, 1034), PackedInt32Array(988, 1034, 991), PackedInt32Array(991, 1034, 1035), PackedInt32Array(1037, 1036, 1027), PackedInt32Array(1027, 1036, 1029), PackedInt32Array(1004, 1038, 872), PackedInt32Array(978, 1034, 982), PackedInt32Array(982, 1034, 1026), PackedInt32Array(1026, 1034, 974), PackedInt32Array(995, 1040, 1039), PackedInt32Array(1041, 1044, 1042), PackedInt32Array(1042, 1044, 1043), PackedInt32Array(999, 1029, 1036), PackedInt32Array(1034, 1045, 1007), PackedInt32Array(995, 1039, 1033), PackedInt32Array(1033, 1039, 1032), PackedInt32Array(1032, 1039, 1031), PackedInt32Array(1031, 1039, 1030), PackedInt32Array(1031, 1030, 1029), PackedInt32Array(1031, 1029, 999), PackedInt32Array(1031, 999, 1000), PackedInt32Array(1043, 1044, 1039), PackedInt32Array(1039, 1044, 1030), PackedInt32Array(994, 995, 1007), PackedInt32Array(1007, 995, 1034), PackedInt32Array(1034, 995, 974), PackedInt32Array(1048, 1047, 1046), PackedInt32Array(1046, 1050, 1049), PackedInt32Array(1051, 1048, 1046), PackedInt32Array(1046, 1049, 1051), PackedInt32Array(1051, 1049, 908), PackedInt32Array(1051, 908, 1053), PackedInt32Array(1051, 1053, 1052), PackedInt32Array(1053, 1054, 1052), PackedInt32Array(1049, 905, 908), PackedInt32Array(1057, 1056, 1055), PackedInt32Array(1057, 1055, 1058), PackedInt32Array(1058, 1055, 1059), PackedInt32Array(1061, 1060, 1062), PackedInt32Array(1062, 1060, 839), PackedInt32Array(1062, 839, 840), PackedInt32Array(1058, 1059, 1063), PackedInt32Array(1063, 1059, 1064), PackedInt32Array(1064, 1059, 1065), PackedInt32Array(1065, 1059, 1061), PackedInt32Array(1062, 1065, 1061), PackedInt32Array(1067, 1066, 1068), PackedInt32Array(1068, 1066, 1069), PackedInt32Array(1071, 1069, 1070), PackedInt32Array(1073, 1072, 1070), PackedInt32Array(1070, 1072, 1071), PackedInt32Array(1071, 1072, 936), PackedInt32Array(1076, 1075, 1074), PackedInt32Array(1077, 1070, 1066), PackedInt32Array(1066, 1070, 1069), PackedInt32Array(1076, 1074, 938), PackedInt32Array(1072, 1078, 936), PackedInt32Array(936, 1078, 1079), PackedInt32Array(936, 1079, 1076), PackedInt32Array(936, 1076, 938), PackedInt32Array(1082, 1081, 1080), PackedInt32Array(1083, 895, 896), PackedInt32Array(1085, 1084, 1086), PackedInt32Array(1086, 1084, 1080), PackedInt32Array(1080, 1084, 1082), PackedInt32Array(1082, 1084, 1087), PackedInt32Array(1087, 1084, 1088), PackedInt32Array(1088, 1084, 1089), PackedInt32Array(1083, 896, 1088), PackedInt32Array(1088, 896, 1087), PackedInt32Array(1091, 1090, 1092), PackedInt32Array(1092, 1090, 1093), PackedInt32Array(1095, 1094, 1096), PackedInt32Array(1096, 1094, 1097), PackedInt32Array(1100, 1099, 1098), PackedInt32Array(1101, 1098, 1102), PackedInt32Array(1098, 1101, 1100), PackedInt32Array(1100, 1101, 1103), PackedInt32Array(1103, 1101, 1104), PackedInt32Array(1105, 1100, 1106), PackedInt32Array(1106, 1100, 1103), PackedInt32Array(1107, 1096, 1108), PackedInt32Array(1108, 1096, 1097), PackedInt32Array(1101, 1051, 1104), PackedInt32Array(1104, 1051, 1097), PackedInt32Array(1097, 1051, 1108), PackedInt32Array(1108, 1051, 1052), PackedInt32Array(1110, 1109, 1111), PackedInt32Array(1111, 1109, 1112), PackedInt32Array(1112, 1109, 1113), PackedInt32Array(1113, 1114, 1112), PackedInt32Array(1118, 1117, 1115), PackedInt32Array(1115, 1117, 1116), PackedInt32Array(1121, 1120, 1119), PackedInt32Array(1123, 1122, 1124), PackedInt32Array(1124, 1122, 1125), PackedInt32Array(1128, 1127, 1126), PackedInt32Array(1131, 1130, 1129), PackedInt32Array(1132, 1119, 1133), PackedInt32Array(1135, 1134, 1136), PackedInt32Array(1136, 1134, 1137), PackedInt32Array(1136, 1137, 1121), PackedInt32Array(1136, 1121, 1119), PackedInt32Array(1128, 1126, 1138), PackedInt32Array(1138, 1126, 1139), PackedInt32Array(1139, 1126, 1137), PackedInt32Array(1125, 1122, 1139), PackedInt32Array(1139, 1122, 1140), PackedInt32Array(1139, 1140, 1138), PackedInt32Array(1131, 1129, 1132), PackedInt32Array(1132, 1129, 1078), PackedInt32Array(1132, 1078, 1136), PackedInt32Array(1132, 1136, 1119), PackedInt32Array(1132, 1141, 1131), PackedInt32Array(1121, 1137, 1126), PackedInt32Array(1129, 1079, 1078), PackedInt32Array(1057, 1142, 1056), PackedInt32Array(1056, 1142, 1143), PackedInt32Array(1146, 1145, 1144), PackedInt32Array(1148, 1147, 1149), PackedInt32Array(1149, 1147, 1146), PackedInt32Array(1149, 1146, 1144), PackedInt32Array(1149, 1144, 1150), PackedInt32Array(1150, 1144, 1151), PackedInt32Array(1144, 1143, 1151), PackedInt32Array(1151, 1143, 1142), PackedInt32Array(1151, 1142, 1152), PackedInt32Array(1156, 1155, 1153), PackedInt32Array(1153, 1155, 1154), PackedInt32Array(1157, 1160, 1158), PackedInt32Array(1158, 1160, 1159), PackedInt32Array(1162, 1161, 1163), PackedInt32Array(1163, 1161, 1160), PackedInt32Array(1163, 1160, 1157), PackedInt32Array(1163, 1157, 1164), PackedInt32Array(1163, 1165, 1162), PackedInt32Array(1162, 1165, 1086), PackedInt32Array(1086, 1165, 1085), PackedInt32Array(1168, 1167, 1166), PackedInt32Array(1170, 1169, 1171), PackedInt32Array(1171, 1169, 1172), PackedInt32Array(1173, 1172, 1174), PackedInt32Array(1174, 1172, 1169), PackedInt32Array(1168, 1176, 1175), PackedInt32Array(1179, 1178, 1177), PackedInt32Array(1179, 1177, 1097), PackedInt32Array(1097, 1177, 1104), PackedInt32Array(1104, 1177, 1172), PackedInt32Array(1172, 1177, 1171), PackedInt32Array(1168, 1166, 1176), PackedInt32Array(1176, 1166, 1172), PackedInt32Array(1104, 1172, 1166), PackedInt32Array(1177, 1180, 1171), PackedInt32Array(1183, 1182, 1181), PackedInt32Array(1187, 1186, 1184), PackedInt32Array(1184, 1186, 1185), PackedInt32Array(1189, 1188, 1133), PackedInt32Array(1189, 1133, 1190), PackedInt32Array(1190, 1133, 1191), PackedInt32Array(1191, 1133, 1119), PackedInt32Array(1193, 1192, 1194), PackedInt32Array(1149, 1196, 1195), PackedInt32Array(1198, 1197, 1199), PackedInt32Array(1199, 1197, 1194), PackedInt32Array(1194, 1192, 1199), PackedInt32Array(1199, 1192, 1148), PackedInt32Array(1199, 1148, 1149), PackedInt32Array(1199, 1149, 1195), PackedInt32Array(1201, 1200, 1202), PackedInt32Array(1202, 1200, 1203), PackedInt32Array(1205, 1204, 1206), PackedInt32Array(1206, 1204, 1207), PackedInt32Array(1206, 1207, 1203), PackedInt32Array(1203, 1207, 1202), PackedInt32Array(1208, 1190, 1209), PackedInt32Array(1209, 1190, 1191), PackedInt32Array(1211, 1209, 1210), PackedInt32Array(1210, 1209, 1191), PackedInt32Array(1214, 1213, 1212), PackedInt32Array(1159, 1214, 1158), PackedInt32Array(1158, 1214, 1215), PackedInt32Array(1217, 1216, 1218), PackedInt32Array(1218, 1216, 1212), PackedInt32Array(1212, 1216, 1214), PackedInt32Array(1214, 1216, 1219), PackedInt32Array(1220, 1216, 1221), PackedInt32Array(1221, 1216, 1222), PackedInt32Array(1222, 1216, 1217), PackedInt32Array(1219, 1215, 1214), PackedInt32Array(1224, 1223, 1225), PackedInt32Array(1225, 1223, 1180), PackedInt32Array(1223, 1226, 1180), PackedInt32Array(1180, 1226, 1227), PackedInt32Array(1180, 1227, 1171), PackedInt32Array(1229, 1228, 1230), PackedInt32Array(1230, 1228, 1231), PackedInt32Array(1234, 1233, 1232), PackedInt32Array(1236, 1235, 1237), PackedInt32Array(1237, 1235, 1238), PackedInt32Array(1238, 1235, 1239), PackedInt32Array(1238, 1239, 1234), PackedInt32Array(1240, 1230, 1237), PackedInt32Array(1237, 1230, 1231), PackedInt32Array(1237, 1231, 1236), PackedInt32Array(1210, 1241, 1211), PackedInt32Array(1211, 1241, 1242), PackedInt32Array(1245, 1244, 1243), PackedInt32Array(1234, 1232, 1243), PackedInt32Array(1247, 1246, 1242), PackedInt32Array(1249, 1248, 1235), PackedInt32Array(1235, 1248, 1239), PackedInt32Array(1238, 1234, 1241), PackedInt32Array(1241, 1234, 1243), PackedInt32Array(1241, 1243, 1242), PackedInt32Array(1242, 1243, 1247), PackedInt32Array(1232, 1245, 1243), PackedInt32Array(1253, 1252, 1250), PackedInt32Array(1250, 1252, 1251), PackedInt32Array(1256, 1255, 1254), PackedInt32Array(1259, 1258, 1257), PackedInt32Array(1262, 1261, 1260), PackedInt32Array(1257, 1264, 1263), PackedInt32Array(1260, 1265, 1262), PackedInt32Array(1262, 1265, 1259), PackedInt32Array(1262, 1259, 1257), PackedInt32Array(1262, 1257, 1263), PackedInt32Array(1266, 1260, 1267), PackedInt32Array(1267, 1260, 1198), PackedInt32Array(1198, 1260, 1197), PackedInt32Array(1260, 1266, 1265), PackedInt32Array(1227, 1269, 1268), PackedInt32Array(1270, 1269, 1271), PackedInt32Array(1271, 1269, 1227), PackedInt32Array(1271, 1227, 1226), PackedInt32Array(1271, 1226, 1272), PackedInt32Array(1274, 1273, 1275), PackedInt32Array(964, 1277, 1276), PackedInt32Array(1276, 1278, 964), PackedInt32Array(964, 1278, 1279), PackedInt32Array(964, 1279, 958), PackedInt32Array(1281, 1280, 1282), PackedInt32Array(1282, 1280, 1283), PackedInt32Array(1282, 1283, 1222), PackedInt32Array(1222, 1283, 1221), PackedInt32Array(1288, 1287, 1284), PackedInt32Array(1284, 1287, 1286), PackedInt32Array(1284, 1286, 1285), PackedInt32Array(1290, 1289, 1291), PackedInt32Array(1291, 1289, 1292), PackedInt32Array(1291, 1292, 1288), PackedInt32Array(1291, 1288, 1284), PackedInt32Array(1280, 1289, 1283), PackedInt32Array(1283, 1289, 1290), PackedInt32Array(1294, 1293, 1272), PackedInt32Array(1272, 1293, 1271), PackedInt32Array(1293, 1296, 1295), PackedInt32Array(1295, 1297, 1293), PackedInt32Array(1293, 1297, 1298), PackedInt32Array(1293, 1298, 1271), PackedInt32Array(1297, 1299, 1298), PackedInt32Array(1301, 1300, 1302), PackedInt32Array(1302, 1300, 1304), PackedInt32Array(1302, 1304, 1303), PackedInt32Array(1302, 1303, 1247), PackedInt32Array(1303, 1305, 1247), PackedInt32Array(1247, 1243, 1302), PackedInt32Array(1300, 1306, 1304), PackedInt32Array(1309, 1308, 1310), PackedInt32Array(1310, 1308, 1264), PackedInt32Array(1264, 1308, 1307), PackedInt32Array(1264, 1307, 1263), PackedInt32Array(1313, 1312, 1314), PackedInt32Array(1314, 1312, 1315), PackedInt32Array(1315, 1312, 1308), PackedInt32Array(1308, 1312, 1311), PackedInt32Array(1308, 1309, 1315), PackedInt32Array(1317, 1316, 1318), PackedInt32Array(1318, 1316, 1319), PackedInt32Array(1320, 1319, 1321), PackedInt32Array(1320, 1279, 1278), PackedInt32Array(1318, 1319, 1322), PackedInt32Array(1322, 1319, 1320), PackedInt32Array(1322, 1320, 1278), PackedInt32Array(1322, 1278, 1323), PackedInt32Array(1327, 1326, 1324), PackedInt32Array(1324, 1326, 1325), PackedInt32Array(1326, 1327, 1329), PackedInt32Array(1329, 1327, 1328), PackedInt32Array(1333, 1332, 1330), PackedInt32Array(1330, 1332, 1331), PackedInt32Array(1335, 1334, 1336), PackedInt32Array(1336, 1334, 1297), PackedInt32Array(1339, 1338, 1337), PackedInt32Array(1337, 1341, 1340), PackedInt32Array(1340, 1299, 1337), PackedInt32Array(1337, 1299, 1339), PackedInt32Array(1342, 1339, 1343), PackedInt32Array(1343, 1339, 1344), PackedInt32Array(1345, 1344, 1339), PackedInt32Array(1334, 1346, 1297), PackedInt32Array(1297, 1346, 1299), PackedInt32Array(1299, 1346, 1339), PackedInt32Array(1339, 1346, 1345), PackedInt32Array(1349, 1348, 1347), PackedInt32Array(1317, 1350, 1316), PackedInt32Array(1316, 1350, 1351), PackedInt32Array(1351, 1350, 976), PackedInt32Array(976, 1350, 1347), PackedInt32Array(976, 1347, 1352), PackedInt32Array(976, 1352, 977), PackedInt32Array(1350, 1349, 1347), PackedInt32Array(1355, 1354, 1353), PackedInt32Array(1358, 1357, 1356), PackedInt32Array(1353, 1360, 1359), PackedInt32Array(1355, 1362, 1361), PackedInt32Array(1365, 1364, 1363), PackedInt32Array(1362, 1355, 1366), PackedInt32Array(1366, 1355, 1353), PackedInt32Array(1366, 1353, 1359), PackedInt32Array(1365, 1363, 1367), PackedInt32Array(1367, 1363, 1356), PackedInt32Array(1356, 1363, 1368), PackedInt32Array(1356, 1368, 1358), PackedInt32Array(1367, 1356, 1369), PackedInt32Array(1366, 1359, 1370), PackedInt32Array(1370, 1359, 1368), PackedInt32Array(1370, 1368, 1363), PackedInt32Array(1370, 1363, 1371), PackedInt32Array(1373, 1372, 1304), PackedInt32Array(1373, 1304, 1374), PackedInt32Array(1374, 1304, 1375), PackedInt32Array(1375, 1304, 1306), PackedInt32Array(1374, 1375, 1376), PackedInt32Array(1378, 1377, 1379), PackedInt32Array(1379, 1377, 1380), PackedInt32Array(1382, 1380, 1381), PackedInt32Array(1381, 1380, 1377), PackedInt32Array(1385, 1384, 1383), PackedInt32Array(1386, 1322, 1387), PackedInt32Array(1387, 1322, 1388), PackedInt32Array(1388, 1322, 1323), PackedInt32Array(1390, 1389, 1391), PackedInt32Array(1391, 1389, 1393), PackedInt32Array(1391, 1393, 1392), PackedInt32Array(1347, 1348, 1394), PackedInt32Array(1396, 1395, 1392), PackedInt32Array(1392, 1395, 1397), PackedInt32Array(1397, 1395, 1398), PackedInt32Array(1394, 1386, 1387), PackedInt32Array(1397, 1399, 1392), PackedInt32Array(1392, 1393, 1396), PackedInt32Array(1395, 1405, 1398), PackedInt32Array(1398, 1405, 1401), PackedInt32Array(1401, 1405, 1402), PackedInt32Array(1402, 1405, 1403), PackedInt32Array(1403, 1404, 1387), PackedInt32Array(1387, 1347, 1394), PackedInt32Array(1347, 1404, 1400), PackedInt32Array(1400, 1405, 1395), PackedInt32Array(1405, 1404, 1403), PackedInt32Array(1404, 1347, 1387), PackedInt32Array(1404, 1405, 1400), PackedInt32Array(1370, 1371, 1376), PackedInt32Array(1376, 1371, 1407), PackedInt32Array(1376, 1407, 1374), PackedInt32Array(1374, 1407, 1406), PackedInt32Array(1409, 1408, 1352), PackedInt32Array(1352, 1347, 1409), PackedInt32Array(1409, 1347, 1410), PackedInt32Array(1410, 1347, 1400), PackedInt32Array(1410, 1411, 1409), PackedInt32Array(1413, 1412, 1414), PackedInt32Array(1414, 1412, 1416), PackedInt32Array(1414, 1416, 1415), PackedInt32Array(1418, 1417, 1415), PackedInt32Array(1415, 1417, 1419), PackedInt32Array(1419, 1417, 1420), PackedInt32Array(1421, 1423, 1422), PackedInt32Array(1422, 1423, 1419), PackedInt32Array(1388, 1420, 1387), PackedInt32Array(1387, 1420, 1403), PackedInt32Array(1403, 1425, 1402), PackedInt32Array(1402, 1425, 1401), PackedInt32Array(1401, 1425, 1398), PackedInt32Array(1398, 1425, 1424), PackedInt32Array(1424, 1425, 1417), PackedInt32Array(1417, 1403, 1420), PackedInt32Array(1403, 1417, 1425), PackedInt32Array(1414, 1415, 1426), PackedInt32Array(1426, 1415, 1423), PackedInt32Array(1423, 1415, 1419), PackedInt32Array(1415, 1427, 1418), PackedInt32Array(1429, 1428, 1430), PackedInt32Array(1430, 1428, 1431), PackedInt32Array(1285, 1286, 1432), PackedInt32Array(1434, 1433, 1432), PackedInt32Array(1432, 1433, 1285), PackedInt32Array(1434, 1435, 1433), PackedInt32Array(1433, 1435, 1431), PackedInt32Array(1433, 1431, 1428), PackedInt32Array(1433, 1428, 1436), PackedInt32Array(1438, 1437, 1439), PackedInt32Array(1439, 1437, 1440), PackedInt32Array(1312, 1313, 1441), PackedInt32Array(1441, 1313, 1442), PackedInt32Array(1442, 1313, 1443), PackedInt32Array(1445, 1444, 1443), PackedInt32Array(1443, 1444, 1442), PackedInt32Array(1449, 1448, 1446), PackedInt32Array(1446, 1448, 1447), PackedInt32Array(1452, 1451, 1450), PackedInt32Array(1450, 1454, 1453), PackedInt32Array(1455, 1452, 1456), PackedInt32Array(1456, 1452, 1457), PackedInt32Array(1457, 1452, 1458), PackedInt32Array(1460, 1459, 1461), PackedInt32Array(1461, 1459, 1462), PackedInt32Array(1450, 1453, 1452), PackedInt32Array(1452, 1453, 1458), PackedInt32Array(1458, 1453, 1463), PackedInt32Array(1453, 1345, 1463), PackedInt32Array(1463, 1345, 1459), PackedInt32Array(1459, 1345, 1462), PackedInt32Array(1462, 1345, 1346), PackedInt32Array(1465, 1464, 1466), PackedInt32Array(1466, 1464, 1468), PackedInt32Array(1466, 1468, 1467), PackedInt32Array(1408, 1409, 1469), PackedInt32Array(1389, 1390, 1470), PackedInt32Array(1470, 1390, 1471), PackedInt32Array(1471, 1472, 1473), PackedInt32Array(1473, 1472, 1475), PackedInt32Array(1473, 1475, 1409), PackedInt32Array(1409, 1475, 1474), PackedInt32Array(1473, 1470, 1471), PackedInt32Array(1474, 1469, 1409), PackedInt32Array(1476, 1025, 1477), PackedInt32Array(1477, 1025, 1474), PackedInt32Array(1474, 1025, 1026), PackedInt32Array(1474, 1475, 1477), PackedInt32Array(1409, 1411, 1473), PackedInt32Array(1479, 1478, 1480), PackedInt32Array(1480, 1478, 1481), PackedInt32Array(1483, 1482, 1484), PackedInt32Array(1484, 1482, 1485), PackedInt32Array(1481, 1486, 1480), PackedInt32Array(1480, 1486, 1487), PackedInt32Array(1487, 1486, 1488), PackedInt32Array(1488, 1486, 1489), PackedInt32Array(1489, 1486, 1407), PackedInt32Array(1407, 1486, 1485), PackedInt32Array(1407, 1485, 1482), PackedInt32Array(1407, 1482, 1406), PackedInt32Array(1490, 1487, 1491), PackedInt32Array(1491, 1487, 1488), PackedInt32Array(1491, 1488, 1492), PackedInt32Array(1494, 1493, 1495), PackedInt32Array(1495, 1493, 1496), PackedInt32Array(1430, 1497, 1429), PackedInt32Array(1429, 1497, 1498), PackedInt32Array(1498, 1497, 1496), PackedInt32Array(1496, 1497, 1495), PackedInt32Array(1495, 1497, 1499), PackedInt32Array(1503, 1502, 1500), PackedInt32Array(1500, 1502, 1501), PackedInt32Array(987, 1021, 1504), PackedInt32Array(1507, 1506, 1505), PackedInt32Array(1509, 1508, 1042), PackedInt32Array(1042, 1508, 1041), PackedInt32Array(1504, 1510, 987), PackedInt32Array(987, 1510, 1505), PackedInt32Array(1505, 1510, 1507), PackedInt32Array(1507, 1510, 1509), PackedInt32Array(1510, 1508, 1509), PackedInt32Array(1513, 1512, 1511), PackedInt32Array(1515, 1514, 1513), PackedInt32Array(1513, 1511, 1515), PackedInt32Array(1515, 1511, 1516), PackedInt32Array(1517, 1463, 1459), PackedInt32Array(1511, 1463, 1516), PackedInt32Array(1516, 1463, 1517), PackedInt32Array(1516, 1517, 1518), PackedInt32Array(1520, 1519, 1521), PackedInt32Array(1521, 1519, 1524), PackedInt32Array(1521, 1524, 1522), PackedInt32Array(1522, 1524, 1523), PackedInt32Array(1444, 1445, 1519), PackedInt32Array(1519, 1445, 1524), PackedInt32Array(1526, 1523, 1525), PackedInt32Array(1525, 1523, 1524), PackedInt32Array(1525, 1528, 1526), PackedInt32Array(1526, 1528, 1527), PackedInt32Array(1526, 1527, 1529), PackedInt32Array(1479, 1531, 1530), PackedInt32Array(1530, 1533, 1532), PackedInt32Array(1532, 1535, 1534), PackedInt32Array(1478, 1479, 1536), PackedInt32Array(1536, 1479, 1530), PackedInt32Array(1536, 1530, 1532), PackedInt32Array(1536, 1532, 1534), PackedInt32Array(1534, 1537, 1536), PackedInt32Array(1536, 1537, 1538), PackedInt32Array(1540, 1539, 1537), PackedInt32Array(1537, 1539, 1541), PackedInt32Array(1541, 1542, 1537), PackedInt32Array(1537, 1542, 1538), PackedInt32Array(1538, 1542, 1543), PackedInt32Array(1543, 1542, 1544), PackedInt32Array(1547, 1546, 1545), PackedInt32Array(1550, 1549, 1548), PackedInt32Array(1552, 1551, 1484), PackedInt32Array(1552, 1484, 1543), PackedInt32Array(1543, 1484, 1553), PackedInt32Array(1553, 1484, 1485), PackedInt32Array(1553, 1538, 1543), PackedInt32Array(1556, 1555, 1554), PackedInt32Array(1559, 1558, 1557), PackedInt32Array(1561, 1560, 1562), PackedInt32Array(1562, 1560, 1563), PackedInt32Array(1564, 1516, 1518), PackedInt32Array(1564, 1518, 1563), PackedInt32Array(1563, 1518, 1562), PackedInt32Array(1569, 1568, 1565), PackedInt32Array(1565, 1568, 1567), PackedInt32Array(1565, 1567, 1566), PackedInt32Array(1572, 1571, 1570), PackedInt32Array(1572, 1570, 1574), PackedInt32Array(1574, 1570, 1573), PackedInt32Array(1574, 1573, 1576), PackedInt32Array(1576, 1573, 1568), PackedInt32Array(1568, 1573, 1567), PackedInt32Array(1567, 1573, 1575), PackedInt32Array(1579, 1578, 1577), PackedInt32Array(1579, 1577, 1580), PackedInt32Array(1583, 1582, 1581), PackedInt32Array(1527, 1528, 1584), PackedInt32Array(1586, 1585, 1587), PackedInt32Array(1587, 1585, 1588), PackedInt32Array(1580, 1590, 1589), PackedInt32Array(1584, 1591, 1527), PackedInt32Array(1527, 1591, 1592), PackedInt32Array(1527, 1592, 1581), PackedInt32Array(1587, 1588, 1594), PackedInt32Array(1594, 1588, 1580), PackedInt32Array(1580, 1588, 1590), PackedInt32Array(1590, 1588, 1593), PackedInt32Array(1580, 1589, 1579), PackedInt32Array(1579, 1589, 1583), PackedInt32Array(1579, 1583, 1592), PackedInt32Array(1592, 1583, 1581), PackedInt32Array(1597, 1596, 1595), PackedInt32Array(1600, 1599, 1598), PackedInt32Array(1602, 1601, 1598), PackedInt32Array(1600, 1598, 1603), PackedInt32Array(1603, 1598, 1601), PackedInt32Array(1603, 1601, 1604), PackedInt32Array(1603, 1604, 1605), PackedInt32Array(1605, 1604, 1607), PackedInt32Array(1605, 1607, 1606), PackedInt32Array(1608, 1606, 1607), PackedInt32Array(1597, 1595, 1609), PackedInt32Array(1609, 1595, 1608), PackedInt32Array(1609, 1608, 1607), PackedInt32Array(1609, 1607, 1610), PackedInt32Array(1613, 1612, 1611), PackedInt32Array(1614, 1616, 1615), PackedInt32Array(1619, 1618, 1620), PackedInt32Array(1620, 1618, 1617), PackedInt32Array(1620, 1617, 1621), PackedInt32Array(1611, 1619, 1620), PackedInt32Array(1616, 1614, 1622), PackedInt32Array(1622, 1614, 1613), PackedInt32Array(1622, 1613, 1611), PackedInt32Array(1622, 1611, 1620), PackedInt32Array(1625, 1624, 1623), PackedInt32Array(1627, 1626, 1628), PackedInt32Array(1628, 1626, 1629), PackedInt32Array(1629, 1626, 1630), PackedInt32Array(1570, 1571, 1631), PackedInt32Array(1631, 1571, 1632), PackedInt32Array(1623, 1631, 1632), PackedInt32Array(1633, 1570, 1631), PackedInt32Array(1623, 1632, 1625), PackedInt32Array(1625, 1632, 1626), PackedInt32Array(1626, 1632, 1630), PackedInt32Array(1630, 1632, 1634), PackedInt32Array(1636, 1635, 1585), PackedInt32Array(1637, 1636, 1638), PackedInt32Array(1638, 1636, 1585), PackedInt32Array(1638, 1585, 1586), PackedInt32Array(1643, 1642, 1637), PackedInt32Array(1637, 1642, 1641), PackedInt32Array(1637, 1641, 1640), PackedInt32Array(1637, 1640, 1639), PackedInt32Array(1641, 1644, 1640), PackedInt32Array(1637, 1638, 1643), PackedInt32Array(1566, 1644, 1565), PackedInt32Array(1565, 1644, 1641), PackedInt32Array(1646, 1645, 1647), PackedInt32Array(1648, 1621, 1649), PackedInt32Array(1649, 1621, 1650), PackedInt32Array(1650, 1621, 1617), PackedInt32Array(1651, 1649, 1650), PackedInt32Array(1647, 1645, 1652), PackedInt32Array(1652, 1645, 1651), PackedInt32Array(1652, 1651, 1650), PackedInt32Array(1652, 1650, 1653), PackedInt32Array(1629, 1647, 1628), PackedInt32Array(1628, 1647, 1652), PackedInt32Array(1656, 1655, 1654), PackedInt32Array(1493, 1494, 1657), PackedInt32Array(1657, 1494, 1658), PackedInt32Array(1658, 1660, 1659), PackedInt32Array(1662, 1661, 1656), PackedInt32Array(1656, 1661, 1657), PackedInt32Array(1656, 1657, 1658), PackedInt32Array(1656, 1658, 1659), PackedInt32Array(1665, 1664, 1663), PackedInt32Array(1663, 1664, 1599), PackedInt32Array(1599, 1664, 1598), PackedInt32Array(1664, 1665, 1666), PackedInt32Array(1666, 1665, 1662), PackedInt32Array(1666, 1662, 1656), PackedInt32Array(1666, 1656, 1654), PackedInt32Array(1669, 1668, 1667), PackedInt32Array(1614, 1615, 1670), PackedInt32Array(1614, 1670, 1671), PackedInt32Array(1671, 1670, 1667), PackedInt32Array(1667, 1670, 1669), PackedInt32Array(1669, 1670, 1672), PackedInt32Array(1670, 1673, 1672), PackedInt32Array(1674, 1669, 1672), PackedInt32Array(1674, 1675, 1669), PackedInt32Array(1669, 1675, 1597), PackedInt32Array(1669, 1597, 1609), PackedInt32Array(1677, 1676, 1678), PackedInt32Array(1678, 1676, 1679), PackedInt32Array(1679, 1680, 1678), PackedInt32Array(1682, 1681, 1683), PackedInt32Array(1683, 1681, 1685), PackedInt32Array(1683, 1685, 1684), PackedInt32Array(1687, 1686, 1688), PackedInt32Array(1688, 1686, 1684), PackedInt32Array(1685, 1688, 1684), PackedInt32Array(1690, 1689, 1691), PackedInt32Array(1691, 1689, 1692), PackedInt32Array(1691, 1692, 1693), PackedInt32Array(1693, 1692, 1694), PackedInt32Array(1696, 1695, 1694), PackedInt32Array(1694, 1695, 1693), PackedInt32Array(1698, 1697, 1699), PackedInt32Array(1699, 1697, 1700), PackedInt32Array(1700, 1697, 1701), PackedInt32Array(1702, 1707, 1703), PackedInt32Array(1703, 1707, 1704), PackedInt32Array(1704, 1707, 1706), PackedInt32Array(1704, 1706, 1705), PackedInt32Array(1710, 1709, 1708), PackedInt32Array(1713, 1712, 1711), PackedInt32Array(1715, 1714, 1711), PackedInt32Array(1711, 1714, 1713), PackedInt32Array(1718, 1717, 1716), PackedInt32Array(1722, 1721, 1719), PackedInt32Array(1719, 1721, 1720), PackedInt32Array(1725, 1724, 1723), PackedInt32Array(1728, 1727, 1726), PackedInt32Array(1731, 1730, 1729), PackedInt32Array(1733, 1732, 1729), PackedInt32Array(1729, 1732, 1731), PackedInt32Array(1736, 1735, 1734), PackedInt32Array(1740, 1739, 1737), PackedInt32Array(1737, 1739, 1738), PackedInt32Array(1744, 1743, 1741), PackedInt32Array(1741, 1743, 1742), PackedInt32Array(1746, 1745, 1747), PackedInt32Array(1747, 1745, 1748), PackedInt32Array(1751, 1750, 1749), PackedInt32Array(1752, 1751, 1753), PackedInt32Array(1753, 1751, 1754), PackedInt32Array(1754, 1751, 1755), PackedInt32Array(1749, 1757, 1756), PackedInt32Array(1760, 1759, 1758), PackedInt32Array(1751, 1749, 1756), PackedInt32Array(1763, 1762, 1761), PackedInt32Array(1760, 1758, 1761), PackedInt32Array(1761, 1758, 1764), PackedInt32Array(1762, 1765, 1761), PackedInt32Array(1756, 1563, 1751), PackedInt32Array(1751, 1563, 1755), PackedInt32Array(1755, 1563, 1764), PackedInt32Array(1761, 1764, 1763), PackedInt32Array(1763, 1764, 1563), PackedInt32Array(1763, 1563, 1766), PackedInt32Array(1766, 1563, 1560), PackedInt32Array(1768, 1767, 1769), PackedInt32Array(1769, 1767, 1770), PackedInt32Array(1773, 1772, 1771), PackedInt32Array(1776, 1775, 1774), PackedInt32Array(1778, 1777, 1779), PackedInt32Array(1779, 1777, 1780), PackedInt32Array(1777, 1781, 1780), PackedInt32Array(1780, 1781, 1782), PackedInt32Array(1782, 1781, 1783), PackedInt32Array(1786, 1785, 1784), PackedInt32Array(1787, 1786, 1788), PackedInt32Array(1788, 1786, 1789), PackedInt32Array(1784, 1791, 1790), PackedInt32Array(1793, 1792, 1794), PackedInt32Array(1794, 1792, 1795), PackedInt32Array(1795, 1789, 1786), PackedInt32Array(1784, 1790, 1763), PackedInt32Array(1786, 1784, 1795), PackedInt32Array(1795, 1784, 1763), PackedInt32Array(1795, 1763, 1794), PackedInt32Array(1794, 1763, 1766), PackedInt32Array(1798, 1797, 1796), PackedInt32Array(1799, 1801, 1800), PackedInt32Array(1802, 1804, 1803), PackedInt32Array(1805, 1807, 1806), PackedInt32Array(1809, 1808, 1798), PackedInt32Array(1807, 1805, 1799), PackedInt32Array(1799, 1805, 1804), PackedInt32Array(1804, 1802, 1809), PackedInt32Array(1799, 1796, 1801), PackedInt32Array(1801, 1796, 1810), PackedInt32Array(1810, 1796, 1811), PackedInt32Array(1811, 1796, 1812), PackedInt32Array(1809, 1798, 1804), PackedInt32Array(1804, 1798, 1799), PackedInt32Array(1796, 1813, 1812), PackedInt32Array(1799, 1798, 1796), PackedInt32Array(1816, 1815, 1814), PackedInt32Array(1819, 1818, 1817), PackedInt32Array(1821, 1820, 1822), PackedInt32Array(1822, 1820, 1823), PackedInt32Array(1823, 1820, 1824), PackedInt32Array(1826, 1825, 1817), PackedInt32Array(1817, 1825, 1827), PackedInt32Array(1817, 1828, 1819), PackedInt32Array(1824, 1820, 1827), PackedInt32Array(1827, 1820, 1829), PackedInt32Array(1827, 1829, 1828), PackedInt32Array(1827, 1828, 1817), PackedInt32Array(1827, 1825, 1830), PackedInt32Array(1834, 1833, 1831), PackedInt32Array(1831, 1833, 1832), PackedInt32Array(1836, 1835, 1801), PackedInt32Array(1838, 1837, 1836), PackedInt32Array(1838, 1836, 1839), PackedInt32Array(1839, 1836, 1801), PackedInt32Array(1839, 1801, 1810), PackedInt32Array(1841, 1840, 1810), PackedInt32Array(1810, 1840, 1839), PackedInt32Array(1839, 1840, 1842), PackedInt32Array(1841, 1843, 1840), PackedInt32Array(1846, 1845, 1844), PackedInt32Array(1849, 1848, 1847), PackedInt32Array(1850, 1846, 1851), PackedInt32Array(1851, 1846, 1852), PackedInt32Array(1855, 1854, 1853), PackedInt32Array(1856, 1544, 1857), PackedInt32Array(1857, 1544, 1853), PackedInt32Array(1853, 1544, 1858), PackedInt32Array(1858, 1544, 1542), PackedInt32Array(1860, 1859, 1861), PackedInt32Array(1861, 1859, 1862), PackedInt32Array(1855, 1864, 1863), PackedInt32Array(1542, 1865, 1858), PackedInt32Array(1852, 1846, 1853), PackedInt32Array(1853, 1846, 1844), PackedInt32Array(1853, 1844, 1866), PackedInt32Array(1853, 1866, 1857), PackedInt32Array(1868, 1867, 1864), PackedInt32Array(1869, 1849, 1847), PackedInt32Array(1871, 1870, 1865), PackedInt32Array(1865, 1870, 1847), PackedInt32Array(1865, 1847, 1858), PackedInt32Array(1859, 1872, 1869), PackedInt32Array(1844, 1873, 1866), PackedInt32Array(1874, 1868, 1864), PackedInt32Array(1864, 1853, 1874), PackedInt32Array(1874, 1853, 1858), PackedInt32Array(1866, 1875, 1857), PackedInt32Array(1862, 1859, 1870), PackedInt32Array(1870, 1859, 1869), PackedInt32Array(1870, 1869, 1847), PackedInt32Array(1864, 1855, 1853), PackedInt32Array(1858, 1876, 1874), PackedInt32Array(1842, 1840, 1857), PackedInt32Array(1857, 1840, 1856), PackedInt32Array(1879, 1878, 1877), PackedInt32Array(1877, 1881, 1880), PackedInt32Array(1877, 1880, 1879), PackedInt32Array(1879, 1880, 1882), PackedInt32Array(1883, 1880, 1884), PackedInt32Array(1883, 1828, 1829), PackedInt32Array(1880, 1883, 1882), PackedInt32Array(1882, 1883, 1829), PackedInt32Array(1882, 1829, 1886), PackedInt32Array(1882, 1886, 1885), PackedInt32Array(1885, 1887, 1882), PackedInt32Array(1888, 1890, 1889), PackedInt32Array(1888, 1892, 1891), PackedInt32Array(1891, 1812, 1888), PackedInt32Array(1888, 1812, 1890), PackedInt32Array(1890, 1812, 1813), PackedInt32Array(1890, 1813, 1893), PackedInt32Array(1893, 1894, 1890), PackedInt32Array(1895, 1792, 1896), PackedInt32Array(1896, 1792, 1793), PackedInt32Array(1898, 1897, 1899), PackedInt32Array(1899, 1897, 1900), PackedInt32Array(1903, 1902, 1901), PackedInt32Array(1897, 1904, 1900), PackedInt32Array(1900, 1904, 1901), PackedInt32Array(1901, 1904, 1903), PackedInt32Array(1903, 1904, 1905), PackedInt32Array(1907, 1906, 1908), PackedInt32Array(1908, 1906, 1909), PackedInt32Array(1909, 1911, 1910), PackedInt32Array(1914, 1913, 1912), PackedInt32Array(1910, 1915, 1905), PackedInt32Array(1914, 1912, 1904), PackedInt32Array(1904, 1912, 1905), PackedInt32Array(1905, 1912, 1908), PackedInt32Array(1908, 1912, 1916), PackedInt32Array(1910, 1905, 1909), PackedInt32Array(1909, 1905, 1908), PackedInt32Array(1918, 1917, 1919), PackedInt32Array(1919, 1917, 1920), PackedInt32Array(1922, 1921, 1830), PackedInt32Array(1830, 1921, 1924), PackedInt32Array(1830, 1924, 1923), PackedInt32Array(1926, 1925, 1921), PackedInt32Array(1921, 1925, 1924), PackedInt32Array(1929, 1928, 1927), PackedInt32Array(1920, 1827, 1919), PackedInt32Array(1919, 1827, 1830), PackedInt32Array(1919, 1830, 1929), PackedInt32Array(1919, 1929, 1916), PackedInt32Array(1924, 1930, 1923), PackedInt32Array(1830, 1923, 1929), PackedInt32Array(1929, 1927, 1916), PackedInt32Array(1919, 1916, 1912), PackedInt32Array(1931, 1832, 1932), PackedInt32Array(1932, 1832, 1933), PackedInt32Array(1935, 1934, 1936), PackedInt32Array(1936, 1934, 1832), PackedInt32Array(1936, 1832, 1937), PackedInt32Array(1937, 1832, 1833), PackedInt32Array(1940, 1939, 1938), PackedInt32Array(1938, 1941, 1933), PackedInt32Array(1940, 1938, 1934), PackedInt32Array(1934, 1938, 1933), PackedInt32Array(1934, 1933, 1832), PackedInt32Array(1782, 1887, 1780), PackedInt32Array(1780, 1887, 1885), PackedInt32Array(1943, 1942, 1781), PackedInt32Array(1781, 1942, 1944), PackedInt32Array(1781, 1944, 1783), PackedInt32Array(1931, 1945, 1832), PackedInt32Array(1832, 1945, 1831), PackedInt32Array(1948, 1947, 1946), PackedInt32Array(1949, 1948, 1950), PackedInt32Array(1950, 1948, 1946), PackedInt32Array(1950, 1946, 1951), PackedInt32Array(1952, 1831, 1951), PackedInt32Array(1951, 1831, 1950), PackedInt32Array(1950, 1831, 1945), PackedInt32Array(1952, 1953, 1831), PackedInt32Array(1956, 1955, 1954), PackedInt32Array(1956, 1954, 1957), PackedInt32Array(1957, 1954, 1937), PackedInt32Array(1960, 1959, 1958), PackedInt32Array(1960, 1958, 1937), PackedInt32Array(1937, 1958, 1957), PackedInt32Array(1957, 1958, 1961), PackedInt32Array(1954, 1936, 1937), PackedInt32Array(1962, 1961, 1963), PackedInt32Array(1963, 1961, 1958), PackedInt32Array(1902, 1895, 1901), PackedInt32Array(1901, 1895, 1896), PackedInt32Array(1965, 1964, 1946), PackedInt32Array(1946, 1964, 1951), PackedInt32Array(1964, 1944, 1951), PackedInt32Array(1951, 1944, 1942), PackedInt32Array(1893, 1962, 1894), PackedInt32Array(1894, 1962, 1963)]
agent_height = 1.75
agent_radius = 0.375
agent_max_climb = 0.5
edge_max_length = 12.0
filter_low_hanging_obstacles = true
filter_ledge_spans = true
filter_walkable_low_height_spans = true

[sub_resource type="StandardMaterial3D" id="StandardMaterial3D_cin4e"]
albedo_texture = ExtResource("15_df8b0")
metallic = 0.2
metallic_texture = ExtResource("16_003du")
roughness = 0.8
uv1_scale = Vector3(10, 10, 1)

[sub_resource type="StandardMaterial3D" id="StandardMaterial3D_01ds2"]
transparency = 1
albedo_color = Color(1, 1, 0, 0.3)
emission_enabled = true
emission = Color(1, 1, 0, 1)

[node name="level01" type="Node3D"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 0, 0, 1.94383)
script = ExtResource("1_hk2b6")
minimap_scene = ExtResource("2_j32d5")
player_avatar_scene = ExtResource("3_0f42i")
item_boxes_scene = ExtResource("4_swvig")
skill_box_ui_scene = ExtResource("5_dbohx")

[node name="WorldEnvironment" type="WorldEnvironment" parent="."]
environment = SubResource("Environment_j4i7h")

[node name="DirectionalLight3D" type="DirectionalLight3D" parent="."]
transform = Transform3D(0.707107, -0.5, 0.5, 0, 0.707107, 0.707107, -0.707107, -0.5, 0.5, 0, 0, 0)
light_color = Color(1, 0.95, 0.8, 1)
light_energy = 1.2
shadow_enabled = true
shadow_opacity = 0.6
shadow_blur = 1.5

[node name="ModularLevelGenerator" type="Node3D" parent="."]
script = ExtResource("6_6ly8b")
level_config = ExtResource("7_r5102")
level_size = Vector2(200, 200)

[node name="FogOfWar" parent="." instance=ExtResource("9_003du")]
player_vision_radius = 8.0
enabled = false

[node name="Camera3D" type="Camera3D" parent="."]
unique_name_in_owner = true
transform = Transform3D(1, 0, 0, 0, 0.866025, 0.5, 0, -0.5, 0.866025, 0, 30, 50)
v_offset = 20.0
projection = 1
current = true
size = 44.805
near = 0.022
script = ExtResource("26_camera_follower")
target_path = NodePath("../Player")
follow_speed = 10.0
position_offset = Vector3(0, 10, 55)

[node name="UIManager" type="Node" parent="."]
script = ExtResource("25_ui_manager")
border_width = 100.0

[node name="Player" parent="." instance=ExtResource("11_82jtv")]

[node name="Chest" parent="." instance=ExtResource("14_1ks1q")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, 4.57862, 0, -14.4123)
interaction_distance = 3.0
item_resource = ExtResource("17_j32d5")

[node name="Chest2" parent="." instance=ExtResource("14_1ks1q")]
transform = Transform3D(1.5, 0, 0, 0, 1.5, 0, 0, 0, 1.5, 20, 0.165, -3.779)
interaction_distance = 3.0
item_resource = ExtResource("18_j32d5")

[node name="EnemyBoy01" parent="." instance=ExtResource("19_0f42i")]
transform = Transform3D(0.8, 0, 0, 0, 0.751754, 0.273616, 0, -0.273616, 0.751754, -10.0014, 0, -8.65824)

[node name="Enemy02" parent="." instance=ExtResource("21_dbohx")]
transform = Transform3D(2.1, 0, 0, 0, 1.97335, 0.718242, 0, -0.718242, 1.97335, 7.86917, 0, -7.768)

[node name="NavigationRegion3D" type="NavigationRegion3D" parent="."]
navigation_mesh = SubResource("NavigationMesh_xcdtp")

[node name="Floor" type="Node3D" parent="NavigationRegion3D"]

[node name="@CSGBox3D@165767" type="CSGBox3D" parent="NavigationRegion3D/Floor"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 0, -0.05, 0)
use_collision = true
size = Vector3(200, 0.1, 200)
material = SubResource("StandardMaterial3D_cin4e")

[node name="FloorTiles" type="Node3D" parent="NavigationRegion3D/Floor"]

[node name="Floor01_0" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 39.7618, 0.1, 11.3672)

[node name="Floor01_1" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 52.3561, 0.1, 33.0367)

[node name="Floor01_2" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -11.9782, 0.1, 18.0415)

[node name="Floor01_3" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 55.2862, 0.1, -24.8758)

[node name="Floor01_4" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 29.6308, 0.1, -66.6661)

[node name="Floor01_5" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 58.1264, 0.1, 10.0124)

[node name="Floor01_6" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -8.4418, 0.1, -53.0584)

[node name="Floor01_7" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -32.613, 0.1, 42.5186)

[node name="Floor01_8" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -52.4171, 0.1, 16.9282)

[node name="Floor01_9" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 33.0841, 0.1, 83.7462)

[node name="Floor01_10" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 57.1935, 0.1, 50.23)

[node name="Floor01_11" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -45.0486, 0.1, -19.3061)

[node name="Floor01_12" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 39.4345, 0.1, 28.5606)

[node name="Floor01_13" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -97.3822, 0.1, -1.40539)

[node name="Floor01_14" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 23.1295, 0.1, 9.51368)

[node name="Floor01_15" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -19.9302, 0.1, 52.6467)

[node name="Floor01_16" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -37.6765, 0.1, -93.5468)

[node name="Floor01_17" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 57.8895, 0.1, 19.6132)

[node name="Floor01_18" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 9.75547, 0.1, -64.3738)

[node name="Floor01_19" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 0.609247, 0.1, -31.4898)

[node name="Floor01_20" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -35.7579, 0.1, -54.7506)

[node name="Floor01_21" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -57.3504, 0.1, 37.4791)

[node name="Floor01_22" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -21.3918, 0.1, -38.3991)

[node name="Floor01_23" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -89.7865, 0.1, 30.0997)

[node name="Floor01_24" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -51.7902, 0.1, -5.97753)

[node name="Floor01_25" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -38.9824, 0.1, -39.1194)

[node name="Floor01_26" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -92.461, 0.1, 91.8421)

[node name="Floor01_27" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -30.2868, 0.1, 96.8079)

[node name="Floor01_28" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -58.9212, 0.1, -14.4992)

[node name="Floor01_29" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 62.3023, 0.1, -81.1677)

[node name="Floor01_30" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 85.2439, 0.1, 22.8133)

[node name="Floor01_31" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -95.0043, 0.1, 68.6526)

[node name="Floor01_32" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -40.8642, 0.1, 70.8059)

[node name="Floor01_33" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 8.7564, 0.1, 69.1155)

[node name="Floor01_34" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -30.6665, 0.1, -66.927)

[node name="Floor01_35" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 59.599, 0.1, 92.6302)

[node name="Floor01_36" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -55.1636, 0.1, -89.3436)

[node name="Floor01_37" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -13.513, 0.1, 36.517)

[node name="Floor01_38" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -48.4735, 0.1, 36.5184)

[node name="Floor01_39" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 89.5972, 0.1, -0.150026)

[node name="Floor01_40" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 92.5373, 0.1, 39.0556)

[node name="Floor01_41" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 96.8692, 0.1, -53.1301)

[node name="Floor01_42" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -68.3377, 0.1, -88.1092)

[node name="Floor01_43" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -78.7301, 0.1, 94.8494)

[node name="Floor01_44" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 14.4739, 0.1, -27.0227)

[node name="Floor01_45" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -2.85284, 0.1, -20.3024)

[node name="Floor01_46" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -24.3154, 0.1, 60.2129)

[node name="Floor01_47" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 15.4981, 0.1, 17.1748)

[node name="Floor01_48" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -93.7415, 0.1, -32.6658)

[node name="Floor01_49" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 16.0073, 0.1, -17.4052)

[node name="Floor01_50" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -87.6281, 0.1, -26.7279)

[node name="Floor01_51" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 90.1414, 0.1, -65.6104)

[node name="Floor01_52" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 12.3561, 0.1, -92.1937)

[node name="Floor01_53" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 40.5408, 0.1, 69.2278)

[node name="Floor01_54" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -44.566, 0.1, 6.4279)

[node name="Floor01_55" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 16.8341, 0.1, -55.7551)

[node name="Floor01_56" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -84.6319, 0.1, 37.0115)

[node name="Floor01_57" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -18.9197, 0.1, 26.5142)

[node name="Floor01_58" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 87.7673, 0.1, -16.9332)

[node name="Floor01_59" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 42.1088, 0.1, -52.4307)

[node name="Floor01_60" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 2.43458, 0.1, 0.826997)

[node name="Floor01_61" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 87.0051, 0.1, 46.0888)

[node name="Floor01_62" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 41.4211, 0.1, -34.4318)

[node name="Floor01_63" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -3.82536, 0.1, -39.0521)

[node name="Floor01_64" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 69.2648, 0.1, -55.8725)

[node name="Floor01_65" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 29.8799, 0.1, 19.3733)

[node name="Floor01_66" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 3.31206, 0.1, 50.1137)

[node name="Floor01_67" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -0.460444, 0.1, 26.1092)

[node name="Floor01_68" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -60.5837, 0.1, -39.0928)

[node name="Floor01_69" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -14.1243, 0.1, 94.8599)

[node name="Floor01_70" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -12.7312, 0.1, 2.16083)

[node name="Floor01_71" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 11.0695, 0.1, 81.272)

[node name="Floor01_72" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -21.8363, 0.1, -89.6299)

[node name="Floor01_73" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -26.1372, 0.1, 32.7126)

[node name="Floor01_74" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 62.6975, 0.1, -63.486)

[node name="Floor01_75" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 0.82308, 0.1, -11.7714)

[node name="Floor01_76" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 16.0863, 0.1, 0.535372)

[node name="Floor01_77" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -13.6963, 0.1, 45.4164)

[node name="Floor01_78" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -50.4135, 0.1, -57.2279)

[node name="Floor01_79" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -28.4768, 0.1, 24.1079)

[node name="Floor01_80" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 18.1192, 0.1, 36.8195)

[node name="Floor01_81" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -82.6726, 0.1, 19.3169)

[node name="Floor01_82" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -3.62023, 0.1, 91.7874)

[node name="Floor01_83" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 60.4803, 0.1, -17.9759)

[node name="Floor01_84" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -93.8929, 0.1, -11.3163)

[node name="Floor01_85" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -52.4981, 0.1, 88.6511)

[node name="Floor01_86" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 93.248, 0.1, 63.8629)

[node name="Floor01_87" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -1.93938, 0.1, 8.10202)

[node name="Floor01_88" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 15.8557, 0.1, -42.738)

[node name="Floor01_89" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 91.6137, 0.1, -37.7546)

[node name="Floor01_90" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 75.8684, 0.1, -95.3548)

[node name="Floor01_91" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -30.1237, 0.1, -17.2176)

[node name="Floor01_92" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 66.4509, 0.1, -90.9975)

[node name="Floor01_93" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 66.5525, 0.1, 84.6571)

[node name="Floor01_94" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 46.9891, 0.1, 44.2278)

[node name="Floor01_95" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -21.2115, 0.1, -26.7043)

[node name="Floor01_96" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -29.6451, 0.1, -84.0133)

[node name="Floor01_97" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 69.0051, 0.1, 8.88431)

[node name="Floor01_98" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 39.5107, 0.1, -62.4092)

[node name="Floor01_99" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -41.6112, 0.1, -66.782)

[node name="Floor01_100" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 11.6286, 0.1, -7.10053)

[node name="Floor01_101" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -80.5988, 0.1, -16.6597)

[node name="Floor01_102" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 82.0703, 0.1, 7.23355)

[node name="Floor01_103" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -62.4568, 0.1, 14.0273)

[node name="Floor01_104" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 69.4924, 0.1, 35.3048)

[node name="Floor01_105" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -97.5423, 0.1, -97.7765)

[node name="Floor01_106" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 1.26058, 0.1, -92.7147)

[node name="Floor01_107" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 9.19692, 0.1, -49.3807)

[node name="Floor01_108" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 60.2834, 0.1, 64.3722)

[node name="Floor01_109" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 49.9927, 0.1, -10.8268)

[node name="Floor01_110" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -56.0755, 0.1, 68.82)

[node name="Floor01_111" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 13.9865, 0.1, 93.8118)

[node name="Floor01_112" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 37.8663, 0.1, 44.456)

[node name="Floor01_113" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -41.8014, 0.1, 58.7003)

[node name="Floor01_114" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 25.0642, 0.1, 90.8406)

[node name="Floor01_115" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 89.9629, 0.1, 97.6034)

[node name="Floor01_116" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 67.2403, 0.1, 24.981)

[node name="Floor01_117" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -17.7001, 0.1, -70.6182)

[node name="Floor01_118" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 64.4818, 0.1, -43.2516)

[node name="Floor01_119" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 27.3048, 0.1, -87.8216)

[node name="Floor01_120" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -28.8292, 0.1, 10.9635)

[node name="Floor01_121" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -68.5735, 0.1, 5.43251)

[node name="Floor01_122" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -58.2628, 0.1, 80.1036)

[node name="Floor01_123" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 80.6183, 0.1, -57.7623)

[node name="Floor01_124" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 4.97092, 0.1, 18.8673)

[node name="Floor01_125" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 30.3703, 0.1, 97.8991)

[node name="Floor01_126" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -88.3809, 0.1, -72.6584)

[node name="Floor01_127" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -78.7564, 0.1, -90.3738)

[node name="Floor01_128" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 5.69693, 0.1, 32.4103)

[node name="Floor01_129" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 95.4954, 0.1, -24.4841)

[node name="Floor01_130" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 77.1571, 0.1, 79.5698)

[node name="Floor01_131" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 62.7843, 0.1, -3.68457)

[node name="Floor01_132" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 26.7223, 0.1, 0.33228)

[node name="Floor01_133" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -92.3436, 0.1, -59.4606)

[node name="Floor01_134" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -5.2657, 0.1, 43.8768)

[node name="Floor01_135" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 87.9655, 0.1, 81.4218)

[node name="Floor01_136" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 44.5719, 0.1, -88.7167)

[node name="Floor01_137" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -84.9725, 0.1, -45.2554)

[node name="Floor01_138" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -63.541, 0.1, -27.1469)

[node name="Floor01_139" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 48.7429, 0.1, 16.1723)

[node name="Floor01_140" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -83.3441, 0.1, 85.6886)

[node name="Floor01_141" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 78.9385, 0.1, 88.485)

[node name="Floor01_142" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -81.0407, 0.1, -66.0022)

[node name="Floor01_143" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 84.9495, 0.1, -87.724)

[node name="Floor01_144" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 46.4277, 0.1, 86.5589)

[node name="Floor01_145" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 38.3114, 0.1, 91.2606)

[node name="Floor01_146" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -17.1107, 0.1, -45.7046)

[node name="Floor01_147" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 70.1148, 0.1, 97.7518)

[node name="Floor01_148" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -39.4991, 0.1, 82.1374)

[node name="Floor01_149" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -84.9754, 0.1, 63.988)

[node name="Floor01_150" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 96.8579, 0.1, -70.7533)

[node name="Floor01_151" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 96.471, 0.1, 76.6094)

[node name="Floor01_152" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 29.1168, 0.1, -43.0136)

[node name="Floor01_153" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 34.6813, 0.1, -26.83)

[node name="Floor01_154" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -96.7683, 0.1, 56.9905)

[node name="Floor01_155" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 96.9998, 0.1, 52.5929)

[node name="Floor01_156" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -91.4864, 0.1, -89.0764)

[node name="Floor01_157" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -81.0534, 0.1, -53.0371)

[node name="Floor01_158" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -95.5501, 0.1, 20.0159)

[node name="Floor01_159" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 25.7638, 0.1, 56.044)

[node name="Floor01_160" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -29.5615, 0.1, 83.6255)

[node name="Floor01_161" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -94.6994, 0.1, 11.8212)

[node name="Floor01_162" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -27.2237, 0.1, 70.4305)

[node name="Floor01_163" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -72.3066, 0.1, 82.1101)

[node name="Floor01_164" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -9.77921, 0.1, 64.2976)

[node name="Floor01_165" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 73.0526, 0.1, -84.3967)

[node name="Floor01_166" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -46.8901, 0.1, 97.5057)

[node name="Floor01_167" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -43.9108, 0.1, 24.008)

[node name="Floor01_168" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -56.6212, 0.1, 24.9692)

[node name="Floor01_169" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -54.413, 0.1, 4.77427)

[node name="Floor01_170" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 47.7108, 0.1, -0.291259)

[node name="Floor01_171" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -80.0157, 0.1, 47.6575)

[node name="Floor01_172" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -63.9285, 0.1, -1.58938)

[node name="Floor01_173" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -22.9853, 0.1, 5.21915)

[node name="Floor01_174" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -20.7759, 0.1, 90.194)

[node name="Floor01_175" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -51.0478, 0.1, -26.4019)

[node name="Floor01_176" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 96.5121, 0.1, -16.3884)

[node name="Floor01_177" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -87.6941, 0.1, 78.1963)

[node name="Floor01_178" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 69.7027, 0.1, 65.4134)

[node name="Floor01_179" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -32.9189, 0.1, 2.7976)

[node name="Floor01_180" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 84.5399, 0.1, -76.5911)

[node name="Floor01_181" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 11.657, 0.1, 54.7992)

[node name="Floor01_182" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 38.3635, 0.1, -97.3935)

[node name="Floor01_183" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -83.4457, 0.1, 7.76913)

[node name="Floor01_184" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 24.0008, 0.1, -20.7188)

[node name="Floor01_185" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 80.7585, 0.1, 61.059)

[node name="Floor01_186" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -63.8291, 0.1, -58.7884)

[node name="Floor01_187" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 53.2538, 0.1, -35.3013)

[node name="Floor01_188" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -16.231, 0.1, -55.6942)

[node name="Floor01_189" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -14.9314, 0.1, 80.1444)

[node name="Floor01_190" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -64.158, 0.1, 54.5142)

[node name="Floor01_191" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 21.8329, 0.1, -34.7657)

[node name="Floor01_192" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -38.8162, 0.1, 96.1484)

[node name="Floor01_193" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 58.3431, 0.1, 84.4841)

[node name="Floor01_194" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 88.2571, 0.1, -46.8049)

[node name="Floor01_195" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 25.088, 0.1, -50.3654)

[node name="Floor01_196" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -62.3136, 0.1, 30.7034)

[node name="Floor01_197" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -6.80339, 0.1, -8.9897)

[node name="Floor01_198" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 48.4464, 0.1, 62.6625)

[node name="Floor01_199" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -84.9457, 0.1, -7.54755)

[node name="Floor01_200" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -40.1963, 0.1, 16.0851)

[node name="Floor01_201" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -85.7776, 0.1, 53.3467)

[node name="Floor01_202" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 33.1097, 0.1, -10.3327)

[node name="Floor01_203" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 96.5601, 0.1, -88.8104)

[node name="Floor01_204" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -68.8718, 0.1, -10.8822)

[node name="Floor01_205" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 5.95657, 0.1, 41.2359)

[node name="Floor01_206" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 97.5569, 0.1, 31.4598)

[node name="Floor01_207" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 68.8412, 0.1, -11.8423)

[node name="Floor01_208" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 43.89, 0.1, -24.8232)

[node name="Floor01_209" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -3.53026, 0.1, -86.1418)

[node name="Floor01_210" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 65.1969, 0.1, -35.1918)

[node name="Floor01_211" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 95.4086, 0.1, -7.65249)

[node name="Floor01_212" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 13.1315, 0.1, 29.2198)

[node name="Floor01_213" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -35.5778, 0.1, -6.874)

[node name="Floor01_214" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -85.1099, 0.1, -34.5497)

[node name="Floor01_215" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -18.7989, 0.1, -4.49396)

[node name="Floor01_216" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -15.351, 0.1, -97.4378)

[node name="Floor01_217" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 80.9063, 0.1, -3.85117)

[node name="Floor01_218" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -63.1214, 0.1, 91.7485)

[node name="Floor01_219" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 53.4998, 0.1, -68.1735)

[node name="Floor01_220" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 24.3878, 0.1, 42.492)

[node name="Floor01_221" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -41.5217, 0.1, -31.4014)

[node name="Floor01_222" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -51.9457, 0.1, 48.1938)

[node name="Floor01_223" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -34.0538, 0.1, 30.815)

[node name="Floor01_224" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 54.0838, 0.1, -97.198)

[node name="Floor01_225" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -0.246422, 0.1, 63.4535)

[node name="Floor01_226" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -26.5135, 0.1, -58.3108)

[node name="Floor01_227" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -56.1528, 0.1, -47.9311)

[node name="Floor01_228" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -48.1412, 0.1, -46.4836)

[node name="Floor01_229" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 62.502, 0.1, 42.1556)

[node name="Floor01_230" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -34.0454, 0.1, 50.7316)

[node name="Floor01_231" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 33.0097, 0.1, -57.0425)

[node name="Floor01_232" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -49.1296, 0.1, -96.9522)

[node name="Floor01_233" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 96.8926, 0.1, 89.7844)

[node name="Floor01_234" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -17.109, 0.1, -80.796)

[node name="Floor01_235" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -45.9119, 0.1, -80.6375)

[node name="Floor01_236" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 52.5469, 0.1, -48.8469)

[node name="Floor01_237" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -95.2025, 0.1, 44.8715)

[node name="Floor01_238" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 27.2997, 0.1, 31.5952)

[node name="Floor01_239" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 67.9938, 0.1, 56.6671)

[node name="Floor01_240" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -32.349, 0.1, -31.2754)

[node name="Floor01_241" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 91.3263, 0.1, -96.5132)

[node name="Floor01_242" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 34.4405, 0.1, -81.0886)

[node name="Floor01_243" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 96.0125, 0.1, 8.70313)

[node name="Floor01_244" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 80.7681, 0.1, -25.0113)

[node name="Floor01_245" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 20.675, 0.1, -10.5325)

[node name="Floor01_246" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -67.8936, 0.1, 36.9341)

[node name="Floor01_247" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -69.7182, 0.1, -46.8648)

[node name="Floor01_248" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -42.6373, 0.1, -2.62454)

[node name="Floor01_249" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 48.9675, 0.1, -60.8277)

[node name="Floor01_250" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 37.6078, 0.1, 0.534946)

[node name="Floor01_251" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -8.69806, 0.1, -31.1478)

[node name="Floor01_252" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 3.04528, 0.1, 86.8315)

[node name="Floor01_253" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -69.4559, 0.1, 47.5437)

[node name="Floor01_254" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 42.8034, 0.1, 51.5899)

[node name="Floor01_255" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 55.7534, 0.1, 1.08828)

[node name="Floor01_256" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 33.8958, 0.1, 51.5494)

[node name="Floor01_257" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -88.6431, 0.1, -96.6724)

[node name="Floor01_258" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -76.4219, 0.1, -82.6737)

[node name="Floor01_259" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 68.4858, 0.1, -19.9849)

[node name="Floor01_260" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 52.2086, 0.1, -84.5076)

[node name="Floor01_261" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -5.56713, 0.1, -67.5197)

[node name="Floor01_262" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -95.8844, 0.1, -21.7462)

[node name="Floor01_263" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, 37.2077, 0.1, -18.7007)

[node name="Floor01_264" type="Node3D" parent="NavigationRegion3D/Floor/FloorTiles" instance=ExtResource("17_2bvpm")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -96.9616, 0.1, 82.1143)

[node name="wall_0" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1.25299, 0, -0.370738, 0, 1, 0, 0.355542, 0, 1.30655, -72.5, 0, -75)

[node name="wall_1" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(0.984753, 0, 0.202547, 0, 1, 0, -0.25494, 0, 0.782378, -67.5, 0, -75)

[node name="wall_2" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(0.896433, 0, 0.0175024, 0, 1, 0, -0.0160986, 0, 0.974601, -60, 0, -75)

[node name="wall_3" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(0.897095, 0, 0.0125819, 0, 1, 0, -0.0101061, 0, 1.11686, -52.5, 0, -75)

[node name="wall_4" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(0.792173, 0, -0.13868, 0, 1, 0, 0.137125, 0, 0.801159, -45, 0, -75)

[node name="wall_5" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1.02033, 0, -0.147293, 0, 1, 0, 0.180555, 0, 0.832365, -37.5, 0, -75)

[node name="wall_6" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(0.815058, 0, -0.27947, 0, 1, 0, 0.222884, 0, 1.02198, -30, 0, -75)

[node name="wall_7" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1.09186, 0, -0.173675, 0, 1, 0, 0.217212, 0, 0.873007, -22.5, 0, -75)

[node name="wall_8" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1.1732, 0, 0.330605, 0, 1, 0, -0.291795, 0, 1.32924, -15, 0, -75)

[node name="wall_9" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1.11577, 0, 0.0374055, 0, 1, 0, -0.0486292, 0, 0.858249, -7.5, 0, -75)

[node name="wall_10" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1.19502, 0, 0.0617191, 0, 1, 0, -0.0721802, 0, 1.02183, 0, 0, -75)

[node name="wall_11" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(0.972296, 0, 0.208521, 0, 1, 0, -0.257532, 0, 0.787256, 5, 0, -75)

[node name="wall_12" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1.07272, 0, 0.270635, 0, 1, 0, -0.282745, 0, 1.02678, 12.5, 0, -75)

[node name="wall_13" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1.12447, 0, 0.0919925, 0, 1, 0, -0.105296, 0, 0.982399, 20, 0, -75)

[node name="wall_14" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1.09228, 0, -0.246317, 0, 1, 0, 0.348819, 0, 0.771311, 27.5, 0, -75)

[node name="wall_15" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(0.849623, 0, 0.316479, 0, 1, 0, -0.235018, 0, 1.14412, 35, 0, -75)

[node name="wall_16" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(0.915393, -0.0102946, -0.200685, 0, 0.99848, -0.0593167, 0.174029, 0.0541496, 1.0556, 42.5, 0.310763, -75)

[node name="wall_17" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1.072, -0.00425906, 0.193436, 0, 0.999801, 0.0180922, -0.234174, -0.019497, 0.885508, 50, 0.477076, -75)

[node name="wall_18" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1.29692, 0, 0.409134, 0, 1, 0, -0.403747, 0, 1.31422, 57.5, 0, -75)

[node name="wall_19" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1.27096, 0, 0.00126587, 0, 1, 0, -0.00131913, 0, 1.21966, 65, 0, -75)

[node name="wall_20" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1.28354, 0, -0.468914, 0, 1, 0, 0.465828, 0, 1.29205, 72.5, 0, -75)

[node name="wall_21" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(0.98786, 0, -0.0482881, 0, 1, 0, 0.050657, 0, 0.941663, -72.5, 0, 75)

[node name="wall_22" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1.29313, 0, -0.172232, 0, 1, 0, 0.180843, 0, 1.23155, -67.5, 0, 75)

[node name="wall_23" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1.16313, 0, 0.351963, 0, 1, 0, -0.33729, 0, 1.21373, -60, 0, 75)

[node name="wall_24" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1.28796, 0, 0.192042, 0, 1, 0, -0.197761, 0, 1.25072, -52.5, 0, 75)

[node name="wall_25" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(0.777873, 0, -0.325361, 0, 1, 0, 0.269636, 0, 0.938632, -45, 0, 75)

[node name="wall_26" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1.22792, 0, -0.348447, 0, 1, 0, 0.37087, 0, 1.15368, -37.5, 0, 75)

[node name="wall_27" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1.06853, 0, 0.169172, 0, 1, 0, -0.187468, 0, 0.964249, -30, 0, 75)

[node name="wall_28" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1.34932, 0, 0.327242, 0, 1, 0, -0.367788, 0, 1.20057, -22.5, 0, 75)

[node name="wall_29" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(0.900717, 0.00248697, -0.0615675, 0, 0.999219, 0.0386793, 0.0568057, -0.0394337, 0.97622, -15, 0.345815, 75)

[node name="wall_30" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(0.904567, 0.0148152, 0.323389, 0, 0.999047, -0.0416332, -0.326376, 0.041061, 0.896287, -7.5, 0.341449, 75)

[node name="wall_31" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(0.979894, 0, -0.0492685, 0, 1, 0, 0.0454856, 0, 1.06139, 0, 0, 75)

[node name="wall_32" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1.27711, 0, -0.290315, 0, 1, 0, 0.308351, 0, 1.20241, 5, 0, 75)

[node name="wall_33" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(0.939622, 0, -0.150256, 0, 1, 0, 0.142497, 0, 0.990786, 12.5, 0, 75)

[node name="wall_34" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(0.8684, 0, 0.409238, 0, 1, 0, -0.315391, 0, 1.1268, 20, 0, 75)

[node name="wall_35" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(0.888007, 0, -0.184501, 0, 1, 0, 0.147125, 0, 1.11359, 27.5, 0, 75)

[node name="wall_36" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1.16986, 0, 0.00161714, 0, 1, 0, -0.00176604, 0, 1.07123, 35, 0, 75)

[node name="wall_37" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1.27983, 0, -0.260861, 0, 1, 0, 0.266525, 0, 1.25263, 42.5, 0, 75)

[node name="wall_38" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1.26052, 0, 0.295435, 0, 1, 0, -0.316621, 0, 1.17617, 50, 0, 75)

[node name="wall_39" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1.13853, 0, 0.163572, 0, 1, 0, -0.218701, 0, 0.851532, 57.5, 0, 75)

[node name="wall_40" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1.16157, 0, -0.164146, 0, 1, 0, 0.204615, 0, 0.931828, 65, 0, 75)

[node name="wall_41" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(1.12003, 0, -0.0532629, 0, 1, 0, 0.0734951, 0, 0.811703, 72.5, 0, 75)

[node name="wall_42" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-0.24675, 0, 1.1143, 0, 1, 0, -0.827533, 0, -0.332258, -75, 0, -70)

[node name="wall_43" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(0.255788, 0, 1.14387, 0, 1, 0, -0.842814, 0, 0.347156, -75, 0, -65)

[node name="wall_44" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(0.301221, 0, 1.16523, 0, 1, 0, -1.20836, 0, 0.29047, -75, 0, -57.5)

[node name="wall_45" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(0.120831, 0, 1.0132, 0, 1, 0, -0.889719, 0, 0.137601, -75, 0, -50)

[node name="wall_46" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-0.118377, 0.00184153, 0.960158, 0, 0.999998, -0.00179233, -1.01256, -0.000215292, -0.112251, -75, 0.43652, -42.5)

[node name="wall_47" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(0.0660062, 0, 0.946452, 0, 1, 0, -1.10554, 0, 0.0565077, -75, 0, -35)

[node name="wall_48" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-0.17116, 0, 1.22186, 0, 1, 0, -1.21455, 0, -0.172191, -75, 0, -27.5)

[node name="wall_49" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(0.36025, 0, 1.04213, 0, 1, 0, -1.10373, 0, 0.340146, -75, 0, -20)

[node name="wall_50" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(0.137337, 0, 0.95266, 0, 1, 0, -1.15755, 0, 0.113028, -75, 0, -12.5)

[node name="wall_51" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(0.0538715, 0, 0.954917, 0, 1, 0, -0.814068, 0, 0.0631923, -75, 0, -5)

[node name="wall_52" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(0.11912, 0, 0.915418, 0, 1, 0, -0.861869, 0, 0.126521, -75, 0, 2.5)

[node name="wall_53" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-0.0623247, 0, 1.08842, 0, 1, 0, -0.881991, 0, -0.0769119, -75, 0, 10)

[node name="wall_54" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(0.364718, 0, 1.12503, 0, 1, 0, -1.10302, 0, 0.371995, -75, 0, 17.5)

[node name="wall_55" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-0.0810911, 0, 0.805037, 0, 1, 0, -0.903595, 0, -0.0722462, -75, 0, 25)

[node name="wall_56" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-0.297945, 0, 0.922051, 0, 1, 0, -1.11786, 0, -0.245756, -75, 0, 32.5)

[node name="wall_57" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-0.0883593, 0, 0.850535, 0, 1, 0, -0.871784, 0, -0.0862056, -75, 0, 40)

[node name="wall_58" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-0.309796, 0, 1.03373, 0, 1, 0, -1.05776, 0, -0.302759, -75, 0, 47.5)

[node name="wall_59" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-0.246143, 0, 0.781485, 0, 1, 0, -0.936083, 0, -0.205492, -75, 0, 55)

[node name="wall_60" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(0.0568826, 0, 0.873889, 0, 1, 0, -1.05431, 0, 0.0471484, -75, 0, 62.5)

[node name="wall_61" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-0.281938, 0, 0.96387, 0, 1, 0, -1.09286, 0, -0.248662, -75, 0, 70)

[node name="wall_62" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-0.14135, 0, 0.922738, 0, 1, 0, -1.12592, 0, -0.115843, 75, 0, -70)

[node name="wall_63" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-0.032965, 0, 1.04997, 0, 1, 0, -0.834793, 0, -0.041462, 75, 0, -65)

[node name="wall_64" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(0.230484, 0, 0.97507, 0, 1, 0, -0.940515, 0, 0.238952, 75, 0, -57.5)

[node name="wall_65" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-0.0673694, 0, 1.28416, 0, 1, 0, -1.32063, 0, -0.0655089, 75, 0, -50)

[node name="wall_66" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(0.0906043, 0, 1.12946, 0, 1, 0, -0.802362, 0, 0.127541, 75, 0, -42.5)

[node name="wall_67" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-0.264652, 0, 0.779752, 0, 1, 0, -0.9033, 0, -0.228454, 75, 0, -35)

[node name="wall_68" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-0.0223433, 0, 1.10408, 0, 1, 0, -1.18646, 0, -0.0207918, 75, 0, -27.5)

[node name="wall_69" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(0.0989008, 0, 1.29118, 0, 1, 0, -1.35483, 0, 0.094254, 75, 0, -20)

[node name="wall_70" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(0.00526806, 0, 0.908843, 0, 1, 0, -1.10344, 0, 0.004339, 75, 0, -12.5)

[node name="wall_71" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-0.282849, 0, 1.30968, 0, 1, 0, -1.26675, 0, -0.292436, 75, 0, -5)

[node name="wall_72" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(0.0583861, 0, 0.919892, 0, 1, 0, -1.0832, 0, 0.0495834, 75, 0, 2.5)

[node name="wall_73" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(0.222904, 0, 0.875762, 0, 1, 0, -0.897597, 0, 0.217481, 75, 0, 10)

[node name="wall_74" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(0.0627886, 0, 0.849293, 0, 1, 0, -0.854557, 0, 0.0624018, 75, 0, 17.5)

[node name="wall_75" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-0.060166, 0, 1.26294, 0, 1, 0, -1.376, 0, -0.0552224, 75, 0, 25)

[node name="wall_76" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-0.309195, 0, 0.904108, 0, 1, 0, -1.0713, 0, -0.260941, 75, 0, 32.5)

[node name="wall_77" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-0.279879, 0.0743011, 0.992482, 0, 0.996927, -0.0822216, -0.837961, -0.0248166, -0.331489, 75, 0.497516, 40)

[node name="wall_78" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(0.230589, -0.0552698, 0.901236, 0, 0.998391, 0.0525247, -1.00371, -0.0126975, 0.207047, 75, 0.419736, 47.5)

[node name="wall_79" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(0.10313, 0, 1.33689, 0, 1, 0, -1.26373, 0, 0.109101, 75, 0, 55)

[node name="wall_80" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(0.216402, 0, 0.842072, 0, 1, 0, -1.08466, 0, 0.168004, 75, 0, 62.5)

[node name="wall_81" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("14_df8b0")]
transform = Transform3D(-0.071386, 0, 0.99083, 0, 1, 0, -1.05454, 0, -0.0670733, 75, 0, 70)

[node name="tree_0" type="Node3D" parent="NavigationRegion3D" instance=ExtResource("15_003du")]
transform = Transform3D(2.5, -0.00145593, -0.00167976, 0.00145725, 2.5, 0.00196474, 0.00167861, -0.00196571, 2.5, -11.8725, 0, -22.1879)
script = ExtResource("18_oh2bt")
highlight_material = SubResource("StandardMaterial3D_01ds2")
progress_bar_scene = ExtResource("19_fcnrs")

[node name="PatrolPoint_0" type="Node3D" parent="NavigationRegion3D"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -11.1116, 1, -26.7774)
script = ExtResource("15_6ly8b")

[node name="tree_1" type="Node3D" parent="NavigationRegion3D" instance=ExtResource("15_003du")]
transform = Transform3D(2.5, -0.00145593, -0.00167976, 0.00145725, 2.5, 0.00196474, 0.00167861, -0.00196571, 2.5, 24.5478, 0, -28.7713)
script = ExtResource("18_oh2bt")
highlight_material = SubResource("StandardMaterial3D_01ds2")
progress_bar_scene = ExtResource("19_fcnrs")

[node name="PatrolPoint_1" type="Node3D" parent="NavigationRegion3D"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 22.3877, 1, -28.5646)
script = ExtResource("15_6ly8b")
point_id = 1

[node name="tree_2" type="Node3D" parent="NavigationRegion3D" instance=ExtResource("15_003du")]
transform = Transform3D(2.5, -0.00145593, -0.00167976, 0.00145725, 2.5, 0.00196474, 0.00167861, -0.00196571, 2.5, -27.9041, 0, -38.2333)
script = ExtResource("18_oh2bt")
highlight_material = SubResource("StandardMaterial3D_01ds2")
preset_burnt_out_type = 1
progress_bar_scene = ExtResource("19_fcnrs")

[node name="PatrolPoint_2" type="Node3D" parent="NavigationRegion3D"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -28.2931, 1, -32.5188)
script = ExtResource("15_6ly8b")
point_id = 2

[node name="tree_3" type="Node3D" parent="NavigationRegion3D" instance=ExtResource("15_003du")]
transform = Transform3D(2.5, -0.00145593, -0.00167976, 0.00145725, 2.5, 0.00196474, 0.00167861, -0.00196571, 2.5, 31.138, 0, 37.6457)
script = ExtResource("18_oh2bt")
highlight_material = SubResource("StandardMaterial3D_01ds2")
progress_bar_scene = ExtResource("19_fcnrs")

[node name="PatrolPoint_3" type="Node3D" parent="NavigationRegion3D"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 32.5406, 1, 43.2859)
script = ExtResource("15_6ly8b")
point_id = 3

[node name="chest_0" type="Area3D" parent="NavigationRegion3D" instance=ExtResource("14_1ks1q")]
transform = Transform3D(0.799064, 0, -1.83344, 0, 2, 0, 1.83344, 0, 0.799064, -64.7323, 0, 62.7414)
collision_layer = 8
collision_mask = 16
script = ExtResource("21_1ks1q")
interaction_distance = 1.5
item_resource = ExtResource("22_xcdtp")
unlock_sound = ExtResource("23_cin4e")

[node name="rock_0" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("21_xcdtp")]
transform = Transform3D(-0.156988, 0, -0.834849, 0, 0.849481, 0, 0.834849, 0, -0.156988, 11.19, 0.01, -39.7153)
collision_layer = 2
collision_mask = 0

[node name="rock_1" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("21_xcdtp")]
transform = Transform3D(-0.879553, 0, -0.25886, 0, 0.916855, 0, 0.25886, 0, -0.879553, -4.40326, 0.01, 54.4804)
collision_layer = 2
collision_mask = 0

[node name="rock_2" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("21_xcdtp")]
transform = Transform3D(0.624193, 0, -0.584537, 0, 0.855161, 0, 0.584537, 0, 0.624193, -39.273, 0.01, 35.962)
collision_layer = 2
collision_mask = 0

[node name="rock_3" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("21_xcdtp")]
transform = Transform3D(1.14249, 0, -0.169151, 0, 1.15495, 0, 0.169151, 0, 1.14249, -53.5993, 0.01, -69.1024)
collision_layer = 2
collision_mask = 0

[node name="rock_4" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("21_xcdtp")]
transform = Transform3D(-0.576165, 0, 0.62191, 0, 0.847785, 0, -0.62191, 0, -0.576165, -3.98712, 0.01, -14.9214)
collision_layer = 2
collision_mask = 0

[node name="rock_5" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("21_xcdtp")]
transform = Transform3D(-0.553454, 0, 0.793708, 0, 0.967617, 0, -0.793708, 0, -0.553454, 56.0679, 0.01, -42.7137)
collision_layer = 2
collision_mask = 0

[node name="rock_6" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("21_xcdtp")]
transform = Transform3D(-0.186338, 0, -0.88105, 0, 0.900539, 0, 0.88105, 0, -0.186338, -50.0223, 0.01, 56.8425)
collision_layer = 2
collision_mask = 0

[node name="rock_7" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("21_xcdtp")]
transform = Transform3D(0.510641, 0, 0.65446, 0, 0.830104, 0, -0.65446, 0, 0.510641, -64.9659, 0.01, -19.5614)
collision_layer = 2
collision_mask = 0

[node name="rock_8" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("21_xcdtp")]
transform = Transform3D(-0.817026, 0, 0.54827, 0, 0.983937, 0, -0.54827, 0, -0.817026, -68.1342, 0.01, -55.3319)
collision_layer = 2
collision_mask = 0

[node name="rock_9" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("21_xcdtp")]
transform = Transform3D(0.620048, 0, -0.781558, 0, 0.997643, 0, 0.781558, 0, 0.620048, -69.1266, 0.01, -39.8945)
collision_layer = 2
collision_mask = 0

[node name="rock_10" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("21_xcdtp")]
transform = Transform3D(1.12645, 0, 0.254583, 0, 1.15486, 0, -0.254583, 0, 1.12645, -10.2056, 0.01, 13.303)
collision_layer = 2
collision_mask = 0

[node name="rock_11" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("21_xcdtp")]
transform = Transform3D(0.510488, 0, 0.718148, 0, 0.881098, 0, -0.718148, 0, 0.510488, 38.3175, 0.01, 60.7273)
collision_layer = 2
collision_mask = 0

[node name="rock_12" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("21_xcdtp")]
transform = Transform3D(0.563871, 0, 0.791827, 0, 0.97208, 0, -0.791827, 0, 0.563871, 50.7458, 0.01, -43.213)
collision_layer = 2
collision_mask = 0

[node name="rock_13" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("21_xcdtp")]
transform = Transform3D(-0.794852, 0, 0.835095, 0, 1.1529, 0, -0.835095, 0, -0.794852, -29.1261, 0.01, -9.53249)
collision_layer = 2
collision_mask = 0

[node name="rock_14" type="StaticBody3D" parent="NavigationRegion3D" instance=ExtResource("21_xcdtp")]
transform = Transform3D(0.919296, 0, 0.293192, 0, 0.964918, 0, -0.293192, 0, 0.919296, 38.8021, 0.01, 6.32484)
collision_layer = 2
collision_mask = 0

[node name="barrel_0" type="StaticBody3D" parent="NavigationRegion3D" groups=["destructible"] instance=ExtResource("22_cin4e")]
transform = Transform3D(-0.591295, 0, 0.691397, 0, 0.909758, 0, -0.691397, 0, -0.591295, 26.2035, 0, 67.9781)
collision_layer = 3
collision_mask = 3

[node name="barrel_1" type="StaticBody3D" parent="NavigationRegion3D" groups=["destructible"] instance=ExtResource("22_cin4e")]
transform = Transform3D(-1.17603, 0, -0.10027, 0, 1.1803, 0, 0.10027, 0, -1.17603, 14.915, 0, 50.3596)
collision_layer = 3
collision_mask = 3

[node name="decoration_0" type="Node3D" parent="NavigationRegion3D" instance=ExtResource("23_6j2fk")]
transform = Transform3D(-0.918081, 0, -0.761088, 0, 1.19253, 0, 0.761088, 0, -0.918081, 86.863, 0, 74.2238)

[node name="decoration_1" type="Node3D" parent="NavigationRegion3D" instance=ExtResource("23_6j2fk")]
transform = Transform3D(0.864581, 0, -0.314103, 0, 0.91987, 0, 0.314103, 0, 0.864581, -36.105, 0, 57.3904)

[node name="decoration_2" type="Node3D" parent="NavigationRegion3D" instance=ExtResource("23_6j2fk")]
transform = Transform3D(-0.24812, 0, -0.654006, 0, 0.699491, 0, 0.654006, 0, -0.24812, -64.1537, 0, 86.676)

[node name="decoration_3" type="Node3D" parent="NavigationRegion3D" instance=ExtResource("23_6j2fk")]
transform = Transform3D(-1.00886, 0, 0.802077, 0, 1.28884, 0, -0.802077, 0, -1.00886, -62.6634, 0, 77.4493)

[node name="decoration_4" type="Node3D" parent="NavigationRegion3D" instance=ExtResource("23_6j2fk")]
transform = Transform3D(0.14284, 0, 0.831147, 0, 0.843332, 0, -0.831147, 0, 0.14284, 38.8975, 0, -91.9757)

[node name="decoration_5" type="Node3D" parent="NavigationRegion3D" instance=ExtResource("23_6j2fk")]
transform = Transform3D(0.544787, 0, 0.831302, 0, 0.99391, 0, -0.831302, 0, 0.544787, 57.1664, 0, -92.872)

[node name="decoration_6" type="Node3D" parent="NavigationRegion3D" instance=ExtResource("23_6j2fk")]
transform = Transform3D(0.127742, 0, 1.22719, 0, 1.23383, 0, -1.22719, 0, 0.127742, -33.9367, 0, -76.4635)

[node name="decoration_7" type="Node3D" parent="NavigationRegion3D" instance=ExtResource("23_6j2fk")]
transform = Transform3D(0.545908, 0, -1.1002, 0, 1.22819, 0, 1.1002, 0, 0.545908, -94.5692, 0, -81.0156)

[node name="decoration_8" type="Node3D" parent="NavigationRegion3D" instance=ExtResource("23_6j2fk")]
transform = Transform3D(0.17861, 0, 0.66675, 0, 0.690259, 0, -0.66675, 0, 0.17861, 36.0322, 0, -43.9614)

[node name="decoration_9" type="Node3D" parent="NavigationRegion3D" instance=ExtResource("23_6j2fk")]
transform = Transform3D(-0.340655, 0, -0.729169, 0, 0.804819, 0, 0.729169, 0, -0.340655, -5.12538, 0, -48.6766)

[node name="decoration_10" type="Node3D" parent="NavigationRegion3D" instance=ExtResource("23_6j2fk")]
transform = Transform3D(-0.322381, 0, -0.572126, 0, 0.656702, 0, 0.572126, 0, -0.322381, 59.5803, 0, -87.9583)

[node name="decoration_11" type="Node3D" parent="NavigationRegion3D" instance=ExtResource("23_6j2fk")]
transform = Transform3D(0.660025, 0, -0.0850424, 0, 0.665481, 0, 0.0850424, 0, 0.660025, -84.4603, 0, 74.257)

[node name="decoration_12" type="Node3D" parent="NavigationRegion3D" instance=ExtResource("23_6j2fk")]
transform = Transform3D(-0.195664, 0, -1.23465, 0, 1.25006, 0, 1.23465, 0, -0.195664, -86.9826, 0, -66.6146)

[node name="decoration_13" type="Node3D" parent="NavigationRegion3D" instance=ExtResource("23_6j2fk")]
transform = Transform3D(0.658419, 0, 1.17115, 0, 1.34354, 0, -1.17115, 0, 0.658419, 74.043, 0, -17.1601)

[node name="decoration_14" type="Node3D" parent="NavigationRegion3D" instance=ExtResource("23_6j2fk")]
transform = Transform3D(0.452347, 0, 0.701695, 0, 0.834861, 0, -0.701695, 0, 0.452347, -63.9188, 0, -51.3805)

[node name="decoration_15" type="Node3D" parent="NavigationRegion3D" instance=ExtResource("23_6j2fk")]
transform = Transform3D(-0.999965, 0, 0.580377, 0, 1.15619, 0, -0.580377, 0, -0.999965, 46.9385, 0, -68.7806)

[node name="decoration_16" type="Node3D" parent="NavigationRegion3D" instance=ExtResource("23_6j2fk")]
transform = Transform3D(-0.730019, 0, -0.913206, 0, 1.16913, 0, 0.913206, 0, -0.730019, 80.4643, 0, -68.117)

[node name="decoration_17" type="Node3D" parent="NavigationRegion3D" instance=ExtResource("23_6j2fk")]
transform = Transform3D(-1.00827, 0, 0.369272, 0, 1.07377, 0, -0.369272, 0, -1.00827, 90.3052, 0, -78.1831)

[node name="decoration_18" type="Node3D" parent="NavigationRegion3D" instance=ExtResource("23_6j2fk")]
transform = Transform3D(-0.190232, 0, 0.749861, 0, 0.773614, 0, -0.749861, 0, -0.190232, 65.078, 0, 13.2364)

[node name="decoration_19" type="Node3D" parent="NavigationRegion3D" instance=ExtResource("23_6j2fk")]
transform = Transform3D(-0.97312, 0, 0.029921, 0, 0.97358, 0, -0.029921, 0, -0.97312, -46.7939, 0, 18.7656)

[node name="PatrolPointManager" type="Node" parent="."]
script = ExtResource("24_j2uky")
