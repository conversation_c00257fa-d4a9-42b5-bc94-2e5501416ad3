shader_type spatial;
render_mode blend_mix, depth_draw_never, cull_disabled, unshaded, depth_test_disabled;

uniform vec2 player_position;
uniform float vision_radius = 10.0;
uniform vec4 fog_color_unexplored : source_color;
uniform vec4 fog_color_explored : source_color;
uniform vec2[100] explored_positions;
uniform int num_explored_areas;

void fragment() {
	// 获取世界坐标
	vec3 world_pos = (INV_VIEW_MATRIX * vec4(VERTEX, 1.0)).xyz;
	vec2 pos_2d = vec2(world_pos.x, world_pos.z);
	
	// 计算到玩家的距离
	float dist_to_player = distance(pos_2d, player_position);
	
	// 初始化为未探索状态（黑色）
	vec4 final_color = fog_color_unexplored;  // 默认使用未探索的颜色（黑色）
	float visibility = 0.0;
	
	// 检查是否在玩家视野范围内
	if (dist_to_player < vision_radius) {
		visibility = 1.0 - smoothstep(vision_radius * 0.8, vision_radius, dist_to_player);
	}
	
	// 检查是否在已探索区域内
	bool is_explored = false;
	for (int i = 0; i < num_explored_areas; i++) {
		float dist_to_explored = distance(pos_2d, explored_positions[i]);
		if (dist_to_explored < vision_radius) {
			is_explored = true;
			visibility = max(visibility, 0.5);
		}
	}
	
	// 根据不同状态设置颜色
	if (visibility >= 1.0) {
		// 完全可见区域，完全透明
		final_color = vec4(0.0, 0.0, 0.0, 0.0);
	} else if (is_explored) {
		// 已探索但不完全可见的区域，使用灰色半透明
		final_color = fog_color_explored;
	} else {
		// 未探索区域，使用黑色不透明
		final_color = fog_color_unexplored;
	}
	
	// 输出最终颜色
	ALBEDO = final_color.rgb;
	ALPHA = final_color.a;
} 