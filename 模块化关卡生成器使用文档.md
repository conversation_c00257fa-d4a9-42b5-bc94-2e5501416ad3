# 模块化关卡生成器使用文档

## 概述

模块化关卡生成器是一个强大的工具，用于在Godot游戏中快速创建和生成3D关卡。它采用模块化的设计，可以生成地面、墙壁、树木、宝箱和装饰物等各种游戏元素。通过灵活的配置系统，您可以轻松控制关卡的外观和内容。

## 主要特性

- **模块化设计**：各个生成器模块相互独立，可以单独启用或禁用
- **可配置的生成参数**：通过配置资源文件，轻松调整关卡大小、元素密度等参数
- **编辑器集成**：直接在Godot编辑器中可视化生成和调整关卡
- **运行时生成**：支持在游戏运行时动态生成关卡
- **自动导航网格生成**：自动为生成的关卡烘焙导航网格，为AI提供寻路支持

## 入门指南

### 1. 准备工作

在使用模块化关卡生成器之前，您需要确保以下文件已存在：

- `Scripts/ModularLevelGenerator.gd`：核心生成器脚本
- `Scripts/LevelConfig.gd`：关卡配置资源类
- `Scripts/GeneratorModules/GeneratorModule.gd`：基础模块类
- 各种特定生成器模块脚本（如FloorGeneratorModule.gd等）
- 适合各模块的预制体（如墙壁、树木等）

### 2. 添加生成器到场景

1. 创建一个新的3D场景
2. 添加一个Node3D节点并重命名为"ModularLevelGenerator"
3. 将`ModularLevelGenerator.gd`脚本附加到该节点
4. 可选：添加一个Node节点并附加`LevelGeneratorStarter.gd`脚本，以启用运行时生成

### 3. 配置生成器参数

在Inspector面板中，您可以设置以下参数：

- **level_config**：关卡配置资源（可选）
- **直接配置**：
  - **level_size**：关卡大小（Vector2）
  - **cell_size**：网格单元大小
  - **wall_height**：墙壁高度
- **生成控制**：
  - **generate**：生成关卡
  - **clear**：清除关卡
  - **reset**：重置参数
  - **bake_navmesh**：烘焙导航网格

## 使用关卡配置资源

### 创建新的关卡配置

1. 在编辑器中，选择ModularLevelGenerator节点
2. 通过代码调用`create_level_config()`方法创建配置：
   ```gdscript
   var config = $ModularLevelGenerator.create_level_config("my_level")
   ```
3. 这将在`res://Resources/LevelConfigs/`目录下创建一个新的配置文件

### 编辑关卡配置

1. 在Godot的文件系统面板中找到生成的配置文件（*.tres）
2. 双击打开它进行编辑
3. 可以设置以下参数：
   - 关卡基本属性（ID、名称、大小等）
   - 启用的生成器模块列表
   - 各模块的具体配置参数

### 在运行时使用配置生成关卡

1. 添加LevelGeneratorStarter节点
2. 设置level_config_path指向要使用的配置文件
3. 将auto_generate设置为true，或在游戏中调用其generate_level()方法

## 可用的生成器模块

### 地面生成器 (FloorGeneratorModule)

创建关卡的基本地面。

**配置选项**：
- **color**：地面颜色
- **metallic**：金属感程度
- **roughness**：粗糙度
- **texture_scale**：纹理缩放比例

### 墙壁生成器 (WallGeneratorModule)

在关卡边界或内部创建墙壁。

**配置选项**：
- **wall_scene**：墙壁场景文件路径
- **spacing**：墙壁间距
- **only_border**：是否只在边界生成
- **wall_height**：墙壁高度

### 树木生成器 (TreeGeneratorModule)

在关卡中随机分布树木。

**配置选项**：
- **tree_scene**：树木场景文件路径
- **max_trees**：最大树木数量
- **min_distance**：树木间最小距离
- **border_margin**：距离边界的边距
- **distribution**：分布类型（random/grid/cluster）
- 其他分布特定参数

### 宝箱生成器 (ChestGeneratorModule)

在关卡中放置宝箱。

**配置选项**：
- **chest_scene**：宝箱场景文件路径
- **max_chests**：最大宝箱数量
- **min_distance**：宝箱间最小距离
- **border_margin**：距离边界的边距
- **distribution**：分布类型（random/strategic）
- **min_distance_to_trees**：与树木的最小距离
- 其他分布特定参数

### 装饰物生成器 (DecorationGeneratorModule)

生成各种装饰物，如岩石、木桶和花草。

**配置选项**：
- 各类装饰物场景路径
- 数量限制
- 距离和边距参数
- 分布类型
- 缩放和旋转选项

## 自定义生成器模块

### 创建自定义模块

1. 创建一个新脚本，继承自GeneratorModule
2. 实现以下方法：
   - `_init()`: 设置模块ID、名称和描述
   - `get_default_config()`: 返回默认配置字典
   - `generate(level_generator, nav_region)`: 生成内容
   - `validate_config()`: 验证配置有效性

### 示例：简单的自定义模块

```gdscript
class_name MyCustomModule
extends GeneratorModule

func _init() -> void:
    module_id = "my_custom_module"
    module_name = "我的自定义模块"
    description = "这是一个自定义模块示例"

func get_default_config() -> Dictionary:
    return {
        "my_parameter": 10,
        "my_other_parameter": "value"
    }

func generate(level_generator, nav_region) -> void:
    # 实现生成逻辑
    pass

func validate_config() -> bool:
    # 验证配置有效性
    return true
```

## 实践案例

### 案例一：创建基本关卡

1. 使用默认配置生成一个简单关卡
2. 调整地面颜色和墙壁高度
3. 减少树木数量，增加它们之间的最小距离

### 案例二：创建多样化关卡

1. 设置树木分布为"cluster"以创建树林区域
2. 配置装饰物生成器使用"grouped"分布
3. 增加宝箱的战略位置

### 案例三：运行时随机关卡

1. 使用LevelGeneratorStarter
2. 通过`choose_random_level_config()`方法随机选择配置
3. 在游戏载入时调用`generate_level()`

## 故障排除

### 常见问题

1. **无法生成内容**：
   - 检查预制体路径是否正确
   - 确认模块配置是否有效

2. **导航网格问题**：
   - 重新烘焙导航网格
   - 检查障碍物碰撞设置

3. **性能问题**：
   - 减少生成的对象数量
   - 简化预制体的复杂度

## 进阶技巧

1. 创建多个关卡配置，用于不同的游戏区域
2. 在配置中结合不同的分布类型
3. 添加自定义特效或逻辑到生成的对象
4. 为关卡创建分层的配置系统，如"森林"、"沙漠"等主题

---

祝您使用愉快！如有任何问题，请联系开发团队。 